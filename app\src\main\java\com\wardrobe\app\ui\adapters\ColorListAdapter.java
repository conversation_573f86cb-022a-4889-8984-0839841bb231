package com.wardrobe.app.ui.adapters;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.wardrobe.app.R;
import com.wardrobe.app.model.CategoryItem;
import java.util.List;

public class ColorListAdapter extends RecyclerView.Adapter<ColorListAdapter.ViewHolder> {

    private final Context context;
    private final List<CategoryItem> items;
    private final OnItemClickListener listener;

    public interface OnItemClickListener {
        void onItemClick(CategoryItem item);
    }

    public ColorListAdapter(Context context, List<CategoryItem> items, OnItemClickListener listener) {
        this.context = context;
        this.items = items;
        this.listener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_category_selector, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        CategoryItem item = items.get(position);
        holder.bind(item, listener);
    }

    @Override
    public int getItemCount() {
        return items.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView icon;
        TextView name;
        View colorCircle;

        ViewHolder(View itemView) {
            super(itemView);
            icon = itemView.findViewById(R.id.item_icon);
            name = itemView.findViewById(R.id.item_name);
            colorCircle = itemView.findViewById(R.id.color_preview_circle);
        }

        void bind(final CategoryItem item, final OnItemClickListener listener) {
            name.setText(item.getName());
            itemView.setOnClickListener(v -> listener.onItemClick(item));

            // Default to icon
            icon.setVisibility(View.VISIBLE);
            colorCircle.setVisibility(View.GONE);

            try {
                // The "iconName" field holds the hex color string for colors
                int color = Color.parseColor(item.getIconName());
                icon.setVisibility(View.GONE);
                colorCircle.setVisibility(View.VISIBLE);
                GradientDrawable bgShape = (GradientDrawable) colorCircle.getBackground();
                bgShape.setColor(color);

                 if (item.getIconName().equalsIgnoreCase("#FFFFFF")) {
                    bgShape.setStroke(2, Color.LTGRAY);
                } else {
                    bgShape.setStroke(0, Color.TRANSPARENT);
                }


            } catch (Exception e) {
                // If not a color string, treat as a named item (like "自定义")
                int resId = itemView.getContext().getResources().getIdentifier(item.getIconName(), "drawable", itemView.getContext().getPackageName());
                if (resId != 0) {
                    icon.setImageResource(resId);
                } else {
                    icon.setImageResource(R.drawable.ic_add); // fallback icon
                }
            }
        }
    }
}