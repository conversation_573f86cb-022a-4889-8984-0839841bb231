// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class MultiSelectToolbarBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView btnCancel;

  @NonNull
  public final TextView btnDelete;

  @NonNull
  public final TextView btnSelectAll;

  @NonNull
  public final LinearLayout multiSelectToolbar;

  @NonNull
  public final TextView tvSelectedCount;

  private MultiSelectToolbarBinding(@NonNull LinearLayout rootView, @NonNull TextView btnCancel,
      @NonNull TextView btnDelete, @NonNull TextView btnSelectAll,
      @NonNull LinearLayout multiSelectToolbar, @NonNull TextView tvSelectedCount) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnDelete = btnDelete;
    this.btnSelectAll = btnSelectAll;
    this.multiSelectToolbar = multiSelectToolbar;
    this.tvSelectedCount = tvSelectedCount;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static MultiSelectToolbarBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static MultiSelectToolbarBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.multi_select_toolbar, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static MultiSelectToolbarBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_cancel;
      TextView btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btn_delete;
      TextView btnDelete = ViewBindings.findChildViewById(rootView, id);
      if (btnDelete == null) {
        break missingId;
      }

      id = R.id.btn_select_all;
      TextView btnSelectAll = ViewBindings.findChildViewById(rootView, id);
      if (btnSelectAll == null) {
        break missingId;
      }

      LinearLayout multiSelectToolbar = (LinearLayout) rootView;

      id = R.id.tv_selected_count;
      TextView tvSelectedCount = ViewBindings.findChildViewById(rootView, id);
      if (tvSelectedCount == null) {
        break missingId;
      }

      return new MultiSelectToolbarBinding((LinearLayout) rootView, btnCancel, btnDelete,
          btnSelectAll, multiSelectToolbar, tvSelectedCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
