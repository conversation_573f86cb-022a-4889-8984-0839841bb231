package com.wardrobe.app.utils;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

/**
 * InputValidator单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class InputValidatorTest {
    
    @Test
    public void testValidateClothingName_ValidInput() {
        InputValidator.ValidationResult result = InputValidator.validateClothingName("白色T恤");
        assertTrue("有效的衣物名称应该通过验证", result.isValid());
        assertNull("有效输入不应该有错误信息", result.getErrorMessage());
        assertEquals("白色T恤", result.getCleanedInput());
    }
    
    @Test
    public void testValidateClothingName_EmptyInput() {
        InputValidator.ValidationResult result = InputValidator.validateClothingName("");
        assertFalse("空的衣物名称不应该通过验证", result.isValid());
        assertEquals("衣物名称不能为空", result.getErrorMessage());
    }
    
    @Test
    public void testValidateClothingName_NullInput() {
        InputValidator.ValidationResult result = InputValidator.validateClothingName(null);
        assertFalse("null的衣物名称不应该通过验证", result.isValid());
        assertEquals("衣物名称不能为空", result.getErrorMessage());
    }
    
    @Test
    public void testValidateClothingName_TooLong() {
        String longName = "这是一个非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常长的衣物名称";
        InputValidator.ValidationResult result = InputValidator.validateClothingName(longName);
        assertFalse("过长的衣物名称不应该通过验证", result.isValid());
        assertEquals("衣物名称不能超过50个字符", result.getErrorMessage());
        assertEquals(50, result.getCleanedInput().length());
    }
    
    @Test
    public void testValidateClothingName_InvalidCharacters() {
        InputValidator.ValidationResult result = InputValidator.validateClothingName("T恤<script>");
        assertFalse("包含无效字符的名称不应该通过验证", result.isValid());
        assertEquals("衣物名称包含不安全内容", result.getErrorMessage());
    }
    
    @Test
    public void testValidateBrandName_ValidInput() {
        InputValidator.ValidationResult result = InputValidator.validateBrandName("Nike");
        assertTrue("有效的品牌名称应该通过验证", result.isValid());
        assertEquals("Nike", result.getCleanedInput());
    }
    
    @Test
    public void testValidateBrandName_EmptyInput() {
        InputValidator.ValidationResult result = InputValidator.validateBrandName("");
        assertTrue("空的品牌名称应该通过验证", result.isValid());
        assertEquals("", result.getCleanedInput());
    }
    
    @Test
    public void testValidateCategory_ValidInput() {
        InputValidator.ValidationResult result = InputValidator.validateCategory("上衣");
        assertTrue("有效的分类应该通过验证", result.isValid());
        assertEquals("上衣", result.getCleanedInput());
    }
    
    @Test
    public void testValidateCategory_EmptyInput() {
        InputValidator.ValidationResult result = InputValidator.validateCategory("");
        assertFalse("空的分类不应该通过验证", result.isValid());
        assertEquals("分类不能为空", result.getErrorMessage());
    }
    
    @Test
    public void testValidateColor_ValidInput() {
        InputValidator.ValidationResult result = InputValidator.validateColor("红色");
        assertTrue("有效的颜色应该通过验证", result.isValid());
        assertEquals("红色", result.getCleanedInput());
    }
    
    @Test
    public void testValidateColor_EmptyInput() {
        InputValidator.ValidationResult result = InputValidator.validateColor("");
        assertTrue("空的颜色应该通过验证", result.isValid());
        assertEquals("", result.getCleanedInput());
    }
    
    @Test
    public void testValidateSize_ValidInput() {
        InputValidator.ValidationResult result = InputValidator.validateSize("L");
        assertTrue("有效的尺寸应该通过验证", result.isValid());
        assertEquals("L", result.getCleanedInput());
    }
    
    @Test
    public void testValidateSize_EmptyInput() {
        InputValidator.ValidationResult result = InputValidator.validateSize("");
        assertTrue("空的尺寸应该通过验证", result.isValid());
        assertEquals("", result.getCleanedInput());
    }
    
    @Test
    public void testValidateDescription_ValidInput() {
        InputValidator.ValidationResult result = InputValidator.validateDescription("这是一件很舒适的T恤");
        assertTrue("有效的描述应该通过验证", result.isValid());
        assertEquals("这是一件很舒适的T恤", result.getCleanedInput());
    }
    
    @Test
    public void testValidateDescription_TooLong() {
        StringBuilder longDesc = new StringBuilder();
        for (int i = 0; i < 250; i++) {
            longDesc.append("很");
        }
        
        InputValidator.ValidationResult result = InputValidator.validateDescription(longDesc.toString());
        assertFalse("过长的描述不应该通过验证", result.isValid());
        assertEquals("描述不能超过200个字符", result.getErrorMessage());
        assertEquals(200, result.getCleanedInput().length());
    }
    
    @Test
    public void testValidatePrice_ValidInput() {
        InputValidator.ValidationResult result = InputValidator.validatePrice("99.99");
        assertTrue("有效的价格应该通过验证", result.isValid());
        assertEquals("99.99", result.getCleanedInput());
    }
    
    @Test
    public void testValidatePrice_EmptyInput() {
        InputValidator.ValidationResult result = InputValidator.validatePrice("");
        assertTrue("空的价格应该通过验证", result.isValid());
        assertEquals("0", result.getCleanedInput());
    }
    
    @Test
    public void testValidatePrice_InvalidFormat() {
        InputValidator.ValidationResult result = InputValidator.validatePrice("abc");
        assertFalse("无效格式的价格不应该通过验证", result.isValid());
        assertEquals("价格格式不正确", result.getErrorMessage());
        assertEquals("0", result.getCleanedInput());
    }
    
    @Test
    public void testValidatePrice_NegativeValue() {
        InputValidator.ValidationResult result = InputValidator.validatePrice("-10");
        assertFalse("负数价格不应该通过验证", result.isValid());
        assertEquals("价格不能为负数", result.getErrorMessage());
        assertEquals("0", result.getCleanedInput());
    }
    
    @Test
    public void testValidatePrice_TooLarge() {
        InputValidator.ValidationResult result = InputValidator.validatePrice("9999999");
        assertFalse("过大的价格不应该通过验证", result.isValid());
        assertEquals("价格不能超过999999.99", result.getErrorMessage());
        assertEquals("999999.99", result.getCleanedInput());
    }
    
    @Test
    public void testCleanInput_RemoveDangerousCharacters() {
        String input = "测试<script>alert('xss')</script>";
        String cleaned = InputValidator.cleanInput(input);
        assertFalse("清理后的输入不应该包含危险字符", cleaned.contains("<"));
        assertFalse("清理后的输入不应该包含危险字符", cleaned.contains(">"));
        assertTrue("清理后的输入应该保留安全内容", cleaned.contains("测试"));
    }
    
    @Test
    public void testCleanInput_TrimWhitespace() {
        String input = "  测试内容  ";
        String cleaned = InputValidator.cleanInput(input);
        assertEquals("测试内容", cleaned);
    }
    
    @Test
    public void testCleanInput_NullInput() {
        String cleaned = InputValidator.cleanInput(null);
        assertEquals("", cleaned);
    }
    
    @Test
    public void testIsFilePathSafe_SafePath() {
        assertTrue("安全的文件路径应该通过验证", InputValidator.isFilePathSafe("images/photo.jpg"));
    }
    
    @Test
    public void testIsFilePathSafe_PathTraversal() {
        assertFalse("路径遍历攻击不应该通过验证", InputValidator.isFilePathSafe("../../../etc/passwd"));
        assertFalse("路径遍历攻击不应该通过验证", InputValidator.isFilePathSafe("./config.xml"));
        assertFalse("路径遍历攻击不应该通过验证", InputValidator.isFilePathSafe("images\\..\\config"));
    }
    
    @Test
    public void testIsFilePathSafe_EmptyPath() {
        assertFalse("空路径不应该通过验证", InputValidator.isFilePathSafe(""));
        assertFalse("null路径不应该通过验证", InputValidator.isFilePathSafe(null));
    }
    
    @Test
    public void testIsFilePathSafe_AbsolutePath() {
        assertFalse("绝对路径不应该通过验证", InputValidator.isFilePathSafe("/system/config"));
        assertTrue("Android资源路径应该通过验证", InputValidator.isFilePathSafe("/android_asset/images/icon.png"));
    }
    
    @Test
    public void testValidateId_ValidUUID() {
        String uuid = "550e8400-e29b-41d4-a716-************";
        InputValidator.ValidationResult result = InputValidator.validateId(uuid);
        assertTrue("有效的UUID应该通过验证", result.isValid());
        assertEquals(uuid, result.getCleanedInput());
    }
    
    @Test
    public void testValidateId_ValidNumericId() {
        InputValidator.ValidationResult result = InputValidator.validateId("12345");
        assertTrue("有效的数字ID应该通过验证", result.isValid());
        assertEquals("12345", result.getCleanedInput());
    }
    
    @Test
    public void testValidateId_InvalidFormat() {
        InputValidator.ValidationResult result = InputValidator.validateId("invalid-id");
        assertFalse("无效格式的ID不应该通过验证", result.isValid());
        assertEquals("ID格式不正确", result.getErrorMessage());
    }
    
    @Test
    public void testValidateId_EmptyId() {
        InputValidator.ValidationResult result = InputValidator.validateId("");
        assertFalse("空ID不应该通过验证", result.isValid());
        assertEquals("ID不能为空", result.getErrorMessage());
    }
    
    @Test
    public void testSQLInjectionDetection() {
        String[] sqlInjectionAttempts = {
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "admin'; DELETE FROM clothing; --",
            "SELECT * FROM users"
        };
        
        for (String attempt : sqlInjectionAttempts) {
            InputValidator.ValidationResult result = InputValidator.validateClothingName(attempt);
            assertFalse("SQL注入尝试不应该通过验证: " + attempt, result.isValid());
        }
    }
    
    @Test
    public void testScriptInjectionDetection() {
        String[] scriptInjectionAttempts = {
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "onload=alert('xss')",
            "<img src=x onerror=alert('xss')>"
        };
        
        for (String attempt : scriptInjectionAttempts) {
            InputValidator.ValidationResult result = InputValidator.validateClothingName(attempt);
            assertFalse("脚本注入尝试不应该通过验证: " + attempt, result.isValid());
        }
    }
}
