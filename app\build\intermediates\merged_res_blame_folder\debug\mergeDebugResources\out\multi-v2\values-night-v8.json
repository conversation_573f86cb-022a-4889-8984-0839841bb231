{"logs": [{"outputFile": "com.wardrobe.app-mergeDebugResources-41:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\WardrobeApp\\app\\src\\main\\res\\values-night\\colors.xml", "from": {"startLines": "48,49,50,29,36,37,32,13,19,14,20,7,11,17,5,6,9,10,16,33,28,41,45,44,40,25,23,24,56,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1556,1609,1664,978,1176,1227,1050,439,639,489,684,232,387,589,131,178,282,331,542,1099,931,1352,1485,1428,1303,858,754,805,1885,1741,1788,1837", "endColumns": "52,54,53,45,50,49,48,49,44,51,46,48,50,48,46,53,48,55,46,53,46,52,47,56,48,47,50,52,46,46,48,47", "endOffsets": "1604,1659,1713,1019,1222,1272,1094,484,679,536,726,276,433,633,173,227,326,382,584,1148,973,1400,1528,1480,1347,901,800,853,1927,1783,1832,1880"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,108,163,217,263,314,364,413,463,508,560,607,656,707,756,803,857,906,962,1009,1063,1110,1163,1211,1268,1317,1365,1416,1469,1516,1563,1612", "endColumns": "52,54,53,45,50,49,48,49,44,51,46,48,50,48,46,53,48,55,46,53,46,52,47,56,48,47,50,52,46,46,48,47", "endOffsets": "103,158,212,258,309,359,408,458,503,555,602,651,702,751,798,852,901,957,1004,1058,1105,1158,1206,1263,1312,1360,1411,1464,1511,1558,1607,1655"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46582a7ddf7236be9612eae4b0c579c2\\transformed\\appcompat-1.7.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "83,84,85,86,87,88,89,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3798,3868,3952,4036,4132,4234,4336,7318", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "3863,3947,4031,4127,4229,4331,4425,7402"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\049b486e733e18fbf112fa9fc097a2f3\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,117,118,119,120,121,122,123,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4430,4505,4616,4705,4806,4913,5020,5119,5226,5329,5456,5544,5668,5770,5872,5988,6090,6204,6332,6448,6570,6706,6826,6960,7080,7192,7407,7524,7648,7778,7900,8038,8172,8288", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "4500,4611,4700,4801,4908,5015,5114,5221,5324,5451,5539,5663,5765,5867,5983,6085,6199,6327,6443,6565,6701,6821,6955,7075,7187,7313,7519,7643,7773,7895,8033,8167,8283,8403"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\WardrobeApp\\app\\src\\main\\res\\values-night\\styles.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "34,73,78", "startColumns": "4,4,4", "startOffsets": "1660,3263,3547", "endLines": "72,77,82", "endColumns": "12,12,12", "endOffsets": "3258,3542,3793"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-mergeDebugResources-41:\\values-night-v8\\values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\WardrobeApp\\app\\src\\main\\res\\values-night\\colors.xml", "from": {"startLines": "33,40,41,36,15,23,16,24,7,12,20,5,6,10,11,19,37,32,45,49,48,44,29,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1047,1247,1298,1121,450,692,500,737,200,377,621,99,146,272,321,574,1170,998,1423,1558,1499,1374,923,817,868", "endColumns": "47,50,49,48,49,44,51,46,48,50,48,46,53,48,55,46,53,48,52,47,58,48,49,50,54", "endOffsets": "1090,1293,1343,1165,495,732,547,779,244,423,665,141,195,316,372,616,1219,1042,1471,1601,1553,1418,968,863,918"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,103,154,204,253,303,348,400,447,496,547,596,643,697,746,802,849,903,952,1005,1053,1112,1161,1211,1262", "endColumns": "47,50,49,48,49,44,51,46,48,50,48,46,53,48,55,46,53,48,52,47,58,48,49,50,54", "endOffsets": "98,149,199,248,298,343,395,442,491,542,591,638,692,741,797,844,898,947,1000,1048,1107,1156,1206,1257,1312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\46582a7ddf7236be9612eae4b0c579c2\\transformed\\appcompat-1.7.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "76,77,78,79,80,81,82,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3455,3525,3609,3693,3789,3891,3993,6975", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "3520,3604,3688,3784,3886,3988,4082,7059"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\049b486e733e18fbf112fa9fc097a2f3\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1081,1169,1293,1395,1497,1613,1715,1829,1957,2073,2195,2331,2451,2585,2705,2817,2943,3060,3184,3314,3436,3574,3708,3824", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1076,1164,1288,1390,1492,1608,1710,1824,1952,2068,2190,2326,2446,2580,2700,2812,2938,3055,3179,3309,3431,3569,3703,3819,3939"}, "to": {"startLines": "83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4087,4162,4273,4362,4463,4570,4677,4776,4883,4986,5113,5201,5325,5427,5529,5645,5747,5861,5989,6105,6227,6363,6483,6617,6737,6849,7064,7181,7305,7435,7557,7695,7829,7945", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "4157,4268,4357,4458,4565,4672,4771,4878,4981,5108,5196,5320,5422,5524,5640,5742,5856,5984,6100,6222,6358,6478,6612,6732,6844,6970,7176,7300,7430,7552,7690,7824,7940,8060"}}, {"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\WardrobeApp\\app\\src\\main\\res\\values-night\\styles.xml", "from": {"startLines": "3,44,51", "startColumns": "4,4,4", "startOffsets": "75,1818,2129", "endLines": "41,48,55", "endColumns": "12,12,12", "endOffsets": "1786,2097,2375"}, "to": {"startLines": "27,66,71", "startColumns": "4,4,4", "startOffsets": "1317,2920,3204", "endLines": "65,70,75", "endColumns": "12,12,12", "endOffsets": "2915,3199,3450"}}]}]}