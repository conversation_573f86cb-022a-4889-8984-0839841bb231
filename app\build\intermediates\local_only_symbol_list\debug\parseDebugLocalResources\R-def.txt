R_DEF: Internal format may change without notice
local
anim fade_in
anim fade_out
anim scale_in
anim slide_in_right
anim slide_out_left
color accent_primary
color background_primary
color background_secondary
color black
color borderColor
color border_primary
color bottom_nav_color_selector
color buttonBackground
color buttonTextColor
color cardBackground
color colorBackground
color colorError
color colorOnBackground
color colorOnError
color colorOnPrimary
color colorOnSecondary
color colorOnSurface
color colorPrimary
color colorPrimaryVariant
color colorSecondary
color colorSecondaryVariant
color colorSurface
color containerBackground
color destructive
color destructiveRed
color dividerColor
color fillTertiary
color labelPrimary
color labelQuaternary
color labelSecondary
color labelTertiary
color navigationBarColor
color opaqueSeparator
color placeholderText
color quaternarySystemGroupedBackground
color rippleColor
color secondarySystemGroupedBackground
color selectedItemBackground
color separator
color statusBarColor
color surface_primary
color systemBackground
color systemBlue
color systemGray
color systemGray2
color systemGray3
color systemGray4
color systemGray5
color systemGray6
color systemGroupedBackground
color systemRed
color textColorHint
color textColorPrimary
color textColorSecondary
color text_primary
color text_secondary
color text_tertiary
color white
drawable button_background
drawable button_background_destructive
drawable button_background_prominent
drawable color_circle_background
drawable dialog_background
drawable dialog_rounded_background
drawable dotted_border_background
drawable edit_text_background_ios
drawable ic_add
drawable ic_calendar
drawable ic_category_accessories
drawable ic_category_bottoms
drawable ic_category_dresses
drawable ic_category_outerwear
drawable ic_category_sets
drawable ic_category_shoes
drawable ic_category_suits
drawable ic_category_tops
drawable ic_category_tuxedo
drawable ic_category_underwear
drawable ic_category_workwear
drawable ic_check_circle
drawable ic_chevron_right
drawable ic_clear
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_occasion_casual
drawable ic_occasion_daily
drawable ic_occasion_date
drawable ic_occasion_formal
drawable ic_occasion_home
drawable ic_occasion_outdoor
drawable ic_occasion_party
drawable ic_occasion_sports
drawable ic_occasion_travel
drawable ic_occasion_work
drawable ic_outfit
drawable ic_profile
drawable ic_replace_photo
drawable ic_search
drawable ic_subcategory_bag
drawable ic_subcategory_belt
drawable ic_subcategory_boots
drawable ic_subcategory_bra
drawable ic_subcategory_briefs
drawable ic_subcategory_camisole
drawable ic_subcategory_camisole_dress
drawable ic_subcategory_canvas_shoes
drawable ic_subcategory_coat
drawable ic_subcategory_dress
drawable ic_subcategory_evening_dress
drawable ic_subcategory_flared_skirt
drawable ic_subcategory_formal_dress
drawable ic_subcategory_formal_pants
drawable ic_subcategory_formal_shoes
drawable ic_subcategory_functional
drawable ic_subcategory_hat
drawable ic_subcategory_heels
drawable ic_subcategory_hoodie
drawable ic_subcategory_jacket
drawable ic_subcategory_jeans
drawable ic_subcategory_jewelry
drawable ic_subcategory_jumpsuit
drawable ic_subcategory_mermaid_skirt
drawable ic_subcategory_other_acc
drawable ic_subcategory_outer_vest
drawable ic_subcategory_pajamas
drawable ic_subcategory_pants
drawable ic_subcategory_pencil_skirt
drawable ic_subcategory_pleated_skirt
drawable ic_subcategory_polo
drawable ic_subcategory_rain_boots
drawable ic_subcategory_sandals
drawable ic_subcategory_scarf
drawable ic_subcategory_shirt
drawable ic_subcategory_shorts
drawable ic_subcategory_skirt
drawable ic_subcategory_skirt_bottom
drawable ic_subcategory_slit_skirt
drawable ic_subcategory_sneakers
drawable ic_subcategory_socks
drawable ic_subcategory_spaghetti_dress
drawable ic_subcategory_sport_pants
drawable ic_subcategory_sport_shoes
drawable ic_subcategory_straight_skirt
drawable ic_subcategory_suit
drawable ic_subcategory_sweater
drawable ic_subcategory_thermal
drawable ic_subcategory_tracksuit
drawable ic_subcategory_traditional
drawable ic_subcategory_tshirt
drawable ic_subcategory_vest
drawable ic_subcategory_wedding
drawable ic_wardrobe
drawable ios_style_group_background_bottom
drawable ios_style_group_background_middle
drawable ios_style_group_background_single
drawable ios_style_group_background_top
drawable option_background
drawable replace_icon_background
drawable search_background
drawable secondary_system_grouped_background_ripple
drawable selected_item_background
drawable spinner_background
id action_delete_multiple
id batch_category_selector_view
id bottom_nav_container
id bottom_navigation
id btn_add_color
id btn_back
id btn_batch_add
id btn_cancel
id btn_cleanup_images
id btn_cleanup_unused
id btn_compress_images
id btn_delete
id btn_save_batch
id btn_save_outfit
id btn_select_all
id btn_select_images_batch
id btn_share_outfit
id btn_view_detail
id button_cancel
id button_choose_gallery
id button_replace_photo
id button_save
id button_take_photo
id category_icon
id category_recycler_view
id category_selector
id category_title
id chip_group_filter
id close_button
id clothing_category
id clothing_image
id clothing_image_view
id clothing_name
id color_circle
id color_name
id color_preview_circle
id color_recycler_view
id color_selector
id count_view
id edit_notes_batch
id edit_story_batch
id edit_tags_batch
id edit_text_name
id empty_view
id et_search
id fab_add_clothing
id fragment_container
id fullscreen_image_view
id grid_view_selected_images
id group_recycler_view
id image_container
id image_placeholder
id item_checkmark
id item_icon
id item_name
id iv_clear_search
id iv_clothing
id iv_selected_icon
id label_text
id layout_about
id layout_category
id layout_color
id layout_feedback
id layout_language
id layout_occasion
id layout_theme
id multi_select_toolbar
id nav_bar
id nav_calendar
id nav_outfit
id nav_profile
id nav_wardrobe
id occasion_icon
id occasion_name
id occasion_recycler_view
id occasion_selector
id option_camera
id option_cancel
id option_gallery
id recycler_view
id rv_clothing_items
id selection_overlay
id spinner_icon
id spinner_text
id subcategory_selector
id switch_auto_backup
id switch_dark_mode
id switch_notifications
id text_title
id text_view_category_value
id text_view_color_value
id text_view_occasion_value
id tv_category
id tv_category_title
id tv_clothing_count
id tv_current_date
id tv_date
id tv_day_of_week
id tv_empty_state
id tv_image_count
id tv_item_count
id tv_language
id tv_loading_state
id tv_name
id tv_outfit_description
id tv_outfit_name
id tv_selected_count
id tv_storage_info
id tv_suggestion
id tv_theme_mode
id tv_title
id value_text
layout activity_add_clothing
layout activity_batch_add_clothing
layout activity_category_select
layout activity_clothing_detail
layout activity_color_select
layout activity_main
layout activity_occasion_select
layout dialog_category_selection
layout dialog_color_selection
layout dialog_image_fullscreen
layout dialog_occasion_selection
layout dialog_photo_selection
layout dialog_photo_selection_ios
layout dialog_recyclerview
layout fragment_calendar
layout fragment_outfit
layout fragment_profile
layout fragment_wardrobe
layout item_calendar
layout item_category_selector
layout item_clothing_horizontal
layout item_clothing_pro
layout item_color_selector
layout item_color_spinner
layout item_occasion_selector
layout item_outfit
layout item_wardrobe_group
layout layout_category_selector
layout list_item_selector_row
layout multi_select_toolbar
layout spinner_item_with_icon
menu action_mode_menu
menu bottom_navigation_menu
menu menu
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
string button_save
string category_accessories
string category_bottoms
string category_dresses
string category_outerwear
string category_placeholder
string category_primary_placeholder
string category_secondary_placeholder
string category_shoes
string category_tops
string color_beige
string color_black
string color_blue
string color_brown
string color_custom
string color_gray
string color_green
string color_khaki
string color_navy
string color_orange
string color_pink
string color_placeholder
string color_purple
string color_red
string color_white
string color_yellow
string compress_all_images
string compressing
string dialog_button_cancel
string dialog_option_camera
string dialog_option_gallery
string dialog_title_select_color
string dialog_title_select_occasion
string dialog_title_select_photo
string error_batch_add_all_failed
string error_cleanup_images
string error_compress_images
string error_get_storage_info
string error_incomplete_selection_batch
string error_load_data
string error_main_category_missing_batch
string error_no_images_selected_batch
string error_start_compress
string hint_clothing_name
string images_selected_count_batch
string info_no_items_to_add_batch
string label_category
string label_color
string label_occasion
string occasion_casual
string occasion_daily
string occasion_date
string occasion_formal
string occasion_home
string occasion_outdoor
string occasion_party
string occasion_placeholder
string occasion_sport
string occasion_sports
string occasion_travel
string occasion_work
string placeholder_select
string section_optional
string section_required
string success_batch_add_clothing_summary
string success_batch_add_clothing_summary_with_failures
string success_cleanup_all_images
string success_cleanup_unused_images
string success_compress_images
string title_add_new_clothing
string title_batch_add_prefix
string title_edit_clothing
string toast_create_image_file_failed
string toast_image_selection_cancelled
string toast_load_failed
string toast_permission_denied
string toast_permission_granted_retry
string toast_permissions_denied
string toast_save_failed
string toast_save_successful
string toast_select_image_first
style AppModalStyle
style AppTheme
style Base.Theme.WardrobeApp
style BottomSheetDialogTheme
style BottomSheetStyle
style ButtonDestructive
style ButtonPrimary
style ButtonSecondary
style ButtonStyle
style ButtonTertiary
style CardStyle
style DestructiveButton
style ProminentButton
style SectionHeader
style Separator
style TextAppearance.App.Base
style TextAppearance.App.Body
style TextAppearance.App.Callout
style TextAppearance.App.Caption1
style TextAppearance.App.Caption2
style TextAppearance.App.Footnote
style TextAppearance.App.Headline
style TextAppearance.App.LargeTitle
style TextAppearance.App.Subheadline
style TextAppearance.App.Title1
style TextAppearance.App.Title2
style TextAppearance.App.Title3
style Theme.WardrobeApp
style Theme.WardrobeApp.AppBarOverlay
style Theme.WardrobeApp.BottomSheetDialog
style Theme.WardrobeApp.Button.Destructive
style Theme.WardrobeApp.Button.Prominent
style Theme.WardrobeApp.Dialog
style Theme.WardrobeApp.EditTextStyle
style Theme.WardrobeApp.NoActionBar
style Theme.WardrobeApp.PopupOverlay
style iOSChevron
style iOSGroupedBackground
style iOSGroupedBackground.Bottom
style iOSGroupedBackground.Middle
style iOSGroupedBackground.Single
style iOSGroupedBackground.Top
style iOSLabel
style iOSListItem
style iOSValue
xml backup_rules
xml data_extraction_rules
xml file_paths
