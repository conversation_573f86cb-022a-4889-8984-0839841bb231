package com.wardrobe.app.ui.fragments;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.wardrobe.app.R;
import com.wardrobe.app.model.ClothingItem;
import com.wardrobe.app.ui.activities.AddClothingActivity;
import com.wardrobe.app.ui.adapters.WardrobeGroupAdapter;
import com.wardrobe.app.data.ClothingManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 衣橱Fragment - iOS风格分组展示
 * 实现分类分组、横向滑动、标签筛选、空状态等功能
 */
public class WardrobeFragment extends Fragment {

    private static final String TAG = "WardrobeFragment";
    private static final int REQUEST_ADD_CLOTHING = 1001;
    
    private ClothingManager clothingManager;
    private RecyclerView groupRecyclerView;
    private WardrobeGroupAdapter groupAdapter;
    private LinearLayout tvEmptyState;
    private LinearLayout tvLoadingState;
    private ChipGroup chipGroupFilter;
    
    private List<ClothingItem> allClothingItems;
    private Map<String, List<ClothingItem>> groupedClothing;
    private List<String> selectedTags = new ArrayList<>();

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_wardrobe, container, false);
        
        initViews(view);
        setupListeners();
        loadData();
        
        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initViews(view);
        setupListeners();
        loadData();
    }

    private void initViews(View view) {
        groupRecyclerView = view.findViewById(R.id.group_recycler_view);
        tvEmptyState = view.findViewById(R.id.tv_empty_state);
        tvLoadingState = view.findViewById(R.id.tv_loading_state);
        chipGroupFilter = view.findViewById(R.id.chip_group_filter);
        
        // 搜索相关视图
        EditText etSearch = view.findViewById(R.id.et_search);
        ImageView ivClearSearch = view.findViewById(R.id.iv_clear_search);
        
        // 设置搜索监听
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                String query = s.toString().trim();
                ivClearSearch.setVisibility(query.isEmpty() ? View.GONE : View.VISIBLE);
                performSearch(query);
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });
        
        // 清除搜索按钮
        ivClearSearch.setOnClickListener(v -> {
            etSearch.setText("");
            etSearch.requestFocus();
        });
        
        // 设置分组列表
        groupRecyclerView.setLayoutManager(new LinearLayoutManager(requireContext()));
        groupAdapter = new WardrobeGroupAdapter();
        groupRecyclerView.setAdapter(groupAdapter);
        
        // 设置标签筛选
        setupFilterChips();
        
        // 设置悬浮按钮
        FloatingActionButton fab = view.findViewById(R.id.fab_add_clothing);
        fab.setOnClickListener(v -> {
            Intent intent = new Intent(requireActivity(), AddClothingActivity.class);
            startActivityForResult(intent, REQUEST_ADD_CLOTHING);
        });
    }

    private void setupListeners() {
        // 监听器设置已在initViews中完成
    }

    private void setupFilterChips() {
        // 添加筛选标签
        String[] filterTags = {"全部", "上衣", "下装", "外套", "鞋子", "配饰"};
        
        for (String tag : filterTags) {
            Chip chip = new Chip(requireContext());
            chip.setText(tag);
            chip.setCheckable(true);
            chip.setCheckedIconVisible(false);
            
            if (tag.equals("全部")) {
                chip.setChecked(true);
                selectedTags.add(tag);
            }
            
            chip.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if (isChecked) {
                    if (tag.equals("全部")) {
                        // 选择"全部"时，取消其他选择
                        for (int i = 0; i < chipGroupFilter.getChildCount(); i++) {
                            Chip otherChip = (Chip) chipGroupFilter.getChildAt(i);
                            if (!otherChip.getText().equals("全部")) {
                                otherChip.setChecked(false);
                            }
                        }
                        selectedTags.clear();
                        selectedTags.add("全部");
                    } else {
                        // 选择其他标签时，取消"全部"选择
                        for (int i = 0; i < chipGroupFilter.getChildCount(); i++) {
                            Chip otherChip = (Chip) chipGroupFilter.getChildAt(i);
                            if (otherChip.getText().equals("全部")) {
                                otherChip.setChecked(false);
                            }
                        }
                        selectedTags.remove("全部");
                        selectedTags.add(tag);
                    }
                } else {
                    selectedTags.remove(tag);
                    // 如果没有选中任何标签，自动选择"全部"
                    if (selectedTags.isEmpty()) {
                        for (int i = 0; i < chipGroupFilter.getChildCount(); i++) {
                            Chip otherChip = (Chip) chipGroupFilter.getChildAt(i);
                            if (otherChip.getText().equals("全部")) {
                                otherChip.setChecked(true);
                                selectedTags.add("全部");
                                break;
                            }
                        }
                    }
                }
                
                filterAndDisplayClothing();
            });
            
            chipGroupFilter.addView(chip);
        }
    }

    private void loadData() {
        showLoadingState();
        
        try {
            clothingManager = ClothingManager.getInstance(requireContext());
            allClothingItems = clothingManager.getClothingList();
            
            if (allClothingItems.isEmpty()) {
                showEmptyState();
            } else {
                groupClothingByCategory();
                filterAndDisplayClothing();
                showContentState();
            }
        } catch (Exception e) {
            Log.e(TAG, "加载数据失败", e);
            Toast.makeText(requireContext(), "加载数据失败", Toast.LENGTH_SHORT).show();
            showEmptyState();
        }
    }

    private void groupClothingByCategory() {
        groupedClothing = new HashMap<>();
        
        for (ClothingItem item : allClothingItems) {
            String category = item.getCategory();
            if (category == null || category.isEmpty()) {
                category = "其他";
            }
            
            if (!groupedClothing.containsKey(category)) {
                groupedClothing.put(category, new ArrayList<>());
            }
            groupedClothing.get(category).add(item);
        }
    }

    private void filterAndDisplayClothing() {
        if (allClothingItems == null || groupedClothing == null) {
            return;
        }
        
        Map<String, List<ClothingItem>> filteredGroups = new HashMap<>();
        
        if (selectedTags.contains("全部")) {
            // 显示所有分类
            filteredGroups.putAll(groupedClothing);
        } else {
            // 只显示选中的分类
            for (String tag : selectedTags) {
                if (groupedClothing.containsKey(tag)) {
                    filteredGroups.put(tag, groupedClothing.get(tag));
                }
            }
        }
        
        // 更新适配器
        groupAdapter.updateData(filteredGroups);
        
        // 检查是否有数据
        boolean hasData = false;
        for (List<ClothingItem> items : filteredGroups.values()) {
            if (!items.isEmpty()) {
                hasData = true;
                break;
            }
        }
        
        if (!hasData) {
            showEmptyState();
        } else {
            showContentState();
        }
    }

    private void showLoadingState() {
        tvLoadingState.setVisibility(View.VISIBLE);
        groupRecyclerView.setVisibility(View.GONE);
        tvEmptyState.setVisibility(View.GONE);
    }

    private void showEmptyState() {
        tvLoadingState.setVisibility(View.GONE);
        groupRecyclerView.setVisibility(View.GONE);
        tvEmptyState.setVisibility(View.VISIBLE);
    }

    private void showContentState() {
        tvLoadingState.setVisibility(View.GONE);
        groupRecyclerView.setVisibility(View.VISIBLE);
        tvEmptyState.setVisibility(View.GONE);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_ADD_CLOTHING && resultCode == android.app.Activity.RESULT_OK) {
            // 当从AddClothingActivity成功返回时，重新加载数据以显示新项目
            Log.d(TAG, "onActivityResult: Received successful result from AddClothingActivity. Reloading data.");
            loadData();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // 每次返回时重新加载数据
        loadData();
    }

    private void performSearch(String query) {
        if (query.isEmpty()) {
            // 显示所有衣物
            groupAdapter.updateData(groupedClothing);
        } else {
            // 过滤衣物
            Map<String, List<ClothingItem>> filteredGroups = new HashMap<>();
            String lowerQuery = query.toLowerCase();
            for (Map.Entry<String, List<ClothingItem>> entry : groupedClothing.entrySet()) {
                List<ClothingItem> filtered = new ArrayList<>();
                for (ClothingItem item : entry.getValue()) {
                    if ((item.getName() != null && item.getName().toLowerCase().contains(lowerQuery))
                        || (item.getCategory() != null && item.getCategory().toLowerCase().contains(lowerQuery))
                        || (item.getSubcategory() != null && item.getSubcategory().toLowerCase().contains(lowerQuery))
                        || (item.getColor() != null && item.getColor().toLowerCase().contains(lowerQuery))
                        || (item.getOccasion() != null && item.getOccasion().toLowerCase().contains(lowerQuery))) {
                        filtered.add(item);
                    }
                }
                if (!filtered.isEmpty()) {
                    filteredGroups.put(entry.getKey(), filtered);
                }
            }
            groupAdapter.updateData(filteredGroups);
        }
    }
}
