<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary">

    <!-- 多选工具栏 -->
    <include
        android:id="@+id/multi_select_toolbar"
        layout="@layout/multi_select_toolbar" />

    <!-- 搜索栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_primary"
        android:elevation="1dp"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:background="@drawable/search_background"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="12dp">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_search"
                android:tint="@color/text_tertiary"
                android:contentDescription="搜索图标" />

            <EditText
                android:id="@+id/et_search"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:background="@null"
                android:hint="搜索衣物..."
                android:textColor="@color/text_primary"
                android:textColorHint="@color/text_tertiary"
                android:textSize="16sp"
                android:inputType="text"
                android:imeOptions="actionSearch"
                android:maxLines="1" />

            <ImageView
                android:id="@+id/iv_clear_search"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_clear"
                android:tint="@color/text_tertiary"
                android:visibility="gone"
                android:contentDescription="清除搜索"
                android:background="?android:attr/selectableItemBackgroundBorderless" />

        </LinearLayout>

    </LinearLayout>

    <!-- 主内容区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 标题栏 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginStart="20dp"
            android:layout_marginBottom="16dp"
            android:text="我的衣橱"
            android:textColor="@color/text_primary"
            android:textSize="28sp"
            android:textStyle="bold" />

        <!-- 标签筛选 -->
        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:scrollbars="none"
            android:clipToPadding="false"
            android:paddingStart="20dp"
            android:paddingEnd="20dp">

            <com.google.android.material.chip.ChipGroup
                android:id="@+id/chip_group_filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:singleSelection="false"
                app:selectionRequired="true" />

        </HorizontalScrollView>

        <!-- 分组列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/group_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:clipToPadding="false"
            android:paddingBottom="80dp"
            tools:listitem="@layout/item_wardrobe_group" />

        <!-- 加载状态 -->
        <LinearLayout
            android:id="@+id/tv_loading_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="gone">

            <ProgressBar
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="正在加载..."
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />

        </LinearLayout>

        <!-- 空状态 -->
        <LinearLayout
            android:id="@+id/tv_empty_state"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="gone">

            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_marginBottom="16dp"
                android:src="@drawable/ic_wardrobe"
                android:tint="@color/text_tertiary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="衣橱空空如也"
                android:textColor="@color/text_primary"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="点击右下角按钮添加第一件衣物"
                android:textAlignment="center"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 悬浮添加按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add_clothing"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="20dp"
        android:src="@drawable/ic_add"
        app:backgroundTint="@color/accent_primary"
        app:tint="@color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
