<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_outfit" modulePackage="com.wardrobe.app" filePath="app\src\main\res\layout\fragment_outfit.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/fragment_outfit_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="96" endOffset="51"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="9" startOffset="4" endLine="20" endOffset="51"/></Target><Target id="@+id/tv_suggestion" view="TextView"><Expressions/><location startLine="23" startOffset="4" endLine="35" endOffset="60"/></Target><Target id="@+id/recycler_view_outfits" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="38" startOffset="4" endLine="53" endOffset="46"/></Target><Target id="@+id/layout_empty_state" view="LinearLayout"><Expressions/><location startLine="56" startOffset="4" endLine="94" endOffset="18"/></Target><Target id="@+id/tv_empty_state" view="TextView"><Expressions/><location startLine="75" startOffset="8" endLine="83" endOffset="38"/></Target></Targets></Layout>