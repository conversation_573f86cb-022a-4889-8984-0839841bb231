// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityClothingDetailBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnBatchAdd;

  @NonNull
  public final TextView categoryTitle;

  @NonNull
  public final TextView countView;

  @NonNull
  public final TextView emptyView;

  @NonNull
  public final RecyclerView recyclerView;

  private ActivityClothingDetailBinding(@NonNull LinearLayout rootView, @NonNull Button btnBatchAdd,
      @NonNull TextView categoryTitle, @NonNull TextView countView, @NonNull TextView emptyView,
      @NonNull RecyclerView recyclerView) {
    this.rootView = rootView;
    this.btnBatchAdd = btnBatchAdd;
    this.categoryTitle = categoryTitle;
    this.countView = countView;
    this.emptyView = emptyView;
    this.recyclerView = recyclerView;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityClothingDetailBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityClothingDetailBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_clothing_detail, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityClothingDetailBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_batch_add;
      Button btnBatchAdd = ViewBindings.findChildViewById(rootView, id);
      if (btnBatchAdd == null) {
        break missingId;
      }

      id = R.id.category_title;
      TextView categoryTitle = ViewBindings.findChildViewById(rootView, id);
      if (categoryTitle == null) {
        break missingId;
      }

      id = R.id.count_view;
      TextView countView = ViewBindings.findChildViewById(rootView, id);
      if (countView == null) {
        break missingId;
      }

      id = R.id.empty_view;
      TextView emptyView = ViewBindings.findChildViewById(rootView, id);
      if (emptyView == null) {
        break missingId;
      }

      id = R.id.recycler_view;
      RecyclerView recyclerView = ViewBindings.findChildViewById(rootView, id);
      if (recyclerView == null) {
        break missingId;
      }

      return new ActivityClothingDetailBinding((LinearLayout) rootView, btnBatchAdd, categoryTitle,
          countView, emptyView, recyclerView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
