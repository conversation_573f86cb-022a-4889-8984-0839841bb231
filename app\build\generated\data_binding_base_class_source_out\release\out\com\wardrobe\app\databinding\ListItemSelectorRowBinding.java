// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ListItemSelectorRowBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageView itemCheckmark;

  @NonNull
  public final ImageView itemIcon;

  @NonNull
  public final TextView itemName;

  @NonNull
  public final TextView labelText;

  @NonNull
  public final TextView valueText;

  private ListItemSelectorRowBinding(@NonNull RelativeLayout rootView,
      @NonNull ImageView itemCheckmark, @NonNull ImageView itemIcon, @NonNull TextView itemName,
      @NonNull TextView labelText, @NonNull TextView valueText) {
    this.rootView = rootView;
    this.itemCheckmark = itemCheckmark;
    this.itemIcon = itemIcon;
    this.itemName = itemName;
    this.labelText = labelText;
    this.valueText = valueText;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ListItemSelectorRowBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ListItemSelectorRowBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.list_item_selector_row, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ListItemSelectorRowBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.item_checkmark;
      ImageView itemCheckmark = ViewBindings.findChildViewById(rootView, id);
      if (itemCheckmark == null) {
        break missingId;
      }

      id = R.id.item_icon;
      ImageView itemIcon = ViewBindings.findChildViewById(rootView, id);
      if (itemIcon == null) {
        break missingId;
      }

      id = R.id.item_name;
      TextView itemName = ViewBindings.findChildViewById(rootView, id);
      if (itemName == null) {
        break missingId;
      }

      id = R.id.label_text;
      TextView labelText = ViewBindings.findChildViewById(rootView, id);
      if (labelText == null) {
        break missingId;
      }

      id = R.id.value_text;
      TextView valueText = ViewBindings.findChildViewById(rootView, id);
      if (valueText == null) {
        break missingId;
      }

      return new ListItemSelectorRowBinding((RelativeLayout) rootView, itemCheckmark, itemIcon,
          itemName, labelText, valueText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
