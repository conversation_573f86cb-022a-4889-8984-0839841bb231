<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.wardrobe.app.utils.SecurityUtilsTest" tests="24" skipped="1" failures="0" errors="0" timestamp="2025-06-25T12:57:36" hostname="AMON" time="0.585">
  <properties/>
  <testcase name="testValidateFileName_InvalidFileName" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.449"/>
  <testcase name="testValidateName_InvalidName" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.003"/>
  <testcase name="testValidateClothingData_ValidData" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.012"/>
  <testcase name="testSecureDeleteFile_NonExistentFile" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.029"/>
  <testcase name="testGenerateRandomFileName" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.026"/>
  <testcase name="testCalculateFileMD5_NonExistentFile" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.005"/>
  <testcase name="testValidateFileName_ValidFileName" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.002"/>
  <testcase name="testGenerateSafeFileName_EmptyInput" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.004"/>
  <testcase name="testDecryptData_InvalidInput" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.003"/>
  <testcase name="testValidateClothingData_InvalidName" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.005"/>
  <testcase name="testGenerateSafeFileName" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.003"/>
  <testcase name="testValidateDescription_ValidDescription" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.002"/>
  <testcase name="testCalculateFileMD5" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.007"/>
  <testcase name="testSecureDeleteFile" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.005"/>
  <testcase name="testValidateName_ValidName" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.002"/>
  <testcase name="testValidateDescription_InvalidDescription" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.002"/>
  <testcase name="testValidationResult" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.001"/>
  <testcase name="testValidateImagePath_InvalidPath" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.003"/>
  <testcase name="testValidateClothingData_InvalidDescription" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.003"/>
  <testcase name="testEncryptAndDecryptData" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.0">
    <skipped/>
  </testcase>
  <testcase name="testValidateColor_ValidColor" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.004"/>
  <testcase name="testEncryptData_InvalidInput" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.002"/>
  <testcase name="testValidateColor_InvalidColor" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.003"/>
  <testcase name="testValidateImagePath_ValidPath" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.002"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[WARNING: A Java agent has been loaded dynamically (C:\Users\<USER>\.gradle\caches\8.11.1\transforms\597f97b58f5d3034e974f4bc3b5fd56f\transformed\jetified-byte-buddy-agent-1.14.8.jar)
WARNING: If a serviceability tool is in use, please run with -XX:+EnableDynamicAgentLoading to hide this warning
WARNING: If a serviceability tool is not in use, please run with -Djdk.instrument.traceUsage for more information
WARNING: Dynamic loading of agents will be disallowed by default in a future release
]]></system-err>
</testsuite>
