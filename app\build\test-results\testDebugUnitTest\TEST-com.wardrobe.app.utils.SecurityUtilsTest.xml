<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.wardrobe.app.utils.SecurityUtilsTest" tests="24" skipped="1" failures="0" errors="0" timestamp="2025-06-25T12:44:37" hostname="AMON" time="0.254">
  <properties/>
  <testcase name="testValidateFileName_InvalidFileName" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.081"/>
  <testcase name="testValidateName_InvalidName" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.006"/>
  <testcase name="testValidateClothingData_ValidData" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.026"/>
  <testcase name="testSecureDeleteFile_NonExistentFile" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.031"/>
  <testcase name="testGenerateRandomFileName" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.026"/>
  <testcase name="testCalculateFileMD5_NonExistentFile" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.004"/>
  <testcase name="testValidateFileName_ValidFileName" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.003"/>
  <testcase name="testGenerateSafeFileName_EmptyInput" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.003"/>
  <testcase name="testDecryptData_InvalidInput" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.004"/>
  <testcase name="testValidateClothingData_InvalidName" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.006"/>
  <testcase name="testGenerateSafeFileName" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.004"/>
  <testcase name="testValidateDescription_ValidDescription" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.002"/>
  <testcase name="testCalculateFileMD5" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.01"/>
  <testcase name="testSecureDeleteFile" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.007"/>
  <testcase name="testValidateName_ValidName" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.004"/>
  <testcase name="testValidateDescription_InvalidDescription" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.002"/>
  <testcase name="testValidationResult" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.001"/>
  <testcase name="testValidateImagePath_InvalidPath" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.003"/>
  <testcase name="testValidateClothingData_InvalidDescription" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.006"/>
  <testcase name="testEncryptAndDecryptData" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.0">
    <skipped/>
  </testcase>
  <testcase name="testValidateColor_ValidColor" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.005"/>
  <testcase name="testEncryptData_InvalidInput" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.005"/>
  <testcase name="testValidateColor_InvalidColor" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.004"/>
  <testcase name="testValidateImagePath_ValidPath" classname="com.wardrobe.app.utils.SecurityUtilsTest" time="0.004"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
