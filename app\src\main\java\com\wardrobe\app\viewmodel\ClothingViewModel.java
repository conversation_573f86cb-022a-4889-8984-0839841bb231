package com.wardrobe.app.viewmodel;

import android.app.Application;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.wardrobe.app.model.ClothingItem;
import com.wardrobe.app.ClothingManager;
import com.wardrobe.app.utils.DataException;
import com.wardrobe.app.utils.ErrorUtils;

import java.util.List;

public class ClothingViewModel extends AndroidViewModel {
    private static final String TAG = "ClothingViewModel";
    
    private final ClothingManager clothingManager;
    private final MutableLiveData<List<ClothingItem>> clothingItems = new MutableLiveData<>();
    private final MutableLiveData<Boolean> isLoading = new MutableLiveData<>(false);
    private final MutableLiveData<String> errorMessage = new MutableLiveData<>();

    public ClothingViewModel(@NonNull Application application) {
        super(application);
        clothingManager = ClothingManager.getInstance(application);
        loadClothingItems();
    }

    public LiveData<List<ClothingItem>> getClothingItems() {
        return clothingItems;
    }

    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }

    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }

    public void loadClothingItems() {
        isLoading.setValue(true);
        try {
            List<ClothingItem> items = clothingManager.getAllClothingItems();
            clothingItems.setValue(items);
            errorMessage.setValue(null);
        } catch (DataException e) {
            ErrorUtils.logError("加载衣物列表失败", e);
            errorMessage.setValue("加载衣物列表失败，请稍后重试");
        } catch (Exception e) {
            ErrorUtils.logError("未知错误: 加载衣物列表", e);
            errorMessage.setValue("加载衣物列表时发生未知错误");
        } finally {
            isLoading.setValue(false);
        }
    }

    public void addClothingItem(ClothingItem item) {
        isLoading.setValue(true);
        try {
            clothingManager.addClothingItem(item);
            loadClothingItems(); // 重新加载列表
            errorMessage.setValue(null);
        } catch (DataException e) {
            ErrorUtils.logError("添加衣物失败", e);
            errorMessage.setValue("添加衣物失败，请检查信息完整性");
        } catch (Exception e) {
            ErrorUtils.logError("未知错误: 添加衣物", e);
            errorMessage.setValue("添加衣物时发生未知错误");
        } finally {
            isLoading.setValue(false);
        }
    }

    public void updateClothingItem(ClothingItem item) {
        isLoading.setValue(true);
        try {
            clothingManager.updateClothingItem(item);
            loadClothingItems(); // 重新加载列表
            errorMessage.setValue(null);
        } catch (DataException e) {
            ErrorUtils.logError("更新衣物失败", e);
            errorMessage.setValue("更新衣物失败，请检查信息完整性");
        } catch (Exception e) {
            ErrorUtils.logError("未知错误: 更新衣物", e);
            errorMessage.setValue("更新衣物时发生未知错误");
        } finally {
            isLoading.setValue(false);
        }
    }

    public void deleteClothingItem(ClothingItem item) {
        isLoading.setValue(true);
        try {
            clothingManager.deleteClothingItem(item);
            loadClothingItems(); // 重新加载列表
            errorMessage.setValue(null);
        } catch (DataException e) {
            ErrorUtils.logError("删除衣物失败", e);
            errorMessage.setValue("删除衣物失败，请重试");
        } catch (Exception e) {
            ErrorUtils.logError("未知错误: 删除衣物", e);
            errorMessage.setValue("删除衣物时发生未知错误");
        } finally {
            isLoading.setValue(false);
        }
    }

    public void deleteMultipleClothingItems(List<ClothingItem> items) {
        isLoading.setValue(true);
        try {
            for (ClothingItem item : items) {
                clothingManager.deleteClothingItem(item);
            }
            loadClothingItems(); // 重新加载列表
            errorMessage.setValue(null);
        } catch (DataException e) {
            ErrorUtils.logError("批量删除衣物失败", e);
            errorMessage.setValue("批量删除衣物失败，请重试");
        } catch (Exception e) {
            ErrorUtils.logError("未知错误: 批量删除衣物", e);
            errorMessage.setValue("批量删除衣物时发生未知错误");
        } finally {
            isLoading.setValue(false);
        }
    }

    public void searchClothingItems(String query) {
        isLoading.setValue(true);
        try {
            List<ClothingItem> items = clothingManager.searchClothingItems(query);
            clothingItems.setValue(items);
            errorMessage.setValue(null);
        } catch (DataException e) {
            ErrorUtils.logError("搜索衣物失败", e);
            errorMessage.setValue("搜索衣物失败，请重试");
        } catch (Exception e) {
            ErrorUtils.logError("未知错误: 搜索衣物", e);
            errorMessage.setValue("搜索衣物时发生未知错误");
        } finally {
            isLoading.setValue(false);
        }
    }

    public void filterByCategory(String category) {
        isLoading.setValue(true);
        try {
            List<ClothingItem> items = clothingManager.getClothingItemsByCategory(category);
            clothingItems.setValue(items);
            errorMessage.setValue(null);
        } catch (DataException e) {
            ErrorUtils.logError("按分类筛选失败", e);
            errorMessage.setValue("按分类筛选失败，请重试");
        } catch (Exception e) {
            ErrorUtils.logError("未知错误: 分类筛选", e);
            errorMessage.setValue("按分类筛选时发生未知错误");
        } finally {
            isLoading.setValue(false);
        }
    }

    public void clearError() {
        errorMessage.setValue(null);
    }
} 