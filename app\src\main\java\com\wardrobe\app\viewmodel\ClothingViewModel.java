package com.wardrobe.app.viewmodel;

import android.app.Application;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.wardrobe.app.model.ClothingItem;
import com.wardrobe.app.data.ClothingManager;
import com.wardrobe.app.utils.DataException;
import com.wardrobe.app.utils.ErrorUtils;
import com.wardrobe.app.utils.GlobalExceptionHandler;
import com.wardrobe.app.utils.Logger;
import com.wardrobe.app.di.ServiceLocator;

import java.util.List;

public class ClothingViewModel extends AndroidViewModel {
    private static final String TAG = "ClothingViewModel";

    private final ClothingManager clothingManager;
    private final GlobalExceptionHandler exceptionHandler;
    private final MutableLiveData<List<ClothingItem>> clothingItems = new MutableLiveData<>();
    private final MutableLiveData<Boolean> isLoading = new MutableLiveData<>(false);
    private final MutableLiveData<String> errorMessage = new MutableLiveData<>();

    public ClothingViewModel(@NonNull Application application) {
        super(application);

        // 使用ServiceLocator获取依赖
        ServiceLocator serviceLocator = ServiceLocator.getInstance(application);
        clothingManager = serviceLocator.getService(ClothingManager.class);
        exceptionHandler = serviceLocator.getService(GlobalExceptionHandler.class);

        loadClothingItems();
    }

    public LiveData<List<ClothingItem>> getClothingItems() {
        return clothingItems;
    }

    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }

    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }

    public void loadClothingItems() {
        Logger.enter(TAG, "loadClothingItems");
        isLoading.setValue(true);

        // 使用异步方法加载数据
        clothingManager.getAllClothingItemsAsync(new com.wardrobe.app.utils.AsyncTaskManager.TaskCallback<List<ClothingItem>>() {
            @Override
            public void onSuccess(List<ClothingItem> items) {
                clothingItems.setValue(items);
                errorMessage.setValue(null);
                isLoading.setValue(false);
                Logger.logDataOperation(TAG, "加载", "衣物列表", items.size());
                Logger.exit(TAG, "loadClothingItems");
            }

            @Override
            public void onError(Exception e) {
                exceptionHandler.handleException("加载衣物列表", e, false);
                errorMessage.setValue("加载衣物列表失败，请稍后重试");
                isLoading.setValue(false);
                Logger.exit(TAG, "loadClothingItems");
            }
        });
    }

    public void addClothingItem(ClothingItem item) {
        Logger.enter(TAG, "addClothingItem");
        isLoading.setValue(true);

        // 使用异步方法添加衣物
        clothingManager.addClothingItemAsync(item, new com.wardrobe.app.utils.AsyncTaskManager.VoidTaskCallback() {
            @Override
            public void onSuccess() {
                loadClothingItems(); // 重新加载列表
                errorMessage.setValue(null);
                Logger.logUserAction(TAG, "添加衣物", "名称: " + item.getName());
                Logger.exit(TAG, "addClothingItem");
            }

            @Override
            public void onError(Exception e) {
                exceptionHandler.handleException("添加衣物", e, false);
                errorMessage.setValue("添加衣物失败，请检查信息完整性");
                isLoading.setValue(false);
                Logger.exit(TAG, "addClothingItem");
            }
        });
    }

    public void updateClothingItem(ClothingItem item) {
        Logger.enter(TAG, "updateClothingItem");
        isLoading.setValue(true);

        try {
            clothingManager.updateClothingItem(item);
            loadClothingItems(); // 重新加载列表
            errorMessage.setValue(null);
            Logger.logUserAction(TAG, "更新衣物", "ID: " + item.getId());
        } catch (Exception e) {
            exceptionHandler.handleException("更新衣物", e, false);
            errorMessage.setValue("更新衣物失败，请检查信息完整性");
        } finally {
            isLoading.setValue(false);
            Logger.exit(TAG, "updateClothingItem");
        }
    }

    public void deleteClothingItem(ClothingItem item) {
        isLoading.setValue(true);
        try {
            clothingManager.deleteClothingItem(item);
            loadClothingItems(); // 重新加载列表
            errorMessage.setValue(null);
        } catch (Exception e) {
            ErrorUtils.logError("删除衣物失败", e);
            errorMessage.setValue("删除衣物失败，请重试");
        } finally {
            isLoading.setValue(false);
        }
    }

    public void deleteMultipleClothingItems(List<ClothingItem> items) {
        isLoading.setValue(true);
        try {
            for (ClothingItem item : items) {
                clothingManager.deleteClothingItem(item);
            }
            loadClothingItems(); // 重新加载列表
            errorMessage.setValue(null);
        } catch (Exception e) {
            ErrorUtils.logError("批量删除衣物失败", e);
            errorMessage.setValue("批量删除衣物失败，请重试");
        } finally {
            isLoading.setValue(false);
        }
    }

    public void searchClothingItems(String query) {
        isLoading.setValue(true);
        try {
            List<ClothingItem> items = clothingManager.searchClothingItems(query);
            clothingItems.setValue(items);
            errorMessage.setValue(null);
        } catch (Exception e) {
            ErrorUtils.logError("搜索衣物失败", e);
            errorMessage.setValue("搜索衣物失败，请重试");
        } finally {
            isLoading.setValue(false);
        }
    }

    public void filterByCategory(String category) {
        isLoading.setValue(true);
        try {
            List<ClothingItem> items = clothingManager.getClothingItemsByCategory(category);
            clothingItems.setValue(items);
            errorMessage.setValue(null);
        } catch (Exception e) {
            ErrorUtils.logError("按分类筛选失败", e);
            errorMessage.setValue("按分类筛选失败，请重试");
        } finally {
            isLoading.setValue(false);
        }
    }

    public void clearError() {
        errorMessage.setValue(null);
    }
} 