// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.Switch;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentProfileBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnCleanupImages;

  @NonNull
  public final Button btnCleanupUnused;

  @NonNull
  public final Button btnCompressImages;

  @NonNull
  public final LinearLayout layoutAbout;

  @NonNull
  public final LinearLayout layoutFeedback;

  @NonNull
  public final LinearLayout layoutLanguage;

  @NonNull
  public final LinearLayout layoutTheme;

  @NonNull
  public final Switch switchAutoBackup;

  @NonNull
  public final Switch switchDarkMode;

  @NonNull
  public final Switch switchNotifications;

  @NonNull
  public final TextView tvClothingCount;

  @NonNull
  public final TextView tvImageCount;

  @NonNull
  public final TextView tvLanguage;

  @NonNull
  public final TextView tvStorageInfo;

  @NonNull
  public final TextView tvThemeMode;

  private FragmentProfileBinding(@NonNull ScrollView rootView, @NonNull Button btnCleanupImages,
      @NonNull Button btnCleanupUnused, @NonNull Button btnCompressImages,
      @NonNull LinearLayout layoutAbout, @NonNull LinearLayout layoutFeedback,
      @NonNull LinearLayout layoutLanguage, @NonNull LinearLayout layoutTheme,
      @NonNull Switch switchAutoBackup, @NonNull Switch switchDarkMode,
      @NonNull Switch switchNotifications, @NonNull TextView tvClothingCount,
      @NonNull TextView tvImageCount, @NonNull TextView tvLanguage, @NonNull TextView tvStorageInfo,
      @NonNull TextView tvThemeMode) {
    this.rootView = rootView;
    this.btnCleanupImages = btnCleanupImages;
    this.btnCleanupUnused = btnCleanupUnused;
    this.btnCompressImages = btnCompressImages;
    this.layoutAbout = layoutAbout;
    this.layoutFeedback = layoutFeedback;
    this.layoutLanguage = layoutLanguage;
    this.layoutTheme = layoutTheme;
    this.switchAutoBackup = switchAutoBackup;
    this.switchDarkMode = switchDarkMode;
    this.switchNotifications = switchNotifications;
    this.tvClothingCount = tvClothingCount;
    this.tvImageCount = tvImageCount;
    this.tvLanguage = tvLanguage;
    this.tvStorageInfo = tvStorageInfo;
    this.tvThemeMode = tvThemeMode;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentProfileBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentProfileBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_profile, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentProfileBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_cleanup_images;
      Button btnCleanupImages = ViewBindings.findChildViewById(rootView, id);
      if (btnCleanupImages == null) {
        break missingId;
      }

      id = R.id.btn_cleanup_unused;
      Button btnCleanupUnused = ViewBindings.findChildViewById(rootView, id);
      if (btnCleanupUnused == null) {
        break missingId;
      }

      id = R.id.btn_compress_images;
      Button btnCompressImages = ViewBindings.findChildViewById(rootView, id);
      if (btnCompressImages == null) {
        break missingId;
      }

      id = R.id.layout_about;
      LinearLayout layoutAbout = ViewBindings.findChildViewById(rootView, id);
      if (layoutAbout == null) {
        break missingId;
      }

      id = R.id.layout_feedback;
      LinearLayout layoutFeedback = ViewBindings.findChildViewById(rootView, id);
      if (layoutFeedback == null) {
        break missingId;
      }

      id = R.id.layout_language;
      LinearLayout layoutLanguage = ViewBindings.findChildViewById(rootView, id);
      if (layoutLanguage == null) {
        break missingId;
      }

      id = R.id.layout_theme;
      LinearLayout layoutTheme = ViewBindings.findChildViewById(rootView, id);
      if (layoutTheme == null) {
        break missingId;
      }

      id = R.id.switch_auto_backup;
      Switch switchAutoBackup = ViewBindings.findChildViewById(rootView, id);
      if (switchAutoBackup == null) {
        break missingId;
      }

      id = R.id.switch_dark_mode;
      Switch switchDarkMode = ViewBindings.findChildViewById(rootView, id);
      if (switchDarkMode == null) {
        break missingId;
      }

      id = R.id.switch_notifications;
      Switch switchNotifications = ViewBindings.findChildViewById(rootView, id);
      if (switchNotifications == null) {
        break missingId;
      }

      id = R.id.tv_clothing_count;
      TextView tvClothingCount = ViewBindings.findChildViewById(rootView, id);
      if (tvClothingCount == null) {
        break missingId;
      }

      id = R.id.tv_image_count;
      TextView tvImageCount = ViewBindings.findChildViewById(rootView, id);
      if (tvImageCount == null) {
        break missingId;
      }

      id = R.id.tv_language;
      TextView tvLanguage = ViewBindings.findChildViewById(rootView, id);
      if (tvLanguage == null) {
        break missingId;
      }

      id = R.id.tv_storage_info;
      TextView tvStorageInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvStorageInfo == null) {
        break missingId;
      }

      id = R.id.tv_theme_mode;
      TextView tvThemeMode = ViewBindings.findChildViewById(rootView, id);
      if (tvThemeMode == null) {
        break missingId;
      }

      return new FragmentProfileBinding((ScrollView) rootView, btnCleanupImages, btnCleanupUnused,
          btnCompressImages, layoutAbout, layoutFeedback, layoutLanguage, layoutTheme,
          switchAutoBackup, switchDarkMode, switchNotifications, tvClothingCount, tvImageCount,
          tvLanguage, tvStorageInfo, tvThemeMode);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
