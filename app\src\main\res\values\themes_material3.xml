<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Material Design 3 主题 -->
    
    <!-- 浅色主题 -->
    <style name="Theme.WardrobeApp.Material3" parent="Theme.Material3.DayNight">
        <!-- 主要颜色 -->
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>
        
        <!-- 次要颜色 -->
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_light_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_light_onSecondaryContainer</item>
        
        <!-- 第三颜色 -->
        <item name="colorTertiary">@color/md_theme_light_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_light_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_light_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_light_onTertiaryContainer</item>
        
        <!-- 错误颜色 -->
        <item name="colorError">@color/md_theme_light_error</item>
        <item name="colorOnError">@color/md_theme_light_onError</item>
        <item name="colorErrorContainer">@color/md_theme_light_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_light_onErrorContainer</item>
        
        <!-- 背景颜色 -->
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_light_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_onSurfaceVariant</item>
        
        <!-- 轮廓颜色 -->
        <item name="colorOutline">@color/md_theme_light_outline</item>
        <item name="colorOutlineVariant">@color/md_theme_light_outlineVariant</item>
        
        <!-- 状态栏和导航栏 -->
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar">true</item>
        
        <!-- 窗口属性 -->
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        
        <!-- 字体 -->
        <item name="textAppearanceHeadlineLarge">@style/TextAppearance.WardrobeApp.HeadlineLarge</item>
        <item name="textAppearanceHeadlineMedium">@style/TextAppearance.WardrobeApp.HeadlineMedium</item>
        <item name="textAppearanceHeadlineSmall">@style/TextAppearance.WardrobeApp.HeadlineSmall</item>
        <item name="textAppearanceTitleLarge">@style/TextAppearance.WardrobeApp.TitleLarge</item>
        <item name="textAppearanceTitleMedium">@style/TextAppearance.WardrobeApp.TitleMedium</item>
        <item name="textAppearanceTitleSmall">@style/TextAppearance.WardrobeApp.TitleSmall</item>
        <item name="textAppearanceBodyLarge">@style/TextAppearance.WardrobeApp.BodyLarge</item>
        <item name="textAppearanceBodyMedium">@style/TextAppearance.WardrobeApp.BodyMedium</item>
        <item name="textAppearanceBodySmall">@style/TextAppearance.WardrobeApp.BodySmall</item>
        <item name="textAppearanceLabelLarge">@style/TextAppearance.WardrobeApp.LabelLarge</item>
        <item name="textAppearanceLabelMedium">@style/TextAppearance.WardrobeApp.LabelMedium</item>
        <item name="textAppearanceLabelSmall">@style/TextAppearance.WardrobeApp.LabelSmall</item>
        
        <!-- 组件样式 -->
        <item name="materialButtonStyle">@style/Widget.WardrobeApp.Button</item>
        <item name="materialCardViewStyle">@style/Widget.WardrobeApp.CardView</item>
        <item name="chipStyle">@style/Widget.WardrobeApp.Chip</item>
        <item name="textInputStyle">@style/Widget.WardrobeApp.TextInputLayout</item>
        
        <!-- 动画 -->
        <item name="android:windowAnimationStyle">@style/Animation.WardrobeApp.Activity</item>
        <item name="android:windowEnterAnimation">@anim/slide_in_right</item>
        <item name="android:windowExitAnimation">@anim/slide_out_left</item>
    </style>
    
    <!-- 深色主题 -->
    <style name="Theme.WardrobeApp.Material3.Dark" parent="Theme.Material3.DayNight">
        <!-- 主要颜色 -->
        <item name="colorPrimary">@color/md_theme_dark_primary</item>
        <item name="colorOnPrimary">@color/md_theme_dark_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_dark_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_dark_onPrimaryContainer</item>
        
        <!-- 次要颜色 -->
        <item name="colorSecondary">@color/md_theme_dark_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_dark_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_dark_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_dark_onSecondaryContainer</item>
        
        <!-- 第三颜色 -->
        <item name="colorTertiary">@color/md_theme_dark_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_dark_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_dark_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_dark_onTertiaryContainer</item>
        
        <!-- 错误颜色 -->
        <item name="colorError">@color/md_theme_dark_error</item>
        <item name="colorOnError">@color/md_theme_dark_onError</item>
        <item name="colorErrorContainer">@color/md_theme_dark_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_dark_onErrorContainer</item>
        
        <!-- 背景颜色 -->
        <item name="android:colorBackground">@color/md_theme_dark_background</item>
        <item name="colorOnBackground">@color/md_theme_dark_onBackground</item>
        <item name="colorSurface">@color/md_theme_dark_surface</item>
        <item name="colorOnSurface">@color/md_theme_dark_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_dark_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_dark_onSurfaceVariant</item>
        
        <!-- 轮廓颜色 -->
        <item name="colorOutline">@color/md_theme_dark_outline</item>
        <item name="colorOutlineVariant">@color/md_theme_dark_outlineVariant</item>
        
        <!-- 状态栏和导航栏 -->
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
        
        <!-- 窗口属性 -->
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
    </style>
    
    <!-- 字体样式 -->
    <style name="TextAppearance.WardrobeApp.HeadlineLarge" parent="TextAppearance.Material3.HeadlineLarge">
        <item name="android:fontFamily">@font/roboto_medium</item>
        <item name="fontFamily">@font/roboto_medium</item>
    </style>
    
    <style name="TextAppearance.WardrobeApp.HeadlineMedium" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:fontFamily">@font/roboto_medium</item>
        <item name="fontFamily">@font/roboto_medium</item>
    </style>
    
    <style name="TextAppearance.WardrobeApp.HeadlineSmall" parent="TextAppearance.Material3.HeadlineSmall">
        <item name="android:fontFamily">@font/roboto_medium</item>
        <item name="fontFamily">@font/roboto_medium</item>
    </style>
    
    <style name="TextAppearance.WardrobeApp.TitleLarge" parent="TextAppearance.Material3.TitleLarge">
        <item name="android:fontFamily">@font/roboto_medium</item>
        <item name="fontFamily">@font/roboto_medium</item>
    </style>
    
    <style name="TextAppearance.WardrobeApp.TitleMedium" parent="TextAppearance.Material3.TitleMedium">
        <item name="android:fontFamily">@font/roboto_medium</item>
        <item name="fontFamily">@font/roboto_medium</item>
    </style>
    
    <style name="TextAppearance.WardrobeApp.TitleSmall" parent="TextAppearance.Material3.TitleSmall">
        <item name="android:fontFamily">@font/roboto_medium</item>
        <item name="fontFamily">@font/roboto_medium</item>
    </style>
    
    <style name="TextAppearance.WardrobeApp.BodyLarge" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:fontFamily">@font/roboto_regular</item>
        <item name="fontFamily">@font/roboto_regular</item>
    </style>
    
    <style name="TextAppearance.WardrobeApp.BodyMedium" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:fontFamily">@font/roboto_regular</item>
        <item name="fontFamily">@font/roboto_regular</item>
    </style>
    
    <style name="TextAppearance.WardrobeApp.BodySmall" parent="TextAppearance.Material3.BodySmall">
        <item name="android:fontFamily">@font/roboto_regular</item>
        <item name="fontFamily">@font/roboto_regular</item>
    </style>
    
    <style name="TextAppearance.WardrobeApp.LabelLarge" parent="TextAppearance.Material3.LabelLarge">
        <item name="android:fontFamily">@font/roboto_medium</item>
        <item name="fontFamily">@font/roboto_medium</item>
    </style>
    
    <style name="TextAppearance.WardrobeApp.LabelMedium" parent="TextAppearance.Material3.LabelMedium">
        <item name="android:fontFamily">@font/roboto_medium</item>
        <item name="fontFamily">@font/roboto_medium</item>
    </style>
    
    <style name="TextAppearance.WardrobeApp.LabelSmall" parent="TextAppearance.Material3.LabelSmall">
        <item name="android:fontFamily">@font/roboto_medium</item>
        <item name="fontFamily">@font/roboto_medium</item>
    </style>
    
    <!-- 组件样式 -->
    <style name="Widget.WardrobeApp.Button" parent="Widget.Material3.Button">
        <item name="android:textAppearance">@style/TextAppearance.WardrobeApp.LabelLarge</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
    </style>
    
    <style name="Widget.WardrobeApp.Button.Outlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textAppearance">@style/TextAppearance.WardrobeApp.LabelLarge</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
    </style>
    
    <style name="Widget.WardrobeApp.Button.Text" parent="Widget.Material3.Button.TextButton">
        <item name="android:textAppearance">@style/TextAppearance.WardrobeApp.LabelLarge</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
    </style>
    
    <style name="Widget.WardrobeApp.CardView" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">16dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:layout_margin">8dp</item>
    </style>
    
    <style name="Widget.WardrobeApp.Chip" parent="Widget.Material3.Chip.Filter">
        <item name="chipCornerRadius">20dp</item>
        <item name="chipMinHeight">40dp</item>
        <item name="android:textAppearance">@style/TextAppearance.WardrobeApp.LabelMedium</item>
    </style>
    
    <style name="Widget.WardrobeApp.TextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxCornerRadiusTopStart">12dp</item>
        <item name="boxCornerRadiusTopEnd">12dp</item>
        <item name="boxCornerRadiusBottomStart">12dp</item>
        <item name="boxCornerRadiusBottomEnd">12dp</item>
        <item name="android:layout_marginBottom">16dp</item>
    </style>
    
    <!-- 动画样式 -->
    <style name="Animation.WardrobeApp.Activity" parent="@android:style/Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/slide_in_right</item>
        <item name="android:activityOpenExitAnimation">@anim/slide_out_left</item>
        <item name="android:activityCloseEnterAnimation">@anim/slide_in_left</item>
        <item name="android:activityCloseExitAnimation">@anim/slide_out_right</item>
    </style>
</resources>
