package com.wardrobe.app.ui.activities;

import android.app.Activity;
import android.content.ClipData;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.wardrobe.app.R;
import com.wardrobe.app.databinding.ActivityBatchAddClothingBinding;
import com.bumptech.glide.Glide;
import com.wardrobe.app.model.ClothingItem;
import com.wardrobe.app.repository.ClothingRepository;
import com.wardrobe.app.ui.components.CategorySelectorManager;
import com.wardrobe.app.utils.ImagePickerHelper;
import com.wardrobe.app.ClothingManager;
import com.wardrobe.app.BuildConfig;
import com.wardrobe.app.utils.ErrorUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

public class BatchAddClothingActivity extends AppCompatActivity {

    private static final String TAG = "BatchAddClothingActivity";
    private String initialMainCategory;
    private ArrayList<Uri> selectedImageUris = new ArrayList<>();
    private ImageAdapter imageAdapter;

    private ClothingManager clothingManager;
    private CategorySelectorManager categorySelectorManager;
    private ActivityResultLauncher<Intent> pickImagesLauncher;
    private ActivityBatchAddClothingBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 使用View Binding
        binding = ActivityBatchAddClothingBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        initialMainCategory = getIntent().getStringExtra("mainCategory");
        if (initialMainCategory == null || initialMainCategory.isEmpty()) {
            ErrorUtils.showUserError(this, getString(R.string.error_main_category_missing_batch));
            finish();
            return;
        }
        setTitle(getString(R.string.title_batch_add_prefix, initialMainCategory));

        clothingManager = ClothingManager.getInstance(this);

        // 初始化CategorySelectorManager
        categorySelectorManager = new CategorySelectorManager(this, binding.getRoot(), completedLevels -> {
            // 可以在这里处理选择变化
        });
        categorySelectorManager.setInitialSelection(initialMainCategory, null, null, null);

        pickImagesLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == Activity.RESULT_OK && result.getData() != null) {
                        ClipData clipData = result.getData().getClipData();
                        if (clipData != null) {
                            for (int i = 0; i < clipData.getItemCount(); i++) {
                                Uri imageUri = clipData.getItemAt(i).getUri();
                                selectedImageUris.add(imageUri);
                            }
                        } else if (result.getData().getData() != null) {
                            Uri imageUri = result.getData().getData();
                            selectedImageUris.add(imageUri);
                        }
                        updateImagePreviews();
                        ErrorUtils.showUserError(this, getString(R.string.images_selected_count_batch, selectedImageUris.size()));
                    }
                }
        );

        binding.btnSelectImagesBatch.setOnClickListener(v -> openImagePicker());
        binding.btnSaveBatch.setOnClickListener(v -> saveBatchClothingItems());

        Log.d(TAG, "BatchAddClothingActivity created for category: " + initialMainCategory);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        // 四级联动选择器现在是内嵌式的，不需要处理Activity结果
    }

    private void openImagePicker() {
        Intent intent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
        intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true);
        intent.setType("image/*");
        pickImagesLauncher.launch(intent);
    }

    private void updateImagePreviews() {
        if (imageAdapter == null) {
            imageAdapter = new ImageAdapter(this, selectedImageUris);
            binding.gridViewSelectedImages.setAdapter(imageAdapter);
        } else {
            imageAdapter.notifyDataSetChanged();
        }
        Log.d(TAG, "Update image previews. Count: " + selectedImageUris.size());
    }

    private void saveBatchClothingItems() {
        if (selectedImageUris.isEmpty()) {
            ErrorUtils.showUserError(this, getString(R.string.error_no_images_selected_batch));
            return;
        }

        String categoryStr = categorySelectorManager.getSelectedCategory();
        String subCategoryStr = categorySelectorManager.getSelectedSubcategory();
        String colorStr = categorySelectorManager.getSelectedColor();
        String occasionStr = categorySelectorManager.getSelectedOccasion();

        String notesStr = binding.editNotesBatch.getText().toString().trim();
        String tagsStr = binding.editTagsBatch.getText().toString().trim();
        String storyStr = binding.editStoryBatch.getText().toString().trim();

        if (categoryStr == null || categoryStr.isEmpty()) {
            ErrorUtils.showUserError(this, getString(R.string.error_incomplete_selection_batch));
            return;
        }

        List<ClothingItem> itemsToAdd = new ArrayList<>();
        int itemCounter = 1;
        int successCount = 0;
        int failureCount = 0;

        for (Uri imageUri : selectedImageUris) {
            String imagePath = ImagePickerHelper.copyImageToAppStorage(this, imageUri);
            if (imagePath == null) {
                ErrorUtils.logError("Failed to get path for URI: " + imageUri, null);
                failureCount++;
                continue;
            }

            String itemName = (subCategoryStr != null && !subCategoryStr.isEmpty() ? subCategoryStr : categoryStr) + " (批量项 " + itemCounter++ + ")";
            List<String> tagsList = new ArrayList<>();
            if (tagsStr != null && !tagsStr.trim().isEmpty()) {
                tagsList.addAll(Arrays.asList(tagsStr.trim().split(",\\s*")));
                tagsList.removeIf(String::isEmpty);
            }

            ClothingItem newItem = new ClothingItem(
                    itemName,
                    categoryStr,
                    subCategoryStr != null ? subCategoryStr : "",
                    colorStr != null ? colorStr : "",
                    occasionStr != null ? occasionStr : "",
                    imagePath,
                    notesStr,
                    storyStr,
                    tagsList
            );
            itemsToAdd.add(newItem);
            successCount++;
        }

        if (!itemsToAdd.isEmpty()) {
            clothingManager.addMultipleItems(itemsToAdd);
        }

        String summaryMessage;
        if (failureCount == 0 && successCount > 0) {
            summaryMessage = getString(R.string.success_batch_add_clothing_summary, successCount, categoryStr);
        } else if (successCount > 0) {
            summaryMessage = getString(R.string.success_batch_add_clothing_summary_with_failures, successCount, categoryStr, failureCount);
        } else if (failureCount > 0) {
            summaryMessage = getString(R.string.error_batch_add_all_failed, failureCount);
        } else {
            summaryMessage = getString(R.string.info_no_items_to_add_batch);
        }
        ErrorUtils.showUserError(this, summaryMessage);

        if (successCount > 0 || selectedImageUris.isEmpty()) {
            finish();
        }
    }

    // ImageAdapter内部类
    private static class ImageAdapter extends ArrayAdapter<Uri> {
        public ImageAdapter(@NonNull Context context, @NonNull List<Uri> uris) {
            super(context, 0, uris);
        }

        @NonNull
        @Override
        public View getView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
            if (convertView == null) {
                convertView = LayoutInflater.from(getContext()).inflate(R.layout.item_clothing_pro, parent, false);
            }

            ImageView imageView = convertView.findViewById(R.id.clothing_image);
            Uri uri = getItem(position);

            if (uri != null) {
                Glide.with(getContext())
                        .load(uri)
                        .centerCrop()
                        .into(imageView);
            }

            return convertView;
        }
    }
}
