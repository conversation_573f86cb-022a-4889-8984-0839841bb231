// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutEmptyBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView ivEmpty;

  @NonNull
  public final TextView tvEmptyMessage;

  @NonNull
  public final TextView tvEmptyTitle;

  private LayoutEmptyBinding(@NonNull LinearLayout rootView, @NonNull ImageView ivEmpty,
      @NonNull TextView tvEmptyMessage, @NonNull TextView tvEmptyTitle) {
    this.rootView = rootView;
    this.ivEmpty = ivEmpty;
    this.tvEmptyMessage = tvEmptyMessage;
    this.tvEmptyTitle = tvEmptyTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutEmptyBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutEmptyBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_empty, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutEmptyBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_empty;
      ImageView ivEmpty = ViewBindings.findChildViewById(rootView, id);
      if (ivEmpty == null) {
        break missingId;
      }

      id = R.id.tv_empty_message;
      TextView tvEmptyMessage = ViewBindings.findChildViewById(rootView, id);
      if (tvEmptyMessage == null) {
        break missingId;
      }

      id = R.id.tv_empty_title;
      TextView tvEmptyTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvEmptyTitle == null) {
        break missingId;
      }

      return new LayoutEmptyBinding((LinearLayout) rootView, ivEmpty, tvEmptyMessage, tvEmptyTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
