<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_outfit" modulePackage="com.wardrobe.app" filePath="app\src\main\res\layout\item_outfit.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_outfit_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="67" endOffset="35"/></Target><Target id="@+id/tv_outfit_name" view="TextView"><Expressions/><location startLine="17" startOffset="8" endLine="25" endOffset="38"/></Target><Target id="@+id/tv_outfit_description" view="TextView"><Expressions/><location startLine="28" startOffset="8" endLine="34" endOffset="37"/></Target><Target id="@+id/btn_save_outfit" view="Button"><Expressions/><location startLine="43" startOffset="12" endLine="51" endOffset="41"/></Target><Target id="@+id/btn_share_outfit" view="Button"><Expressions/><location startLine="53" startOffset="12" endLine="61" endOffset="41"/></Target></Targets></Layout>