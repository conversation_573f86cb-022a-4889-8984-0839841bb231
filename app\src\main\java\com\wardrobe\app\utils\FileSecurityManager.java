package com.wardrobe.app.utils;

import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Environment;
import androidx.core.content.ContextCompat;
import java.io.File;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 文件安全管理器
 * 提供安全的文件访问、路径验证和权限检查功能
 * 防止路径遍历攻击和未授权文件访问
 */
public class FileSecurityManager {
    
    private static final String TAG = "FileSecurityManager";
    private static final String ALLOWED_IMAGE_EXTENSIONS = "jpg,jpeg,png,gif,bmp,webp";
    private static final long MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    
    private final Context context;
    private final File appPrivateDir;
    private final File appCacheDir;
    
    public FileSecurityManager(Context context) {
        this.context = context.getApplicationContext();
        this.appPrivateDir = context.getFilesDir();
        this.appCacheDir = context.getCacheDir();
        Logger.d(TAG, "FileSecurityManager 初始化完成");
    }
    
    /**
     * 文件访问结果
     */
    public static class FileAccessResult {
        private final boolean allowed;
        private final String errorMessage;
        private final File secureFile;
        
        public FileAccessResult(boolean allowed, String errorMessage, File secureFile) {
            this.allowed = allowed;
            this.errorMessage = errorMessage;
            this.secureFile = secureFile;
        }
        
        public boolean isAllowed() {
            return allowed;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public File getSecureFile() {
            return secureFile;
        }
    }
    
    /**
     * 验证并获取安全的文件路径
     * 
     * @param filePath 文件路径
     * @return 文件访问结果
     */
    public FileAccessResult validateAndGetSecureFile(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return new FileAccessResult(false, "文件路径不能为空", null);
        }
        
        String cleanPath = filePath.trim();
        
        // 检查路径遍历攻击
        if (containsPathTraversal(cleanPath)) {
            Logger.w(TAG, "检测到路径遍历攻击尝试: " + cleanPath);
            return new FileAccessResult(false, "不安全的文件路径", null);
        }
        
        try {
            File file = new File(cleanPath);
            String canonicalPath = file.getCanonicalPath();
            
            // 检查文件是否在允许的目录内
            if (!isPathInAllowedDirectory(canonicalPath)) {
                Logger.w(TAG, "文件路径不在允许的目录内: " + canonicalPath);
                return new FileAccessResult(false, "文件路径不在允许的目录内", null);
            }
            
            // 检查文件扩展名
            if (!isAllowedFileExtension(file.getName())) {
                Logger.w(TAG, "不支持的文件类型: " + file.getName());
                return new FileAccessResult(false, "不支持的文件类型", null);
            }
            
            return new FileAccessResult(true, null, file);
            
        } catch (IOException e) {
            Logger.e(TAG, "文件路径解析失败: " + cleanPath, e);
            return new FileAccessResult(false, "文件路径无效", null);
        }
    }
    
    /**
     * 检查是否包含路径遍历字符
     * 
     * @param path 文件路径
     * @return 是否包含路径遍历
     */
    private boolean containsPathTraversal(String path) {
        return path.contains("..") || 
               path.contains("./") || 
               path.contains(".\\") ||
               path.contains("//") ||
               path.contains("\\\\") ||
               path.contains("%2e%2e") ||
               path.contains("%2f") ||
               path.contains("%5c");
    }
    
    /**
     * 检查路径是否在允许的目录内
     * 
     * @param canonicalPath 规范化路径
     * @return 是否在允许的目录内
     */
    private boolean isPathInAllowedDirectory(String canonicalPath) {
        try {
            String appPrivatePath = appPrivateDir.getCanonicalPath();
            String appCachePath = appCacheDir.getCanonicalPath();
            String externalFilesPath = context.getExternalFilesDir(null) != null ? 
                    context.getExternalFilesDir(null).getCanonicalPath() : "";
            String externalCachePath = context.getExternalCacheDir() != null ?
                    context.getExternalCacheDir().getCanonicalPath() : "";
            
            return canonicalPath.startsWith(appPrivatePath) ||
                   canonicalPath.startsWith(appCachePath) ||
                   (!externalFilesPath.isEmpty() && canonicalPath.startsWith(externalFilesPath)) ||
                   (!externalCachePath.isEmpty() && canonicalPath.startsWith(externalCachePath));
                   
        } catch (IOException e) {
            Logger.e(TAG, "检查允许目录时发生错误", e);
            return false;
        }
    }
    
    /**
     * 检查文件扩展名是否被允许
     * 
     * @param fileName 文件名
     * @return 是否允许
     */
    private boolean isAllowedFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return false;
        }
        
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        return ALLOWED_IMAGE_EXTENSIONS.contains(extension);
    }
    
    /**
     * 检查文件大小是否在允许范围内
     * 
     * @param file 文件
     * @return 是否在允许范围内
     */
    public boolean isFileSizeAllowed(File file) {
        if (file == null || !file.exists()) {
            return false;
        }
        
        long fileSize = file.length();
        boolean allowed = fileSize <= MAX_FILE_SIZE;
        
        if (!allowed) {
            Logger.w(TAG, "文件大小超出限制: " + fileSize + " bytes, 最大允许: " + MAX_FILE_SIZE + " bytes");
        }
        
        return allowed;
    }
    
    /**
     * 创建安全的临时文件
     * 
     * @param prefix 文件名前缀
     * @param suffix 文件名后缀
     * @return 临时文件
     * @throws IOException 创建失败
     */
    public File createSecureTempFile(String prefix, String suffix) throws IOException {
        // 验证前缀和后缀
        if (prefix == null || prefix.trim().isEmpty()) {
            prefix = "temp";
        }
        if (suffix == null || !isAllowedFileExtension("dummy." + suffix.replace(".", ""))) {
            suffix = ".tmp";
        }
        
        // 清理文件名
        prefix = cleanFileName(prefix);
        suffix = cleanFileName(suffix);
        
        File tempFile = File.createTempFile(prefix, suffix, appCacheDir);
        Logger.d(TAG, "创建安全临时文件: " + tempFile.getAbsolutePath());
        
        return tempFile;
    }
    
    /**
     * 清理文件名，移除危险字符
     * 
     * @param fileName 原文件名
     * @return 清理后的文件名
     */
    public String cleanFileName(String fileName) {
        if (fileName == null) {
            return "unknown";
        }
        
        // 移除危险字符
        String cleaned = fileName.replaceAll("[^a-zA-Z0-9._\\-]", "_");
        
        // 限制长度
        if (cleaned.length() > 50) {
            cleaned = cleaned.substring(0, 50);
        }
        
        return cleaned;
    }
    
    /**
     * 计算文件的MD5哈希值
     * 
     * @param file 文件
     * @return MD5哈希值
     */
    public String calculateFileHash(File file) {
        if (file == null || !file.exists()) {
            return null;
        }
        
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            java.io.FileInputStream fis = new java.io.FileInputStream(file);
            byte[] buffer = new byte[8192];
            int bytesRead;
            
            while ((bytesRead = fis.read(buffer)) != -1) {
                md.update(buffer, 0, bytesRead);
            }
            
            fis.close();
            
            byte[] hashBytes = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            
            return sb.toString();
            
        } catch (NoSuchAlgorithmException | IOException e) {
            Logger.e(TAG, "计算文件哈希值失败", e);
            return null;
        }
    }
    
    /**
     * 检查存储权限
     * 
     * @return 是否有存储权限
     */
    public boolean hasStoragePermission() {
        return ContextCompat.checkSelfPermission(context, 
                android.Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED;
    }
    
    /**
     * 检查相机权限
     * 
     * @return 是否有相机权限
     */
    public boolean hasCameraPermission() {
        return ContextCompat.checkSelfPermission(context, 
                android.Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED;
    }
    
    /**
     * 获取安全的图片存储目录
     * 
     * @return 图片存储目录
     */
    public File getSecureImageDirectory() {
        File imageDir = new File(appPrivateDir, "images");
        if (!imageDir.exists()) {
            boolean created = imageDir.mkdirs();
            if (created) {
                Logger.d(TAG, "创建图片目录: " + imageDir.getAbsolutePath());
            } else {
                Logger.e(TAG, "创建图片目录失败: " + imageDir.getAbsolutePath());
            }
        }
        return imageDir;
    }
    
    /**
     * 安全删除文件
     * 
     * @param file 要删除的文件
     * @return 是否删除成功
     */
    public boolean secureDeleteFile(File file) {
        if (file == null || !file.exists()) {
            return false;
        }
        
        // 验证文件路径安全性
        FileAccessResult result = validateAndGetSecureFile(file.getAbsolutePath());
        if (!result.isAllowed()) {
            Logger.w(TAG, "尝试删除不安全的文件: " + file.getAbsolutePath());
            return false;
        }
        
        boolean deleted = file.delete();
        if (deleted) {
            Logger.d(TAG, "安全删除文件: " + file.getAbsolutePath());
        } else {
            Logger.e(TAG, "删除文件失败: " + file.getAbsolutePath());
        }
        
        return deleted;
    }
    
    /**
     * 清理临时文件
     */
    public void cleanupTempFiles() {
        File[] tempFiles = appCacheDir.listFiles();
        if (tempFiles != null) {
            int deletedCount = 0;
            for (File file : tempFiles) {
                if (file.isFile() && file.getName().startsWith("temp")) {
                    if (file.delete()) {
                        deletedCount++;
                    }
                }
            }
            Logger.d(TAG, "清理临时文件完成，删除 " + deletedCount + " 个文件");
        }
    }
}
