package com.wardrobe.app.ui.activities;

import android.content.Intent;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.flask.colorpicker.ColorPickerView;
import com.flask.colorpicker.builder.ColorPickerDialogBuilder;
import com.wardrobe.app.R;
import com.wardrobe.app.data.SharedPreferencesManager;
import com.wardrobe.app.ClothingManager;
import com.wardrobe.app.model.CategoryItem;
import com.wardrobe.app.model.ClothingItem;
import com.wardrobe.app.ui.adapters.ColorListAdapter;
import com.wardrobe.app.ui.adapters.OccasionListAdapter;
import com.wardrobe.app.ui.components.ColorProvider;
import com.wardrobe.app.ui.components.OccasionProvider;
import com.wardrobe.app.utils.ImagePickerHelper;
import com.wardrobe.app.utils.ErrorUtils;

import java.util.List;
import java.util.UUID;

public class AddClothingActivity extends AppCompatActivity {

    public static final String EXTRA_CLOTHING_ID = "extra_clothing_id";
    private static final int REQUEST_CODE_SELECT_CATEGORY = 101;

    private ImageView clothingImageView;
    private FrameLayout imagePlaceholder;
    private ImageView replacePhotoButton;
    private EditText nameEditText;
    private TextView categoryTextView;
    private TextView colorTextView;
    private TextView occasionTextView;
    private Button saveButton;

    private ImagePickerHelper imagePickerHelper;
    private ClothingManager clothingManager;

    private Uri currentImageUri;
    private String selectedCategory;
    private String selectedSubCategory;
    private String selectedColor;
    private String selectedOccasion;

    private boolean isEditMode = false;
    private String editingClothingId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_add_clothing);

        // Initialize managers
        SharedPreferencesManager sharedPreferencesManager = new SharedPreferencesManager(this);
        clothingManager = new ClothingManager(sharedPreferencesManager);

        // Initialize views
        initViews();
        setupListeners();

        // Setup image picker
        imagePickerHelper = new ImagePickerHelper(this, this::onImagePicked);

        // Check for edit mode
        if (getIntent().hasExtra(EXTRA_CLOTHING_ID)) {
            isEditMode = true;
            editingClothingId = getIntent().getStringExtra(EXTRA_CLOTHING_ID);
            setTitle(R.string.title_edit_clothing);
            loadClothingItemForEdit();
        } else {
            setTitle(R.string.title_add_new_clothing);
        }

        updateSaveButtonState();
    }

    private void initViews() {
        clothingImageView = findViewById(R.id.clothing_image_view);
        imagePlaceholder = findViewById(R.id.image_placeholder);
        replacePhotoButton = findViewById(R.id.button_replace_photo);
        nameEditText = findViewById(R.id.edit_text_name);
        categoryTextView = findViewById(R.id.text_view_category_value);
        colorTextView = findViewById(R.id.text_view_color_value);
        occasionTextView = findViewById(R.id.text_view_occasion_value);
        saveButton = findViewById(R.id.button_save);
    }

    private void setupListeners() {
        imagePlaceholder.setOnClickListener(v -> imagePickerHelper.showPhotoDialog());
        replacePhotoButton.setOnClickListener(v -> imagePickerHelper.showPhotoDialog());

        findViewById(R.id.layout_category).setOnClickListener(v -> {
            Intent intent = new Intent(this, CategorySelectActivity.class);
            startActivityForResult(intent, REQUEST_CODE_SELECT_CATEGORY);
        });

        findViewById(R.id.layout_color).setOnClickListener(v -> showColorSelectionDialog());
        findViewById(R.id.layout_occasion).setOnClickListener(v -> showOccasionSelectionDialog());

        nameEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                updateSaveButtonState();
            }
        });

        saveButton.setOnClickListener(v -> saveClothingItem());
    }

    private void onImagePicked(Uri imageUri) {
        currentImageUri = imageUri;
        Glide.with(this).load(currentImageUri).into(clothingImageView);
        clothingImageView.setVisibility(View.VISIBLE);
        replacePhotoButton.setVisibility(View.VISIBLE);
        imagePlaceholder.setVisibility(View.GONE);
        updateSaveButtonState();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_SELECT_CATEGORY && resultCode == RESULT_OK && data != null) {
            selectedCategory = data.getStringExtra(CategorySelectActivity.EXTRA_SELECTED_CATEGORY);
            selectedSubCategory = data.getStringExtra(CategorySelectActivity.EXTRA_SELECTED_SUBCATEGORY);
            categoryTextView.setText(String.format("%s > %s", selectedCategory, selectedSubCategory));
            updateSaveButtonState();
        }
    }

    private void showColorSelectionDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(R.string.dialog_title_select_color);

        View view = getLayoutInflater().inflate(R.layout.dialog_recyclerview, null);
        RecyclerView recyclerView = view.findViewById(R.id.recycler_view);
        recyclerView.setLayoutManager(new GridLayoutManager(this, 4));

        List<CategoryItem> colors = ColorProvider.getColors(this);
        ColorListAdapter adapter = new ColorListAdapter(this, colors, item -> {
            selectedColor = item.getIcon();
            colorTextView.setText(item.getName());
        });
        recyclerView.setAdapter(adapter);

        builder.setView(view);
        builder.setNegativeButton(R.string.dialog_button_cancel, (dialog, which) -> dialog.dismiss());
        AlertDialog dialog = builder.create();
        dialog.show();
    }

    private void showColorPickerDialog() {
        ColorPickerDialogBuilder
                .with(this)
                .setTitle(R.string.dialog_title_select_color)
                .initialColor(Color.WHITE)
                .wheelType(ColorPickerView.WHEEL_TYPE.FLOWER)
                .density(12)
                .setPositiveButton("OK", (dialog, selectedColorInt, allColors) -> {
                    selectedColor = String.format("#%06X", (0xFFFFFF & selectedColorInt));
                    colorTextView.setText(selectedColor);
                })
                .setNegativeButton(R.string.dialog_button_cancel, (dialog, which) -> {})
                .build()
                .show();
    }

    private void showOccasionSelectionDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(R.string.dialog_title_select_occasion);

        View view = getLayoutInflater().inflate(R.layout.dialog_recyclerview, null);
        RecyclerView recyclerView = view.findViewById(R.id.recycler_view);
        recyclerView.setLayoutManager(new GridLayoutManager(this, 4));

        List<CategoryItem> occasions = OccasionProvider.getOccasions(this);
        OccasionListAdapter adapter = new OccasionListAdapter(occasions, item -> {
            selectedOccasion = item.getName();
            occasionTextView.setText(selectedOccasion);
        });
        recyclerView.setAdapter(adapter);

        builder.setView(view);
        builder.setNegativeButton(R.string.dialog_button_cancel, (dialog, which) -> dialog.dismiss());
        AlertDialog dialog = builder.create();
        dialog.show();
    }


    private void loadClothingItemForEdit() {
        ClothingItem item = clothingManager.getClothingItemById(editingClothingId);
        if (item != null) {
            nameEditText.setText(item.getName());

            if (item.getImageUri() != null && !item.getImageUri().isEmpty()) {
                currentImageUri = Uri.parse(item.getImageUri());
                onImagePicked(currentImageUri);
            }

            selectedCategory = item.getCategory();
            selectedSubCategory = item.getSubcategory();
            categoryTextView.setText(String.format("%s > %s", selectedCategory, selectedSubCategory));

            selectedColor = item.getColor();
            colorTextView.setText(selectedColor); // You might want a mapping from hex to name here

            selectedOccasion = item.getOccasion();
            occasionTextView.setText(selectedOccasion);

        } else {
            ErrorUtils.showUserError(this, getString(R.string.toast_load_failed));
            finish();
        }
    }

    private void updateSaveButtonState() {
        boolean isNameValid = !nameEditText.getText().toString().trim().isEmpty();
        boolean isCategoryValid = selectedCategory != null && !selectedCategory.isEmpty();
        boolean isImageValid = currentImageUri != null;
        saveButton.setEnabled(isNameValid && isCategoryValid && isImageValid);
    }

    private void saveClothingItem() {
        String name = nameEditText.getText().toString().trim();
        String imageUriString = (currentImageUri != null) ? currentImageUri.toString() : null;

        ClothingItem item;
        if (isEditMode) {
            item = clothingManager.getClothingItemById(editingClothingId);
            if (item == null) {
                // Should not happen, but as a safeguard
                ErrorUtils.showUserError(this, getString(R.string.toast_save_failed));
                return;
            }
        } else {
            item = new ClothingItem();
            item.setId(UUID.randomUUID().toString()); // Set new ID for new item
            item.setTimestamp(System.currentTimeMillis()); // Set creation timestamp
        }

        item.setName(name);
        item.setImageUri(imageUriString);
        item.setCategory(selectedCategory);
        item.setSubcategory(selectedSubCategory);
        item.setColor(selectedColor);
        item.setOccasion(selectedOccasion);
        // Set other optional fields if they exist in your model

        if (isEditMode) {
            clothingManager.updateClothingItem(item);
        } else {
            clothingManager.addClothingItem(item);
        }

        ErrorUtils.showUserError(this, getString(R.string.toast_save_successful));
        setResult(RESULT_OK); // Set result for the calling activity
        finish();
    }
}