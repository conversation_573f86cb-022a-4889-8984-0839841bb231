<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:padding="16dp"
    android:background="@color/secondarySystemGroupedBackground">

    <ImageView
        android:id="@+id/color_circle"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginEnd="12dp"
        android:src="@drawable/color_circle_background" />

    <TextView
        android:id="@+id/spinner_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textAppearance="?attr/textAppearanceBody1"
        android:textColor="@color/labelPrimary"
        android:text="Color Name" />

</LinearLayout>
