package com.wardrobe.app.utils;

import android.app.ActivityManager;
import android.content.Context;
import android.graphics.Bitmap;
import android.os.Debug;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 内存优化管理器
 * 提供内存监控、清理和优化功能
 * 防止内存泄漏和OOM异常
 */
public class MemoryOptimizer {
    
    private static final String TAG = "MemoryOptimizer";
    private static volatile MemoryOptimizer instance;
    private static final Object LOCK = new Object();
    
    // 内存阈值配置
    private static final float LOW_MEMORY_THRESHOLD = 0.85f; // 85%内存使用率
    private static final float CRITICAL_MEMORY_THRESHOLD = 0.95f; // 95%内存使用率
    private static final long MEMORY_CHECK_INTERVAL = 30000; // 30秒检查一次
    
    private final Context context;
    private final ActivityManager activityManager;
    private final List<WeakReference<MemoryCleanupListener>> cleanupListeners;
    private final AsyncTaskManager asyncTaskManager;
    
    private boolean isMonitoring = false;
    private long lastMemoryCheck = 0;
    
    /**
     * 内存清理监听器
     */
    public interface MemoryCleanupListener {
        void onLowMemory();
        void onCriticalMemory();
        void onMemoryCleanupRequested();
    }
    
    /**
     * 内存状态信息
     */
    public static class MemoryInfo {
        public final long totalMemory;
        public final long usedMemory;
        public final long freeMemory;
        public final float usagePercentage;
        public final boolean isLowMemory;
        public final boolean isCriticalMemory;
        
        public MemoryInfo(long totalMemory, long usedMemory, long freeMemory, 
                         float usagePercentage, boolean isLowMemory, boolean isCriticalMemory) {
            this.totalMemory = totalMemory;
            this.usedMemory = usedMemory;
            this.freeMemory = freeMemory;
            this.usagePercentage = usagePercentage;
            this.isLowMemory = isLowMemory;
            this.isCriticalMemory = isCriticalMemory;
        }
        
        @Override
        public String toString() {
            return String.format("内存使用: %.1f%% (%dMB/%dMB), 可用: %dMB", 
                    usagePercentage, usedMemory / 1024 / 1024, totalMemory / 1024 / 1024, 
                    freeMemory / 1024 / 1024);
        }
    }
    
    private MemoryOptimizer(Context context) {
        this.context = context.getApplicationContext();
        this.activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        this.cleanupListeners = new ArrayList<>();
        this.asyncTaskManager = AsyncTaskManager.getInstance();
        
        Logger.d(TAG, "MemoryOptimizer 初始化完成");
    }
    
    /**
     * 获取MemoryOptimizer实例
     * 
     * @param context 上下文
     * @return MemoryOptimizer实例
     */
    public static MemoryOptimizer getInstance(Context context) {
        if (instance == null) {
            synchronized (LOCK) {
                if (instance == null) {
                    instance = new MemoryOptimizer(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * 获取当前内存状态
     * 
     * @return 内存状态信息
     */
    public MemoryInfo getCurrentMemoryInfo() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        float usagePercentage = (usedMemory * 100.0f) / totalMemory;
        
        boolean isLowMemory = usagePercentage >= (LOW_MEMORY_THRESHOLD * 100);
        boolean isCriticalMemory = usagePercentage >= (CRITICAL_MEMORY_THRESHOLD * 100);
        
        return new MemoryInfo(totalMemory, usedMemory, freeMemory, usagePercentage, 
                             isLowMemory, isCriticalMemory);
    }
    
    /**
     * 获取系统内存信息
     * 
     * @return 系统内存信息
     */
    public ActivityManager.MemoryInfo getSystemMemoryInfo() {
        ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
        activityManager.getMemoryInfo(memoryInfo);
        return memoryInfo;
    }
    
    /**
     * 开始内存监控
     */
    public void startMemoryMonitoring() {
        if (isMonitoring) {
            return;
        }
        
        isMonitoring = true;
        Logger.d(TAG, "开始内存监控");
        
        // 启动后台监控任务
        asyncTaskManager.executeBackground(() -> {
            while (isMonitoring) {
                try {
                    checkMemoryStatus();
                    Thread.sleep(MEMORY_CHECK_INTERVAL);
                } catch (InterruptedException e) {
                    Logger.d(TAG, "内存监控被中断");
                    break;
                } catch (Exception e) {
                    Logger.e(TAG, "内存监控异常", e);
                }
            }
            return null;
        }, null);
    }
    
    /**
     * 停止内存监控
     */
    public void stopMemoryMonitoring() {
        isMonitoring = false;
        Logger.d(TAG, "停止内存监控");
    }
    
    /**
     * 检查内存状态
     */
    private void checkMemoryStatus() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastMemoryCheck < MEMORY_CHECK_INTERVAL) {
            return;
        }
        
        lastMemoryCheck = currentTime;
        MemoryInfo memoryInfo = getCurrentMemoryInfo();
        
        Logger.d(TAG, memoryInfo.toString());
        
        if (memoryInfo.isCriticalMemory) {
            Logger.w(TAG, "检测到严重内存不足");
            notifyListeners(listener -> listener.onCriticalMemory());
            performEmergencyCleanup();
        } else if (memoryInfo.isLowMemory) {
            Logger.w(TAG, "检测到内存不足");
            notifyListeners(listener -> listener.onLowMemory());
            performLightCleanup();
        }
    }
    
    /**
     * 执行轻度清理
     */
    private void performLightCleanup() {
        Logger.d(TAG, "执行轻度内存清理");
        
        // 建议垃圾回收
        System.gc();
        
        // 通知监听器
        notifyListeners(listener -> listener.onMemoryCleanupRequested());
    }
    
    /**
     * 执行紧急清理
     */
    private void performEmergencyCleanup() {
        Logger.w(TAG, "执行紧急内存清理");
        
        // 强制垃圾回收
        System.gc();
        System.runFinalization();
        System.gc();
        
        // 清理图片缓存
        ImageOptimizer imageOptimizer = ImageOptimizer.getInstance(context);
        imageOptimizer.clearMemoryCache();
        
        // 通知监听器
        notifyListeners(listener -> listener.onMemoryCleanupRequested());
        
        Logger.d(TAG, "紧急清理完成: " + getCurrentMemoryInfo());
    }
    
    /**
     * 手动触发内存清理
     */
    public void triggerMemoryCleanup() {
        Logger.d(TAG, "手动触发内存清理");
        
        MemoryInfo memoryInfo = getCurrentMemoryInfo();
        if (memoryInfo.isCriticalMemory) {
            performEmergencyCleanup();
        } else if (memoryInfo.isLowMemory) {
            performLightCleanup();
        } else {
            // 即使内存充足也进行轻度清理
            System.gc();
        }
    }
    
    /**
     * 添加内存清理监听器
     * 
     * @param listener 监听器
     */
    public void addMemoryCleanupListener(MemoryCleanupListener listener) {
        synchronized (cleanupListeners) {
            cleanupListeners.add(new WeakReference<>(listener));
        }
    }
    
    /**
     * 移除内存清理监听器
     * 
     * @param listener 监听器
     */
    public void removeMemoryCleanupListener(MemoryCleanupListener listener) {
        synchronized (cleanupListeners) {
            Iterator<WeakReference<MemoryCleanupListener>> iterator = cleanupListeners.iterator();
            while (iterator.hasNext()) {
                WeakReference<MemoryCleanupListener> ref = iterator.next();
                MemoryCleanupListener l = ref.get();
                if (l == null || l == listener) {
                    iterator.remove();
                }
            }
        }
    }
    
    /**
     * 通知所有监听器
     * 
     * @param action 执行的动作
     */
    private void notifyListeners(ListenerAction action) {
        synchronized (cleanupListeners) {
            Iterator<WeakReference<MemoryCleanupListener>> iterator = cleanupListeners.iterator();
            while (iterator.hasNext()) {
                WeakReference<MemoryCleanupListener> ref = iterator.next();
                MemoryCleanupListener listener = ref.get();
                if (listener == null) {
                    iterator.remove();
                } else {
                    try {
                        action.execute(listener);
                    } catch (Exception e) {
                        Logger.e(TAG, "通知监听器失败", e);
                    }
                }
            }
        }
    }
    
    /**
     * 监听器动作接口
     */
    private interface ListenerAction {
        void execute(MemoryCleanupListener listener);
    }
    
    /**
     * 获取内存使用详情
     * 
     * @return 内存使用详情
     */
    public String getDetailedMemoryInfo() {
        MemoryInfo appMemory = getCurrentMemoryInfo();
        ActivityManager.MemoryInfo systemMemory = getSystemMemoryInfo();
        
        // 获取堆内存信息
        long heapSize = Debug.getNativeHeapSize() / 1024 / 1024;
        long heapAllocated = Debug.getNativeHeapAllocatedSize() / 1024 / 1024;
        long heapFree = Debug.getNativeHeapFreeSize() / 1024 / 1024;
        
        StringBuilder info = new StringBuilder();
        info.append("=== 应用内存信息 ===\n");
        info.append(appMemory.toString()).append("\n");
        info.append(String.format("堆内存: %dMB (已分配: %dMB, 可用: %dMB)\n", 
                heapSize, heapAllocated, heapFree));
        
        info.append("\n=== 系统内存信息 ===\n");
        info.append(String.format("可用内存: %dMB\n", systemMemory.availMem / 1024 / 1024));
        info.append(String.format("总内存: %dMB\n", systemMemory.totalMem / 1024 / 1024));
        info.append(String.format("内存不足: %s\n", systemMemory.lowMemory ? "是" : "否"));
        info.append(String.format("内存阈值: %dMB\n", systemMemory.threshold / 1024 / 1024));
        
        return info.toString();
    }
    
    /**
     * 检查是否存在内存泄漏风险
     * 
     * @return 是否存在风险
     */
    public boolean checkMemoryLeakRisk() {
        MemoryInfo memoryInfo = getCurrentMemoryInfo();
        
        // 简单的内存泄漏检测逻辑
        boolean hasRisk = false;
        
        // 检查内存使用率是否持续过高
        if (memoryInfo.usagePercentage > 80) {
            Logger.w(TAG, "内存使用率过高，可能存在内存泄漏风险");
            hasRisk = true;
        }
        
        // 检查可用内存是否过少
        if (memoryInfo.freeMemory < 50 * 1024 * 1024) { // 少于50MB
            Logger.w(TAG, "可用内存过少，可能存在内存泄漏风险");
            hasRisk = true;
        }
        
        return hasRisk;
    }
    
    /**
     * 优化Bitmap内存使用
     * 
     * @param bitmap Bitmap对象
     * @return 优化后的Bitmap
     */
    public static Bitmap optimizeBitmap(Bitmap bitmap) {
        if (bitmap == null || bitmap.isRecycled()) {
            return bitmap;
        }
        
        // 如果是ARGB_8888格式且不需要透明度，转换为RGB_565
        if (bitmap.getConfig() == Bitmap.Config.ARGB_8888 && !bitmap.hasAlpha()) {
            Bitmap optimized = bitmap.copy(Bitmap.Config.RGB_565, false);
            if (optimized != null) {
                Logger.d(TAG, "Bitmap优化: ARGB_8888 -> RGB_565, 内存节省: " + 
                        (bitmap.getByteCount() - optimized.getByteCount()) / 1024 + "KB");
                return optimized;
            }
        }
        
        return bitmap;
    }
    
    /**
     * 安全回收Bitmap
     * 
     * @param bitmap Bitmap对象
     */
    public static void recycleBitmap(Bitmap bitmap) {
        if (bitmap != null && !bitmap.isRecycled()) {
            bitmap.recycle();
            Logger.d(TAG, "Bitmap已回收");
        }
    }
}
