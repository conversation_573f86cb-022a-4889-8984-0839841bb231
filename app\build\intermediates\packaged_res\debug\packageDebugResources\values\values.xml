<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent">#D0BCFF</color>
    <color name="accent_primary">#007AFF</color>
    <color name="background_primary">#FFFBFE</color>
    <color name="background_primary_ios">#FFFFFF</color>
    <color name="background_secondary">#F7F2FA</color>
    <color name="background_secondary_ios">#F2F2F7</color>
    <color name="background_tertiary">#E7E0EC</color>
    <color name="black">#FF000000</color>
    <color name="black_alpha_12">#1F000000</color>
    <color name="black_alpha_24">#3D000000</color>
    <color name="black_alpha_54">#8A000000</color>
    <color name="border">#79747E</color>
    <color name="borderColor">#79747E</color>
    <color name="border_primary">#D1D1D6</color>
    <color name="buttonBackground">#6750A4</color>
    <color name="buttonTextColor">#FFFFFF</color>
    <color name="cardBackground">#FFFFFF</color>
    <color name="category_accessories">#FCE4EC</color>
    <color name="category_bottoms">#F3E5F5</color>
    <color name="category_other">#F5F5F5</color>
    <color name="category_outerwear">#E8F5E8</color>
    <color name="category_shoes">#FFF3E0</color>
    <color name="category_tops">#E3F2FD</color>
    <color name="colorBackground">#FFFBFE</color>
    <color name="colorError">#BA1A1A</color>
    <color name="colorOnBackground">#1C1B1F</color>
    <color name="colorOnError">#FFFFFF</color>
    <color name="colorOnPrimary">#FFFFFF</color>
    <color name="colorOnSecondary">#FFFFFF</color>
    <color name="colorOnSurface">#1C1B1F</color>
    <color name="colorPrimary">#6750A4</color>
    <color name="colorPrimaryVariant">#4F378B</color>
    <color name="colorSecondary">#625B71</color>
    <color name="colorSecondaryVariant">#4A4458</color>
    <color name="colorSurface">#FFFBFE</color>
    <color name="containerBackground">#F7F2FA</color>
    <color name="destructive">#FF3B30</color>
    <color name="destructiveRed">#FF3B30</color>
    <color name="disabled">#F7F2FA</color>
    <color name="divider">#CAC4D0</color>
    <color name="dividerColor">#CAC4D0</color>
    <color name="fillTertiary">#D1D1D6</color>
    <color name="info">#2196F3</color>
    <color name="labelPrimary">#000000</color>
    <color name="labelQuaternary">#3C3C43</color>
    <color name="labelSecondary">#3C3C43</color>
    <color name="labelTertiary">#3C3C43</color>
    <color name="md_theme_dark_background">#1C1B1F</color>
    <color name="md_theme_dark_error">#FFB4AB</color>
    <color name="md_theme_dark_errorContainer">#93000A</color>
    <color name="md_theme_dark_onBackground">#E6E1E5</color>
    <color name="md_theme_dark_onError">#690005</color>
    <color name="md_theme_dark_onErrorContainer">#FFDAD6</color>
    <color name="md_theme_dark_onPrimary">#381E72</color>
    <color name="md_theme_dark_onPrimaryContainer">#EADDFF</color>
    <color name="md_theme_dark_onSecondary">#332D41</color>
    <color name="md_theme_dark_onSecondaryContainer">#E8DEF8</color>
    <color name="md_theme_dark_onSurface">#E6E1E5</color>
    <color name="md_theme_dark_onSurfaceVariant">#CAC4D0</color>
    <color name="md_theme_dark_onTertiary">#492532</color>
    <color name="md_theme_dark_onTertiaryContainer">#FFD8E4</color>
    <color name="md_theme_dark_outline">#938F99</color>
    <color name="md_theme_dark_outlineVariant">#49454F</color>
    <color name="md_theme_dark_primary">#D0BCFF</color>
    <color name="md_theme_dark_primaryContainer">#4F378B</color>
    <color name="md_theme_dark_secondary">#CCC2DC</color>
    <color name="md_theme_dark_secondaryContainer">#4A4458</color>
    <color name="md_theme_dark_surface">#1C1B1F</color>
    <color name="md_theme_dark_surfaceVariant">#49454F</color>
    <color name="md_theme_dark_tertiary">#EFB8C8</color>
    <color name="md_theme_dark_tertiaryContainer">#633B48</color>
    <color name="md_theme_light_background">#FFFBFE</color>
    <color name="md_theme_light_error">#BA1A1A</color>
    <color name="md_theme_light_errorContainer">#FFDAD6</color>
    <color name="md_theme_light_onBackground">#1C1B1F</color>
    <color name="md_theme_light_onError">#FFFFFF</color>
    <color name="md_theme_light_onErrorContainer">#410002</color>
    <color name="md_theme_light_onPrimary">#FFFFFF</color>
    <color name="md_theme_light_onPrimaryContainer">#21005D</color>
    <color name="md_theme_light_onSecondary">#FFFFFF</color>
    <color name="md_theme_light_onSecondaryContainer">#1D192B</color>
    <color name="md_theme_light_onSurface">#1C1B1F</color>
    <color name="md_theme_light_onSurfaceVariant">#49454F</color>
    <color name="md_theme_light_onTertiary">#FFFFFF</color>
    <color name="md_theme_light_onTertiaryContainer">#31111D</color>
    <color name="md_theme_light_outline">#79747E</color>
    <color name="md_theme_light_outlineVariant">#CAC4D0</color>
    <color name="md_theme_light_primary">#6750A4</color>
    <color name="md_theme_light_primaryContainer">#EADDFF</color>
    <color name="md_theme_light_secondary">#625B71</color>
    <color name="md_theme_light_secondaryContainer">#E8DEF8</color>
    <color name="md_theme_light_surface">#FFFBFE</color>
    <color name="md_theme_light_surfaceVariant">#E7E0EC</color>
    <color name="md_theme_light_tertiary">#7D5260</color>
    <color name="md_theme_light_tertiaryContainer">#FFD8E4</color>
    <color name="navigationBarColor">#FFFBFE</color>
    <color name="opaqueSeparator">#C6C6C8</color>
    <color name="overlay">#80000000</color>
    <color name="placeholderText">#8E8E93</color>
    <color name="pressed">#E8DEF8</color>
    <color name="primary">#6750A4</color>
    <color name="primary_dark">#4F378B</color>
    <color name="quaternarySystemGroupedBackground">#FFFFFF</color>
    <color name="rippleColor">#1F6750A4</color>
    <color name="secondarySystemGroupedBackground">#F2F2F7</color>
    <color name="selected">#EADDFF</color>
    <color name="selectedItemBackground">#EADDFF</color>
    <color name="separator">#3C3C43</color>
    <color name="shimmer_base">#F0F0F0</color>
    <color name="shimmer_highlight">#FFFFFF</color>
    <color name="statusBarColor">#6750A4</color>
    <color name="success">#4CAF50</color>
    <color name="surface_primary">#FFFFFF</color>
    <color name="systemBackground">#FFFFFF</color>
    <color name="systemBlue">#007AFF</color>
    <color name="systemGray">#8E8E93</color>
    <color name="systemGray2">#AEAEB2</color>
    <color name="systemGray3">#C7C7CC</color>
    <color name="systemGray4">#D1D1D6</color>
    <color name="systemGray5">#E5E5EA</color>
    <color name="systemGray6">#F2F2F7</color>
    <color name="systemGroupedBackground">#F2F2F7</color>
    <color name="systemRed">#FF3B30</color>
    <color name="textColorHint">#79747E</color>
    <color name="textColorPrimary">#1C1B1F</color>
    <color name="textColorSecondary">#49454F</color>
    <color name="text_inverse">#FFFFFF</color>
    <color name="text_primary">#1C1B1F</color>
    <color name="text_primary_ios">#000000</color>
    <color name="text_secondary">#49454F</color>
    <color name="text_secondary_ios">#8E8E93</color>
    <color name="text_tertiary">#79747E</color>
    <color name="text_tertiary_ios">#C7C7CC</color>
    <color name="warning">#FF9800</color>
    <color name="white">#FFFFFFFF</color>
    <color name="white_alpha_12">#1FFFFFFF</color>
    <color name="white_alpha_24">#3DFFFFFF</color>
    <color name="white_alpha_54">#8AFFFFFF</color>
    <string name="accessibility_add_button">添加新衣物</string>
    <string name="accessibility_clothing_image">衣物图片</string>
    <string name="accessibility_filter_button">筛选衣物</string>
    <string name="accessibility_search_button">搜索衣物</string>
    <string name="app_name">WardrobeApp</string>
    <string name="button_add">添加</string>
    <string name="button_cancel">取消</string>
    <string name="button_clear">清除</string>
    <string name="button_confirm">确认</string>
    <string name="button_delete">删除</string>
    <string name="button_deselect_all">取消全选</string>
    <string name="button_edit">编辑</string>
    <string name="button_filter">筛选</string>
    <string name="button_save">保存</string>
    <string name="button_search">搜索</string>
    <string name="button_select_all">全选</string>
    <string name="category_accessories">配饰</string>
    <string name="category_bottoms">下装</string>
    <string name="category_dresses">连衣裙</string>
    <string name="category_outerwear">外套</string>
    <string name="category_placeholder">请选择分类</string>
    <string name="category_primary_placeholder">请选择主分类</string>
    <string name="category_secondary_placeholder">请选择子分类</string>
    <string name="category_sets">套装</string>
    <string name="category_shoes">鞋子</string>
    <string name="category_suits">西装</string>
    <string name="category_tops">上衣</string>
    <string name="category_underwear">内衣</string>
    <string name="category_workwear">工作服</string>
    <string name="color_beige">米色</string>
    <string name="color_black">黑色</string>
    <string name="color_blue">蓝色</string>
    <string name="color_brown">棕色</string>
    <string name="color_custom">自定义</string>
    <string name="color_gray">灰色</string>
    <string name="color_green">绿色</string>
    <string name="color_khaki">卡其色</string>
    <string name="color_navy">藏青色</string>
    <string name="color_orange">橙色</string>
    <string name="color_pink">粉色</string>
    <string name="color_placeholder">请选择颜色</string>
    <string name="color_purple">紫色</string>
    <string name="color_red">红色</string>
    <string name="color_white">白色</string>
    <string name="color_yellow">黄色</string>
    <string name="compress_all_images">压缩所有图片</string>
    <string name="compressing">压缩中...</string>
    <string name="dialog_button_cancel">取消</string>
    <string name="dialog_option_camera">拍照</string>
    <string name="dialog_option_gallery">从相册选择</string>
    <string name="dialog_title_select_color">选择颜色</string>
    <string name="dialog_title_select_occasion">选择场合</string>
    <string name="dialog_title_select_photo">选择图片</string>
    <string name="error_batch_add_all_failed">全部失败，共%1$d张图片未添加</string>
    <string name="error_cleanup_images">清理失败: %1$s</string>
    <string name="error_compress_images">压缩失败: %1$s</string>
    <string name="error_file_not_found">文件未找到</string>
    <string name="error_generic">发生错误</string>
    <string name="error_get_storage_info">获取存储信息失败</string>
    <string name="error_incomplete_selection_batch">请先选择完整的分类</string>
    <string name="error_invalid_data">数据无效</string>
    <string name="error_load_data">加载数据失败</string>
    <string name="error_main_category_missing_batch">主分类缺失，无法批量添加</string>
    <string name="error_network">网络连接失败</string>
    <string name="error_no_images_selected_batch">请至少选择一张图片</string>
    <string name="error_permission">权限被拒绝</string>
    <string name="error_start_compress">启动压缩失败: %1$s</string>
    <string name="filter_by_category">按分类筛选</string>
    <string name="filter_by_color">按颜色筛选</string>
    <string name="filter_by_occasion">按场合筛选</string>
    <string name="filter_clear_all">清除所有筛选</string>
    <string name="hint_clothing_name">衣物名称</string>
    <string name="images_selected_count_batch">已选择%1$d张图片</string>
    <string name="info_no_items_to_add_batch">没有可添加的衣物</string>
    <string name="label_category">分类</string>
    <string name="label_color">颜色</string>
    <string name="label_occasion">场合</string>
    <string name="message_add_success">添加成功</string>
    <string name="message_delete_success">删除成功</string>
    <string name="message_loading">加载中...</string>
    <string name="message_network_error">网络连接失败</string>
    <string name="message_no_data">暂无数据</string>
    <string name="message_operation_failed">操作失败</string>
    <string name="message_permission_denied">权限被拒绝</string>
    <string name="message_save_success">保存成功</string>
    <string name="message_search_no_results">未找到搜索结果</string>
    <string name="message_update_success">更新成功</string>
    <string name="nav_calendar">日历</string>
    <string name="nav_outfit">搭配</string>
    <string name="nav_profile">个人</string>
    <string name="nav_wardrobe">衣橱</string>
    <string name="occasion_casual">休闲</string>
    <string name="occasion_daily">日常</string>
    <string name="occasion_date">约会</string>
    <string name="occasion_formal">正式</string>
    <string name="occasion_home">居家</string>
    <string name="occasion_outdoor">户外</string>
    <string name="occasion_party">派对</string>
    <string name="occasion_placeholder">请选择场合</string>
    <string name="occasion_sport">运动</string>
    <string name="occasion_sports">运动</string>
    <string name="occasion_travel">旅行</string>
    <string name="occasion_work">工作</string>
    <string name="photo_choose_gallery">从相册选择</string>
    <string name="photo_remove">移除照片</string>
    <string name="photo_take_photo">拍照</string>
    <string name="placeholder_select">请选择</string>
    <string name="search_hint">搜索衣物...</string>
    <string name="section_optional">可选信息</string>
    <string name="section_required">必填信息</string>
    <string name="settings_about">关于</string>
    <string name="settings_language">语言</string>
    <string name="settings_notifications">通知</string>
    <string name="settings_privacy">隐私</string>
    <string name="settings_theme">主题</string>
    <string name="success_batch_add_clothing_summary">成功添加%1$d件%2$s</string>
    <string name="success_batch_add_clothing_summary_with_failures">成功添加%1$d件%2$s，%3$d张图片失败</string>
    <string name="success_cleanup_all_images">清理完成，删除了 %1$d 个图片文件</string>
    <string name="success_cleanup_unused_images">清理完成，删除了 %1$d 个未使用的图片文件</string>
    <string name="success_compress_images">压缩完成，处理了 %1$d 个图片文件</string>
    <string name="title_add_new_clothing">添加新衣物</string>
    <string name="title_batch_add_prefix">批量添加：%1$s</string>
    <string name="title_edit_clothing">编辑衣物</string>
    <string name="toast_create_image_file_failed">无法创建图片文件</string>
    <string name="toast_image_selection_cancelled">已取消选择图片</string>
    <string name="toast_load_failed">加载失败</string>
    <string name="toast_permission_denied">权限被拒绝，无法继续操作</string>
    <string name="toast_permission_granted_retry">权限已授予，请重试操作</string>
    <string name="toast_permissions_denied">权限被拒绝，无法选择图片</string>
    <string name="toast_save_failed">保存失败</string>
    <string name="toast_save_successful">保存成功</string>
    <string name="toast_select_image_first">请先选择一张图片</string>
    <string name="validation_category_required">分类不能为空</string>
    <string name="validation_invalid_input">输入无效</string>
    <string name="validation_name_required">衣物名称不能为空</string>
    <string name="validation_name_too_long">名称过长</string>
    <style name="Animation.WardrobeApp.Activity" parent="@android:style/Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/slide_in_right</item>
        <item name="android:activityOpenExitAnimation">@anim/slide_out_left</item>
        <item name="android:activityCloseEnterAnimation">@anim/slide_in_left</item>
        <item name="android:activityCloseExitAnimation">@anim/slide_out_right</item>
    </style>
    <style name="AppModalStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@drawable/dialog_rounded_background</item>
    </style>
    <style name="Base.Theme.WardrobeApp" parent="Theme.WardrobeApp.Material3">
        
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="BottomSheetDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomSheetStyle</item>
    </style>
    <style name="BottomSheetStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
    </style>
    <style name="ButtonDestructive" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background_destructive</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:minHeight">44dp</item>
        <item name="android:textAllCaps">false</item>
    </style>
    <style name="ButtonPrimary" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background_prominent</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:minHeight">44dp</item>
        <item name="android:textAllCaps">false</item>
    </style>
    <style name="ButtonSecondary" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background</item>
        <item name="android:textColor">@color/systemBlue</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:minHeight">44dp</item>
        <item name="android:textAllCaps">false</item>
    </style>
    <style name="ButtonTertiary" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background</item>
        <item name="android:textColor">@color/labelSecondary</item>
        <item name="android:textSize">14sp</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:minHeight">32dp</item>
        <item name="android:textAllCaps">false</item>
    </style>
    <style name="DestructiveButton" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background_destructive</item>
        <item name="android:textColor">@color/destructiveRed</item>
        <item name="android:textSize">17sp</item>
    </style>
    <style name="ProminentButton" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background_prominent</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">17sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="SectionHeader">
        <item name="android:textSize">13sp</item>
        <item name="android:textColor">@color/systemGray</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingBottom">8dp</item>
    </style>
    <style name="Separator">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0.5dp</item>
        <item name="android:background">@color/separator</item>
    </style>
    <style name="TextAppearance.App.Base" parent="">
        <item name="android:textColor">@color/labelPrimary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.App.Body" parent="TextAppearance.App.Base">
        <item name="android:textSize">17sp</item>
    </style>
    <style name="TextAppearance.App.Callout" parent="TextAppearance.App.Base">
        <item name="android:textSize">16sp</item>
    </style>
    <style name="TextAppearance.App.Caption1" parent="TextAppearance.App.Base">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/labelSecondary</item>
    </style>
    <style name="TextAppearance.App.Caption2" parent="TextAppearance.App.Base">
        <item name="android:textSize">11sp</item>
        <item name="android:textColor">@color/labelTertiary</item>
    </style>
    <style name="TextAppearance.App.Footnote" parent="TextAppearance.App.Base">
        <item name="android:textSize">13sp</item>
        <item name="android:textColor">@color/labelSecondary</item>
    </style>
    <style name="TextAppearance.App.Headline" parent="TextAppearance.App.Base">
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textSize">17sp</item>
    </style>
    <style name="TextAppearance.App.LargeTitle" parent="TextAppearance.App.Base">
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:textSize">34sp</item>
    </style>
    <style name="TextAppearance.App.Subheadline" parent="TextAppearance.App.Base">
        <item name="android:textSize">15sp</item>
        <item name="android:textColor">@color/labelSecondary</item>
    </style>
    <style name="TextAppearance.App.Title1" parent="TextAppearance.App.Base">
        <item name="android:textSize">28sp</item>
    </style>
    <style name="TextAppearance.App.Title2" parent="TextAppearance.App.Base">
        <item name="android:textSize">22sp</item>
    </style>
    <style name="TextAppearance.App.Title3" parent="TextAppearance.App.Base">
        <item name="android:textSize">20sp</item>
    </style>
    <style name="TextAppearance.WardrobeApp.BodyLarge" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.WardrobeApp.BodyMedium" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.WardrobeApp.BodySmall" parent="TextAppearance.Material3.BodySmall">
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.WardrobeApp.HeadlineLarge" parent="TextAppearance.Material3.HeadlineLarge">
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.WardrobeApp.HeadlineMedium" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.WardrobeApp.HeadlineSmall" parent="TextAppearance.Material3.HeadlineSmall">
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.WardrobeApp.LabelLarge" parent="TextAppearance.Material3.LabelLarge">
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.WardrobeApp.LabelMedium" parent="TextAppearance.Material3.LabelMedium">
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.WardrobeApp.LabelSmall" parent="TextAppearance.Material3.LabelSmall">
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.WardrobeApp.TitleLarge" parent="TextAppearance.Material3.TitleLarge">
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.WardrobeApp.TitleMedium" parent="TextAppearance.Material3.TitleMedium">
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.WardrobeApp.TitleSmall" parent="TextAppearance.Material3.TitleSmall">
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    <style name="Theme.WardrobeApp" parent="Base.Theme.WardrobeApp"/>
    <style name="Theme.WardrobeApp.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="Theme.WardrobeApp.BottomSheetDialog" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/AppModalStyle</item>
    </style>
    <style name="Theme.WardrobeApp.Button.Destructive" parent="ButtonDestructive"/>
    <style name="Theme.WardrobeApp.Button.Prominent" parent="ButtonPrimary"/>
    <style name="Theme.WardrobeApp.Dialog" parent="Theme.Material3.DayNight.Dialog.Alert">
        <item name="colorPrimary">@color/systemBlue</item>
        <item name="android:background">@color/secondarySystemGroupedBackground</item>
        <item name="android:windowBackground">@drawable/dialog_rounded_background</item>
    </style>
    <style name="Theme.WardrobeApp.EditTextStyle" parent="Widget.AppCompat.EditText">
        <item name="android:textColor">@color/labelPrimary</item>
        <item name="android:textColorHint">@color/placeholderText</item>
        <item name="android:textSize">16sp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
    </style>
    <style name="Theme.WardrobeApp.Material3" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>
        
        
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_light_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_light_onSecondaryContainer</item>
        
        
        <item name="colorTertiary">@color/md_theme_light_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_light_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_light_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_light_onTertiaryContainer</item>
        
        
        <item name="colorError">@color/md_theme_light_error</item>
        <item name="colorOnError">@color/md_theme_light_onError</item>
        <item name="colorErrorContainer">@color/md_theme_light_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_light_onErrorContainer</item>
        
        
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_light_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_onSurfaceVariant</item>
        
        
        <item name="colorOutline">@color/md_theme_light_outline</item>
        <item name="colorOutlineVariant">@color/md_theme_light_outlineVariant</item>
        
        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar">true</item>
        
        
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        
        
        <item name="textAppearanceHeadlineLarge">@style/TextAppearance.WardrobeApp.HeadlineLarge</item>
        <item name="textAppearanceHeadlineMedium">@style/TextAppearance.WardrobeApp.HeadlineMedium</item>
        <item name="textAppearanceHeadlineSmall">@style/TextAppearance.WardrobeApp.HeadlineSmall</item>
        <item name="textAppearanceTitleLarge">@style/TextAppearance.WardrobeApp.TitleLarge</item>
        <item name="textAppearanceTitleMedium">@style/TextAppearance.WardrobeApp.TitleMedium</item>
        <item name="textAppearanceTitleSmall">@style/TextAppearance.WardrobeApp.TitleSmall</item>
        <item name="textAppearanceBodyLarge">@style/TextAppearance.WardrobeApp.BodyLarge</item>
        <item name="textAppearanceBodyMedium">@style/TextAppearance.WardrobeApp.BodyMedium</item>
        <item name="textAppearanceBodySmall">@style/TextAppearance.WardrobeApp.BodySmall</item>
        <item name="textAppearanceLabelLarge">@style/TextAppearance.WardrobeApp.LabelLarge</item>
        <item name="textAppearanceLabelMedium">@style/TextAppearance.WardrobeApp.LabelMedium</item>
        <item name="textAppearanceLabelSmall">@style/TextAppearance.WardrobeApp.LabelSmall</item>
        
        
        <item name="materialButtonStyle">@style/Widget.WardrobeApp.Button</item>
        <item name="materialCardViewStyle">@style/Widget.WardrobeApp.CardView</item>
        <item name="chipStyle">@style/Widget.WardrobeApp.Chip</item>
        <item name="textInputStyle">@style/Widget.WardrobeApp.TextInputLayout</item>
        
        
        <item name="android:windowAnimationStyle">@style/Animation.WardrobeApp.Activity</item>
        <item name="android:windowEnterAnimation">@anim/slide_in_right</item>
        <item name="android:windowExitAnimation">@anim/slide_out_left</item>
    </style>
    <style name="Theme.WardrobeApp.Material3.Dark" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/md_theme_dark_primary</item>
        <item name="colorOnPrimary">@color/md_theme_dark_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_dark_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_dark_onPrimaryContainer</item>
        
        
        <item name="colorSecondary">@color/md_theme_dark_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_dark_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_dark_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_dark_onSecondaryContainer</item>
        
        
        <item name="colorTertiary">@color/md_theme_dark_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_dark_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_dark_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_dark_onTertiaryContainer</item>
        
        
        <item name="colorError">@color/md_theme_dark_error</item>
        <item name="colorOnError">@color/md_theme_dark_onError</item>
        <item name="colorErrorContainer">@color/md_theme_dark_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_dark_onErrorContainer</item>
        
        
        <item name="android:colorBackground">@color/md_theme_dark_background</item>
        <item name="colorOnBackground">@color/md_theme_dark_onBackground</item>
        <item name="colorSurface">@color/md_theme_dark_surface</item>
        <item name="colorOnSurface">@color/md_theme_dark_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_dark_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_dark_onSurfaceVariant</item>
        
        
        <item name="colorOutline">@color/md_theme_dark_outline</item>
        <item name="colorOutlineVariant">@color/md_theme_dark_outlineVariant</item>
        
        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
        
        
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
    </style>
    <style name="Theme.WardrobeApp.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.WardrobeApp.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/>
    <style name="Widget.WardrobeApp.Button" parent="Widget.Material3.Button">
        <item name="android:textAppearance">@style/TextAppearance.WardrobeApp.LabelLarge</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
    </style>
    <style name="Widget.WardrobeApp.Button.Outlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textAppearance">@style/TextAppearance.WardrobeApp.LabelLarge</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
    </style>
    <style name="Widget.WardrobeApp.Button.Text" parent="Widget.Material3.Button.TextButton">
        <item name="android:textAppearance">@style/TextAppearance.WardrobeApp.LabelLarge</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
    </style>
    <style name="Widget.WardrobeApp.CardView" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">16dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:layout_margin">8dp</item>
    </style>
    <style name="Widget.WardrobeApp.Chip" parent="Widget.Material3.Chip.Filter">
        <item name="chipCornerRadius">20dp</item>
        <item name="chipMinHeight">40dp</item>
        <item name="android:textAppearance">@style/TextAppearance.WardrobeApp.LabelMedium</item>
    </style>
    <style name="Widget.WardrobeApp.TextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxCornerRadiusTopStart">12dp</item>
        <item name="boxCornerRadiusTopEnd">12dp</item>
        <item name="boxCornerRadiusBottomStart">12dp</item>
        <item name="boxCornerRadiusBottomEnd">12dp</item>
        <item name="android:layout_marginBottom">16dp</item>
    </style>
    <style name="iOSChevron">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_alignParentEnd">true</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:src">@drawable/ic_chevron_right</item>
        
    </style>
    <style name="iOSGroupedBackground">
        <item name="android:background">@color/white</item>
        <item name="android:orientation">vertical</item>
    </style>
    <style name="iOSGroupedBackground.Bottom" parent="iOSGroupedBackground">
        <item name="android:background">@drawable/ios_style_group_background_bottom</item>
    </style>
    <style name="iOSGroupedBackground.Middle" parent="iOSGroupedBackground">
        <item name="android:background">@drawable/ios_style_group_background_middle</item>
    </style>
    <style name="iOSGroupedBackground.Single" parent="iOSGroupedBackground">
        <item name="android:background">@drawable/ios_style_group_background_single</item>
    </style>
    <style name="iOSGroupedBackground.Top" parent="iOSGroupedBackground">
        <item name="android:background">@drawable/ios_style_group_background_top</item>
    </style>
    <style name="iOSLabel">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_alignParentStart">true</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:textColor">@color/labelPrimary</item>
        <item name="android:textSize">17sp</item>
    </style>
    <style name="iOSListItem">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:minHeight">44dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">16dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
        <item name="android:background">@drawable/secondary_system_grouped_background_ripple</item>
    </style>
    <style name="iOSValue">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:textColor">@color/systemGray</item>
        <item name="android:textSize">17sp</item>
        <item name="android:layout_marginEnd">8dp</item>
    </style>
</resources>