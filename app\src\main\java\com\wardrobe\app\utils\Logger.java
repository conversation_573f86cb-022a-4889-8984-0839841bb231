package com.wardrobe.app.utils;

import android.util.Log;
import com.wardrobe.app.BuildConfig;

/**
 * 统一日志管理器
 * 提供统一的日志输出格式和级别控制
 * 在Release版本中自动禁用调试日志
 */
public class Logger {
    
    private static final String APP_TAG = "WardrobeApp";
    private static final boolean DEBUG_ENABLED = BuildConfig.DEBUG;
    
    /**
     * 输出调试日志
     * 
     * @param tag 标签
     * @param message 消息
     */
    public static void d(String tag, String message) {
        if (DEBUG_ENABLED) {
            Log.d(formatTag(tag), message);
        }
    }
    
    /**
     * 输出调试日志（带异常）
     * 
     * @param tag 标签
     * @param message 消息
     * @param throwable 异常
     */
    public static void d(String tag, String message, Throwable throwable) {
        if (DEBUG_ENABLED) {
            Log.d(formatTag(tag), message, throwable);
        }
    }
    
    /**
     * 输出信息日志
     * 
     * @param tag 标签
     * @param message 消息
     */
    public static void i(String tag, String message) {
        Log.i(formatTag(tag), message);
    }
    
    /**
     * 输出信息日志（带异常）
     * 
     * @param tag 标签
     * @param message 消息
     * @param throwable 异常
     */
    public static void i(String tag, String message, Throwable throwable) {
        Log.i(formatTag(tag), message, throwable);
    }
    
    /**
     * 输出警告日志
     * 
     * @param tag 标签
     * @param message 消息
     */
    public static void w(String tag, String message) {
        Log.w(formatTag(tag), message);
    }
    
    /**
     * 输出警告日志（带异常）
     * 
     * @param tag 标签
     * @param message 消息
     * @param throwable 异常
     */
    public static void w(String tag, String message, Throwable throwable) {
        Log.w(formatTag(tag), message, throwable);
    }
    
    /**
     * 输出错误日志
     * 
     * @param tag 标签
     * @param message 消息
     */
    public static void e(String tag, String message) {
        Log.e(formatTag(tag), message);
    }
    
    /**
     * 输出错误日志（带异常）
     * 
     * @param tag 标签
     * @param message 消息
     * @param throwable 异常
     */
    public static void e(String tag, String message, Throwable throwable) {
        Log.e(formatTag(tag), message, throwable);
    }
    
    /**
     * 输出详细日志
     * 
     * @param tag 标签
     * @param message 消息
     */
    public static void v(String tag, String message) {
        if (DEBUG_ENABLED) {
            Log.v(formatTag(tag), message);
        }
    }
    
    /**
     * 输出详细日志（带异常）
     * 
     * @param tag 标签
     * @param message 消息
     * @param throwable 异常
     */
    public static void v(String tag, String message, Throwable throwable) {
        if (DEBUG_ENABLED) {
            Log.v(formatTag(tag), message, throwable);
        }
    }
    
    /**
     * 格式化标签
     * 
     * @param tag 原始标签
     * @return 格式化后的标签
     */
    private static String formatTag(String tag) {
        return APP_TAG + "-" + tag;
    }
    
    /**
     * 记录方法进入
     * 
     * @param tag 标签
     * @param methodName 方法名
     */
    public static void enter(String tag, String methodName) {
        if (DEBUG_ENABLED) {
            d(tag, ">>> " + methodName);
        }
    }
    
    /**
     * 记录方法退出
     * 
     * @param tag 标签
     * @param methodName 方法名
     */
    public static void exit(String tag, String methodName) {
        if (DEBUG_ENABLED) {
            d(tag, "<<< " + methodName);
        }
    }
    
    /**
     * 记录方法执行时间
     * 
     * @param tag 标签
     * @param methodName 方法名
     * @param startTime 开始时间
     */
    public static void logExecutionTime(String tag, String methodName, long startTime) {
        if (DEBUG_ENABLED) {
            long executionTime = System.currentTimeMillis() - startTime;
            d(tag, methodName + " 执行时间: " + executionTime + "ms");
        }
    }
    
    /**
     * 记录对象状态
     * 
     * @param tag 标签
     * @param objectName 对象名
     * @param state 状态描述
     */
    public static void logState(String tag, String objectName, String state) {
        if (DEBUG_ENABLED) {
            d(tag, objectName + " 状态: " + state);
        }
    }
    
    /**
     * 记录数据操作
     * 
     * @param tag 标签
     * @param operation 操作类型
     * @param dataType 数据类型
     * @param count 数据数量
     */
    public static void logDataOperation(String tag, String operation, String dataType, int count) {
        i(tag, String.format("数据操作: %s %s, 数量: %d", operation, dataType, count));
    }
    
    /**
     * 记录用户操作
     * 
     * @param tag 标签
     * @param action 用户操作
     * @param details 操作详情
     */
    public static void logUserAction(String tag, String action, String details) {
        i(tag, String.format("用户操作: %s - %s", action, details));
    }
    
    /**
     * 记录性能指标
     * 
     * @param tag 标签
     * @param metric 指标名称
     * @param value 指标值
     * @param unit 单位
     */
    public static void logPerformance(String tag, String metric, long value, String unit) {
        i(tag, String.format("性能指标: %s = %d %s", metric, value, unit));
    }
    
    /**
     * 检查是否启用调试模式
     * 
     * @return 是否启用调试
     */
    public static boolean isDebugEnabled() {
        return DEBUG_ENABLED;
    }
}
