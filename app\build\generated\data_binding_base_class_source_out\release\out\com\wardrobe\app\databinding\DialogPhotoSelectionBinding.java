// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogPhotoSelectionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout optionCamera;

  @NonNull
  public final TextView optionCancel;

  @NonNull
  public final LinearLayout optionGallery;

  private DialogPhotoSelectionBinding(@NonNull LinearLayout rootView,
      @NonNull LinearLayout optionCamera, @NonNull TextView optionCancel,
      @NonNull LinearLayout optionGallery) {
    this.rootView = rootView;
    this.optionCamera = optionCamera;
    this.optionCancel = optionCancel;
    this.optionGallery = optionGallery;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogPhotoSelectionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogPhotoSelectionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_photo_selection, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogPhotoSelectionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.option_camera;
      LinearLayout optionCamera = ViewBindings.findChildViewById(rootView, id);
      if (optionCamera == null) {
        break missingId;
      }

      id = R.id.option_cancel;
      TextView optionCancel = ViewBindings.findChildViewById(rootView, id);
      if (optionCancel == null) {
        break missingId;
      }

      id = R.id.option_gallery;
      LinearLayout optionGallery = ViewBindings.findChildViewById(rootView, id);
      if (optionGallery == null) {
        break missingId;
      }

      return new DialogPhotoSelectionBinding((LinearLayout) rootView, optionCamera, optionCancel,
          optionGallery);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
