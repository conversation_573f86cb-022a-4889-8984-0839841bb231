package com.wardrobe.app.repository;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.wardrobe.app.model.OutfitRecord;
import com.wardrobe.app.utils.DataException;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static com.wardrobe.app.data.DataConstants.*;

/**
 * 穿搭记录Repository
 * 管理穿搭记录的数据访问和业务逻辑
 */
public class OutfitRepository {
    
    private static final String TAG = "OutfitRepository";
    
    private final SharedPreferences sharedPreferences;
    private final Gson gson;
    private final ExecutorService executorService;
    
    // 回调接口
    public interface DataCallback<T> {
        void onSuccess(T result);
        void onError(String error);
    }
    
    public interface BooleanCallback {
        void onSuccess(boolean result);
        void onError(String error);
    }
    
    public OutfitRepository(Context context) {
        this.sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.gson = new GsonBuilder()
                .setPrettyPrinting()
                .setDateFormat(DATETIME_FORMAT_STORAGE)
                .create();
        this.executorService = Executors.newFixedThreadPool(2);
        
        Log.d(TAG, "OutfitRepository 初始化完成");
    }
    
    // ==================== 同步方法 ====================
    
    /**
     * 获取所有穿搭记录
     */
    public List<OutfitRecord> getAllOutfitRecords() throws DataException {
        try {
            String json = sharedPreferences.getString(OUTFIT_RECORDS_KEY, "");
            if (json.isEmpty()) {
                return new ArrayList<>();
            }
            
            Type type = new TypeToken<List<OutfitRecord>>(){}.getType();
            List<OutfitRecord> records = gson.fromJson(json, type);
            return records != null ? records : new ArrayList<>();
            
        } catch (Exception e) {
            Log.e(TAG, "获取穿搭记录失败", e);
            throw new DataException("获取穿搭记录失败", e);
        }
    }
    
    /**
     * 根据ID获取穿搭记录
     */
    public OutfitRecord getOutfitRecordById(String id) {
        try {
            return getAllOutfitRecords().stream()
                    .filter(record -> id.equals(record.getId()))
                    .findFirst()
                    .orElse(null);
        } catch (DataException e) {
            Log.e(TAG, "获取穿搭记录失败", e);
            return null;
        }
    }
    
    /**
     * 添加穿搭记录
     */
    public boolean addOutfitRecord(OutfitRecord record) throws DataException {
        try {
            validateOutfitRecord(record);
            
            List<OutfitRecord> records = getAllOutfitRecords();
            
            // 检查是否已存在同一天的记录
            if (hasRecordForDate(records, record.getDate())) {
                throw new DataException("该日期已有穿搭记录，请先删除或编辑现有记录");
            }
            
            // 检查数量限制
            if (records.size() >= MAX_OUTFIT_RECORDS) {
                // 删除最旧的记录
                removeOldestRecord(records);
            }
            
            records.add(0, record);
            saveOutfitRecords(records);
            
            Log.d(TAG, "添加穿搭记录成功: " + record.getId());
            return true;
            
        } catch (DataException e) {
            throw e;
        } catch (Exception e) {
            Log.e(TAG, "添加穿搭记录失败", e);
            throw new DataException("添加穿搭记录失败", e);
        }
    }
    
    /**
     * 更新穿搭记录
     */
    public boolean updateOutfitRecord(OutfitRecord record) throws DataException {
        try {
            validateOutfitRecord(record);
            
            List<OutfitRecord> records = getAllOutfitRecords();
            boolean found = false;
            
            for (int i = 0; i < records.size(); i++) {
                if (records.get(i).getId().equals(record.getId())) {
                    records.set(i, record);
                    found = true;
                    break;
                }
            }
            
            if (!found) {
                throw new DataException("未找到要更新的穿搭记录");
            }
            
            saveOutfitRecords(records);
            Log.d(TAG, "更新穿搭记录成功: " + record.getId());
            return true;
            
        } catch (DataException e) {
            throw e;
        } catch (Exception e) {
            Log.e(TAG, "更新穿搭记录失败", e);
            throw new DataException("更新穿搭记录失败", e);
        }
    }
    
    /**
     * 删除穿搭记录
     */
    public boolean deleteOutfitRecord(String id) throws DataException {
        try {
            List<OutfitRecord> records = getAllOutfitRecords();
            boolean removed = records.removeIf(record -> record.getId().equals(id));
            
            if (!removed) {
                throw new DataException("未找到要删除的穿搭记录");
            }
            
            saveOutfitRecords(records);
            Log.d(TAG, "删除穿搭记录成功: " + id);
            return true;
            
        } catch (DataException e) {
            throw e;
        } catch (Exception e) {
            Log.e(TAG, "删除穿搭记录失败", e);
            throw new DataException("删除穿搭记录失败", e);
        }
    }
    
    /**
     * 获取指定日期的穿搭记录
     */
    public OutfitRecord getOutfitRecordForDate(Date date) {
        try {
            List<OutfitRecord> records = getAllOutfitRecords();
            return findRecordForDate(records, date);
        } catch (DataException e) {
            Log.e(TAG, "获取指定日期穿搭记录失败", e);
            return null;
        }
    }
    
    /**
     * 获取指定月份的穿搭记录
     */
    public List<OutfitRecord> getOutfitRecordsForMonth(int year, int month) {
        try {
            List<OutfitRecord> allRecords = getAllOutfitRecords();
            Calendar calendar = Calendar.getInstance();
            
            return allRecords.stream()
                    .filter(record -> {
                        calendar.setTime(record.getDate());
                        return calendar.get(Calendar.YEAR) == year &&
                               calendar.get(Calendar.MONTH) == month;
                    })
                    .collect(Collectors.toList());
                    
        } catch (DataException e) {
            Log.e(TAG, "获取月份穿搭记录失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 获取穿搭记录总数
     */
    public int getTotalRecordCount() {
        try {
            return getAllOutfitRecords().size();
        } catch (DataException e) {
            Log.e(TAG, "获取记录总数失败", e);
            return 0;
        }
    }
    
    // ==================== 异步方法 ====================
    
    /**
     * 异步获取所有穿搭记录
     */
    public void getAllOutfitRecordsAsync(DataCallback<List<OutfitRecord>> callback) {
        executorService.execute(() -> {
            try {
                List<OutfitRecord> records = getAllOutfitRecords();
                callback.onSuccess(records);
            } catch (DataException e) {
                callback.onError("获取穿搭记录失败: " + e.getMessage());
            }
        });
    }
    
    /**
     * 异步添加穿搭记录
     */
    public void addOutfitRecordAsync(OutfitRecord record, BooleanCallback callback) {
        executorService.execute(() -> {
            try {
                boolean result = addOutfitRecord(record);
                callback.onSuccess(result);
            } catch (DataException e) {
                callback.onError("添加穿搭记录失败: " + e.getMessage());
            }
        });
    }
    
    /**
     * 异步更新穿搭记录
     */
    public void updateOutfitRecordAsync(OutfitRecord record, BooleanCallback callback) {
        executorService.execute(() -> {
            try {
                boolean result = updateOutfitRecord(record);
                callback.onSuccess(result);
            } catch (DataException e) {
                callback.onError("更新穿搭记录失败: " + e.getMessage());
            }
        });
    }
    
    // ==================== 私有辅助方法 ====================
    
    /**
     * 保存穿搭记录列表
     */
    private void saveOutfitRecords(List<OutfitRecord> records) {
        try {
            String json = gson.toJson(records);
            sharedPreferences.edit()
                    .putString(OUTFIT_RECORDS_KEY, json)
                    .apply();
            Log.d(TAG, "穿搭记录保存成功，共 " + records.size() + " 条记录");
        } catch (Exception e) {
            Log.e(TAG, "保存穿搭记录失败", e);
            throw new RuntimeException("保存穿搭记录失败", e);
        }
    }
    
    /**
     * 验证穿搭记录
     */
    private void validateOutfitRecord(OutfitRecord record) throws DataException {
        if (record == null) {
            throw new DataException("穿搭记录不能为空");
        }
        
        if (!record.isValid()) {
            throw new DataException("穿搭记录数据无效");
        }
    }
    
    /**
     * 检查是否已有指定日期的记录
     */
    private boolean hasRecordForDate(List<OutfitRecord> records, Date date) {
        return findRecordForDate(records, date) != null;
    }
    
    /**
     * 查找指定日期的记录
     */
    private OutfitRecord findRecordForDate(List<OutfitRecord> records, Date date) {
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal1.setTime(date);
        
        for (OutfitRecord record : records) {
            cal2.setTime(record.getDate());
            if (cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
                cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR)) {
                return record;
            }
        }
        return null;
    }
    
    /**
     * 删除最旧的记录
     */
    private void removeOldestRecord(List<OutfitRecord> records) {
        if (records.isEmpty()) return;
        
        OutfitRecord oldest = records.get(0);
        for (OutfitRecord record : records) {
            if (record.getDate().before(oldest.getDate())) {
                oldest = record;
            }
        }
        records.remove(oldest);
        Log.d(TAG, "删除最旧的穿搭记录: " + oldest.getId());
    }
    
    /**
     * 关闭资源
     */
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}
