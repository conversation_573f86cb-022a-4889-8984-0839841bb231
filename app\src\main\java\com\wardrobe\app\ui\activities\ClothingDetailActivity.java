package com.wardrobe.app.ui.activities;

import android.app.Dialog;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.wardrobe.app.R;
import com.wardrobe.app.databinding.ActivityClothingDetailBinding;
import com.wardrobe.app.model.ClothingItem;
import com.wardrobe.app.ui.adapters.ClothingListAdapter;
import com.wardrobe.app.ClothingManager;
import com.wardrobe.app.utils.ErrorUtils;

import java.io.File;
import java.util.List;

public class ClothingDetailActivity extends AppCompatActivity {

    private static final String TAG = "ClothingDetailActivity";
    private ClothingListAdapter adapter;
    private ClothingManager clothingManager;
    private String category;
    private ActivityClothingDetailBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        binding = ActivityClothingDetailBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        category = getIntent().getStringExtra("mainCategory");
        if (category == null) {
            category = "全部";
        }
        setTitle(category + "列表");
        
        clothingManager = ClothingManager.getInstance(this);
        setupRecyclerView();
        loadClothingData();
    }

    private void setupRecyclerView() {
        StaggeredGridLayoutManager layoutManager = new StaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL);
        binding.recyclerView.setLayoutManager(layoutManager);
        adapter = new ClothingListAdapter();
        binding.recyclerView.setAdapter(adapter);

        adapter.setOnItemClickListener(item -> {
            // 显示图片全屏预览
            showFullscreenImage(item.getImageUri());
        });
    }

    private void showFullscreenImage(String imageUri) {
        if (imageUri == null || imageUri.isEmpty()) {
            ErrorUtils.showUserError(this, getString(R.string.toast_select_image_first));
            return;
        }

        ErrorUtils.logError("显示全屏图片: " + imageUri, null);

        // 创建全屏对话框
        Dialog dialog = new Dialog(this, android.R.style.Theme_Black_NoTitleBar_Fullscreen);
        dialog.setContentView(R.layout.dialog_image_fullscreen);

        ImageView fullscreenImageView = dialog.findViewById(R.id.fullscreen_image_view);
        ImageView closeButton = dialog.findViewById(R.id.close_button);

        // 检查文件是否存在
        File imageFile = new File(imageUri);
        if (!imageFile.exists()) {
            ErrorUtils.showAndLog(this, getString(R.string.toast_image_selection_cancelled), "图片文件不存在: " + imageUri, null);
            return;
        }

        try {
            // 使用BitmapFactory同步加载图片
            Bitmap bitmap = BitmapFactory.decodeFile(imageUri);
            if (bitmap != null) {
                fullscreenImageView.setImageBitmap(bitmap);
                ErrorUtils.logError("图片加载成功，尺寸: " + bitmap.getWidth() + "x" + bitmap.getHeight(), null);
            } else {
                ErrorUtils.showAndLog(this, getString(R.string.toast_load_failed), "图片解码失败", null);
                return;
            }
        } catch (Exception e) {
            ErrorUtils.showAndLog(this, getString(R.string.toast_load_failed), "加载图片时出错", e);
            return;
        }

        // 关闭按钮点击事件
        closeButton.setOnClickListener(v -> dialog.dismiss());

        // 图片点击事件 - 关闭对话框
        fullscreenImageView.setOnClickListener(v -> dialog.dismiss());

        // 显示对话框
        dialog.show();
    }

    private void loadClothingData() {
        Log.d(TAG, "开始加载数据，目标分类: '" + category + "'");
        List<ClothingItem> clothingList = clothingManager.getClothingByMainCategory(category);
        Log.d(TAG, "成功获取到 " + clothingList.size() + " 件衣物");
        adapter.updateData(clothingList);
        updateCountAndEmptyState(clothingList);
    }

    private void updateCountAndEmptyState(List<ClothingItem> clothingList) {
        if (clothingList.isEmpty()) {
            binding.recyclerView.setVisibility(View.GONE);
            binding.emptyView.setVisibility(View.VISIBLE);
            binding.countView.setText("暂无衣物");
        } else {
            binding.recyclerView.setVisibility(View.VISIBLE);
            binding.emptyView.setVisibility(View.GONE);
            binding.countView.setText("共 " + clothingList.size() + " 件衣物");
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        loadClothingData();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        super.onCreateOptionsMenu(menu);
        menu.add(0, 100, 0, "批量添加").setShowAsAction(MenuItem.SHOW_AS_ACTION_IF_ROOM);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == 100) {
            Intent intent = new Intent(this, BatchAddClothingActivity.class);
            intent.putExtra("mainCategory", category);
            startActivity(intent);
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
} 