<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Test Summary</title>
<link href="css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="css/style.css" rel="stylesheet" type="text/css"/>
<script src="js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Test Summary</h1>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">47</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">1</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.570s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox skipped" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Ignored tests</a>
</li>
<li>
<a href="#tab1">Packages</a>
</li>
<li>
<a href="#tab2">Classes</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Ignored tests</h2>
<ul class="linkList">
<li>
<a href="classes/com.wardrobe.app.utils.SecurityUtilsTest.html">SecurityUtilsTest</a>.
<a href="classes/com.wardrobe.app.utils.SecurityUtilsTest.html#testEncryptAndDecryptData">testEncryptAndDecryptData</a>
</li>
</ul>
</div>
<div id="tab1" class="tab">
<h2>Packages</h2>
<table>
<thead>
<tr>
<th>Package</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="packages/com.wardrobe.app.html">com.wardrobe.app</a>
</td>
<td>7</td>
<td>0</td>
<td>0</td>
<td>0.009s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="packages/com.wardrobe.app.model.html">com.wardrobe.app.model</a>
</td>
<td>11</td>
<td>0</td>
<td>0</td>
<td>0.004s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="skipped">
<a href="packages/com.wardrobe.app.utils.html">com.wardrobe.app.utils</a>
</td>
<td>29</td>
<td>0</td>
<td>1</td>
<td>0.557s</td>
<td class="skipped">100%</td>
</tr>
</tbody>
</table>
</div>
<div id="tab2" class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="classes/com.wardrobe.app.ClothingItemTest.html">com.wardrobe.app.ClothingItemTest</a>
</td>
<td>7</td>
<td>0</td>
<td>0</td>
<td>0.009s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.wardrobe.app.model.ClothingItemTest.html">com.wardrobe.app.model.ClothingItemTest</a>
</td>
<td>11</td>
<td>0</td>
<td>0</td>
<td>0.004s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="skipped">
<a href="classes/com.wardrobe.app.utils.SecurityUtilsTest.html">com.wardrobe.app.utils.SecurityUtilsTest</a>
</td>
<td>24</td>
<td>0</td>
<td>1</td>
<td>0.557s</td>
<td class="skipped">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.wardrobe.app.utils.SimpleValidatorTest.html">com.wardrobe.app.utils.SimpleValidatorTest</a>
</td>
<td>5</td>
<td>0</td>
<td>0</td>
<td>0s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.11.1</a> at 2025年6月25日 20:57:44</p>
</div>
</div>
</body>
</html>
