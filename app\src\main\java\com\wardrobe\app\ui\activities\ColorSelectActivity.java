package com.wardrobe.app.ui.activities;

import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.GridLayoutManager;

import com.flask.colorpicker.ColorPickerView;
import com.flask.colorpicker.OnColorSelectedListener;
import com.flask.colorpicker.builder.ColorPickerClickListener;
import com.flask.colorpicker.builder.ColorPickerDialogBuilder;
import com.wardrobe.app.R;
import com.wardrobe.app.ui.adapters.ColorListAdapter;
import com.wardrobe.app.ui.components.ColorProvider;
import com.wardrobe.app.model.CategoryItem;

import java.util.List;

public class ColorSelectActivity extends AppCompatActivity implements ColorListAdapter.OnItemClickListener {
    public static final String EXTRA_SELECTED_COLOR_NAME = "selected_color_name";
    public static final String EXTRA_SELECTED_COLOR_HEX = "selected_color_hex";

    private List<CategoryItem> allColors;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_color_select);

        RecyclerView recyclerView = findViewById(R.id.recycler_view);
        TextView tvTitle = findViewById(R.id.tv_title);
        // The add button is no longer needed in this new flow
        findViewById(R.id.btn_add_color).setVisibility(View.GONE);
        
        recyclerView.setLayoutManager(new GridLayoutManager(this, 3));
        tvTitle.setText("选择颜色");

        findViewById(R.id.btn_back).setOnClickListener(v -> finish());
        
        loadColors(recyclerView);
    }

    private void loadColors(RecyclerView recyclerView) {
        allColors = ColorProvider.getColors(this);
        ColorListAdapter adapter = new ColorListAdapter(this, allColors, this);
        recyclerView.setAdapter(adapter);
    }

    @Override
    public void onItemClick(CategoryItem color) {
        if (color.isCustom() && color.getName().equals("自定义颜色")) {
            // User clicked the placeholder to add a new custom color
            openColorPickerDialog();
        } else {
            // User clicked a pre-existing color
            returnSelectedColor(color);
        }
    }

    private void openColorPickerDialog() {
        ColorPickerDialogBuilder
                .with(this, R.style.Theme_WardrobeApp_Dialog)
                .setTitle("选择一个颜色")
                .initialColor(0xFFFFFFFF) // Initial white color
                .wheelType(ColorPickerView.WHEEL_TYPE.FLOWER)
                .density(12)
                .setOnColorSelectedListener(new OnColorSelectedListener() {
                    @Override
                    public void onColorSelected(int selectedColor) {
                        // Optionally show a toast, but it's better to act on confirmation
                    }
                })
                .setPositiveButton("确定", new ColorPickerClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int selectedColor, Integer[] allColors) {
                        String hexColor = String.format("#%06X", (0xFFFFFF & selectedColor));
                        // Create a new custom color item with the hex value as its name
                        CategoryItem customColor = new CategoryItem(hexColor, hexColor);
                        returnSelectedColor(customColor);
                    }
                })
                .setNegativeButton("取消", (dialog, which) -> {
                })
                .build()
                .show();
    }

    private void returnSelectedColor(CategoryItem color) {
        Intent result = new Intent();
        result.putExtra(EXTRA_SELECTED_COLOR_NAME, color.getName());
        result.putExtra(EXTRA_SELECTED_COLOR_HEX, color.getIcon());
        setResult(RESULT_OK, result);
        finish();
    }
}