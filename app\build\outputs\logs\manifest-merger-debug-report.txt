-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:68:9-76:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:72:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:70:13-64
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:71:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:69:13-62
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:2:1-82:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:2:1-82:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:2:1-82:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:2:1-82:12
MERGED from [androidx.databinding:viewbinding:8.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50308c514c1a619557837e8ee7f6d15a\transformed\jetified-viewbinding-8.11.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b37ed3c3253d49ff585314266092e27\transformed\jetified-leakcanary-android-2.12\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\86e6281540a3cdfac31ba0f2ba29551c\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ebe57103d982ca8eb201ae4b37e472a\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.github.QuadFlask:colorpicker:0.0.13] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c9ae20e25c3ada439badd0f0fb03783\transformed\jetified-colorpicker-0.0.13\AndroidManifest.xml:2:1-14:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d231231441e26a11cbe297d38851637d\transformed\jetified-appcompat-resources-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a486d55bfd94b1f617db1e4b30fa627\transformed\appcompat-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9a93f03855b5eaabfcf28171e1208d\transformed\jetified-glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42bf959001d7beb490584aea655afdd0\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b540fc5ee75f5a99d12a736194321936\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ddb7f22faf7911a0aac4088dd68b05f\transformed\jetified-activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ff8b6fe12f2a1ed8ccad07a67773747\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa397e0249b688eb5b4ea512c1e6a26e\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6518e484ddb0210d340d62f2dea32bb3\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\261dc2496d162faf100ce2a908446a7a\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\013ddaa7c74171bcbd63ac0b80e4b546\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28f562e484d33c29cb2a9d39d9a240f7\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\929852580eb9ed64730878aadb8e54d0\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\def2f62b9cdbe7df12fd261c87fe7bfa\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bad8db1df68290b0fb27469c5a2b612\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\738d5a9d64f4d6f099a0f7e738cb7d6c\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d6ad538ba7b6953c405adc3690b1cef\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\145319a23cfba167e7fff5ae3a69ebcb\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cf06a72a9028920b4515aeda562aa8\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b7209686a4398a8999780988859e2a5\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f790513bbcdc09b3d69b7655b0bf4d88\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b1add4d1f76b267de44dc49e5165183\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c54f80f996dbc27ca4d97bc54a4bdba2\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9d0401d009487db703b94fa2ddca06f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d02a45d38bd6e57dbcbb8c6429538283\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72e1db31088a1973f44f08714d8e02e6\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4bd42021b644526778d9bd9a197fa80\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea473efc4287bc0512d9da8cbf7e2\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5d22000b08a71c3dd7f38312ba6df48\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c181db924cc129978d48a9d015208edd\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c204589df1d54d59bef38b0eef3b4c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3acc0960f49fd7d41482923f66cfe378\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:17:1-105:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\4987f749363a1fe7966e5bd39851db31\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:2:1-15:12
MERGED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e281f90316021b68c0a4b703740198\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:2:1-15:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61d056d75b7c9a30c47279eb53465682\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\a300e5392e32223e3491faa8aff6af73\transformed\jetified-leakcanary-object-watcher-android-androidx-2.12\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\db0da940ec5c3ceec86a9ccacc8dd423\transformed\jetified-leakcanary-object-watcher-android-support-fragments-2.12\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae957e13b4c2b547c71ee98b7e6b0fa1\transformed\jetified-leakcanary-object-watcher-android-core-2.12\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:plumber-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\12e97ff55cfe3f4e7236c03093882802\transformed\jetified-plumber-android-core-2.12\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34e8f3fa0a2ad845011b9b8033df609b\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3dbabc2cffec10254cd1414d2a95df3\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\706886d08e4f3ae52ffc6c763d500f25\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\33683b1d148e9aec0096d5f9f23b3283\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2bc23b0d992a6b851995dd74ca534e2\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5f6ea8554e330acf3cee235e478a407\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a9b2b1116a9f1f3329ab4fac38816d8\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\373282b9cb2f7fbefe91fec60bbd5507\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a03ec50202507a1e627ae85095f95f3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12a19358ab36da93773975033ee61da1\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\158222f13156704b72b023e561c5da26\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\632d6717f5e7c91da40df01d6b27deee\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a372606dc661a756d9dc937e0ca52dd\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ffc8b1d24125bf0c8257b3aa5791943\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.squareup.leakcanary:leakcanary-android-utils:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fc80a398d9e02a11fbc59aedb585cb7\transformed\jetified-leakcanary-android-utils-2.12\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.curtains:curtains:1.2.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\93468047877023e7209fc5aa36e965a4\transformed\jetified-curtains-1.2.4\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:7:5-107
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:25:5-80
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:25:5-80
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:7:78-104
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:7:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:9:5-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:9:22-73
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:11:5-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:11:22-62
uses-feature#android.hardware.camera
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:17:5-19:36
	android:required
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:19:9-33
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:18:9-47
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:21:5-80:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:21:5-80:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\86e6281540a3cdfac31ba0f2ba29551c\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\86e6281540a3cdfac31ba0f2ba29551c\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ebe57103d982ca8eb201ae4b37e472a\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ebe57103d982ca8eb201ae4b37e472a\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [com.github.QuadFlask:colorpicker:0.0.13] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c9ae20e25c3ada439badd0f0fb03783\transformed\jetified-colorpicker-0.0.13\AndroidManifest.xml:12:5-20
MERGED from [com.github.QuadFlask:colorpicker:0.0.13] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c9ae20e25c3ada439badd0f0fb03783\transformed\jetified-colorpicker-0.0.13\AndroidManifest.xml:12:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6518e484ddb0210d340d62f2dea32bb3\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6518e484ddb0210d340d62f2dea32bb3\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\013ddaa7c74171bcbd63ac0b80e4b546\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\013ddaa7c74171bcbd63ac0b80e4b546\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea473efc4287bc0512d9da8cbf7e2\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea473efc4287bc0512d9da8cbf7e2\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:31:5-103:19
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:31:5-103:19
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\4987f749363a1fe7966e5bd39851db31\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:7:5-13:19
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\4987f749363a1fe7966e5bd39851db31\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:7:5-13:19
MERGED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e281f90316021b68c0a4b703740198\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:7:5-13:19
MERGED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e281f90316021b68c0a4b703740198\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:7:5-13:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2bc23b0d992a6b851995dd74ca534e2\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2bc23b0d992a6b851995dd74ca534e2\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a03ec50202507a1e627ae85095f95f3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a03ec50202507a1e627ae85095f95f3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea473efc4287bc0512d9da8cbf7e2\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:29:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:27:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:25:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:28:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:31:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:26:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:23:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:30:9-77
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:24:9-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:22:9-44
activity#com.wardrobe.app.ui.activities.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:33:9-40:20
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:35:13-36
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:34:13-55
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:36:13-39:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:37:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:37:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:38:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:38:27-74
activity#com.wardrobe.app.ui.activities.AddClothingActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:42:9-44:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:44:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:43:13-62
activity#com.wardrobe.app.ui.activities.CategorySelectActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:46:9-48:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:48:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:47:13-65
activity#com.wardrobe.app.ui.activities.ColorSelectActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:50:9-52:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:52:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:51:13-62
activity#com.wardrobe.app.ui.activities.OccasionSelectActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:54:9-56:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:56:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:55:13-65
activity#com.wardrobe.app.ui.activities.ClothingDetailActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:58:9-60:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:60:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:59:13-65
activity#com.wardrobe.app.ui.activities.BatchAddClothingActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:63:9-65:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:65:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:64:13-67
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:73:13-75:54
	android:resource
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:75:17-51
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:74:17-67
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50308c514c1a619557837e8ee7f6d15a\transformed\jetified-viewbinding-8.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\50308c514c1a619557837e8ee7f6d15a\transformed\jetified-viewbinding-8.11.0\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b37ed3c3253d49ff585314266092e27\transformed\jetified-leakcanary-android-2.12\AndroidManifest.xml:20:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b37ed3c3253d49ff585314266092e27\transformed\jetified-leakcanary-android-2.12\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\86e6281540a3cdfac31ba0f2ba29551c\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\86e6281540a3cdfac31ba0f2ba29551c\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ebe57103d982ca8eb201ae4b37e472a\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ebe57103d982ca8eb201ae4b37e472a\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.github.QuadFlask:colorpicker:0.0.13] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c9ae20e25c3ada439badd0f0fb03783\transformed\jetified-colorpicker-0.0.13\AndroidManifest.xml:8:5-10:41
MERGED from [com.github.QuadFlask:colorpicker:0.0.13] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c9ae20e25c3ada439badd0f0fb03783\transformed\jetified-colorpicker-0.0.13\AndroidManifest.xml:8:5-10:41
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d231231441e26a11cbe297d38851637d\transformed\jetified-appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d231231441e26a11cbe297d38851637d\transformed\jetified-appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a486d55bfd94b1f617db1e4b30fa627\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a486d55bfd94b1f617db1e4b30fa627\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9a93f03855b5eaabfcf28171e1208d\transformed\jetified-glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ab9a93f03855b5eaabfcf28171e1208d\transformed\jetified-glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42bf959001d7beb490584aea655afdd0\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42bf959001d7beb490584aea655afdd0\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b540fc5ee75f5a99d12a736194321936\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\b540fc5ee75f5a99d12a736194321936\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ddb7f22faf7911a0aac4088dd68b05f\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ddb7f22faf7911a0aac4088dd68b05f\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ff8b6fe12f2a1ed8ccad07a67773747\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ff8b6fe12f2a1ed8ccad07a67773747\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa397e0249b688eb5b4ea512c1e6a26e\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fa397e0249b688eb5b4ea512c1e6a26e\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6518e484ddb0210d340d62f2dea32bb3\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6518e484ddb0210d340d62f2dea32bb3\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\261dc2496d162faf100ce2a908446a7a\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\261dc2496d162faf100ce2a908446a7a\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\013ddaa7c74171bcbd63ac0b80e4b546\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\013ddaa7c74171bcbd63ac0b80e4b546\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28f562e484d33c29cb2a9d39d9a240f7\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\28f562e484d33c29cb2a9d39d9a240f7\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\929852580eb9ed64730878aadb8e54d0\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\929852580eb9ed64730878aadb8e54d0\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\def2f62b9cdbe7df12fd261c87fe7bfa\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\def2f62b9cdbe7df12fd261c87fe7bfa\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bad8db1df68290b0fb27469c5a2b612\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bad8db1df68290b0fb27469c5a2b612\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\738d5a9d64f4d6f099a0f7e738cb7d6c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\738d5a9d64f4d6f099a0f7e738cb7d6c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d6ad538ba7b6953c405adc3690b1cef\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d6ad538ba7b6953c405adc3690b1cef\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\145319a23cfba167e7fff5ae3a69ebcb\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\145319a23cfba167e7fff5ae3a69ebcb\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cf06a72a9028920b4515aeda562aa8\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\15cf06a72a9028920b4515aeda562aa8\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b7209686a4398a8999780988859e2a5\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b7209686a4398a8999780988859e2a5\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f790513bbcdc09b3d69b7655b0bf4d88\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f790513bbcdc09b3d69b7655b0bf4d88\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b1add4d1f76b267de44dc49e5165183\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b1add4d1f76b267de44dc49e5165183\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c54f80f996dbc27ca4d97bc54a4bdba2\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c54f80f996dbc27ca4d97bc54a4bdba2\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9d0401d009487db703b94fa2ddca06f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9d0401d009487db703b94fa2ddca06f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d02a45d38bd6e57dbcbb8c6429538283\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d02a45d38bd6e57dbcbb8c6429538283\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72e1db31088a1973f44f08714d8e02e6\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72e1db31088a1973f44f08714d8e02e6\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4bd42021b644526778d9bd9a197fa80\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4bd42021b644526778d9bd9a197fa80\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea473efc4287bc0512d9da8cbf7e2\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea473efc4287bc0512d9da8cbf7e2\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5d22000b08a71c3dd7f38312ba6df48\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f5d22000b08a71c3dd7f38312ba6df48\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c181db924cc129978d48a9d015208edd\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c181db924cc129978d48a9d015208edd\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c204589df1d54d59bef38b0eef3b4c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\80c204589df1d54d59bef38b0eef3b4c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3acc0960f49fd7d41482923f66cfe378\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3acc0960f49fd7d41482923f66cfe378\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:20:5-22:41
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:20:5-22:41
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\4987f749363a1fe7966e5bd39851db31\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\4987f749363a1fe7966e5bd39851db31\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e281f90316021b68c0a4b703740198\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e281f90316021b68c0a4b703740198\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61d056d75b7c9a30c47279eb53465682\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61d056d75b7c9a30c47279eb53465682\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\a300e5392e32223e3491faa8aff6af73\transformed\jetified-leakcanary-object-watcher-android-androidx-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\a300e5392e32223e3491faa8aff6af73\transformed\jetified-leakcanary-object-watcher-android-androidx-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\db0da940ec5c3ceec86a9ccacc8dd423\transformed\jetified-leakcanary-object-watcher-android-support-fragments-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\db0da940ec5c3ceec86a9ccacc8dd423\transformed\jetified-leakcanary-object-watcher-android-support-fragments-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae957e13b4c2b547c71ee98b7e6b0fa1\transformed\jetified-leakcanary-object-watcher-android-core-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae957e13b4c2b547c71ee98b7e6b0fa1\transformed\jetified-leakcanary-object-watcher-android-core-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\12e97ff55cfe3f4e7236c03093882802\transformed\jetified-plumber-android-core-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\12e97ff55cfe3f4e7236c03093882802\transformed\jetified-plumber-android-core-2.12\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34e8f3fa0a2ad845011b9b8033df609b\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\34e8f3fa0a2ad845011b9b8033df609b\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3dbabc2cffec10254cd1414d2a95df3\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.13\transforms\a3dbabc2cffec10254cd1414d2a95df3\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\706886d08e4f3ae52ffc6c763d500f25\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\706886d08e4f3ae52ffc6c763d500f25\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\33683b1d148e9aec0096d5f9f23b3283\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\33683b1d148e9aec0096d5f9f23b3283\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2bc23b0d992a6b851995dd74ca534e2\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2bc23b0d992a6b851995dd74ca534e2\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5f6ea8554e330acf3cee235e478a407\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5f6ea8554e330acf3cee235e478a407\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a9b2b1116a9f1f3329ab4fac38816d8\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a9b2b1116a9f1f3329ab4fac38816d8\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\373282b9cb2f7fbefe91fec60bbd5507\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\373282b9cb2f7fbefe91fec60bbd5507\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a03ec50202507a1e627ae85095f95f3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a03ec50202507a1e627ae85095f95f3\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12a19358ab36da93773975033ee61da1\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12a19358ab36da93773975033ee61da1\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\158222f13156704b72b023e561c5da26\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\158222f13156704b72b023e561c5da26\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\632d6717f5e7c91da40df01d6b27deee\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\632d6717f5e7c91da40df01d6b27deee\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a372606dc661a756d9dc937e0ca52dd\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a372606dc661a756d9dc937e0ca52dd\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ffc8b1d24125bf0c8257b3aa5791943\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8ffc8b1d24125bf0c8257b3aa5791943\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android-utils:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fc80a398d9e02a11fbc59aedb585cb7\transformed\jetified-leakcanary-android-utils-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android-utils:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fc80a398d9e02a11fbc59aedb585cb7\transformed\jetified-leakcanary-android-utils-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.curtains:curtains:1.2.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\93468047877023e7209fc5aa36e965a4\transformed\jetified-curtains-1.2.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.squareup.curtains:curtains:1.2.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\93468047877023e7209fc5aa36e965a4\transformed\jetified-curtains-1.2.4\AndroidManifest.xml:5:5-7:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6518e484ddb0210d340d62f2dea32bb3\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\013ddaa7c74171bcbd63ac0b80e4b546\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\013ddaa7c74171bcbd63ac0b80e4b546\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2bc23b0d992a6b851995dd74ca534e2\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2bc23b0d992a6b851995dd74ca534e2\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6518e484ddb0210d340d62f2dea32bb3\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6518e484ddb0210d340d62f2dea32bb3\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6518e484ddb0210d340d62f2dea32bb3\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6518e484ddb0210d340d62f2dea32bb3\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6518e484ddb0210d340d62f2dea32bb3\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6518e484ddb0210d340d62f2dea32bb3\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6518e484ddb0210d340d62f2dea32bb3\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\013ddaa7c74171bcbd63ac0b80e4b546\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\013ddaa7c74171bcbd63ac0b80e4b546\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\013ddaa7c74171bcbd63ac0b80e4b546\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea473efc4287bc0512d9da8cbf7e2\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea473efc4287bc0512d9da8cbf7e2\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea473efc4287bc0512d9da8cbf7e2\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.wardrobe.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea473efc4287bc0512d9da8cbf7e2\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea473efc4287bc0512d9da8cbf7e2\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea473efc4287bc0512d9da8cbf7e2\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea473efc4287bc0512d9da8cbf7e2\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea473efc4287bc0512d9da8cbf7e2\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.wardrobe.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea473efc4287bc0512d9da8cbf7e2\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea473efc4287bc0512d9da8cbf7e2\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:26:5-81
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:26:22-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:29:5-77
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:29:22-74
provider#leakcanary.internal.LeakCanaryFileProvider
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:32:9-40:20
	android:grantUriPermissions
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:36:13-47
	android:authorities
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:34:13-88
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:35:13-37
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:33:13-70
activity#leakcanary.internal.activity.LeakActivity
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:42:9-73:20
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:46:13-71
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:44:13-36
	android:icon
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:45:13-52
	android:theme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:48:13-63
	android:taskAffinity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:47:13-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:43:13-69
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:*+data:mimeType:*/*+data:pathPattern:.*\\..*\\..*\\..*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\.hprof+data:pathPattern:.*\\.hprof+data:scheme:content+data:scheme:file
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:49:13-72:29
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:49:28-81
action#android.intent.action.VIEW
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:50:17-69
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:50:25-66
category#android.intent.category.DEFAULT
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:52:17-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:52:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:53:17-78
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:53:27-75
data
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
	android:scheme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:23-44
activity-alias#leakcanary.internal.activity.LeakLauncherActivity
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:75:9-92:26
	android:enabled
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:78:13-66
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:81:13-71
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:79:13-36
	android:targetActivity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:82:13-79
	android:icon
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:80:13-52
	android:banner
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:77:13-59
	android:theme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:84:13-63
	android:taskAffinity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:83:13-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:76:13-77
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:85:13-91:29
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:90:17-86
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:90:27-83
activity#leakcanary.internal.RequestPermissionActivity
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:94:9-100:68
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:98:13-82
	android:excludeFromRecents
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:96:13-46
	android:icon
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:97:13-52
	android:theme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:100:13-65
	android:taskAffinity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:99:13-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:95:13-73
receiver#leakcanary.internal.NotificationReceiver
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:102:9-77
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:102:19-74
provider#leakcanary.internal.MainProcessAppWatcherInstaller
ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\4987f749363a1fe7966e5bd39851db31\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:8:9-12:40
	android:enabled
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\4987f749363a1fe7966e5bd39851db31\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:11:13-69
	android:authorities
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\4987f749363a1fe7966e5bd39851db31\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:10:13-72
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\4987f749363a1fe7966e5bd39851db31\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\4987f749363a1fe7966e5bd39851db31\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:9:13-78
provider#leakcanary.internal.PlumberInstaller
ADDED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e281f90316021b68c0a4b703740198\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:8:9-12:40
	android:enabled
		ADDED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e281f90316021b68c0a4b703740198\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:11:13-69
	android:authorities
		ADDED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e281f90316021b68c0a4b703740198\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:10:13-69
	android:exported
		ADDED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e281f90316021b68c0a4b703740198\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e281f90316021b68c0a4b703740198\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:9:13-64
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
