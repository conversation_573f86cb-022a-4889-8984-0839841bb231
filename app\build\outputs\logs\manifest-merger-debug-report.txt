-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:65:9-73:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:69:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:67:13-64
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:68:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:66:13-62
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:2:1-79:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:2:1-79:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:2:1-79:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:2:1-79:12
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59f532884cb1bb9abada84eb0f9b00e2\transformed\jetified-viewbinding-8.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\811604d3863bca31626557ac8606c24b\transformed\jetified-leakcanary-android-2.12\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\049b486e733e18fbf112fa9fc097a2f3\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5abfda4a05db92edc4ed0b608e413d64\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.github.QuadFlask:colorpicker:0.0.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\060c694a3c40b635c3510f4af8faaaef\transformed\jetified-colorpicker-0.0.13\AndroidManifest.xml:2:1-14:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cc26277969d13848258b84d5eb950c5\transformed\jetified-appcompat-resources-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46582a7ddf7236be9612eae4b0c579c2\transformed\appcompat-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c58295e18587b76701f1dbec03b2cd1\transformed\jetified-glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0770c3d14f7037e1a981f24254e0647b\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15159b840b74bb7e9ff75002d0821b19\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\acd48b7f434b4eb81efcdaf911394de3\transformed\jetified-activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6d0f713e2480fe8d98d2ab7e949da05\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bedde39795cec62d12743f646549b8b\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff799407a27be64bd6bd7a0117022fed\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\710e1e3cca06d39ef5f5ec834a7465b3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8df4c2bf94180d7207138d5536e85da\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d7e02d66d20ba814e7a86cda25373ff\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2738e4de59b90dd5ec301d88d7b4eea5\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\868f4bef48fb4fa6d23440a7ca010595\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7d96df336272224cf49dfdc1fd60c01\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2be63568309e885752d70988cb2094f4\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c86f448aee69100fb27a2afe4cc1f338\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a5b4fdc733b5799215cde553020e235\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd5b3d03bd792874e7d136f41b830067\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da93eb5054b045613f979ad4c1403584\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bd5d6f216104422faecea35d341ab1\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c732ef48c045c8037252a29830c59e3a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\599dc6df7e778fecad2c701329a7122d\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e11ab1df40cc8c1dc45e7fa6610dab3c\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0dfa1844179c565bce16a38d14dc0659\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e17fd54cd2ffce6770c6ef72276489f6\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18eaf398f47abd175ed50b451879129c\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e929b83b04a689b4469e7f143f7af245\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e36bb9c1540ae359e4abe81c5ffd3113\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8da990c00b16836307bce0075693bd1b\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:17:1-105:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa77027ba7d1354e3a853d05bfbf16aa\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:2:1-15:12
MERGED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f2ce96999776b68457ba832c6b7bd7d\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:2:1-15:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ebf01a427a45f3010f5406c94836ff0\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7999dfaa6b5c8bef32e652b3e8668c96\transformed\jetified-leakcanary-object-watcher-android-androidx-2.12\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6c1e26d38d4cade94ca06677f85bf493\transformed\jetified-leakcanary-object-watcher-android-support-fragments-2.12\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1eef3773d27e06a52ddb78a86a31d57f\transformed\jetified-leakcanary-object-watcher-android-core-2.12\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:plumber-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\********************************\transformed\jetified-plumber-android-core-2.12\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08b2e06f7bc5c421014b4de9b903740f\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e3f341b35f0be97db4c2fc8795e40a\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4dc66f361fbe9b9a6d5762dd801aad82\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7b9ff5a45e5f995a2b3c56b92b7cb6\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77aa445f8b2a5d7f749de8848ad8ac15\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42e76597207595bf9090c1abb2235e17\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6f92bf630b313d9edd5cf460f80848b\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f8106878d9866a87f22044fe26734e1\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ef6fd04bd282fdc10c7646c7ff33b9a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fab0d69a6d288ac5927aaaacddf4cf0e\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf9efd5a9ceecdf7b4e30d70705c7eec\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2ebbfecac339d2fb22f0cab107c00f8\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15a0119d21e18d0262d7aee33d2deb00\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f63f63cffcf8b92e9a5a44b8dbda204e\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.squareup.leakcanary:leakcanary-android-utils:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8111d444431b243361ab6ae0bb35123a\transformed\jetified-leakcanary-android-utils-2.12\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.curtains:curtains:1.2.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\598881f5b152fa590289297f5345556c\transformed\jetified-curtains-1.2.4\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:6:5-107
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:25:5-80
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:25:5-80
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:6:78-104
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:6:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:7:5-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:7:22-73
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:8:5-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:8:22-62
uses-feature#android.hardware.camera
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:14:5-16:36
	android:required
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:16:9-33
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:15:9-47
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:18:5-77:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:18:5-77:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\049b486e733e18fbf112fa9fc097a2f3\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\049b486e733e18fbf112fa9fc097a2f3\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5abfda4a05db92edc4ed0b608e413d64\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5abfda4a05db92edc4ed0b608e413d64\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [com.github.QuadFlask:colorpicker:0.0.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\060c694a3c40b635c3510f4af8faaaef\transformed\jetified-colorpicker-0.0.13\AndroidManifest.xml:12:5-20
MERGED from [com.github.QuadFlask:colorpicker:0.0.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\060c694a3c40b635c3510f4af8faaaef\transformed\jetified-colorpicker-0.0.13\AndroidManifest.xml:12:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\710e1e3cca06d39ef5f5ec834a7465b3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\710e1e3cca06d39ef5f5ec834a7465b3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:31:5-103:19
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:31:5-103:19
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa77027ba7d1354e3a853d05bfbf16aa\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:7:5-13:19
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa77027ba7d1354e3a853d05bfbf16aa\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:7:5-13:19
MERGED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f2ce96999776b68457ba832c6b7bd7d\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:7:5-13:19
MERGED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f2ce96999776b68457ba832c6b7bd7d\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:7:5-13:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77aa445f8b2a5d7f749de8848ad8ac15\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77aa445f8b2a5d7f749de8848ad8ac15\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ef6fd04bd282fdc10c7646c7ff33b9a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ef6fd04bd282fdc10c7646c7ff33b9a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:26:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:24:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:22:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:25:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:28:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:23:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:20:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:27:9-77
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:21:9-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:19:9-44
activity#com.wardrobe.app.ui.activities.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:30:9-37:20
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:32:13-36
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:31:13-55
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:33:13-36:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:34:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:34:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:35:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:35:27-74
activity#com.wardrobe.app.ui.activities.AddClothingActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:39:9-41:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:41:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:40:13-62
activity#com.wardrobe.app.ui.activities.CategorySelectActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:43:9-45:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:45:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:44:13-65
activity#com.wardrobe.app.ui.activities.ColorSelectActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:47:9-49:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:49:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:48:13-62
activity#com.wardrobe.app.ui.activities.OccasionSelectActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:51:9-53:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:53:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:52:13-65
activity#com.wardrobe.app.ui.activities.ClothingDetailActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:55:9-57:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:57:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:56:13-65
activity#com.wardrobe.app.ui.activities.BatchAddClothingActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:60:9-62:40
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:62:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:61:13-67
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:70:13-72:54
	android:resource
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:72:17-51
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:71:17-67
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59f532884cb1bb9abada84eb0f9b00e2\transformed\jetified-viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59f532884cb1bb9abada84eb0f9b00e2\transformed\jetified-viewbinding-8.10.1\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\811604d3863bca31626557ac8606c24b\transformed\jetified-leakcanary-android-2.12\AndroidManifest.xml:20:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\811604d3863bca31626557ac8606c24b\transformed\jetified-leakcanary-android-2.12\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\049b486e733e18fbf112fa9fc097a2f3\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\049b486e733e18fbf112fa9fc097a2f3\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5abfda4a05db92edc4ed0b608e413d64\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5abfda4a05db92edc4ed0b608e413d64\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.github.QuadFlask:colorpicker:0.0.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\060c694a3c40b635c3510f4af8faaaef\transformed\jetified-colorpicker-0.0.13\AndroidManifest.xml:8:5-10:41
MERGED from [com.github.QuadFlask:colorpicker:0.0.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\060c694a3c40b635c3510f4af8faaaef\transformed\jetified-colorpicker-0.0.13\AndroidManifest.xml:8:5-10:41
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cc26277969d13848258b84d5eb950c5\transformed\jetified-appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cc26277969d13848258b84d5eb950c5\transformed\jetified-appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46582a7ddf7236be9612eae4b0c579c2\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46582a7ddf7236be9612eae4b0c579c2\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c58295e18587b76701f1dbec03b2cd1\transformed\jetified-glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c58295e18587b76701f1dbec03b2cd1\transformed\jetified-glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0770c3d14f7037e1a981f24254e0647b\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0770c3d14f7037e1a981f24254e0647b\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15159b840b74bb7e9ff75002d0821b19\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15159b840b74bb7e9ff75002d0821b19\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\acd48b7f434b4eb81efcdaf911394de3\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\acd48b7f434b4eb81efcdaf911394de3\transformed\jetified-activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6d0f713e2480fe8d98d2ab7e949da05\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6d0f713e2480fe8d98d2ab7e949da05\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bedde39795cec62d12743f646549b8b\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5bedde39795cec62d12743f646549b8b\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff799407a27be64bd6bd7a0117022fed\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff799407a27be64bd6bd7a0117022fed\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\710e1e3cca06d39ef5f5ec834a7465b3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\710e1e3cca06d39ef5f5ec834a7465b3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8df4c2bf94180d7207138d5536e85da\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b8df4c2bf94180d7207138d5536e85da\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d7e02d66d20ba814e7a86cda25373ff\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9d7e02d66d20ba814e7a86cda25373ff\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2738e4de59b90dd5ec301d88d7b4eea5\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2738e4de59b90dd5ec301d88d7b4eea5\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\868f4bef48fb4fa6d23440a7ca010595\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\868f4bef48fb4fa6d23440a7ca010595\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7d96df336272224cf49dfdc1fd60c01\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7d96df336272224cf49dfdc1fd60c01\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2be63568309e885752d70988cb2094f4\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2be63568309e885752d70988cb2094f4\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c86f448aee69100fb27a2afe4cc1f338\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c86f448aee69100fb27a2afe4cc1f338\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a5b4fdc733b5799215cde553020e235\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a5b4fdc733b5799215cde553020e235\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd5b3d03bd792874e7d136f41b830067\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd5b3d03bd792874e7d136f41b830067\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da93eb5054b045613f979ad4c1403584\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da93eb5054b045613f979ad4c1403584\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bd5d6f216104422faecea35d341ab1\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0bd5d6f216104422faecea35d341ab1\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c732ef48c045c8037252a29830c59e3a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c732ef48c045c8037252a29830c59e3a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\599dc6df7e778fecad2c701329a7122d\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\599dc6df7e778fecad2c701329a7122d\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e11ab1df40cc8c1dc45e7fa6610dab3c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e11ab1df40cc8c1dc45e7fa6610dab3c\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0dfa1844179c565bce16a38d14dc0659\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0dfa1844179c565bce16a38d14dc0659\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e17fd54cd2ffce6770c6ef72276489f6\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e17fd54cd2ffce6770c6ef72276489f6\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18eaf398f47abd175ed50b451879129c\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18eaf398f47abd175ed50b451879129c\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e929b83b04a689b4469e7f143f7af245\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e929b83b04a689b4469e7f143f7af245\transformed\jetified-lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e36bb9c1540ae359e4abe81c5ffd3113\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e36bb9c1540ae359e4abe81c5ffd3113\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8da990c00b16836307bce0075693bd1b\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8da990c00b16836307bce0075693bd1b\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:20:5-22:41
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:20:5-22:41
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa77027ba7d1354e3a853d05bfbf16aa\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa77027ba7d1354e3a853d05bfbf16aa\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f2ce96999776b68457ba832c6b7bd7d\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f2ce96999776b68457ba832c6b7bd7d\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ebf01a427a45f3010f5406c94836ff0\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ebf01a427a45f3010f5406c94836ff0\transformed\jetified-core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7999dfaa6b5c8bef32e652b3e8668c96\transformed\jetified-leakcanary-object-watcher-android-androidx-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7999dfaa6b5c8bef32e652b3e8668c96\transformed\jetified-leakcanary-object-watcher-android-androidx-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6c1e26d38d4cade94ca06677f85bf493\transformed\jetified-leakcanary-object-watcher-android-support-fragments-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6c1e26d38d4cade94ca06677f85bf493\transformed\jetified-leakcanary-object-watcher-android-support-fragments-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1eef3773d27e06a52ddb78a86a31d57f\transformed\jetified-leakcanary-object-watcher-android-core-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1eef3773d27e06a52ddb78a86a31d57f\transformed\jetified-leakcanary-object-watcher-android-core-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\********************************\transformed\jetified-plumber-android-core-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\********************************\transformed\jetified-plumber-android-core-2.12\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08b2e06f7bc5c421014b4de9b903740f\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08b2e06f7bc5c421014b4de9b903740f\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e3f341b35f0be97db4c2fc8795e40a\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e3f341b35f0be97db4c2fc8795e40a\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4dc66f361fbe9b9a6d5762dd801aad82\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4dc66f361fbe9b9a6d5762dd801aad82\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7b9ff5a45e5f995a2b3c56b92b7cb6\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ab7b9ff5a45e5f995a2b3c56b92b7cb6\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77aa445f8b2a5d7f749de8848ad8ac15\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77aa445f8b2a5d7f749de8848ad8ac15\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42e76597207595bf9090c1abb2235e17\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\42e76597207595bf9090c1abb2235e17\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6f92bf630b313d9edd5cf460f80848b\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b6f92bf630b313d9edd5cf460f80848b\transformed\jetified-gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f8106878d9866a87f22044fe26734e1\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f8106878d9866a87f22044fe26734e1\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ef6fd04bd282fdc10c7646c7ff33b9a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7ef6fd04bd282fdc10c7646c7ff33b9a\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fab0d69a6d288ac5927aaaacddf4cf0e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fab0d69a6d288ac5927aaaacddf4cf0e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf9efd5a9ceecdf7b4e30d70705c7eec\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bf9efd5a9ceecdf7b4e30d70705c7eec\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2ebbfecac339d2fb22f0cab107c00f8\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b2ebbfecac339d2fb22f0cab107c00f8\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15a0119d21e18d0262d7aee33d2deb00\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15a0119d21e18d0262d7aee33d2deb00\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f63f63cffcf8b92e9a5a44b8dbda204e\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f63f63cffcf8b92e9a5a44b8dbda204e\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android-utils:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8111d444431b243361ab6ae0bb35123a\transformed\jetified-leakcanary-android-utils-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android-utils:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8111d444431b243361ab6ae0bb35123a\transformed\jetified-leakcanary-android-utils-2.12\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.curtains:curtains:1.2.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\598881f5b152fa590289297f5345556c\transformed\jetified-curtains-1.2.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.squareup.curtains:curtains:1.2.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\598881f5b152fa590289297f5345556c\transformed\jetified-curtains-1.2.4\AndroidManifest.xml:5:5-7:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\710e1e3cca06d39ef5f5ec834a7465b3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\710e1e3cca06d39ef5f5ec834a7465b3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77aa445f8b2a5d7f749de8848ad8ac15\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77aa445f8b2a5d7f749de8848ad8ac15\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\710e1e3cca06d39ef5f5ec834a7465b3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\710e1e3cca06d39ef5f5ec834a7465b3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\710e1e3cca06d39ef5f5ec834a7465b3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.wardrobe.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.wardrobe.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:26:5-81
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:26:22-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:29:5-77
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:29:22-74
provider#leakcanary.internal.LeakCanaryFileProvider
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:32:9-40:20
	android:grantUriPermissions
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:36:13-47
	android:authorities
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:34:13-88
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:35:13-37
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:33:13-70
activity#leakcanary.internal.activity.LeakActivity
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:42:9-73:20
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:46:13-71
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:44:13-36
	android:icon
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:45:13-52
	android:theme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:48:13-63
	android:taskAffinity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:47:13-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:43:13-69
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:*+data:mimeType:*/*+data:pathPattern:.*\\..*\\..*\\..*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\.hprof+data:pathPattern:.*\\.hprof+data:scheme:content+data:scheme:file
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:49:13-72:29
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:49:28-81
action#android.intent.action.VIEW
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:50:17-69
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:50:25-66
category#android.intent.category.DEFAULT
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:52:17-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:52:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:53:17-78
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:53:27-75
data
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
	android:scheme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:23-44
activity-alias#leakcanary.internal.activity.LeakLauncherActivity
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:75:9-92:26
	android:enabled
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:78:13-66
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:81:13-71
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:79:13-36
	android:targetActivity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:82:13-79
	android:icon
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:80:13-52
	android:banner
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:77:13-59
	android:theme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:84:13-63
	android:taskAffinity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:83:13-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:76:13-77
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:85:13-91:29
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:90:17-86
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:90:27-83
activity#leakcanary.internal.RequestPermissionActivity
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:94:9-100:68
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:98:13-82
	android:excludeFromRecents
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:96:13-46
	android:icon
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:97:13-52
	android:theme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:100:13-65
	android:taskAffinity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:99:13-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:95:13-73
receiver#leakcanary.internal.NotificationReceiver
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:102:9-77
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:102:19-74
provider#leakcanary.internal.MainProcessAppWatcherInstaller
ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa77027ba7d1354e3a853d05bfbf16aa\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:8:9-12:40
	android:enabled
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa77027ba7d1354e3a853d05bfbf16aa\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:11:13-69
	android:authorities
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa77027ba7d1354e3a853d05bfbf16aa\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:10:13-72
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa77027ba7d1354e3a853d05bfbf16aa\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa77027ba7d1354e3a853d05bfbf16aa\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:9:13-78
provider#leakcanary.internal.PlumberInstaller
ADDED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f2ce96999776b68457ba832c6b7bd7d\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:8:9-12:40
	android:enabled
		ADDED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f2ce96999776b68457ba832c6b7bd7d\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:11:13-69
	android:authorities
		ADDED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f2ce96999776b68457ba832c6b7bd7d\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:10:13-69
	android:exported
		ADDED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f2ce96999776b68457ba832c6b7bd7d\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f2ce96999776b68457ba832c6b7bd7d\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:9:13-64
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
