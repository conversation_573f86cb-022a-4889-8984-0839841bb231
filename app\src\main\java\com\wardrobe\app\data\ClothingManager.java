package com.wardrobe.app.data;

import android.content.Context;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.wardrobe.app.model.ClothingItem;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

/**
 * 衣物管理器 - 重构版本
 * 移动到data包中，作为数据管理的核心类
 * 解决了内存泄漏问题，使用Application Context
 * 添加了线程安全机制
 */
public class ClothingManager {

    private static final String CLOTHING_LIST_KEY = "clothing_list";
    private final SharedPreferencesManager sharedPreferencesManager;
    private final Gson gson;
    private static volatile ClothingManager instance;
    private static final Object LOCK = new Object();

    private ClothingManager(SharedPreferencesManager sharedPreferencesManager) {
        this.sharedPreferencesManager = sharedPreferencesManager;
        this.gson = new Gson();
    }

    /**
     * 获取衣物列表（线程安全）
     * 
     * @return 衣物列表
     */
    public synchronized List<ClothingItem> getClothingList() {
        String json = sharedPreferencesManager.readString(CLOTHING_LIST_KEY, "[]");
        Type type = new TypeToken<ArrayList<ClothingItem>>() {}.getType();
        List<ClothingItem> items = gson.fromJson(json, type);
        return items != null ? items : new ArrayList<>();
    }

    /**
     * 保存衣物列表（线程安全）
     * 
     * @param items 要保存的衣物列表
     */
    private synchronized void saveClothingList(List<ClothingItem> items) {
        String json = gson.toJson(items);
        sharedPreferencesManager.writeString(CLOTHING_LIST_KEY, json);
    }

    /**
     * 添加衣物项目（线程安全）
     * 
     * @param item 要添加的衣物项目
     */
    public synchronized void addClothingItem(ClothingItem item) {
        List<ClothingItem> items = getClothingList();
        items.add(0, item); // Add to the top of the list
        saveClothingList(items);
    }

    /**
     * 更新衣物项目（线程安全）
     * 
     * @param itemToUpdate 要更新的衣物项目
     */
    public synchronized void updateClothingItem(ClothingItem itemToUpdate) {
        List<ClothingItem> items = getClothingList();
        for (int i = 0; i < items.size(); i++) {
            if (items.get(i).getId().equals(itemToUpdate.getId())) {
                items.set(i, itemToUpdate);
                saveClothingList(items);
                return;
            }
        }
    }

    /**
     * 根据ID获取衣物项目
     * 
     * @param id 衣物ID
     * @return 衣物项目，如果不存在则返回null
     */
    public ClothingItem getClothingItemById(String id) {
        List<ClothingItem> items = getClothingList();
        for (ClothingItem item : items) {
            if (item.getId().equals(id)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 获取ClothingManager单例实例
     * 使用双重检查锁定模式确保线程安全
     * 使用Application Context避免内存泄漏
     * 
     * @param context 上下文，会自动转换为Application Context
     * @return ClothingManager实例
     */
    public static ClothingManager getInstance(Context context) {
        if (instance == null) {
            synchronized (LOCK) {
                if (instance == null) {
                    // 使用Application Context避免内存泄漏
                    Context appContext = context.getApplicationContext();
                    instance = new ClothingManager(new SharedPreferencesManager(appContext));
                }
            }
        }
        return instance;
    }

    /**
     * 获取ClothingManager单例实例（Application版本）
     * 
     * @param application Application实例
     * @return ClothingManager实例
     */
    public static ClothingManager getInstance(android.app.Application application) {
        return getInstance((Context) application);
    }

    /**
     * 批量添加衣物项目（线程安全）
     * 
     * @param itemsToAdd 要添加的衣物列表
     */
    public synchronized void addMultipleItems(List<ClothingItem> itemsToAdd) {
        List<ClothingItem> items = getClothingList();
        items.addAll(0, itemsToAdd);
        saveClothingList(items);
    }

    /**
     * 获取所有衣物项目
     * 
     * @return 所有衣物列表
     */
    public List<ClothingItem> getAllClothingItems() {
        return getClothingList();
    }

    /**
     * 删除衣物项目（线程安全）
     * 
     * @param item 要删除的衣物项目
     */
    public synchronized void deleteClothingItem(ClothingItem item) {
        List<ClothingItem> items = getClothingList();
        items.removeIf(i -> i.getId().equals(item.getId()));
        saveClothingList(items);
    }

    /**
     * 搜索衣物项目
     * 
     * @param query 搜索关键词
     * @return 匹配的衣物列表
     */
    public List<ClothingItem> searchClothingItems(String query) {
        List<ClothingItem> items = getClothingList();
        List<ClothingItem> result = new ArrayList<>();
        for (ClothingItem item : items) {
            if (item.getName() != null && item.getName().contains(query)) {
                result.add(item);
            }
        }
        return result;
    }

    /**
     * 根据分类获取衣物列表
     * 
     * @param category 分类名称
     * @return 该分类下的衣物列表
     */
    public List<ClothingItem> getClothingItemsByCategory(String category) {
        List<ClothingItem> items = getClothingList();
        List<ClothingItem> result = new ArrayList<>();
        for (ClothingItem item : items) {
            if (item.getCategory() != null && item.getCategory().equals(category)) {
                result.add(item);
            }
        }
        return result;
    }

    /**
     * 根据主分类获取衣物列表（兼容性方法）
     * 
     * @param mainCategory 主分类名称
     * @return 该分类下的衣物列表
     */
    public List<ClothingItem> getClothingByMainCategory(String mainCategory) {
        return getClothingItemsByCategory(mainCategory);
    }

    /**
     * 清除所有数据（线程安全）
     */
    public synchronized void clearAllData() {
        sharedPreferencesManager.clear();
    }

    /**
     * 获取衣物总数
     * 
     * @return 衣物总数
     */
    public int getTotalClothingCount() {
        return getClothingList().size();
    }

    /**
     * 根据分类获取衣物数量
     * 
     * @param category 分类名称
     * @return 该分类下的衣物数量
     */
    public int getClothingCountByCategory(String category) {
        return getClothingItemsByCategory(category).size();
    }
}
