package com.wardrobe.app.data;

import android.content.Context;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.wardrobe.app.model.ClothingItem;
import com.wardrobe.app.utils.AsyncTaskManager;
import com.wardrobe.app.utils.Logger;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;

/**
 * 衣物管理器 - 重构版本
 * 移动到data包中，作为数据管理的核心类
 * 解决了内存泄漏问题，使用Application Context
 * 添加了线程安全机制
 *
 * @deprecated 此类已废弃，请使用 {@link com.wardrobe.app.repository.ClothingRepository} 替代
 * 将在下个版本中移除
 */
@Deprecated
public class ClothingManager {

    private static final String TAG = "ClothingManager";
    private static final String CLOTHING_LIST_KEY = "clothing_list";
    private final SharedPreferencesManager sharedPreferencesManager;
    private final Gson gson;
    private final AsyncTaskManager asyncTaskManager;
    private static volatile ClothingManager instance;
    private static final Object LOCK = new Object();

    private ClothingManager(SharedPreferencesManager sharedPreferencesManager) {
        this.sharedPreferencesManager = sharedPreferencesManager;
        this.gson = new Gson();
        this.asyncTaskManager = AsyncTaskManager.getInstance();
    }

    /**
     * 获取衣物列表（线程安全）
     * 
     * @return 衣物列表
     */
    public synchronized List<ClothingItem> getClothingList() {
        try {
            String json = sharedPreferencesManager.readString(CLOTHING_LIST_KEY, "[]");

            // 数据完整性检查
            if (json == null || json.trim().isEmpty()) {
                Logger.w(TAG, "衣物数据为空，返回空列表");
                return new ArrayList<>();
            }

            Type type = new TypeToken<ArrayList<ClothingItem>>() {}.getType();
            List<ClothingItem> items = gson.fromJson(json, type);

            if (items == null) {
                Logger.w(TAG, "JSON反序列化返回null，返回空列表");
                return new ArrayList<>();
            }

            // 数据验证和清理
            List<ClothingItem> validItems = new ArrayList<>();
            for (ClothingItem item : items) {
                if (isValidClothingItem(item)) {
                    validItems.add(item);
                } else {
                    Logger.w(TAG, "发现无效衣物数据，已跳过: " + (item != null ? item.getId() : "null"));
                }
            }

            // 如果有无效数据被清理，重新保存
            if (validItems.size() != items.size()) {
                Logger.i(TAG, "清理了 " + (items.size() - validItems.size()) + " 条无效数据");
                saveClothingList(validItems);
            }

            return validItems;

        } catch (Exception e) {
            Logger.e(TAG, "加载衣物列表失败，尝试恢复", e);
            return recoverClothingData();
        }
    }

    /**
     * 保存衣物列表（线程安全）
     * 
     * @param items 要保存的衣物列表
     */
    private synchronized void saveClothingList(List<ClothingItem> items) {
        String json = gson.toJson(items);
        sharedPreferencesManager.writeString(CLOTHING_LIST_KEY, json);
    }

    /**
     * 添加衣物项目（线程安全）
     * 
     * @param item 要添加的衣物项目
     */
    public synchronized void addClothingItem(ClothingItem item) {
        List<ClothingItem> items = getClothingList();
        items.add(0, item); // Add to the top of the list
        saveClothingList(items);
    }

    /**
     * 更新衣物项目（线程安全）
     * 
     * @param itemToUpdate 要更新的衣物项目
     */
    public synchronized void updateClothingItem(ClothingItem itemToUpdate) {
        List<ClothingItem> items = getClothingList();
        for (int i = 0; i < items.size(); i++) {
            if (items.get(i).getId().equals(itemToUpdate.getId())) {
                items.set(i, itemToUpdate);
                saveClothingList(items);
                return;
            }
        }
    }

    /**
     * 根据ID获取衣物项目
     * 
     * @param id 衣物ID
     * @return 衣物项目，如果不存在则返回null
     */
    public ClothingItem getClothingItemById(String id) {
        List<ClothingItem> items = getClothingList();
        for (ClothingItem item : items) {
            if (item.getId().equals(id)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 获取ClothingManager单例实例
     * 使用双重检查锁定模式确保线程安全
     * 使用Application Context避免内存泄漏
     * 
     * @param context 上下文，会自动转换为Application Context
     * @return ClothingManager实例
     */
    public static ClothingManager getInstance(Context context) {
        if (instance == null) {
            synchronized (LOCK) {
                if (instance == null) {
                    // 使用Application Context避免内存泄漏
                    Context appContext = context.getApplicationContext();
                    instance = new ClothingManager(new SharedPreferencesManager(appContext));
                }
            }
        }
        return instance;
    }

    /**
     * 获取ClothingManager单例实例（Application版本）
     * 
     * @param application Application实例
     * @return ClothingManager实例
     */
    public static ClothingManager getInstance(android.app.Application application) {
        return getInstance((Context) application);
    }

    /**
     * 批量添加衣物项目（线程安全）
     * 
     * @param itemsToAdd 要添加的衣物列表
     */
    public synchronized void addMultipleItems(List<ClothingItem> itemsToAdd) {
        List<ClothingItem> items = getClothingList();
        items.addAll(0, itemsToAdd);
        saveClothingList(items);
    }

    /**
     * 获取所有衣物项目
     * 
     * @return 所有衣物列表
     */
    public List<ClothingItem> getAllClothingItems() {
        return getClothingList();
    }

    /**
     * 删除衣物项目（线程安全）
     * 
     * @param item 要删除的衣物项目
     */
    public synchronized void deleteClothingItem(ClothingItem item) {
        List<ClothingItem> items = getClothingList();
        items.removeIf(i -> i.getId().equals(item.getId()));
        saveClothingList(items);
    }

    /**
     * 搜索衣物项目
     * 
     * @param query 搜索关键词
     * @return 匹配的衣物列表
     */
    public List<ClothingItem> searchClothingItems(String query) {
        List<ClothingItem> items = getClothingList();
        List<ClothingItem> result = new ArrayList<>();
        for (ClothingItem item : items) {
            if (item.getName() != null && item.getName().contains(query)) {
                result.add(item);
            }
        }
        return result;
    }

    /**
     * 根据分类获取衣物列表
     * 
     * @param category 分类名称
     * @return 该分类下的衣物列表
     */
    public List<ClothingItem> getClothingItemsByCategory(String category) {
        List<ClothingItem> items = getClothingList();
        List<ClothingItem> result = new ArrayList<>();
        for (ClothingItem item : items) {
            if (item.getCategory() != null && item.getCategory().equals(category)) {
                result.add(item);
            }
        }
        return result;
    }

    /**
     * 根据主分类获取衣物列表（兼容性方法）
     * 
     * @param mainCategory 主分类名称
     * @return 该分类下的衣物列表
     */
    public List<ClothingItem> getClothingByMainCategory(String mainCategory) {
        return getClothingItemsByCategory(mainCategory);
    }

    /**
     * 清除所有数据（线程安全）
     */
    public synchronized void clearAllData() {
        sharedPreferencesManager.clear();
    }

    /**
     * 获取衣物总数
     * 
     * @return 衣物总数
     */
    public int getTotalClothingCount() {
        return getClothingList().size();
    }

    /**
     * 根据分类获取衣物数量
     *
     * @param category 分类名称
     * @return 该分类下的衣物数量
     */
    public int getClothingCountByCategory(String category) {
        return getClothingItemsByCategory(category).size();
    }

    // ==================== 异步方法 ====================

    /**
     * 异步获取所有衣物列表
     *
     * @param callback 回调接口
     * @return Future对象
     */
    public Future<?> getAllClothingItemsAsync(AsyncTaskManager.TaskCallback<List<ClothingItem>> callback) {
        return asyncTaskManager.executeIO(() -> getAllClothingItems(), callback);
    }

    /**
     * 异步添加衣物项目
     *
     * @param item 衣物项目
     * @param callback 回调接口
     * @return Future对象
     */
    public Future<?> addClothingItemAsync(ClothingItem item, AsyncTaskManager.VoidTaskCallback callback) {
        return asyncTaskManager.executeVoidIO(() -> addClothingItem(item), callback);
    }

    /**
     * 异步更新衣物项目
     *
     * @param item 衣物项目
     * @param callback 回调接口
     * @return Future对象
     */
    public Future<?> updateClothingItemAsync(ClothingItem item, AsyncTaskManager.VoidTaskCallback callback) {
        return asyncTaskManager.executeVoidIO(() -> updateClothingItem(item), callback);
    }

    /**
     * 异步删除衣物项目
     *
     * @param item 衣物项目
     * @param callback 回调接口
     * @return Future对象
     */
    public Future<?> deleteClothingItemAsync(ClothingItem item, AsyncTaskManager.VoidTaskCallback callback) {
        return asyncTaskManager.executeVoidIO(() -> deleteClothingItem(item), callback);
    }

    /**
     * 异步搜索衣物项目
     *
     * @param query 搜索关键词
     * @param callback 回调接口
     * @return Future对象
     */
    public Future<?> searchClothingItemsAsync(String query, AsyncTaskManager.TaskCallback<List<ClothingItem>> callback) {
        return asyncTaskManager.executeIO(() -> searchClothingItems(query), callback);
    }

    /**
     * 异步根据分类获取衣物列表
     *
     * @param category 分类名称
     * @param callback 回调接口
     * @return Future对象
     */
    public Future<?> getClothingItemsByCategoryAsync(String category, AsyncTaskManager.TaskCallback<List<ClothingItem>> callback) {
        return asyncTaskManager.executeIO(() -> getClothingItemsByCategory(category), callback);
    }

    /**
     * 异步批量添加衣物项目
     *
     * @param items 衣物列表
     * @param callback 回调接口
     * @return Future对象
     */
    public Future<?> addMultipleItemsAsync(List<ClothingItem> items, AsyncTaskManager.VoidTaskCallback callback) {
        return asyncTaskManager.executeVoidIO(() -> addMultipleItems(items), callback);
    }

    // ==================== 数据完整性和恢复方法 ====================

    /**
     * 验证衣物项目数据完整性
     *
     * @param item 衣物项目
     * @return 是否有效
     */
    private boolean isValidClothingItem(ClothingItem item) {
        if (item == null) {
            return false;
        }

        // 检查必需字段
        if (item.getId() == null || item.getId().trim().isEmpty()) {
            return false;
        }

        if (item.getName() == null || item.getName().trim().isEmpty()) {
            return false;
        }

        if (item.getCategory() == null || item.getCategory().trim().isEmpty()) {
            return false;
        }

        // 检查时间戳合理性
        if (item.getTimestamp() < 0 || item.getTimestamp() > System.currentTimeMillis()) {
            return false;
        }

        return true;
    }

    /**
     * 数据恢复机制
     *
     * @return 恢复的衣物列表
     */
    private List<ClothingItem> recoverClothingData() {
        Logger.w(TAG, "尝试数据恢复");

        try {
            // 尝试从备份恢复
            String backupJson = sharedPreferencesManager.readString(CLOTHING_LIST_KEY + "_backup", null);
            if (backupJson != null && !backupJson.trim().isEmpty()) {
                Type type = new TypeToken<ArrayList<ClothingItem>>() {}.getType();
                List<ClothingItem> backupItems = gson.fromJson(backupJson, type);
                if (backupItems != null && !backupItems.isEmpty()) {
                    Logger.i(TAG, "从备份恢复了 " + backupItems.size() + " 条数据");
                    return backupItems;
                }
            }
        } catch (Exception e) {
            Logger.e(TAG, "备份恢复失败", e);
        }

        // 清除损坏的数据
        sharedPreferencesManager.writeString(CLOTHING_LIST_KEY, "[]");
        Logger.w(TAG, "数据恢复失败，已重置为空列表");
        return new ArrayList<>();
    }
}
