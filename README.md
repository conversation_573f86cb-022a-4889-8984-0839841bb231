# WardrobeApp - 智能衣柜管理应用

## 项目简介

WardrobeApp 是一款专为个人衣物管理设计的 Android 应用，帮助用户高效管理衣橱、记录穿着搭配、追踪衣物使用情况。应用采用现代化的 MVVM 架构，提供直观的用户界面和强大的功能特性。

## 主要功能

### 🎯 核心功能
- **衣物管理**: 添加、编辑、删除衣物信息
- **分类系统**: 支持主分类和子分类的衣物组织
- **图片管理**: 拍摄或选择衣物照片
- **颜色标记**: 为衣物添加颜色标签
- **场合分类**: 按穿着场合分类管理

### 📊 数据统计
- **分类统计**: 各类型衣物数量统计
- **使用记录**: 衣物穿着频率追踪
- **搭配历史**: 记录搭配组合

### 🎨 用户体验
- **直观界面**: Material Design 3 设计语言
- **流畅动画**: 优化的交互动画效果
- **响应式设计**: 适配不同屏幕尺寸
- **无障碍支持**: 支持屏幕阅读器

## 技术架构

### 架构模式
- **MVVM (Model-View-ViewModel)**: 清晰的分层架构
- **Repository Pattern**: 数据访问抽象层
- **LiveData**: 响应式数据绑定
- **ViewBinding**: 类型安全的视图绑定

### 核心技术栈
- **开发语言**: Java 8+
- **最低支持**: Android API 24 (Android 7.0)
- **目标版本**: Android API 35 (Android 15)
- **构建工具**: Gradle 8.0+
- **依赖管理**: Gradle Version Catalogs

### 数据存储
- **本地存储**: SharedPreferences (JSON格式)
- **图片存储**: 应用私有目录
- **缓存策略**: 内存缓存 + 磁盘缓存

## 项目结构

```
app/
├── src/main/
│   ├── java/com/wardrobe/app/
│   │   ├── data/                    # 数据层
│   │   │   ├── ClothingDataSource.java
│   │   │   └── SharedPreferencesClothingDataSource.java
│   │   ├── model/                   # 数据模型
│   │   │   ├── ClothingItem.java
│   │   │   └── CategoryItem.java
│   │   ├── repository/              # 仓库层
│   │   │   └── ClothingRepository.java
│   │   ├── ui/                      # 用户界面
│   │   │   ├── activities/          # Activity
│   │   │   ├── fragments/           # Fragment
│   │   │   ├── adapters/            # 适配器
│   │   │   ├── components/          # 自定义组件
│   │   │   └── theme/               # 主题样式
│   │   ├── utils/                   # 工具类
│   │   │   ├── SecurityUtils.java
│   │   │   ├── UIUtils.java
│   │   │   └── ImagePickerHelper.java
│   │   ├── viewmodel/               # ViewModel
│   │   │   └── ClothingViewModel.java
│   │   └── ClothingManager.java     # 核心管理器
│   └── res/                         # 资源文件
│       ├── layout/                  # 布局文件
│       ├── drawable/                # 图标资源
│       ├── values/                  # 字符串、颜色、主题
│       └── xml/                     # 配置文件
└── src/test/                        # 单元测试
```

## 快速开始

### 环境要求
- Android Studio Hedgehog | 2023.1.1 或更高版本
- JDK 21
- Android SDK API 24+
- Gradle 8.0+

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/WardrobeApp.git
   cd WardrobeApp
   ```

2. **打开项目**
   - 使用 Android Studio 打开项目
   - 等待 Gradle 同步完成

3. **配置设备**
   - 连接 Android 设备或启动模拟器
   - 确保设备 API 级别 >= 24

4. **运行应用**
   ```bash
   ./gradlew assembleDebug
   ```
   或在 Android Studio 中点击 "Run" 按钮

### 构建变体

```bash
# Debug 版本
./gradlew assembleDebug

# Release 版本
./gradlew assembleRelease

# 运行测试
./gradlew test

# 运行集成测试
./gradlew connectedAndroidTest
```

## 开发指南

### 代码规范

#### 命名规范
- **类名**: PascalCase (如: `ClothingItem`)
- **方法名**: camelCase (如: `getClothingList()`)
- **常量**: UPPER_SNAKE_CASE (如: `MAX_CLOTHING_ITEMS`)
- **包名**: 全小写 (如: `com.wardrobe.app`)

#### 代码格式
- **缩进**: 4个空格
- **行宽**: 120字符
- **空行**: 方法间使用空行分隔
- **注释**: 中文注释，关键逻辑添加行内注释

#### 架构原则
- **单一职责**: 每个类只负责一个功能
- **依赖倒置**: 依赖抽象而非具体实现
- **开闭原则**: 对扩展开放，对修改封闭
- **接口隔离**: 使用小接口而非大接口

### 添加新功能

1. **创建数据模型** (如需要)
   ```java
   public class NewModel {
       // 属性和方法
   }
   ```

2. **实现数据访问层**
   ```java
   public interface NewDataSource {
       // 数据操作方法
   }
   ```

3. **创建仓库类**
   ```java
   public class NewRepository {
       // 业务逻辑
   }
   ```

4. **实现 ViewModel**
   ```java
   public class NewViewModel extends ViewModel {
       // UI 逻辑
   }
   ```

5. **创建 UI 组件**
   ```java
   public class NewActivity extends AppCompatActivity {
       // 用户界面
   }
   ```

### 测试指南

#### 单元测试
- 测试业务逻辑和数据操作
- 使用 Mockito 进行依赖模拟
- 测试覆盖率目标 > 80%

#### 集成测试
- 测试 Activity 和 Fragment
- 验证用户交互流程
- 测试数据持久化

#### 性能测试
- 内存使用监控
- 启动时间测试
- 图片加载性能

### 安全考虑

#### 数据安全
- 输入验证和清理
- 文件路径安全检查
- 敏感数据加密存储

#### 权限管理
- 最小权限原则
- 运行时权限处理
- 权限使用说明

## 性能优化

### 内存优化
- 使用 ViewBinding 避免内存泄漏
- 图片加载优化和缓存
- Fragment 生命周期管理

### 启动优化
- 延迟初始化非关键组件
- 异步数据加载
- 启动画面优化

### UI 性能
- RecyclerView 优化
- 动画性能优化
- 布局层次优化

## 性能与内存监控

- **LeakCanary内存泄漏监控**：
  - 已在`debug`构建类型下集成LeakCanary（`com.squareup.leakcanary:leakcanary-android:2.14.1`）。
  - 开发/调试时自动检测内存泄漏，出现泄漏会弹窗提示并生成详细报告。
  - [LeakCanary官方文档](https://square.github.io/leakcanary/)：可参考如何分析和修复内存泄漏。

- **性能分析建议**：
  - 推荐使用Android Studio Profiler定期分析大图片、批量操作等极端场景下的内存和卡顿。
  - 重点关注图片加载、批量压缩、页面切换等高风险点。

## 故障排除

### 常见问题

#### 编译错误
1. **Gradle 同步失败**
   - 检查网络连接
   - 清理项目: `./gradlew clean`
   - 重新同步 Gradle

2. **依赖冲突**
   - 检查 `build.gradle` 依赖版本
   - 使用 `./gradlew app:dependencies` 查看依赖树

#### 运行时错误
1. **权限问题**
   - 检查 AndroidManifest.xml 权限声明
   - 确保运行时权限已授予

2. **内存不足**
   - 检查图片大小和数量
   - 优化内存使用

### 调试技巧

#### 日志输出
```java
if (BuildConfig.DEBUG) {
    Log.d(TAG, "调试信息");
}
```

#### 性能分析
- 使用 Android Studio Profiler
- 监控内存使用和 CPU 使用率
- 分析启动时间和响应时间

## 贡献指南

### 提交规范
- **feat**: 新功能
- **fix**: 修复问题
- **docs**: 文档更新
- **style**: 代码格式调整
- **refactor**: 代码重构
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动

### 代码审查
- 所有代码变更需要代码审查
- 确保测试覆盖率
- 遵循代码规范

## 版本历史

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基础衣物管理功能
- MVVM 架构实现

### v1.1.0 (计划中)
- 性能优化
- 安全性增强
- UI/UX 改进

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- **项目维护者**: [Your Name]
- **邮箱**: [<EMAIL>]
- **项目地址**: [https://github.com/your-username/WardrobeApp]

## 致谢

感谢所有为这个项目做出贡献的开发者和用户。

---

**注意**: 这是一个持续开发的项目，文档会随着功能更新而更新。如有问题或建议，请通过 Issues 或 Pull Requests 参与项目改进。 