[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_occasion_date.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_occasion_date.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_layout_loading.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\layout_loading.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_item_wardrobe_group.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\item_wardrobe_group.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_dialog_category_selection.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\dialog_category_selection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_occasion_outdoor.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_occasion_outdoor.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ios_style_group_background_middle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ios_style_group_background_middle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_flared_skirt.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_flared_skirt.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_pajamas.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_pajamas.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_layout_category_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\layout_category_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_occasion_daily.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_occasion_daily.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\menu_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\menu\\menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_occasion_work.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_occasion_work.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_category_workwear.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_category_workwear.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\anim_fade_out.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\anim\\fade_out.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_jewelry.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_jewelry.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_wedding.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_wedding.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_check_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_check_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_functional.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_functional.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_occasion_casual.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_occasion_casual.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\xml_file_paths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\xml\\file_paths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\anim_scale_in.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\anim\\scale_in.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_clear.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_clear.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_selected_item_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\selected_item_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_vest.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_vest.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_category_underwear.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_category_underwear.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_button_background_destructive.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\button_background_destructive.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\anim_slide_in_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\anim\\slide_in_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_sport_shoes.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_sport_shoes.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_slit_skirt.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_slit_skirt.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_edit_text_background_ios.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\edit_text_background_ios.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_option_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\option_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_item_clothing_pro.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\item_clothing_pro.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_category_bottoms.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_category_bottoms.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_occasion_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_occasion_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\color_bottom_nav_color_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\color\\bottom_nav_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_dialog_photo_selection.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\dialog_photo_selection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_socks.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_socks.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_heels.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_heels.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_dialog_recyclerview.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\dialog_recyclerview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_occasion_party.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_occasion_party.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_dotted_border_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\dotted_border_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_suit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_suit.xml"}, {"merged": "com.wardrobe.app-debug-43:/layout_fragment_outfit.xml.flat", "source": "com.wardrobe.app-main-45:/layout/fragment_outfit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_button_background_prominent.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\button_background_prominent.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_activity_clothing_detail.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\activity_clothing_detail.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_activity_add_clothing.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\activity_add_clothing.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_layout_empty.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\layout_empty.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_sneakers.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_sneakers.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_replace_icon_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\replace_icon_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\anim_slide_out_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\anim\\slide_out_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ios_style_group_background_single.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ios_style_group_background_single.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_thermal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_thermal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_belt.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_belt.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_tracksuit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_tracksuit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_skirt_bottom.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_skirt_bottom.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_replace_photo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_replace_photo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_sport_pants.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_sport_pants.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_error.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_error.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_briefs.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_briefs.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_category_sets.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_category_sets.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_other_acc.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_other_acc.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_search_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\search_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_activity_category_select.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\activity_category_select.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_canvas_shoes.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_canvas_shoes.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_item_clothing_horizontal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\item_clothing_horizontal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_pencil_skirt.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_pencil_skirt.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_outfit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_outfit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_hat.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_hat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_occasion_travel.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_occasion_travel.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_item_category_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\item_category_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_category_shoes.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_category_shoes.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_shirt.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_shirt.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_category_suits.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_category_suits.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_add.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_add.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_category_tuxedo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_category_tuxedo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_camisole_dress.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_camisole_dress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\menu_bottom_navigation_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\menu\\bottom_navigation_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_chevron_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_chevron_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_dialog_photo_selection_ios.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\dialog_photo_selection_ios.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_activity_batch_add_clothing.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\activity_batch_add_clothing.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\anim_slide_out_left.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\anim\\slide_out_left.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_formal_dress.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_formal_dress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_calendar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_calendar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_sweater.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_sweater.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_straight_skirt.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_straight_skirt.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_boots.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_boots.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_pants.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_pants.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_clothing_placeholder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_clothing_placeholder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_layout_error.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\layout_error.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_coat.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_coat.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_occasion_sports.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_occasion_sports.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_empty_box.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_empty_box.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_bra.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_bra.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_sandals.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_sandals.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_activity_color_select.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\activity_color_select.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_formal_shoes.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_formal_shoes.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_polo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_polo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\menu_action_mode_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\menu\\action_mode_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_activity_occasion_select.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\activity_occasion_select.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_profile.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_tshirt.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_tshirt.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_dialog_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\dialog_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_jumpsuit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_jumpsuit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_wardrobe.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_wardrobe.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_dialog_image_fullscreen.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\dialog_image_fullscreen.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_multi_select_toolbar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\multi_select_toolbar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_spinner_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\spinner_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_mermaid_skirt.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_mermaid_skirt.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_outer_vest.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_outer_vest.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_item_occasion_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\item_occasion_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_camisole.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_camisole.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_category_dresses.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_category_dresses.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_pleated_skirt.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_pleated_skirt.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_fragment_wardrobe.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\fragment_wardrobe.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_item_outfit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\item_outfit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_category_accessories.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_category_accessories.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_color_circle_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\color_circle_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_dialog_rounded_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\dialog_rounded_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_item_color_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\item_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_category_outerwear.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_category_outerwear.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_rain_boots.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_rain_boots.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\anim_slide_up.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\anim\\slide_up.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_scarf.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_scarf.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_occasion_formal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_occasion_formal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_bag.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_bag.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_traditional.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_traditional.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ios_style_group_background_top.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ios_style_group_background_top.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_secondary_system_grouped_background_ripple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\secondary_system_grouped_background_ripple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_fragment_profile.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\fragment_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_list_item_selector_row.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\list_item_selector_row.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_item_calendar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\item_calendar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_dialog_color_selection.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\dialog_color_selection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_jeans.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_jeans.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_shorts.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_shorts.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_dress.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_dress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\anim_slide_in_left.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\anim\\slide_in_left.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_evening_dress.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_evening_dress.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_formal_pants.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_formal_pants.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_spinner_item_with_icon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\spinner_item_with_icon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_category_tops.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_category_tops.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\anim_fade_in.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\anim\\fade_in.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ios_style_group_background_bottom.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ios_style_group_background_bottom.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_hoodie.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_hoodie.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_skirt.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_skirt.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_spaghetti_dress.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_spaghetti_dress.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_fragment_calendar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\fragment_calendar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\drawable_ic_subcategory_jacket.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\drawable\\ic_subcategory_jacket.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_item_color_spinner.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\item_color_spinner.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-debug-43:\\layout_dialog_occasion_selection.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wardrobe.app-main-45:\\layout\\dialog_occasion_selection.xml"}]