@echo off
chcp 65001 >nul
echo ========================================
echo Fix Android Project Chinese Encoding
echo ========================================
echo.

echo [1/5] Setting environment variables...
set JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF-8
set GRADLE_OPTS=-Dfile.encoding=UTF-8 -Duser.language=zh -Duser.country=CN

echo [2/5] Cleaning build cache...
call gradlew clean

echo [3/5] Checking file encoding...
echo Checking Java source files...

echo [4/5] Rebuilding project...
call gradlew assembleDebug --info --stacktrace

echo [5/5] Verifying build results...
if exist "app\build\outputs\apk\debug\*.apk" (
    echo Build successful! APK generated
    echo You can install to phone to test Chinese display
) else (
    echo Build failed, please check error messages
)

echo.
echo ========================================
echo Encoding fix completed
echo ========================================
echo.
echo Tips:
echo 1. If still have encoding issues, restart Android Studio
echo 2. Make sure Android Studio is set to UTF-8 encoding
echo 3. Test Chinese display on phone
echo.
pause
