<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_clothing_horizontal" modulePackage="com.wardrobe.app" filePath="app\src\main\res\layout\item_clothing_horizontal.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_clothing_horizontal_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="66" endOffset="35"/></Target><Target id="@+id/iv_clothing" view="ImageView"><Expressions/><location startLine="20" startOffset="12" endLine="27" endOffset="51"/></Target><Target id="@+id/tv_name" view="TextView"><Expressions/><location startLine="30" startOffset="12" endLine="40" endOffset="42"/></Target><Target id="@+id/selection_overlay" view="View"><Expressions/><location startLine="45" startOffset="8" endLine="51" endOffset="39"/></Target><Target id="@+id/iv_selected_icon" view="ImageView"><Expressions/><location startLine="54" startOffset="8" endLine="62" endOffset="39"/></Target></Targets></Layout>