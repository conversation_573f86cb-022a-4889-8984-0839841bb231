// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemOutfitBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final Button btnSaveOutfit;

  @NonNull
  public final Button btnShareOutfit;

  @NonNull
  public final TextView tvOutfitDescription;

  @NonNull
  public final TextView tvOutfitName;

  private ItemOutfitBinding(@NonNull CardView rootView, @NonNull Button btnSaveOutfit,
      @NonNull Button btnShareOutfit, @NonNull TextView tvOutfitDescription,
      @NonNull TextView tvOutfitName) {
    this.rootView = rootView;
    this.btnSaveOutfit = btnSaveOutfit;
    this.btnShareOutfit = btnShareOutfit;
    this.tvOutfitDescription = tvOutfitDescription;
    this.tvOutfitName = tvOutfitName;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemOutfitBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemOutfitBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_outfit, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemOutfitBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_save_outfit;
      Button btnSaveOutfit = ViewBindings.findChildViewById(rootView, id);
      if (btnSaveOutfit == null) {
        break missingId;
      }

      id = R.id.btn_share_outfit;
      Button btnShareOutfit = ViewBindings.findChildViewById(rootView, id);
      if (btnShareOutfit == null) {
        break missingId;
      }

      id = R.id.tv_outfit_description;
      TextView tvOutfitDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvOutfitDescription == null) {
        break missingId;
      }

      id = R.id.tv_outfit_name;
      TextView tvOutfitName = ViewBindings.findChildViewById(rootView, id);
      if (tvOutfitName == null) {
        break missingId;
      }

      return new ItemOutfitBinding((CardView) rootView, btnSaveOutfit, btnShareOutfit,
          tvOutfitDescription, tvOutfitName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
