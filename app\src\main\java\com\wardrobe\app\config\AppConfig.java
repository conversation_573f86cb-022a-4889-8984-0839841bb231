package com.wardrobe.app.config;

import android.content.Context;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.wardrobe.app.data.SharedPreferencesManager;
import com.wardrobe.app.utils.Logger;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 应用配置管理器
 * 管理可配置的分类、颜色、尺寸等系统配置
 * 支持动态修改和持久化存储
 */
public class AppConfig {
    
    private static final String TAG = "AppConfig";
    private static final String CONFIG_KEY_PREFIX = "app_config_";
    
    // 配置键名
    private static final String KEY_CATEGORIES = CONFIG_KEY_PREFIX + "categories";
    private static final String KEY_COLORS = CONFIG_KEY_PREFIX + "colors";
    private static final String KEY_SIZES = CONFIG_KEY_PREFIX + "sizes";
    private static final String KEY_BRANDS = CONFIG_KEY_PREFIX + "brands";
    private static final String KEY_MATERIALS = CONFIG_KEY_PREFIX + "materials";
    private static final String KEY_SEASONS = CONFIG_KEY_PREFIX + "seasons";
    
    private static volatile AppConfig instance;
    private static final Object LOCK = new Object();
    
    private final SharedPreferencesManager prefsManager;
    private final Gson gson;
    
    // 缓存配置数据
    private Map<String, List<String>> configCache = new HashMap<>();
    
    private AppConfig(Context context) {
        this.prefsManager = new SharedPreferencesManager(context.getApplicationContext());
        this.gson = new Gson();
        initializeDefaultConfigs();
        Logger.d(TAG, "AppConfig 初始化完成");
    }
    
    /**
     * 获取AppConfig实例
     * 
     * @param context 上下文
     * @return AppConfig实例
     */
    public static AppConfig getInstance(Context context) {
        if (instance == null) {
            synchronized (LOCK) {
                if (instance == null) {
                    instance = new AppConfig(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * 初始化默认配置
     */
    private void initializeDefaultConfigs() {
        // 初始化默认分类
        if (!prefsManager.contains(KEY_CATEGORIES)) {
            List<String> defaultCategories = Arrays.asList(
                "上衣", "下装", "外套", "鞋子", "配饰", "内衣", "运动装", "正装", "休闲装"
            );
            setCategories(defaultCategories);
        }
        
        // 初始化默认颜色
        if (!prefsManager.contains(KEY_COLORS)) {
            List<String> defaultColors = Arrays.asList(
                "黑色", "白色", "灰色", "红色", "蓝色", "绿色", "黄色", "紫色", 
                "粉色", "橙色", "棕色", "米色", "卡其色", "海军蓝", "酒红色"
            );
            setColors(defaultColors);
        }
        
        // 初始化默认尺寸
        if (!prefsManager.contains(KEY_SIZES)) {
            List<String> defaultSizes = Arrays.asList(
                "XS", "S", "M", "L", "XL", "XXL", "XXXL", "均码"
            );
            setSizes(defaultSizes);
        }
        
        // 初始化默认品牌
        if (!prefsManager.contains(KEY_BRANDS)) {
            List<String> defaultBrands = Arrays.asList(
                "优衣库", "ZARA", "H&M", "Nike", "Adidas", "无品牌", "其他"
            );
            setBrands(defaultBrands);
        }
        
        // 初始化默认材质
        if (!prefsManager.contains(KEY_MATERIALS)) {
            List<String> defaultMaterials = Arrays.asList(
                "棉", "聚酯纤维", "羊毛", "丝绸", "亚麻", "牛仔布", "皮革", "尼龙", "其他"
            );
            setMaterials(defaultMaterials);
        }
        
        // 初始化默认季节
        if (!prefsManager.contains(KEY_SEASONS)) {
            List<String> defaultSeasons = Arrays.asList(
                "春季", "夏季", "秋季", "冬季", "四季通用"
            );
            setSeasons(defaultSeasons);
        }
        
        Logger.d(TAG, "默认配置初始化完成");
    }
    
    /**
     * 获取分类列表
     * 
     * @return 分类列表
     */
    public List<String> getCategories() {
        return getConfigList(KEY_CATEGORIES);
    }
    
    /**
     * 设置分类列表
     * 
     * @param categories 分类列表
     */
    public void setCategories(List<String> categories) {
        setConfigList(KEY_CATEGORIES, categories);
        Logger.d(TAG, "更新分类配置，数量: " + categories.size());
    }
    
    /**
     * 添加分类
     * 
     * @param category 新分类
     */
    public void addCategory(String category) {
        List<String> categories = new ArrayList<>(getCategories());
        if (!categories.contains(category)) {
            categories.add(category);
            setCategories(categories);
            Logger.d(TAG, "添加新分类: " + category);
        }
    }
    
    /**
     * 删除分类
     * 
     * @param category 要删除的分类
     */
    public void removeCategory(String category) {
        List<String> categories = new ArrayList<>(getCategories());
        if (categories.remove(category)) {
            setCategories(categories);
            Logger.d(TAG, "删除分类: " + category);
        }
    }
    
    /**
     * 获取颜色列表
     * 
     * @return 颜色列表
     */
    public List<String> getColors() {
        return getConfigList(KEY_COLORS);
    }
    
    /**
     * 设置颜色列表
     * 
     * @param colors 颜色列表
     */
    public void setColors(List<String> colors) {
        setConfigList(KEY_COLORS, colors);
        Logger.d(TAG, "更新颜色配置，数量: " + colors.size());
    }
    
    /**
     * 获取尺寸列表
     * 
     * @return 尺寸列表
     */
    public List<String> getSizes() {
        return getConfigList(KEY_SIZES);
    }
    
    /**
     * 设置尺寸列表
     * 
     * @param sizes 尺寸列表
     */
    public void setSizes(List<String> sizes) {
        setConfigList(KEY_SIZES, sizes);
        Logger.d(TAG, "更新尺寸配置，数量: " + sizes.size());
    }
    
    /**
     * 获取品牌列表
     * 
     * @return 品牌列表
     */
    public List<String> getBrands() {
        return getConfigList(KEY_BRANDS);
    }
    
    /**
     * 设置品牌列表
     * 
     * @param brands 品牌列表
     */
    public void setBrands(List<String> brands) {
        setConfigList(KEY_BRANDS, brands);
        Logger.d(TAG, "更新品牌配置，数量: " + brands.size());
    }
    
    /**
     * 获取材质列表
     * 
     * @return 材质列表
     */
    public List<String> getMaterials() {
        return getConfigList(KEY_MATERIALS);
    }
    
    /**
     * 设置材质列表
     * 
     * @param materials 材质列表
     */
    public void setMaterials(List<String> materials) {
        setConfigList(KEY_MATERIALS, materials);
        Logger.d(TAG, "更新材质配置，数量: " + materials.size());
    }
    
    /**
     * 获取季节列表
     * 
     * @return 季节列表
     */
    public List<String> getSeasons() {
        return getConfigList(KEY_SEASONS);
    }
    
    /**
     * 设置季节列表
     * 
     * @param seasons 季节列表
     */
    public void setSeasons(List<String> seasons) {
        setConfigList(KEY_SEASONS, seasons);
        Logger.d(TAG, "更新季节配置，数量: " + seasons.size());
    }
    
    /**
     * 获取配置列表的通用方法
     * 
     * @param key 配置键
     * @return 配置列表
     */
    private List<String> getConfigList(String key) {
        // 先从缓存中获取
        if (configCache.containsKey(key)) {
            return new ArrayList<>(configCache.get(key));
        }
        
        // 从存储中读取
        String json = prefsManager.readString(key, "[]");
        Type type = new TypeToken<ArrayList<String>>() {}.getType();
        List<String> list = gson.fromJson(json, type);
        
        if (list == null) {
            list = new ArrayList<>();
        }
        
        // 更新缓存
        configCache.put(key, new ArrayList<>(list));
        
        return list;
    }
    
    /**
     * 设置配置列表的通用方法
     * 
     * @param key 配置键
     * @param list 配置列表
     */
    private void setConfigList(String key, List<String> list) {
        String json = gson.toJson(list);
        prefsManager.writeString(key, json);
        
        // 更新缓存
        configCache.put(key, new ArrayList<>(list));
    }
    
    /**
     * 清除所有配置缓存
     */
    public void clearCache() {
        configCache.clear();
        Logger.d(TAG, "配置缓存已清除");
    }
    
    /**
     * 重置所有配置为默认值
     */
    public void resetToDefaults() {
        // 清除所有配置
        prefsManager.remove(KEY_CATEGORIES);
        prefsManager.remove(KEY_COLORS);
        prefsManager.remove(KEY_SIZES);
        prefsManager.remove(KEY_BRANDS);
        prefsManager.remove(KEY_MATERIALS);
        prefsManager.remove(KEY_SEASONS);
        
        // 清除缓存
        clearCache();
        
        // 重新初始化默认配置
        initializeDefaultConfigs();
        
        Logger.d(TAG, "配置已重置为默认值");
    }
}
