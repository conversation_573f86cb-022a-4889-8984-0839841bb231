// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.GridView;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.textfield.TextInputEditText;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityBatchAddClothingBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LayoutCategorySelectorBinding batchCategorySelectorView;

  @NonNull
  public final Button btnSaveBatch;

  @NonNull
  public final Button btnSelectImagesBatch;

  @NonNull
  public final TextInputEditText editNotesBatch;

  @NonNull
  public final TextInputEditText editStoryBatch;

  @NonNull
  public final TextInputEditText editTagsBatch;

  @NonNull
  public final GridView gridViewSelectedImages;

  private ActivityBatchAddClothingBinding(@NonNull LinearLayout rootView,
      @NonNull LayoutCategorySelectorBinding batchCategorySelectorView,
      @NonNull Button btnSaveBatch, @NonNull Button btnSelectImagesBatch,
      @NonNull TextInputEditText editNotesBatch, @NonNull TextInputEditText editStoryBatch,
      @NonNull TextInputEditText editTagsBatch, @NonNull GridView gridViewSelectedImages) {
    this.rootView = rootView;
    this.batchCategorySelectorView = batchCategorySelectorView;
    this.btnSaveBatch = btnSaveBatch;
    this.btnSelectImagesBatch = btnSelectImagesBatch;
    this.editNotesBatch = editNotesBatch;
    this.editStoryBatch = editStoryBatch;
    this.editTagsBatch = editTagsBatch;
    this.gridViewSelectedImages = gridViewSelectedImages;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityBatchAddClothingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityBatchAddClothingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_batch_add_clothing, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityBatchAddClothingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.batch_category_selector_view;
      View batchCategorySelectorView = ViewBindings.findChildViewById(rootView, id);
      if (batchCategorySelectorView == null) {
        break missingId;
      }
      LayoutCategorySelectorBinding binding_batchCategorySelectorView = LayoutCategorySelectorBinding.bind(batchCategorySelectorView);

      id = R.id.btn_save_batch;
      Button btnSaveBatch = ViewBindings.findChildViewById(rootView, id);
      if (btnSaveBatch == null) {
        break missingId;
      }

      id = R.id.btn_select_images_batch;
      Button btnSelectImagesBatch = ViewBindings.findChildViewById(rootView, id);
      if (btnSelectImagesBatch == null) {
        break missingId;
      }

      id = R.id.edit_notes_batch;
      TextInputEditText editNotesBatch = ViewBindings.findChildViewById(rootView, id);
      if (editNotesBatch == null) {
        break missingId;
      }

      id = R.id.edit_story_batch;
      TextInputEditText editStoryBatch = ViewBindings.findChildViewById(rootView, id);
      if (editStoryBatch == null) {
        break missingId;
      }

      id = R.id.edit_tags_batch;
      TextInputEditText editTagsBatch = ViewBindings.findChildViewById(rootView, id);
      if (editTagsBatch == null) {
        break missingId;
      }

      id = R.id.grid_view_selected_images;
      GridView gridViewSelectedImages = ViewBindings.findChildViewById(rootView, id);
      if (gridViewSelectedImages == null) {
        break missingId;
      }

      return new ActivityBatchAddClothingBinding((LinearLayout) rootView,
          binding_batchCategorySelectorView, btnSaveBatch, btnSelectImagesBatch, editNotesBatch,
          editStoryBatch, editTagsBatch, gridViewSelectedImages);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
