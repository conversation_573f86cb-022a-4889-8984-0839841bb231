<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 深色模式颜色配置 -->
    
    <!-- 主色调 -->
    <color name="colorPrimary">#BB86FC</color>
    <color name="colorPrimaryVariant">#3700B3</color>
    <color name="colorOnPrimary">#000000</color>
    
    <!-- 次要色调 -->
    <color name="colorSecondary">#03DAC6</color>
    <color name="colorSecondaryVariant">#018786</color>
    <color name="colorOnSecondary">#000000</color>
    
    <!-- 背景色 -->
    <color name="colorBackground">#121212</color>
    <color name="colorOnBackground">#FFFFFF</color>
    
    <!-- 表面色 -->
    <color name="colorSurface">#1E1E1E</color>
    <color name="colorOnSurface">#FFFFFF</color>
    
    <!-- 错误色 -->
    <color name="colorError">#CF6679</color>
    <color name="colorOnError">#000000</color>
    
    <!-- 自定义颜色 - 深色模式版本 -->
    <color name="textColorPrimary">#FFFFFF</color>
    <color name="textColorSecondary">#B3FFFFFF</color>
    <color name="textColorHint">#66FFFFFF</color>
    
    <!-- 分割线和边框 -->
    <color name="dividerColor">#33FFFFFF</color>
    <color name="borderColor">#33FFFFFF</color>
    
    <!-- 卡片和容器背景 -->
    <color name="cardBackground">#2C2C2C</color>
    <color name="containerBackground">#1E1E1E</color>
    
    <!-- 按钮颜色 -->
    <color name="buttonBackground">#BB86FC</color>
    <color name="buttonTextColor">#000000</color>
    
    <!-- 状态栏和导航栏 -->
    <color name="statusBarColor">#000000</color>
    <color name="navigationBarColor">#121212</color>
    
    <!-- 选中状态 -->
    <color name="selectedItemBackground">#33BB86FC</color>
    <color name="rippleColor">#33FFFFFF</color>
</resources>
