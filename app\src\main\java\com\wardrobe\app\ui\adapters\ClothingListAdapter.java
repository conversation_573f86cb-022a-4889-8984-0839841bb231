package com.wardrobe.app.ui.adapters;

import android.app.Dialog;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.wardrobe.app.R;
import com.wardrobe.app.model.ClothingItem;
import com.wardrobe.app.utils.ErrorUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 衣物列表适配器 - 支持横向滑动展示和多选功能
 */
public class ClothingListAdapter extends RecyclerView.Adapter<ClothingListAdapter.ViewHolder> {
    
    private static final String TAG = "ClothingListAdapter";
    private List<ClothingItem> clothingItems = new ArrayList<>();
    private Set<Integer> selectedPositions = new HashSet<>();
    private OnItemClickListener listener;
    private OnMultiSelectListener multiSelectListener;
    private boolean isMultiSelectMode = false;

    public interface OnItemClickListener {
        void onItemClick(ClothingItem item);
    }

    public interface OnMultiSelectListener {
        void onMultiSelectStateChanged(boolean enabled);
        void onItemSelectionChanged(int count);
    }

    public void setOnItemClickListener(OnItemClickListener listener) {
        this.listener = listener;
    }

    public void setOnMultiSelectListener(OnMultiSelectListener listener) {
        this.multiSelectListener = listener;
    }

    public void updateData(List<ClothingItem> items) {
        this.clothingItems = items != null ? items : new ArrayList<>();
        clearSelections();
        notifyDataSetChanged();
    }

    public void toggleMultiSelectMode(boolean enabled) {
        isMultiSelectMode = enabled;
        if (!enabled) {
            clearSelections();
        }
        notifyDataSetChanged();
        
        if (multiSelectListener != null) {
            multiSelectListener.onMultiSelectStateChanged(enabled);
        }
    }

    public void toggleSelection(int position) {
        if (selectedPositions.contains(position)) {
            selectedPositions.remove(position);
        } else {
            selectedPositions.add(position);
        }
        notifyItemChanged(position);
        
        if (multiSelectListener != null) {
            multiSelectListener.onItemSelectionChanged(selectedPositions.size());
        }
    }

    public void clearSelections() {
        selectedPositions.clear();
        notifyDataSetChanged();
        
        if (multiSelectListener != null) {
            multiSelectListener.onItemSelectionChanged(0);
        }
    }

    public List<ClothingItem> getSelectedItems() {
        List<ClothingItem> selected = new ArrayList<>();
        for (Integer position : selectedPositions) {
            if (position >= 0 && position < clothingItems.size()) {
                selected.add(clothingItems.get(position));
            }
        }
        return selected;
    }

    public boolean isSelected(int position) {
        return selectedPositions.contains(position);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_clothing_horizontal, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        ClothingItem item = clothingItems.get(position);
        holder.bind(item, position);
    }

    @Override
    public int getItemCount() {
        return clothingItems.size();
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        private ImageView ivClothing;
        private TextView tvName;
        private View selectionOverlay;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivClothing = itemView.findViewById(R.id.iv_clothing);
            tvName = itemView.findViewById(R.id.tv_name);
            selectionOverlay = itemView.findViewById(R.id.selection_overlay);
            
            // 图片点击事件 - 全屏预览
            ivClothing.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION) {
                    ClothingItem item = clothingItems.get(position);
                    showFullscreenImage(item.getImageUri());
                }
            });
            
            // 整体点击事件
            itemView.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION) {
                    if (isMultiSelectMode) {
                        toggleSelection(position);
                    } else if (listener != null) {
                        listener.onItemClick(clothingItems.get(position));
                    }
                }
            });
            
            // 长按事件 - 进入多选模式
            itemView.setOnLongClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && !isMultiSelectMode) {
                    toggleMultiSelectMode(true);
                    toggleSelection(position);
                    return true;
                }
                return false;
            });
        }

        private void showFullscreenImage(String imageUri) {
            if (imageUri == null || imageUri.isEmpty()) {
                ErrorUtils.showUserError(itemView.getContext(), itemView.getContext().getString(R.string.toast_select_image_first));
                return;
            }

            ErrorUtils.logError("显示全屏图片: " + imageUri, null);

            // 创建全屏对话框
            Dialog dialog = new Dialog(itemView.getContext(), android.R.style.Theme_Black_NoTitleBar_Fullscreen);
            dialog.setContentView(R.layout.dialog_image_fullscreen);

            ImageView fullscreenImageView = dialog.findViewById(R.id.fullscreen_image_view);
            ImageView closeButton = dialog.findViewById(R.id.close_button);

            // 检查文件是否存在
            File imageFile = new File(imageUri);
            if (!imageFile.exists()) {
                ErrorUtils.showAndLog(itemView.getContext(), itemView.getContext().getString(R.string.toast_image_selection_cancelled), "图片文件不存在: " + imageUri, null);
                return;
            }

            try {
                // 使用BitmapFactory同步加载图片
                Bitmap bitmap = BitmapFactory.decodeFile(imageUri);
                if (bitmap != null) {
                    fullscreenImageView.setImageBitmap(bitmap);
                    ErrorUtils.logError("图片加载成功，尺寸: " + bitmap.getWidth() + "x" + bitmap.getHeight(), null);
                } else {
                    ErrorUtils.showAndLog(itemView.getContext(), itemView.getContext().getString(R.string.toast_load_failed), "图片解码失败", null);
                    return;
                }
            } catch (Exception e) {
                ErrorUtils.showAndLog(itemView.getContext(), itemView.getContext().getString(R.string.toast_load_failed), "加载图片时出错", e);
                return;
            }

            // 关闭按钮点击事件
            closeButton.setOnClickListener(v -> dialog.dismiss());
        
            // 图片点击事件 - 关闭对话框
            fullscreenImageView.setOnClickListener(v -> dialog.dismiss());
        
            // 显示对话框
            dialog.show();
        }

        public void bind(ClothingItem item, int position) {
            tvName.setText(item.getName());
            
            // 加载图片
            if (item.getImageUri() != null && !item.getImageUri().isEmpty()) {
                Glide.with(itemView.getContext())
                        .load(item.getImageUri())
                        .placeholder(R.drawable.ic_wardrobe)
                        .error(R.drawable.ic_wardrobe)
                        .centerCrop()
                        .into(ivClothing);
            } else {
                ivClothing.setImageResource(R.drawable.ic_wardrobe);
            }
            
            // 更新选择状态
            if (selectionOverlay != null) {
                selectionOverlay.setVisibility(isSelected(position) ? View.VISIBLE : View.GONE);
            }
            
            // 更新背景
            if (isSelected(position)) {
                itemView.setBackgroundResource(R.drawable.selected_item_background);
            } else {
                itemView.setBackgroundResource(android.R.color.transparent);
            }
        }
    }
}
