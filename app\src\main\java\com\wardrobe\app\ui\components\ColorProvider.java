package com.wardrobe.app.ui.components;

import android.content.Context;
import com.wardrobe.app.R;
import com.wardrobe.app.model.CategoryItem;
import java.util.ArrayList;
import java.util.List;

public class ColorProvider {

    public static List<CategoryItem> getColors(Context context) {
        List<CategoryItem> colors = new ArrayList<>();
        // The "name" is the human-readable name, the "iconName" is the hex code for the adapter
        colors.add(new CategoryItem(context.getString(R.string.color_black), "#000000"));
        colors.add(new CategoryItem(context.getString(R.string.color_white), "#FFFFFF"));
        colors.add(new CategoryItem(context.getString(R.string.color_gray), "#8E8E93"));
        colors.add(new CategoryItem(context.getString(R.string.color_red), "#FF3B30"));
        colors.add(new CategoryItem(context.getString(R.string.color_green), "#34C759"));
        colors.add(new CategoryItem(context.getString(R.string.color_blue), "#007AFF"));
        colors.add(new CategoryItem(context.getString(R.string.color_yellow), "#FFCC00"));
        colors.add(new CategoryItem(context.getString(R.string.color_orange), "#FF9500"));
        colors.add(new CategoryItem(context.getString(R.string.color_purple), "#AF52DE"));
        colors.add(new CategoryItem(context.getString(R.string.color_pink), "#FF2D55"));
        colors.add(new CategoryItem(context.getString(R.string.color_brown), "#A52A2A"));
        colors.add(new CategoryItem(context.getString(R.string.color_navy), "#000080"));
        return colors;
    }
}