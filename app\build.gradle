plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
}

android {
    namespace 'com.wardrobe.app'
    compileSdk 35

    defaultConfig {
        applicationId "com.wardrobe.app"
        minSdk 21  // Android 5.0 - 覆盖95%+设备
        targetSdk 34  // Android 14 - 稳定版本
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
    }

    buildTypes {
        debug {
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
            debuggable true
            minifyEnabled false
            shrinkResources false
            // 启用构建缓存
            buildConfigField "boolean", "DEBUG_MODE", "true"
        }

        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            debuggable false

            // 构建优化
            buildConfigField "boolean", "DEBUG_MODE", "false"

            // 签名配置（需要配置keystore）
            // signingConfig signingConfigs.release
        }
    }

    // 编译优化
    compileOptions {
        // 启用增量编译
        incremental true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
        encoding = 'UTF-8'
    }

    // 强制所有任务使用UTF-8编码
    tasks.withType(JavaCompile) {
        options.encoding = 'UTF-8'
    }

    // Android资源编码
    android.applicationVariants.all { variant ->
        variant.outputs.all {
            def buildType = variant.buildType.name
            def versionName = variant.versionName
            def date = new Date().format('yyyyMMdd')
            outputFileName = "WardrobeApp-${buildType}-${versionName}-${date}.apk"
        }
    }
    kotlinOptions {
        jvmTarget = '17'
    }
    buildFeatures {
        viewBinding true
        // dataBinding true  // 暂时禁用DataBinding
        buildConfig true
    }
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
}

dependencies {
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    implementation(libs.androidx.constraintlayout)
    implementation(libs.androidx.activity)

    implementation(libs.gson)
    implementation(libs.glide)

    // 安全组件
    implementation 'androidx.security:security-crypto:1.1.0-alpha06'

    // 生命周期组件
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.lifecycle.viewmodel.ktx)
    implementation(libs.androidx.lifecycle.livedata.ktx)

    // 测试依赖
    testImplementation(libs.junit)
    testImplementation(libs.mockito.core)
    testImplementation 'androidx.arch.core:core-testing:2.2.0'
    testImplementation 'androidx.test:core:1.5.0'
    testImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation 'androidx.test:runner:1.5.2'
    androidTestImplementation 'androidx.test:rules:1.5.0'

    // Color Picker Library
    implementation 'com.github.QuadFlask:colorpicker:0.0.13'

    debugImplementation 'com.squareup.leakcanary:leakcanary-android:2.12'
}