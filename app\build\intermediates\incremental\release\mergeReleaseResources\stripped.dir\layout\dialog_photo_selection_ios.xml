<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/dialog_rounded_background">

    <TextView
        android:id="@+id/text_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="更换图片"
        android:textSize="18sp"
        android:textStyle="bold"
        android:gravity="center"
        android:paddingBottom="16dp"
        android:textColor="?android:attr/textColorPrimary" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/ios_style_group_background_single"
        android:padding="2dp">

        <TextView
            android:id="@+id/button_take_photo"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="拍照"
            android:gravity="center"
            android:textSize="18sp"
            android:textColor="@color/systemBlue"
            android:background="?attr/selectableItemBackground"
             />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="?android:attr/listDivider" />

        <TextView
            android:id="@+id/button_choose_gallery"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="从相册选择"
            android:gravity="center"
            android:textSize="18sp"
            android:textColor="@color/systemBlue"
            android:background="?attr/selectableItemBackground"
            />
    </LinearLayout>

    <TextView
        android:id="@+id/button_cancel"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_marginTop="16dp"
        android:text="取消"
        android:gravity="center"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/systemBlue"
        android:background="@drawable/ios_style_group_background_single"/>

</LinearLayout> 