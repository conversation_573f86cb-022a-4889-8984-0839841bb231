// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutCategorySelectorBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ListItemSelectorRowBinding categorySelector;

  @NonNull
  public final ListItemSelectorRowBinding colorSelector;

  @NonNull
  public final ListItemSelectorRowBinding occasionSelector;

  @NonNull
  public final ListItemSelectorRowBinding subcategorySelector;

  private LayoutCategorySelectorBinding(@NonNull LinearLayout rootView,
      @NonNull ListItemSelectorRowBinding categorySelector,
      @NonNull ListItemSelectorRowBinding colorSelector,
      @NonNull ListItemSelectorRowBinding occasionSelector,
      @NonNull ListItemSelectorRowBinding subcategorySelector) {
    this.rootView = rootView;
    this.categorySelector = categorySelector;
    this.colorSelector = colorSelector;
    this.occasionSelector = occasionSelector;
    this.subcategorySelector = subcategorySelector;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutCategorySelectorBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutCategorySelectorBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_category_selector, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutCategorySelectorBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.category_selector;
      View categorySelector = ViewBindings.findChildViewById(rootView, id);
      if (categorySelector == null) {
        break missingId;
      }
      ListItemSelectorRowBinding binding_categorySelector = ListItemSelectorRowBinding.bind(categorySelector);

      id = R.id.color_selector;
      View colorSelector = ViewBindings.findChildViewById(rootView, id);
      if (colorSelector == null) {
        break missingId;
      }
      ListItemSelectorRowBinding binding_colorSelector = ListItemSelectorRowBinding.bind(colorSelector);

      id = R.id.occasion_selector;
      View occasionSelector = ViewBindings.findChildViewById(rootView, id);
      if (occasionSelector == null) {
        break missingId;
      }
      ListItemSelectorRowBinding binding_occasionSelector = ListItemSelectorRowBinding.bind(occasionSelector);

      id = R.id.subcategory_selector;
      View subcategorySelector = ViewBindings.findChildViewById(rootView, id);
      if (subcategorySelector == null) {
        break missingId;
      }
      ListItemSelectorRowBinding binding_subcategorySelector = ListItemSelectorRowBinding.bind(subcategorySelector);

      return new LayoutCategorySelectorBinding((LinearLayout) rootView, binding_categorySelector,
          binding_colorSelector, binding_occasionSelector, binding_subcategorySelector);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
