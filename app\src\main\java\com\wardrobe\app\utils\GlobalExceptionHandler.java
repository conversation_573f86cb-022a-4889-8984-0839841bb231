package com.wardrobe.app.utils;

import android.content.Context;
import android.util.Log;
import android.widget.Toast;

/**
 * 全局异常处理器
 * 提供统一的异常处理、日志记录和用户提示机制
 */
public class GlobalExceptionHandler {
    
    private static final String TAG = "GlobalExceptionHandler";
    private static volatile GlobalExceptionHandler instance;
    private static final Object LOCK = new Object();
    
    private Context applicationContext;
    
    private GlobalExceptionHandler(Context context) {
        this.applicationContext = context.getApplicationContext();
    }
    
    /**
     * 获取全局异常处理器实例
     * 
     * @param context 上下文
     * @return 异常处理器实例
     */
    public static GlobalExceptionHandler getInstance(Context context) {
        if (instance == null) {
            synchronized (LOCK) {
                if (instance == null) {
                    instance = new GlobalExceptionHandler(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * 处理数据异常
     * 
     * @param operation 操作描述
     * @param exception 异常对象
     * @param showToUser 是否向用户显示错误
     */
    public void handleDataException(String operation, DataException exception, boolean showToUser) {
        String errorMessage = String.format("数据操作失败: %s - %s", operation, exception.getMessage());
        Log.e(TAG, errorMessage, exception);
        
        if (showToUser) {
            showUserError("数据操作失败，请稍后重试");
        }
    }
    
    /**
     * 处理网络异常
     * 
     * @param operation 操作描述
     * @param exception 异常对象
     * @param showToUser 是否向用户显示错误
     */
    public void handleNetworkException(String operation, NetworkException exception, boolean showToUser) {
        String errorMessage = String.format("网络操作失败: %s - %s", operation, exception.getMessage());
        Log.e(TAG, errorMessage, exception);
        
        if (showToUser) {
            showUserError("网络连接失败，请检查网络设置");
        }
    }
    
    /**
     * 处理权限异常
     * 
     * @param operation 操作描述
     * @param exception 异常对象
     * @param showToUser 是否向用户显示错误
     */
    public void handlePermissionException(String operation, PermissionException exception, boolean showToUser) {
        String errorMessage = String.format("权限异常: %s - %s", operation, exception.getMessage());
        Log.e(TAG, errorMessage, exception);
        
        if (showToUser) {
            showUserError("缺少必要权限，请在设置中授予权限");
        }
    }
    
    /**
     * 处理一般异常
     * 
     * @param operation 操作描述
     * @param exception 异常对象
     * @param showToUser 是否向用户显示错误
     */
    public void handleGeneralException(String operation, Exception exception, boolean showToUser) {
        String errorMessage = String.format("操作失败: %s - %s", operation, exception.getMessage());
        Log.e(TAG, errorMessage, exception);
        
        if (showToUser) {
            showUserError("操作失败，请稍后重试");
        }
    }
    
    /**
     * 处理严重错误（可能导致应用崩溃）
     * 
     * @param operation 操作描述
     * @param error 错误对象
     */
    public void handleCriticalError(String operation, Throwable error) {
        String errorMessage = String.format("严重错误: %s - %s", operation, error.getMessage());
        Log.e(TAG, errorMessage, error);
        
        // 记录到崩溃报告系统（如果有的话）
        // CrashReporter.recordCrash(error);
        
        showUserError("应用遇到严重错误，即将重启");
    }
    
    /**
     * 向用户显示错误信息
     * 
     * @param message 错误信息
     */
    private void showUserError(String message) {
        if (applicationContext != null) {
            // 使用Handler确保在主线程中显示Toast
            android.os.Handler mainHandler = new android.os.Handler(android.os.Looper.getMainLooper());
            mainHandler.post(() -> {
                Toast.makeText(applicationContext, message, Toast.LENGTH_SHORT).show();
            });
        }
    }
    
    /**
     * 记录操作成功信息
     * 
     * @param operation 操作描述
     * @param details 详细信息
     */
    public void logSuccess(String operation, String details) {
        Log.i(TAG, String.format("操作成功: %s - %s", operation, details));
    }
    
    /**
     * 记录警告信息
     * 
     * @param operation 操作描述
     * @param warning 警告信息
     */
    public void logWarning(String operation, String warning) {
        Log.w(TAG, String.format("警告: %s - %s", operation, warning));
    }
    
    /**
     * 记录调试信息
     * 
     * @param operation 操作描述
     * @param debug 调试信息
     */
    public void logDebug(String operation, String debug) {
        if (android.util.Log.isLoggable(TAG, Log.DEBUG)) {
            Log.d(TAG, String.format("调试: %s - %s", operation, debug));
        }
    }
    
    /**
     * 异常处理结果枚举
     */
    public enum ExceptionResult {
        HANDLED,        // 已处理
        RETRY_NEEDED,   // 需要重试
        CRITICAL_ERROR  // 严重错误
    }
    
    /**
     * 根据异常类型自动选择处理方式
     *
     * @param operation 操作描述
     * @param throwable 异常对象
     * @param showToUser 是否向用户显示错误
     * @return 处理结果
     */
    public ExceptionResult handleException(String operation, Throwable throwable, boolean showToUser) {
        if (throwable instanceof DataException) {
            handleDataException(operation, (DataException) throwable, showToUser);
            return ExceptionResult.RETRY_NEEDED;
        } else if (throwable instanceof NetworkException) {
            handleNetworkException(operation, (NetworkException) throwable, showToUser);
            return ExceptionResult.RETRY_NEEDED;
        } else if (throwable instanceof PermissionException) {
            handlePermissionException(operation, (PermissionException) throwable, showToUser);
            return ExceptionResult.HANDLED;
        } else if (throwable instanceof OutOfMemoryError) {
            handleCriticalError(operation, throwable);
            return ExceptionResult.CRITICAL_ERROR;
        } else if (throwable instanceof Exception) {
            handleGeneralException(operation, (Exception) throwable, showToUser);
            return ExceptionResult.HANDLED;
        } else {
            handleCriticalError(operation, throwable);
            return ExceptionResult.CRITICAL_ERROR;
        }
    }
}
