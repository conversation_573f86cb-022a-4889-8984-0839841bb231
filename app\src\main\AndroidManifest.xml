<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 照片相关权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.CAMERA" />

    <!-- 如果您的应用需要访问网络，请取消注释以下权限 -->
    <!-- <uses-permission android:name="android.permission.INTERNET" /> -->

    <!-- 相机功能声明 -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <application
        android:name=".WardrobeApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.MaterialComponents.DayNight.NoActionBar"
        tools:targetApi="31">

        <activity
            android:name=".ui.activities.MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.activities.AddClothingActivity"
            android:exported="false" />

        <activity
            android:name=".ui.activities.CategorySelectActivity"
            android:exported="false" />

        <activity
            android:name=".ui.activities.ColorSelectActivity"
            android:exported="false" />

        <activity
            android:name=".ui.activities.OccasionSelectActivity"
            android:exported="false" />

        <activity
            android:name=".ui.activities.ClothingDetailActivity"
            android:exported="false" />

        <!-- 如果您有 BatchAddClothingActivity，请确保它已声明 -->
        <activity
            android:name=".ui.activities.BatchAddClothingActivity"
            android:exported="false" />

        <!-- FileProvider for secure file sharing -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- 请确保您应用中所有其他的 Activity, Service, Receiver, Provider 都在此处声明 -->

    </application>

</manifest>