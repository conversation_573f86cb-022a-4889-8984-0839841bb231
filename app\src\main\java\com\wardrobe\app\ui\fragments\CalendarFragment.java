package com.wardrobe.app.ui.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.wardrobe.app.R;
import com.wardrobe.app.data.ClothingManager;
import com.wardrobe.app.model.ClothingItem;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * 日历页面
 * 提供穿搭记录和日历视图功能
 */
public class CalendarFragment extends Fragment {

    private static final String TAG = "CalendarFragment";
    
    private TextView tvCurrentDate;
    private LinearLayout tvEmptyState;
    private RecyclerView recyclerView;
    
    private ClothingManager clothingManager;
    private CalendarAdapter calendarAdapter;
    private List<CalendarItem> calendarList;

    public CalendarFragment() {
        // Required empty public constructor
    }

    public static CalendarFragment newInstance() {
        return new CalendarFragment();
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        clothingManager = ClothingManager.getInstance(requireContext());
        calendarList = new ArrayList<>();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_calendar, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initViews(view);
        setupRecyclerView();
        updateCurrentDate();
        loadCalendarData();
    }

    private void initViews(View view) {
        tvCurrentDate = view.findViewById(R.id.tv_current_date);
        tvEmptyState = view.findViewById(R.id.tv_empty_state);
        recyclerView = view.findViewById(R.id.recycler_view);
    }

    private void setupRecyclerView() {
        calendarAdapter = new CalendarAdapter(calendarList);
        recyclerView.setLayoutManager(new LinearLayoutManager(requireContext()));
        recyclerView.setAdapter(calendarAdapter);
    }

    private void updateCurrentDate() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日 EEEE", Locale.CHINESE);
        String currentDate = dateFormat.format(new Date());
        tvCurrentDate.setText(currentDate);
    }

    private void loadCalendarData() {
        try {
            List<ClothingItem> allClothing = clothingManager.getClothingList();
            
            if (allClothing.isEmpty()) {
                showEmptyState();
                return;
            }

            // 生成模拟的日历数据
            calendarList.clear();
            
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat dateFormat = new SimpleDateFormat("MM月dd日", Locale.CHINESE);
            
            // 生成最近7天的穿搭记录
            for (int i = 6; i >= 0; i--) {
                calendar.add(Calendar.DAY_OF_MONTH, -i);
                
                CalendarItem item = new CalendarItem();
                item.setDate(dateFormat.format(calendar.getTime()));
                item.setDayOfWeek(getDayOfWeek(calendar.get(Calendar.DAY_OF_WEEK)));
                
                // 随机选择一些衣物作为当天的穿搭
                List<ClothingItem> dailyOutfit = generateDailyOutfit(allClothing);
                item.setOutfit(dailyOutfit);
                
                calendarList.add(item);
                
                // 重置日历
                calendar = Calendar.getInstance();
            }

            calendarAdapter.notifyDataSetChanged();
            hideEmptyState();
            
        } catch (Exception e) {
            showEmptyState();
            Toast.makeText(requireContext(), "加载日历数据失败", Toast.LENGTH_SHORT).show();
        }
    }

    private String getDayOfWeek(int dayOfWeek) {
        switch (dayOfWeek) {
            case Calendar.SUNDAY: return "周日";
            case Calendar.MONDAY: return "周一";
            case Calendar.TUESDAY: return "周二";
            case Calendar.WEDNESDAY: return "周三";
            case Calendar.THURSDAY: return "周四";
            case Calendar.FRIDAY: return "周五";
            case Calendar.SATURDAY: return "周六";
            default: return "";
        }
    }

    private List<ClothingItem> generateDailyOutfit(List<ClothingItem> allClothing) {
        List<ClothingItem> outfit = new ArrayList<>();
        
        // 随机选择2-4件衣物
        int count = (int) (Math.random() * 3) + 2;
        
        for (int i = 0; i < count && i < allClothing.size(); i++) {
            int randomIndex = (int) (Math.random() * allClothing.size());
            outfit.add(allClothing.get(randomIndex));
        }
        
        return outfit;
    }

    private void showEmptyState() {
        tvEmptyState.setVisibility(View.VISIBLE);
        recyclerView.setVisibility(View.GONE);
    }

    private void hideEmptyState() {
        tvEmptyState.setVisibility(View.GONE);
        recyclerView.setVisibility(View.VISIBLE);
    }

    @Override
    public void onResume() {
        super.onResume();
        updateCurrentDate();
        loadCalendarData();
    }

    /**
     * 日历项数据类
     */
    public static class CalendarItem {
        private String date;
        private String dayOfWeek;
        private List<ClothingItem> outfit;

        public String getDate() { return date; }
        public void setDate(String date) { this.date = date; }
        
        public String getDayOfWeek() { return dayOfWeek; }
        public void setDayOfWeek(String dayOfWeek) { this.dayOfWeek = dayOfWeek; }
        
        public List<ClothingItem> getOutfit() { return outfit; }
        public void setOutfit(List<ClothingItem> outfit) { this.outfit = outfit; }
    }

    /**
     * 日历列表适配器
     */
    private static class CalendarAdapter extends RecyclerView.Adapter<CalendarAdapter.ViewHolder> {
        
        private List<CalendarItem> calendarList;

        public CalendarAdapter(List<CalendarItem> calendarList) {
            this.calendarList = calendarList;
        }

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_calendar, parent, false);
            return new ViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            CalendarItem item = calendarList.get(position);
            holder.tvDate.setText(item.getDate());
            holder.tvDayOfWeek.setText(item.getDayOfWeek());
            
            // 构建穿搭描述
            StringBuilder description = new StringBuilder();
            if (item.getOutfit() != null) {
                for (int i = 0; i < item.getOutfit().size(); i++) {
                    if (i > 0) description.append(" + ");
                    description.append(item.getOutfit().get(i).getName());
                }
            }
            
            if (description.length() == 0) {
                description.append("暂无穿搭记录");
            }
            
            holder.tvOutfitDescription.setText(description.toString());
        }

        @Override
        public int getItemCount() {
            return calendarList.size();
        }

        static class ViewHolder extends RecyclerView.ViewHolder {
            TextView tvDate;
            TextView tvDayOfWeek;
            TextView tvOutfitDescription;

            ViewHolder(View itemView) {
                super(itemView);
                tvDate = itemView.findViewById(R.id.tv_date);
                tvDayOfWeek = itemView.findViewById(R.id.tv_day_of_week);
                tvOutfitDescription = itemView.findViewById(R.id.tv_outfit_description);
            }
        }
    }
}