<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/systemBackground">

    <!-- Main Content Area -->
    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/bottom_nav_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Container for Bottom Navigation and Separator -->
    <LinearLayout
        android:id="@+id/bottom_nav_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Separator Line -->
        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/separator"/>

        <!-- Bottom Navigation View - Redesigned for Apple HIG -->
        <com.google.android.material.bottomnavigation.BottomNavigationView
            android:id="@+id/bottom_navigation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/systemBackground"
            app:menu="@menu/bottom_navigation_menu"
            app:itemIconTint="@color/bottom_nav_color_selector"
            app:itemTextColor="@color/bottom_nav_color_selector"
            app:itemTextAppearanceActive="@style/TextAppearance.App.Caption1"
            app:itemTextAppearanceInactive="@style/TextAppearance.App.Caption1"
            app:labelVisibilityMode="labeled"
            app:elevation="0dp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
