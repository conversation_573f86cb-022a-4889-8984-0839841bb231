package com.wardrobe.app.data;

import com.wardrobe.app.model.ClothingItem;
import java.util.List;
import com.wardrobe.app.utils.DataException; // ← 必须补充这一行

/**
 * 衣物数据访问接口
 * 定义衣物数据操作的标准接口，支持不同的数据源实现
 * 为将来可能的数据库迁移提供抽象层
 */
public interface ClothingDataSource {
    
    /**
     * 获取所有衣物列表
     * @return 衣物列表
     */
    List<ClothingItem> getAllClothingItems() throws DataException;
    
    /**
     * 根据ID获取衣物
     * @param id 衣物ID
     * @return 衣物项目，如果不存在则返回null
     */
    ClothingItem getClothingItemById(String id);
    
    /**
     * 添加衣物项目
     * @param item 要添加的衣物项目
     * @return 是否添加成功
     */
    boolean addClothingItem(ClothingItem item) throws DataException;
    
    /**
     * 批量添加衣物项目
     * @param items 要添加的衣物列表
     * @return 成功添加的数量
     */
    int addMultipleClothingItems(List<ClothingItem> items) throws DataException;
    
    /**
     * 更新衣物项目
     * @param item 要更新的衣物项目
     * @return 是否更新成功
     */
    boolean updateClothingItem(ClothingItem item) throws DataException;
    
    /**
     * 删除衣物项目
     * @param id 要删除的衣物ID
     * @return 是否删除成功
     */
    boolean deleteClothingItem(String id) throws DataException;
    
    /**
     * 批量删除衣物项目
     * @param ids 要删除的衣物ID列表
     * @return 成功删除的数量
     */
    int deleteMultipleClothingItems(List<String> ids) throws DataException;
    
    /**
     * 根据分类获取衣物列表
     * @param category 分类名称
     * @return 该分类下的衣物列表
     */
    List<ClothingItem> getClothingItemsByCategory(String category);
    
    /**
     * 搜索衣物
     * @param query 搜索关键词
     * @return 匹配的衣物列表
     * @throws DataException 搜索过程中发生的数据异常
     */
    List<ClothingItem> searchClothingItems(String query) throws DataException;
    
    /**
     * 获取指定分类的衣物数量
     * @param category 分类名称
     * @return 该分类下的衣物数量
     */
    int getClothingCountByCategory(String category);
    
    /**
     * 获取总衣物数量
     * @return 总衣物数量
     */
    int getTotalClothingCount();
    
    /**
     * 检查衣物是否存在
     * @param id 衣物ID
     * @return 是否存在
     */
    boolean exists(String id);
    
    /**
     * 清理所有数据
     * @return 是否清理成功
     */
    boolean clearAllData() throws DataException;
    
    /**
     * 获取数据源统计信息
     * @return 统计信息字符串
     */
    String getStatistics();
} 