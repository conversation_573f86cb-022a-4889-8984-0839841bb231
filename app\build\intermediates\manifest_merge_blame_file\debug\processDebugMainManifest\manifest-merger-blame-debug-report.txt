1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.wardrobe.app.debug"
4    android:versionCode="1"
5    android:versionName="1.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <!-- 照片相关权限 -->
12    <!-- 用于读取设备上的图片文件，添加到衣橱管理 -->
13    <uses-permission
13-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:7:5-107
14        android:name="android.permission.READ_EXTERNAL_STORAGE"
14-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:7:22-77
15        android:maxSdkVersion="32" />
15-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:7:78-104
16    <!-- Android 13+的新媒体权限，用于访问图片 -->
17    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
17-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:9:5-76
17-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:9:22-73
18    <!-- 用于拍摄衣物照片 -->
19    <uses-permission android:name="android.permission.CAMERA" />
19-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:11:5-65
19-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:11:22-62
20
21    <!-- 如果您的应用需要访问网络，请取消注释以下权限 -->
22    <!-- <uses-permission android:name="android.permission.INTERNET" /> -->
23
24
25    <!-- 相机功能声明 -->
26    <uses-feature
26-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:17:5-19:36
27        android:name="android.hardware.camera"
27-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:18:9-47
28        android:required="false" />
28-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:19:9-33
29
30    <permission
30-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
31        android:name="com.wardrobe.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
31-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
32        android:protectionLevel="signature" />
32-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
33
34    <uses-permission android:name="com.wardrobe.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
34-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
34-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
35    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- To allow posting notifications on Android 13 -->
35-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:26:5-81
35-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:26:22-78
36    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
36-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:29:5-77
36-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:29:22-74
37
38    <application
38-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:21:5-80:19
39        android:name="com.wardrobe.app.WardrobeApplication"
39-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:22:9-44
40        android:allowBackup="true"
40-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:23:9-35
41        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
41-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
42        android:dataExtractionRules="@xml/data_extraction_rules"
42-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:24:9-65
43        android:debuggable="true"
44        android:extractNativeLibs="true"
45        android:fullBackupContent="@xml/backup_rules"
45-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:25:9-54
46        android:icon="@mipmap/ic_launcher"
46-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:26:9-43
47        android:label="@string/app_name"
47-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:27:9-41
48        android:roundIcon="@mipmap/ic_launcher_round"
48-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:28:9-54
49        android:supportsRtl="true"
49-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:29:9-35
50        android:testOnly="true"
51        android:theme="@style/Theme.MaterialComponents.DayNight.NoActionBar" >
51-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:30:9-77
52        <activity
52-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:33:9-40:20
53            android:name="com.wardrobe.app.ui.activities.MainActivity"
53-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:34:13-55
54            android:exported="true" >
54-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:35:13-36
55            <intent-filter>
55-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:36:13-39:29
56                <action android:name="android.intent.action.MAIN" />
56-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:37:17-69
56-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:37:25-66
57
58                <category android:name="android.intent.category.LAUNCHER" />
58-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:38:17-77
58-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:38:27-74
59            </intent-filter>
60        </activity>
61        <activity
61-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:42:9-44:40
62            android:name="com.wardrobe.app.ui.activities.AddClothingActivity"
62-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:43:13-62
63            android:exported="false" />
63-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:44:13-37
64        <activity
64-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:46:9-48:40
65            android:name="com.wardrobe.app.ui.activities.CategorySelectActivity"
65-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:47:13-65
66            android:exported="false" />
66-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:48:13-37
67        <activity
67-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:50:9-52:40
68            android:name="com.wardrobe.app.ui.activities.ColorSelectActivity"
68-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:51:13-62
69            android:exported="false" />
69-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:52:13-37
70        <activity
70-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:54:9-56:40
71            android:name="com.wardrobe.app.ui.activities.OccasionSelectActivity"
71-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:55:13-65
72            android:exported="false" />
72-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:56:13-37
73        <activity
73-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:58:9-60:40
74            android:name="com.wardrobe.app.ui.activities.ClothingDetailActivity"
74-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:59:13-65
75            android:exported="false" />
75-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:60:13-37
76
77        <!-- 如果您有 BatchAddClothingActivity，请确保它已声明 -->
78        <activity
78-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:63:9-65:40
79            android:name="com.wardrobe.app.ui.activities.BatchAddClothingActivity"
79-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:64:13-67
80            android:exported="false" />
80-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:65:13-37
81
82        <!-- FileProvider for secure file sharing -->
83        <provider
84            android:name="androidx.core.content.FileProvider"
84-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:69:13-62
85            android:authorities="com.wardrobe.app.debug.fileprovider"
85-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:70:13-64
86            android:exported="false"
86-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:71:13-37
87            android:grantUriPermissions="true" >
87-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:72:13-47
88            <meta-data
88-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:73:13-75:54
89                android:name="android.support.FILE_PROVIDER_PATHS"
89-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:74:17-67
90                android:resource="@xml/file_paths" />
90-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:75:17-51
91        </provider>
92
93        <!-- 请确保您应用中所有其他的 Activity, Service, Receiver, Provider 都在此处声明 -->
94
95        <provider
95-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
96            android:name="androidx.startup.InitializationProvider"
96-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
97            android:authorities="com.wardrobe.app.debug.androidx-startup"
97-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
98            android:exported="false" >
98-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
99            <meta-data
99-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
100                android:name="androidx.emoji2.text.EmojiCompatInitializer"
100-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
101                android:value="androidx.startup" />
101-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
102            <meta-data
102-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\710e1e3cca06d39ef5f5ec834a7465b3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
103                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
103-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\710e1e3cca06d39ef5f5ec834a7465b3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
104                android:value="androidx.startup" />
104-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\710e1e3cca06d39ef5f5ec834a7465b3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
105            <meta-data
105-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
106                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
106-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
107                android:value="androidx.startup" />
107-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
108        </provider>
109        <provider
109-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:32:9-40:20
110            android:name="leakcanary.internal.LeakCanaryFileProvider"
110-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:33:13-70
111            android:authorities="com.squareup.leakcanary.fileprovider.com.wardrobe.app.debug"
111-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:34:13-88
112            android:exported="false"
112-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:35:13-37
113            android:grantUriPermissions="true" >
113-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:36:13-47
114            <meta-data
114-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:73:13-75:54
115                android:name="android.support.FILE_PROVIDER_PATHS"
115-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:74:17-67
116                android:resource="@xml/leak_canary_file_paths" />
116-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:75:17-51
117        </provider>
118
119        <activity
119-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:42:9-73:20
120            android:name="leakcanary.internal.activity.LeakActivity"
120-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:43:13-69
121            android:exported="true"
121-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:44:13-36
122            android:icon="@mipmap/leak_canary_icon"
122-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:45:13-52
123            android:label="@string/leak_canary_display_activity_label"
123-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:46:13-71
124            android:taskAffinity="com.squareup.leakcanary.com.wardrobe.app.debug"
124-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:47:13-76
125            android:theme="@style/leak_canary_LeakCanary.Base" >
125-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:48:13-63
126            <intent-filter android:label="@string/leak_canary_import_hprof_file" >
126-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:49:13-72:29
126-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:49:28-81
127                <action android:name="android.intent.action.VIEW" />
127-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:50:17-69
127-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:50:25-66
128
129                <category android:name="android.intent.category.DEFAULT" />
129-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:52:17-76
129-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:52:27-73
130                <category android:name="android.intent.category.BROWSABLE" />
130-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:53:17-78
130-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:53:27-75
131
132                <data android:scheme="file" />
132-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
132-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:23-44
133                <data android:scheme="content" />
133-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
133-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:23-44
134                <data android:mimeType="*/*" />
134-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
135                <data android:host="*" />
135-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
136                <data android:pathPattern=".*\\.hprof" />
136-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
137                <data android:pathPattern=".*\\..*\\.hprof" />
137-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
138                <data android:pathPattern=".*\\..*\\..*\\.hprof" />
138-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
139                <data android:pathPattern=".*\\..*\\..*\\..*\\.hprof" />
139-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
140                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.hprof" />
140-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
141                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\.hprof" />
141-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
142                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\..*\\.hprof" />
142-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
143                <!--
144            Since hprof isn't a standard MIME type, we have to declare such patterns.
145            Most file providers will generate URIs including their own package name,
146            which contains `.` characters that must be explicitly escaped in pathPattern.
147            @see https://stackoverflow.com/a/31028507/703646
148                -->
149            </intent-filter>
150        </activity>
151
152        <activity-alias
152-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:75:9-92:26
153            android:name="leakcanary.internal.activity.LeakLauncherActivity"
153-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:76:13-77
154            android:banner="@drawable/leak_canary_tv_icon"
154-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:77:13-59
155            android:enabled="@bool/leak_canary_add_launcher_icon"
155-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:78:13-66
156            android:exported="true"
156-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:79:13-36
157            android:icon="@mipmap/leak_canary_icon"
157-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:80:13-52
158            android:label="@string/leak_canary_display_activity_label"
158-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:81:13-71
159            android:targetActivity="leakcanary.internal.activity.LeakActivity"
159-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:82:13-79
160            android:taskAffinity="com.squareup.leakcanary.com.wardrobe.app.debug"
160-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:83:13-76
161            android:theme="@style/leak_canary_LeakCanary.Base" >
161-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:84:13-63
162            <intent-filter>
162-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:85:13-91:29
163                <action android:name="android.intent.action.MAIN" />
163-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:37:17-69
163-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:37:25-66
164
165                <category android:name="android.intent.category.LAUNCHER" />
165-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:38:17-77
165-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:38:27-74
166                <!-- Android TV launcher intent -->
167                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
167-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:90:17-86
167-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:90:27-83
168            </intent-filter>
169        </activity-alias>
170
171        <activity
171-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:94:9-100:68
172            android:name="leakcanary.internal.RequestPermissionActivity"
172-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:95:13-73
173            android:excludeFromRecents="true"
173-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:96:13-46
174            android:icon="@mipmap/leak_canary_icon"
174-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:97:13-52
175            android:label="@string/leak_canary_storage_permission_activity_label"
175-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:98:13-82
176            android:taskAffinity="com.squareup.leakcanary.com.wardrobe.app.debug"
176-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:99:13-76
177            android:theme="@style/leak_canary_Theme.Transparent" />
177-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:100:13-65
178
179        <receiver android:name="leakcanary.internal.NotificationReceiver" />
179-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:102:9-77
179-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:102:19-74
180
181        <provider
181-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa77027ba7d1354e3a853d05bfbf16aa\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:8:9-12:40
182            android:name="leakcanary.internal.MainProcessAppWatcherInstaller"
182-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa77027ba7d1354e3a853d05bfbf16aa\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:9:13-78
183            android:authorities="com.wardrobe.app.debug.leakcanary-installer"
183-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa77027ba7d1354e3a853d05bfbf16aa\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:10:13-72
184            android:enabled="@bool/leak_canary_watcher_auto_install"
184-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa77027ba7d1354e3a853d05bfbf16aa\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:11:13-69
185            android:exported="false" />
185-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa77027ba7d1354e3a853d05bfbf16aa\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:12:13-37
186        <provider
186-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f2ce96999776b68457ba832c6b7bd7d\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:8:9-12:40
187            android:name="leakcanary.internal.PlumberInstaller"
187-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f2ce96999776b68457ba832c6b7bd7d\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:9:13-64
188            android:authorities="com.wardrobe.app.debug.plumber-installer"
188-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f2ce96999776b68457ba832c6b7bd7d\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:10:13-69
189            android:enabled="@bool/leak_canary_plumber_auto_install"
189-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f2ce96999776b68457ba832c6b7bd7d\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:11:13-69
190            android:exported="false" />
190-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f2ce96999776b68457ba832c6b7bd7d\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:12:13-37
191
192        <receiver
192-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
193            android:name="androidx.profileinstaller.ProfileInstallReceiver"
193-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
194            android:directBootAware="false"
194-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
195            android:enabled="true"
195-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
196            android:exported="true"
196-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
197            android:permission="android.permission.DUMP" >
197-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
198            <intent-filter>
198-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
199                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
199-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
199-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
200            </intent-filter>
201            <intent-filter>
201-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
202                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
202-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
202-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
203            </intent-filter>
204            <intent-filter>
204-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
205                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
205-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
205-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
206            </intent-filter>
207            <intent-filter>
207-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
208                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
208-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
208-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
209            </intent-filter>
210        </receiver>
211    </application>
212
213</manifest>
