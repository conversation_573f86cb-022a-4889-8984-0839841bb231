1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.wardrobe.app.debug"
4    android:versionCode="1"
5    android:versionName="1.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <!-- 照片相关权限 -->
12    <!-- 用于读取设备上的图片文件，添加到衣橱管理 -->
13    <uses-permission
13-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:7:5-107
14        android:name="android.permission.READ_EXTERNAL_STORAGE"
14-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:7:22-77
15        android:maxSdkVersion="32" />
15-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:7:78-104
16    <!-- Android 13+的新媒体权限，用于访问图片 -->
17    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
17-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:9:5-76
17-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:9:22-73
18    <!-- 用于拍摄衣物照片 -->
19    <uses-permission android:name="android.permission.CAMERA" />
19-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:11:5-65
19-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:11:22-62
20
21    <!-- 如果您的应用需要访问网络，请取消注释以下权限 -->
22    <!-- <uses-permission android:name="android.permission.INTERNET" /> -->
23
24
25    <!-- 相机功能声明 -->
26    <uses-feature
26-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:17:5-19:36
27        android:name="android.hardware.camera"
27-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:18:9-47
28        android:required="false" />
28-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:19:9-33
29
30    <permission
30-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea473efc4287bc0512d9da8cbf7e2\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
31        android:name="com.wardrobe.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
31-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea473efc4287bc0512d9da8cbf7e2\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
32        android:protectionLevel="signature" />
32-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea473efc4287bc0512d9da8cbf7e2\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
33
34    <uses-permission android:name="com.wardrobe.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
34-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea473efc4287bc0512d9da8cbf7e2\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
34-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea473efc4287bc0512d9da8cbf7e2\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
35    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- To allow posting notifications on Android 13 -->
35-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:26:5-81
35-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:26:22-78
36    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
36-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:29:5-77
36-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:29:22-74
37
38    <application
38-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:21:5-80:19
39        android:name="com.wardrobe.app.WardrobeApplication"
39-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:22:9-44
40        android:allowBackup="true"
40-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:23:9-35
41        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
41-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7fea473efc4287bc0512d9da8cbf7e2\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
42        android:dataExtractionRules="@xml/data_extraction_rules"
42-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:24:9-65
43        android:debuggable="true"
44        android:extractNativeLibs="true"
45        android:fullBackupContent="@xml/backup_rules"
45-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:25:9-54
46        android:icon="@mipmap/ic_launcher"
46-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:26:9-43
47        android:label="@string/app_name"
47-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:27:9-41
48        android:roundIcon="@mipmap/ic_launcher_round"
48-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:28:9-54
49        android:supportsRtl="true"
49-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:29:9-35
50        android:theme="@style/Theme.MaterialComponents.DayNight.NoActionBar" >
50-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:30:9-77
51        <activity
51-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:33:9-40:20
52            android:name="com.wardrobe.app.ui.activities.MainActivity"
52-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:34:13-55
53            android:exported="true" >
53-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:35:13-36
54            <intent-filter>
54-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:36:13-39:29
55                <action android:name="android.intent.action.MAIN" />
55-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:37:17-69
55-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:37:25-66
56
57                <category android:name="android.intent.category.LAUNCHER" />
57-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:38:17-77
57-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:38:27-74
58            </intent-filter>
59        </activity>
60        <activity
60-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:42:9-44:40
61            android:name="com.wardrobe.app.ui.activities.AddClothingActivity"
61-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:43:13-62
62            android:exported="false" />
62-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:44:13-37
63        <activity
63-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:46:9-48:40
64            android:name="com.wardrobe.app.ui.activities.CategorySelectActivity"
64-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:47:13-65
65            android:exported="false" />
65-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:48:13-37
66        <activity
66-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:50:9-52:40
67            android:name="com.wardrobe.app.ui.activities.ColorSelectActivity"
67-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:51:13-62
68            android:exported="false" />
68-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:52:13-37
69        <activity
69-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:54:9-56:40
70            android:name="com.wardrobe.app.ui.activities.OccasionSelectActivity"
70-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:55:13-65
71            android:exported="false" />
71-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:56:13-37
72        <activity
72-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:58:9-60:40
73            android:name="com.wardrobe.app.ui.activities.ClothingDetailActivity"
73-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:59:13-65
74            android:exported="false" />
74-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:60:13-37
75
76        <!-- 如果您有 BatchAddClothingActivity，请确保它已声明 -->
77        <activity
77-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:63:9-65:40
78            android:name="com.wardrobe.app.ui.activities.BatchAddClothingActivity"
78-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:64:13-67
79            android:exported="false" />
79-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:65:13-37
80
81        <!-- FileProvider for secure file sharing -->
82        <provider
83            android:name="androidx.core.content.FileProvider"
83-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:69:13-62
84            android:authorities="com.wardrobe.app.debug.fileprovider"
84-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:70:13-64
85            android:exported="false"
85-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:71:13-37
86            android:grantUriPermissions="true" >
86-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:72:13-47
87            <meta-data
87-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:73:13-75:54
88                android:name="android.support.FILE_PROVIDER_PATHS"
88-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:74:17-67
89                android:resource="@xml/file_paths" />
89-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:75:17-51
90        </provider>
91
92        <!-- 请确保您应用中所有其他的 Activity, Service, Receiver, Provider 都在此处声明 -->
93
94        <provider
94-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6518e484ddb0210d340d62f2dea32bb3\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
95            android:name="androidx.startup.InitializationProvider"
95-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6518e484ddb0210d340d62f2dea32bb3\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
96            android:authorities="com.wardrobe.app.debug.androidx-startup"
96-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6518e484ddb0210d340d62f2dea32bb3\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
97            android:exported="false" >
97-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6518e484ddb0210d340d62f2dea32bb3\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
98            <meta-data
98-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6518e484ddb0210d340d62f2dea32bb3\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
99                android:name="androidx.emoji2.text.EmojiCompatInitializer"
99-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6518e484ddb0210d340d62f2dea32bb3\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
100                android:value="androidx.startup" />
100-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6518e484ddb0210d340d62f2dea32bb3\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
101            <meta-data
101-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\013ddaa7c74171bcbd63ac0b80e4b546\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
102                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
102-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\013ddaa7c74171bcbd63ac0b80e4b546\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
103                android:value="androidx.startup" />
103-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\013ddaa7c74171bcbd63ac0b80e4b546\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
104            <meta-data
104-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
105                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
105-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
106                android:value="androidx.startup" />
106-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
107        </provider>
108        <provider
108-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:32:9-40:20
109            android:name="leakcanary.internal.LeakCanaryFileProvider"
109-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:33:13-70
110            android:authorities="com.squareup.leakcanary.fileprovider.com.wardrobe.app.debug"
110-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:34:13-88
111            android:exported="false"
111-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:35:13-37
112            android:grantUriPermissions="true" >
112-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:36:13-47
113            <meta-data
113-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:73:13-75:54
114                android:name="android.support.FILE_PROVIDER_PATHS"
114-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:74:17-67
115                android:resource="@xml/leak_canary_file_paths" />
115-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:75:17-51
116        </provider>
117
118        <activity
118-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:42:9-73:20
119            android:name="leakcanary.internal.activity.LeakActivity"
119-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:43:13-69
120            android:exported="true"
120-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:44:13-36
121            android:icon="@mipmap/leak_canary_icon"
121-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:45:13-52
122            android:label="@string/leak_canary_display_activity_label"
122-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:46:13-71
123            android:taskAffinity="com.squareup.leakcanary.com.wardrobe.app.debug"
123-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:47:13-76
124            android:theme="@style/leak_canary_LeakCanary.Base" >
124-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:48:13-63
125            <intent-filter android:label="@string/leak_canary_import_hprof_file" >
125-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:49:13-72:29
125-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:49:28-81
126                <action android:name="android.intent.action.VIEW" />
126-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:50:17-69
126-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:50:25-66
127
128                <category android:name="android.intent.category.DEFAULT" />
128-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:52:17-76
128-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:52:27-73
129                <category android:name="android.intent.category.BROWSABLE" />
129-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:53:17-78
129-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:53:27-75
130
131                <data android:scheme="file" />
131-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
131-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:23-44
132                <data android:scheme="content" />
132-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
132-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:23-44
133                <data android:mimeType="*/*" />
133-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
134                <data android:host="*" />
134-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
135                <data android:pathPattern=".*\\.hprof" />
135-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
136                <data android:pathPattern=".*\\..*\\.hprof" />
136-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
137                <data android:pathPattern=".*\\..*\\..*\\.hprof" />
137-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
138                <data android:pathPattern=".*\\..*\\..*\\..*\\.hprof" />
138-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
139                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.hprof" />
139-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
140                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\.hprof" />
140-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
141                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\..*\\.hprof" />
141-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
142                <!--
143            Since hprof isn't a standard MIME type, we have to declare such patterns.
144            Most file providers will generate URIs including their own package name,
145            which contains `.` characters that must be explicitly escaped in pathPattern.
146            @see https://stackoverflow.com/a/31028507/703646
147                -->
148            </intent-filter>
149        </activity>
150
151        <activity-alias
151-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:75:9-92:26
152            android:name="leakcanary.internal.activity.LeakLauncherActivity"
152-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:76:13-77
153            android:banner="@drawable/leak_canary_tv_icon"
153-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:77:13-59
154            android:enabled="@bool/leak_canary_add_launcher_icon"
154-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:78:13-66
155            android:exported="true"
155-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:79:13-36
156            android:icon="@mipmap/leak_canary_icon"
156-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:80:13-52
157            android:label="@string/leak_canary_display_activity_label"
157-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:81:13-71
158            android:targetActivity="leakcanary.internal.activity.LeakActivity"
158-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:82:13-79
159            android:taskAffinity="com.squareup.leakcanary.com.wardrobe.app.debug"
159-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:83:13-76
160            android:theme="@style/leak_canary_LeakCanary.Base" >
160-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:84:13-63
161            <intent-filter>
161-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:85:13-91:29
162                <action android:name="android.intent.action.MAIN" />
162-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:37:17-69
162-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:37:25-66
163
164                <category android:name="android.intent.category.LAUNCHER" />
164-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:38:17-77
164-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:38:27-74
165                <!-- Android TV launcher intent -->
166                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
166-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:90:17-86
166-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:90:27-83
167            </intent-filter>
168        </activity-alias>
169
170        <activity
170-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:94:9-100:68
171            android:name="leakcanary.internal.RequestPermissionActivity"
171-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:95:13-73
172            android:excludeFromRecents="true"
172-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:96:13-46
173            android:icon="@mipmap/leak_canary_icon"
173-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:97:13-52
174            android:label="@string/leak_canary_storage_permission_activity_label"
174-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:98:13-82
175            android:taskAffinity="com.squareup.leakcanary.com.wardrobe.app.debug"
175-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:99:13-76
176            android:theme="@style/leak_canary_Theme.Transparent" />
176-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:100:13-65
177
178        <receiver android:name="leakcanary.internal.NotificationReceiver" />
178-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:102:9-77
178-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f749c5fa3586a284bdf9d7118f42ebf\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:102:19-74
179
180        <provider
180-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\4987f749363a1fe7966e5bd39851db31\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:8:9-12:40
181            android:name="leakcanary.internal.MainProcessAppWatcherInstaller"
181-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\4987f749363a1fe7966e5bd39851db31\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:9:13-78
182            android:authorities="com.wardrobe.app.debug.leakcanary-installer"
182-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\4987f749363a1fe7966e5bd39851db31\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:10:13-72
183            android:enabled="@bool/leak_canary_watcher_auto_install"
183-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\4987f749363a1fe7966e5bd39851db31\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:11:13-69
184            android:exported="false" />
184-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\4987f749363a1fe7966e5bd39851db31\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:12:13-37
185        <provider
185-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e281f90316021b68c0a4b703740198\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:8:9-12:40
186            android:name="leakcanary.internal.PlumberInstaller"
186-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e281f90316021b68c0a4b703740198\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:9:13-64
187            android:authorities="com.wardrobe.app.debug.plumber-installer"
187-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e281f90316021b68c0a4b703740198\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:10:13-69
188            android:enabled="@bool/leak_canary_plumber_auto_install"
188-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e281f90316021b68c0a4b703740198\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:11:13-69
189            android:exported="false" />
189-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.13\transforms\e1e281f90316021b68c0a4b703740198\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:12:13-37
190
191        <receiver
191-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
192            android:name="androidx.profileinstaller.ProfileInstallReceiver"
192-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
193            android:directBootAware="false"
193-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
194            android:enabled="true"
194-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
195            android:exported="true"
195-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
196            android:permission="android.permission.DUMP" >
196-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
197            <intent-filter>
197-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
198                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
198-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
198-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
199            </intent-filter>
200            <intent-filter>
200-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
201                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
201-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
201-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
202            </intent-filter>
203            <intent-filter>
203-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
204                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
204-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
204-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
205            </intent-filter>
206            <intent-filter>
206-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
207                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
207-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
207-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\76701f37f5ad5a2615d9d6840589391c\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
208            </intent-filter>
209        </receiver>
210    </application>
211
212</manifest>
