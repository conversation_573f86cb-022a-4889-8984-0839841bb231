1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.wardrobe.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- 照片相关权限 -->
12    <uses-permission
12-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:6:5-107
13        android:name="android.permission.READ_EXTERNAL_STORAGE"
13-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:6:22-77
14        android:maxSdkVersion="32" />
14-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:6:78-104
15    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
15-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:7:5-76
15-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:7:22-73
16    <uses-permission android:name="android.permission.CAMERA" />
16-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:8:5-65
16-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:8:22-62
17
18    <!-- 如果您的应用需要访问网络，请取消注释以下权限 -->
19    <!-- <uses-permission android:name="android.permission.INTERNET" /> -->
20
21
22    <!-- 相机功能声明 -->
23    <uses-feature
23-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:14:5-16:36
24        android:name="android.hardware.camera"
24-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:15:9-47
25        android:required="false" />
25-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:16:9-33
26
27    <permission
27-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
28        android:name="com.wardrobe.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
28-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
29        android:protectionLevel="signature" />
29-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
30
31    <uses-permission android:name="com.wardrobe.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
31-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
31-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
32    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- To allow posting notifications on Android 13 -->
32-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:26:5-81
32-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:26:22-78
33    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
33-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:29:5-77
33-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:29:22-74
34
35    <application
35-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:18:5-77:19
36        android:name="com.wardrobe.app.WardrobeApplication"
36-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:19:9-44
37        android:allowBackup="true"
37-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:20:9-35
38        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
38-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
39        android:dataExtractionRules="@xml/data_extraction_rules"
39-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:21:9-65
40        android:debuggable="true"
41        android:extractNativeLibs="false"
42        android:fullBackupContent="@xml/backup_rules"
42-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:22:9-54
43        android:icon="@mipmap/ic_launcher"
43-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:23:9-43
44        android:label="@string/app_name"
44-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:24:9-41
45        android:roundIcon="@mipmap/ic_launcher_round"
45-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:25:9-54
46        android:supportsRtl="true"
46-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:26:9-35
47        android:theme="@style/Theme.MaterialComponents.DayNight.NoActionBar" >
47-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:27:9-77
48        <activity
48-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:30:9-37:20
49            android:name="com.wardrobe.app.ui.activities.MainActivity"
49-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:31:13-55
50            android:exported="true" >
50-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:32:13-36
51            <intent-filter>
51-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:33:13-36:29
52                <action android:name="android.intent.action.MAIN" />
52-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:34:17-69
52-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:34:25-66
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:35:17-77
54-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:35:27-74
55            </intent-filter>
56        </activity>
57        <activity
57-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:39:9-41:40
58            android:name="com.wardrobe.app.ui.activities.AddClothingActivity"
58-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:40:13-62
59            android:exported="false" />
59-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:41:13-37
60        <activity
60-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:43:9-45:40
61            android:name="com.wardrobe.app.ui.activities.CategorySelectActivity"
61-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:44:13-65
62            android:exported="false" />
62-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:45:13-37
63        <activity
63-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:47:9-49:40
64            android:name="com.wardrobe.app.ui.activities.ColorSelectActivity"
64-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:48:13-62
65            android:exported="false" />
65-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:49:13-37
66        <activity
66-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:51:9-53:40
67            android:name="com.wardrobe.app.ui.activities.OccasionSelectActivity"
67-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:52:13-65
68            android:exported="false" />
68-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:53:13-37
69        <activity
69-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:55:9-57:40
70            android:name="com.wardrobe.app.ui.activities.ClothingDetailActivity"
70-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:56:13-65
71            android:exported="false" />
71-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:57:13-37
72
73        <!-- 如果您有 BatchAddClothingActivity，请确保它已声明 -->
74        <activity
74-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:60:9-62:40
75            android:name="com.wardrobe.app.ui.activities.BatchAddClothingActivity"
75-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:61:13-67
76            android:exported="false" />
76-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:62:13-37
77
78        <!-- FileProvider for secure file sharing -->
79        <provider
80            android:name="androidx.core.content.FileProvider"
80-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:66:13-62
81            android:authorities="com.wardrobe.app.fileprovider"
81-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:67:13-64
82            android:exported="false"
82-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:68:13-37
83            android:grantUriPermissions="true" >
83-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:69:13-47
84            <meta-data
84-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:70:13-72:54
85                android:name="android.support.FILE_PROVIDER_PATHS"
85-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:71:17-67
86                android:resource="@xml/file_paths" />
86-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:72:17-51
87        </provider>
88
89        <!-- 请确保您应用中所有其他的 Activity, Service, Receiver, Provider 都在此处声明 -->
90
91        <provider
91-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
92            android:name="androidx.startup.InitializationProvider"
92-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
93            android:authorities="com.wardrobe.app.androidx-startup"
93-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
94            android:exported="false" >
94-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
95            <meta-data
95-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
96                android:name="androidx.emoji2.text.EmojiCompatInitializer"
96-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
97                android:value="androidx.startup" />
97-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
98            <meta-data
98-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\710e1e3cca06d39ef5f5ec834a7465b3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
99                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
99-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\710e1e3cca06d39ef5f5ec834a7465b3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
100                android:value="androidx.startup" />
100-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\710e1e3cca06d39ef5f5ec834a7465b3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
101            <meta-data
101-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
102                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
102-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
103                android:value="androidx.startup" />
103-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
104        </provider>
105        <provider
105-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:32:9-40:20
106            android:name="leakcanary.internal.LeakCanaryFileProvider"
106-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:33:13-70
107            android:authorities="com.squareup.leakcanary.fileprovider.com.wardrobe.app"
107-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:34:13-88
108            android:exported="false"
108-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:35:13-37
109            android:grantUriPermissions="true" >
109-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:36:13-47
110            <meta-data
110-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:70:13-72:54
111                android:name="android.support.FILE_PROVIDER_PATHS"
111-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:71:17-67
112                android:resource="@xml/leak_canary_file_paths" />
112-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:72:17-51
113        </provider>
114
115        <activity
115-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:42:9-73:20
116            android:name="leakcanary.internal.activity.LeakActivity"
116-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:43:13-69
117            android:exported="true"
117-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:44:13-36
118            android:icon="@mipmap/leak_canary_icon"
118-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:45:13-52
119            android:label="@string/leak_canary_display_activity_label"
119-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:46:13-71
120            android:taskAffinity="com.squareup.leakcanary.com.wardrobe.app"
120-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:47:13-76
121            android:theme="@style/leak_canary_LeakCanary.Base" >
121-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:48:13-63
122            <intent-filter android:label="@string/leak_canary_import_hprof_file" >
122-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:49:13-72:29
122-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:49:28-81
123                <action android:name="android.intent.action.VIEW" />
123-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:50:17-69
123-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:50:25-66
124
125                <category android:name="android.intent.category.DEFAULT" />
125-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:52:17-76
125-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:52:27-73
126                <category android:name="android.intent.category.BROWSABLE" />
126-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:53:17-78
126-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:53:27-75
127
128                <data android:scheme="file" />
128-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
128-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:23-44
129                <data android:scheme="content" />
129-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
129-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:23-44
130                <data android:mimeType="*/*" />
130-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
131                <data android:host="*" />
131-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
132                <data android:pathPattern=".*\\.hprof" />
132-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
133                <data android:pathPattern=".*\\..*\\.hprof" />
133-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
134                <data android:pathPattern=".*\\..*\\..*\\.hprof" />
134-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
135                <data android:pathPattern=".*\\..*\\..*\\..*\\.hprof" />
135-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
136                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.hprof" />
136-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
137                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\.hprof" />
137-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
138                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\..*\\.hprof" />
138-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:55:17-47
139                <!--
140            Since hprof isn't a standard MIME type, we have to declare such patterns.
141            Most file providers will generate URIs including their own package name,
142            which contains `.` characters that must be explicitly escaped in pathPattern.
143            @see https://stackoverflow.com/a/31028507/703646
144                -->
145            </intent-filter>
146        </activity>
147
148        <activity-alias
148-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:75:9-92:26
149            android:name="leakcanary.internal.activity.LeakLauncherActivity"
149-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:76:13-77
150            android:banner="@drawable/leak_canary_tv_icon"
150-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:77:13-59
151            android:enabled="@bool/leak_canary_add_launcher_icon"
151-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:78:13-66
152            android:exported="true"
152-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:79:13-36
153            android:icon="@mipmap/leak_canary_icon"
153-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:80:13-52
154            android:label="@string/leak_canary_display_activity_label"
154-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:81:13-71
155            android:targetActivity="leakcanary.internal.activity.LeakActivity"
155-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:82:13-79
156            android:taskAffinity="com.squareup.leakcanary.com.wardrobe.app"
156-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:83:13-76
157            android:theme="@style/leak_canary_LeakCanary.Base" >
157-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:84:13-63
158            <intent-filter>
158-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:85:13-91:29
159                <action android:name="android.intent.action.MAIN" />
159-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:34:17-69
159-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:34:25-66
160
161                <category android:name="android.intent.category.LAUNCHER" />
161-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:35:17-77
161-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:35:27-74
162                <!-- Android TV launcher intent -->
163                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
163-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:90:17-86
163-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:90:27-83
164            </intent-filter>
165        </activity-alias>
166
167        <activity
167-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:94:9-100:68
168            android:name="leakcanary.internal.RequestPermissionActivity"
168-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:95:13-73
169            android:excludeFromRecents="true"
169-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:96:13-46
170            android:icon="@mipmap/leak_canary_icon"
170-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:97:13-52
171            android:label="@string/leak_canary_storage_permission_activity_label"
171-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:98:13-82
172            android:taskAffinity="com.squareup.leakcanary.com.wardrobe.app"
172-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:99:13-76
173            android:theme="@style/leak_canary_Theme.Transparent" />
173-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:100:13-65
174
175        <receiver android:name="leakcanary.internal.NotificationReceiver" />
175-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:102:9-77
175-->[com.squareup.leakcanary:leakcanary-android-core:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cda4372faace0d2ab027ac1660d7fb39\transformed\jetified-leakcanary-android-core-2.12\AndroidManifest.xml:102:19-74
176
177        <provider
177-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa77027ba7d1354e3a853d05bfbf16aa\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:8:9-12:40
178            android:name="leakcanary.internal.MainProcessAppWatcherInstaller"
178-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa77027ba7d1354e3a853d05bfbf16aa\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:9:13-78
179            android:authorities="com.wardrobe.app.leakcanary-installer"
179-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa77027ba7d1354e3a853d05bfbf16aa\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:10:13-72
180            android:enabled="@bool/leak_canary_watcher_auto_install"
180-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa77027ba7d1354e3a853d05bfbf16aa\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:11:13-69
181            android:exported="false" />
181-->[com.squareup.leakcanary:leakcanary-object-watcher-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa77027ba7d1354e3a853d05bfbf16aa\transformed\jetified-leakcanary-object-watcher-android-2.12\AndroidManifest.xml:12:13-37
182        <provider
182-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f2ce96999776b68457ba832c6b7bd7d\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:8:9-12:40
183            android:name="leakcanary.internal.PlumberInstaller"
183-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f2ce96999776b68457ba832c6b7bd7d\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:9:13-64
184            android:authorities="com.wardrobe.app.plumber-installer"
184-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f2ce96999776b68457ba832c6b7bd7d\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:10:13-69
185            android:enabled="@bool/leak_canary_plumber_auto_install"
185-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f2ce96999776b68457ba832c6b7bd7d\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:11:13-69
186            android:exported="false" />
186-->[com.squareup.leakcanary:plumber-android:2.12] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f2ce96999776b68457ba832c6b7bd7d\transformed\jetified-plumber-android-2.12\AndroidManifest.xml:12:13-37
187
188        <receiver
188-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
189            android:name="androidx.profileinstaller.ProfileInstallReceiver"
189-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
190            android:directBootAware="false"
190-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
191            android:enabled="true"
191-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
192            android:exported="true"
192-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
193            android:permission="android.permission.DUMP" >
193-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
194            <intent-filter>
194-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
195                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
195-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
195-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
196            </intent-filter>
197            <intent-filter>
197-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
198                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
198-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
198-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
199            </intent-filter>
200            <intent-filter>
200-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
201                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
201-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
201-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
202            </intent-filter>
203            <intent-filter>
203-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
204                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
204-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
204-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
205            </intent-filter>
206        </receiver>
207    </application>
208
209</manifest>
