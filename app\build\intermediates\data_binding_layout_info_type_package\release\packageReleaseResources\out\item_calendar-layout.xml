<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_calendar" modulePackage="com.wardrobe.app" filePath="app\src\main\res\layout\item_calendar.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_calendar_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="66" endOffset="35"/></Target><Target id="@+id/tv_date" view="TextView"><Expressions/><location startLine="23" startOffset="12" endLine="30" endOffset="42"/></Target><Target id="@+id/tv_day_of_week" view="TextView"><Expressions/><location startLine="32" startOffset="12" endLine="39" endOffset="41"/></Target><Target id="@+id/tv_outfit_description" view="TextView"><Expressions/><location startLine="44" startOffset="8" endLine="52" endOffset="37"/></Target><Target id="@+id/btn_view_detail" view="Button"><Expressions/><location startLine="55" startOffset="8" endLine="62" endOffset="37"/></Target></Targets></Layout>