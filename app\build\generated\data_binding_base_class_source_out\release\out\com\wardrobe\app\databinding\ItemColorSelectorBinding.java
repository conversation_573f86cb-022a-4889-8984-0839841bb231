// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemColorSelectorBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView colorCircle;

  @NonNull
  public final TextView colorName;

  @NonNull
  public final View colorPreviewCircle;

  private ItemColorSelectorBinding(@NonNull LinearLayout rootView, @NonNull ImageView colorCircle,
      @NonNull TextView colorName, @NonNull View colorPreviewCircle) {
    this.rootView = rootView;
    this.colorCircle = colorCircle;
    this.colorName = colorName;
    this.colorPreviewCircle = colorPreviewCircle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemColorSelectorBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemColorSelectorBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_color_selector, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemColorSelectorBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.color_circle;
      ImageView colorCircle = ViewBindings.findChildViewById(rootView, id);
      if (colorCircle == null) {
        break missingId;
      }

      id = R.id.color_name;
      TextView colorName = ViewBindings.findChildViewById(rootView, id);
      if (colorName == null) {
        break missingId;
      }

      id = R.id.color_preview_circle;
      View colorPreviewCircle = ViewBindings.findChildViewById(rootView, id);
      if (colorPreviewCircle == null) {
        break missingId;
      }

      return new ItemColorSelectorBinding((LinearLayout) rootView, colorCircle, colorName,
          colorPreviewCircle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
