<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_category_select" modulePackage="com.wardrobe.app" filePath="app\src\main\res\layout\activity_category_select.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_category_select_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="61" endOffset="51"/></Target><Target id="@+id/nav_bar" view="LinearLayout"><Expressions/><location startLine="9" startOffset="4" endLine="44" endOffset="18"/></Target><Target id="@+id/btn_back" view="ImageButton"><Expressions/><location startLine="20" startOffset="8" endLine="28" endOffset="44"/></Target><Target id="@+id/tv_title" view="TextView"><Expressions/><location startLine="30" startOffset="8" endLine="39" endOffset="38"/></Target><Target id="@+id/recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="47" startOffset="4" endLine="59" endOffset="57"/></Target></Targets></Layout>