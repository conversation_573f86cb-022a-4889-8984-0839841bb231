package com.wardrobe.app.ui.adapters;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.wardrobe.app.R;

import java.util.List;

/**
 * 颜色选择器适配器
 * 为颜色Spinner提供自定义的显示效果
 */
public class ColorSpinnerAdapter extends RecyclerView.Adapter<ColorSpinnerAdapter.ColorViewHolder> {
    
    private final List<String> colorOptions;
    
    public ColorSpinnerAdapter(Context context, List<String> colorOptions) {
        this.colorOptions = colorOptions;
    }
    
    @Override
    public ColorViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.spinner_item_with_icon, parent, false);
        return new ColorViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ColorViewHolder holder, int position) {
        String colorName = colorOptions.get(position);
        holder.colorTextView.setText(colorName);

        // 核心修复：为圆形Drawable填充颜色，而不是设置View背景色
        GradientDrawable gradientDrawable = (GradientDrawable) holder.colorCircleView.getDrawable();
        if (gradientDrawable != null) {
            gradientDrawable.setColor(getColorValue(colorName));
        } else {
            // 如果尚未设置，则创建一个新的
            GradientDrawable newDrawable = (GradientDrawable) ContextCompat.getDrawable(holder.itemView.getContext(), R.drawable.color_circle_background).mutate();
            newDrawable.setColor(getColorValue(colorName));
            holder.colorCircleView.setImageDrawable(newDrawable);
        }

        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onColorSelected(colorName);
            }
        });
    }
    
    @Override
    public int getItemCount() {
        return colorOptions.size();
    }
    
    public static class ColorViewHolder extends RecyclerView.ViewHolder {
        private final TextView colorTextView;
        private final ImageView colorCircleView;

        public ColorViewHolder(@NonNull View itemView) {
            super(itemView);
            colorTextView = itemView.findViewById(R.id.spinner_text);
            colorCircleView = itemView.findViewById(R.id.spinner_icon);

            // 确保 ImageView 有一个基础的圆形Drawable
            colorCircleView.setImageResource(R.drawable.color_circle_background);
        }
    }
    
    public interface OnColorSelectedListener {
        void onColorSelected(String colorName);
    }
    
    private OnColorSelectedListener listener;
    
    public void setOnColorSelectedListener(OnColorSelectedListener listener) {
        this.listener = listener;
    }
    
    /**
     * 根据颜色名称获取颜色值
     * @param colorName 颜色名称
     * @return 颜色值
     */
    private int getColorValue(String colorName) {
        switch (colorName) {
            case "红色":
                return Color.RED;
            case "蓝色":
                return Color.BLUE;
            case "绿色":
                return Color.GREEN;
            case "黄色":
                return Color.YELLOW;
            case "黑色":
                return Color.BLACK;
            case "白色":
                return Color.WHITE;
            case "灰色":
                return Color.GRAY;
            case "棕色":
                return Color.rgb(139, 69, 19); // 棕色
            case "橙色":
                return Color.rgb(255, 165, 0); // 橙色
            case "紫色":
                return Color.rgb(128, 0, 128); // 紫色
            case "粉色":
                return Color.rgb(255, 192, 203); // 粉色
            default:
                return Color.TRANSPARENT;
        }
    }
}
