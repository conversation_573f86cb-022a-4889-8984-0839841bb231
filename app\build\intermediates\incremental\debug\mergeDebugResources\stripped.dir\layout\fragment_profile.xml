<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 🍎 标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="个人资料"
            android:textSize="24sp"
            android:textColor="#1D1D1F"
            android:textStyle="bold"
            android:fontFamily="sans-serif"
            android:layout_marginBottom="24dp" />

        <!-- 🍎 个性化设置 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="个性化设置"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <!-- 深色模式 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:background="@color/surface_primary"
            android:layout_marginBottom="8dp"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="深色模式"
                android:textColor="@color/text_primary"
                android:textSize="16sp" />

            <Switch
                android:id="@+id/switch_dark_mode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </LinearLayout>

        <!-- 通知设置 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:background="@color/surface_primary"
            android:layout_marginBottom="8dp"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="通知提醒"
                android:textColor="@color/text_primary"
                android:textSize="16sp" />

            <Switch
                android:id="@+id/switch_notifications"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </LinearLayout>

        <!-- 自动备份 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:background="@color/surface_primary"
            android:layout_marginBottom="16dp"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="自动备份"
                android:textColor="@color/text_primary"
                android:textSize="16sp" />

            <Switch
                android:id="@+id/switch_auto_backup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </LinearLayout>

        <!-- 主题设置 -->
        <LinearLayout
            android:id="@+id/layout_theme"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:layout_marginBottom="8dp"
            android:gravity="center_vertical"
            android:clickable="true"
            android:focusable="true"
            android:background="?android:attr/selectableItemBackground">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="主题模式"
                android:textColor="@color/text_primary"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_theme_mode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="跟随系统"
                android:textColor="@color/text_secondary"
                android:textSize="16sp"
                android:layout_marginEnd="8dp" />

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_chevron_right"
                android:tint="@color/text_tertiary" />

        </LinearLayout>

        <!-- 语言设置 -->
        <LinearLayout
            android:id="@+id/layout_language"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:layout_marginBottom="16dp"
            android:gravity="center_vertical"
            android:clickable="true"
            android:focusable="true"
            android:background="?android:attr/selectableItemBackground">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="语言"
                android:textColor="@color/text_primary"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_language"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="简体中文"
                android:textColor="@color/text_secondary"
                android:textSize="16sp"
                android:layout_marginEnd="8dp" />

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_chevron_right"
                android:tint="@color/text_tertiary" />

        </LinearLayout>

        <!-- 🍎 存储信息卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:strokeWidth="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="存储空间"
                    android:textSize="18sp"
                    android:textColor="#1D1D1F"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:id="@+id/tv_storage_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="存储空间: 0 B"
                    android:textSize="16sp"
                    android:textColor="#666666"
                    android:fontFamily="sans-serif"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_image_count"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="图片文件: 0 个"
                    android:textSize="16sp"
                    android:textColor="#666666"
                    android:fontFamily="sans-serif"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tv_clothing_count"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="衣物数量: 0 件"
                    android:textSize="16sp"
                    android:textColor="#666666"
                    android:fontFamily="sans-serif" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 🍎 存储管理卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:strokeWidth="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="存储管理"
                    android:textSize="18sp"
                    android:textColor="#1D1D1F"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif"
                    android:layout_marginBottom="16dp" />

                <!-- 压缩图片按钮 -->
                <Button
                    android:id="@+id/btn_compress_images"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="压缩所有图片"
                    android:textColor="@color/white"
                    android:background="@drawable/button_background_prominent"
                    android:layout_marginBottom="8dp" />

                <!-- 清理未使用图片按钮 -->
                <Button
                    android:id="@+id/btn_cleanup_unused"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="清理未使用图片"
                    android:textColor="@color/white"
                    android:background="@drawable/button_background_prominent"
                    android:layout_marginBottom="16dp" />

                <!-- 清理所有图片按钮 -->
                <Button
                    android:id="@+id/btn_cleanup_images"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="清理所有图片"
                    android:textSize="16sp"
                    android:textColor="#FF3B30"
                    android:background="@drawable/button_background"
                    android:padding="12dp"
                    android:enabled="false" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 🍎 应用信息卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:strokeWidth="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="应用信息"
                    android:textSize="18sp"
                    android:textColor="#1D1D1F"
                    android:textStyle="bold"
                    android:fontFamily="sans-serif"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="版本: 1.0.0"
                    android:textSize="16sp"
                    android:textColor="#666666"
                    android:fontFamily="sans-serif"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="开发者: Wardrobe App Team"
                    android:textSize="16sp"
                    android:textColor="#666666"
                    android:fontFamily="sans-serif"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="© 2024 Wardrobe App"
                    android:textSize="16sp"
                    android:textColor="#666666"
                    android:fontFamily="sans-serif" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 其他选项 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="其他"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <!-- 关于 -->
        <LinearLayout
            android:id="@+id/layout_about"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:layout_marginBottom="8dp"
            android:gravity="center_vertical"
            android:clickable="true"
            android:focusable="true"
            android:background="?android:attr/selectableItemBackground">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="关于"
                android:textColor="@color/text_primary"
                android:textSize="16sp" />

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_chevron_right"
                android:tint="@color/text_tertiary" />

        </LinearLayout>

        <!-- 反馈 -->
        <LinearLayout
            android:id="@+id/layout_feedback"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:layout_marginBottom="8dp"
            android:gravity="center_vertical"
            android:clickable="true"
            android:focusable="true"
            android:background="?android:attr/selectableItemBackground">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="反馈"
                android:textColor="@color/text_primary"
                android:textSize="16sp" />

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_chevron_right"
                android:tint="@color/text_tertiary" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
