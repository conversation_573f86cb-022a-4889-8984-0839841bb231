{"logs": [{"outputFile": "com.wardrobe.app-mergeDebugResources-41:/values-en/values-en.xml", "map": [{"source": "C:\\Users\\<USER>\\AndroidStudioProjects\\WardrobeApp\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "133,136,135,134,2,37,33,40,34,35,42,36,39,7,38,41,56,52,53,54,20,23,24,59,55,58,51,57,60,67,64,70,69,65,74,73,71,21,72,63,68,66,28,29,30,129,126,130,127,128,113,114,115,116,8,15,16,17,91,90,97,94,96,93,95,89,98,92,47,46,48,45,77,86,83,78,84,85,80,22,81,82,79,108,109,107,25,112,12,11,123,119,121,122,120,5,6,102,104,101,103", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6015,6234,6162,6090,55,1596,1402,1737,1451,1502,1841,1551,1688,280,1639,1784,2405,2193,2246,2299,739,928,1006,2572,2356,2523,2146,2466,2619,2875,2737,3011,2967,2781,3197,3149,3057,804,3101,2695,2921,2827,1173,1240,1313,5863,5682,5927,5742,5798,5104,5170,5230,5296,325,562,614,660,3924,3852,4343,4133,4281,4063,4209,3784,4398,3991,2023,1977,2073,1927,3265,3712,3565,3316,3612,3659,3414,863,3463,3514,3367,4890,4959,4834,1084,5045,475,409,5604,5382,5486,5551,5437,150,218,4589,4737,4510,4667", "endColumns": "74,76,71,71,53,42,48,46,50,48,60,44,48,44,48,56,60,52,52,56,64,77,77,46,48,48,46,56,54,45,43,45,43,45,43,47,43,58,47,41,45,47,66,72,66,63,59,59,55,64,65,59,65,62,60,51,45,51,66,71,54,75,61,69,71,67,77,71,49,45,47,49,50,48,46,50,46,52,48,64,50,50,46,68,53,55,60,58,65,65,48,54,64,52,48,67,61,77,66,78,69", "endOffsets": "6085,6306,6229,6157,104,1634,1446,1779,1497,1546,1897,1591,1732,320,1683,1836,2461,2241,2294,2351,799,1001,1079,2614,2400,2567,2188,2518,2669,2916,2776,3052,3006,2822,3236,3192,3096,858,3144,2732,2962,2870,1235,1308,1375,5922,5737,5982,5793,5858,5165,5225,5291,5354,381,609,655,707,3986,3919,4393,4204,4338,4128,4276,3847,4471,4058,2068,2018,2116,1972,3311,3756,3607,3362,3654,3707,3458,923,3509,3560,3409,4954,5008,4885,1140,5099,536,470,5648,5432,5546,5599,5481,213,275,4662,4799,4584,4732"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,207,279,351,405,448,497,544,595,644,705,750,799,844,893,950,1011,1064,1117,1174,1239,1317,1395,1442,1491,1540,1587,1644,1699,1745,1789,1835,1879,1925,1969,2017,2061,2120,2168,2210,2256,2304,2371,2444,2511,2575,2635,2695,2751,2816,2882,2942,3008,3071,3132,3184,3230,3282,3349,3421,3476,3552,3614,3684,3756,3824,3902,3974,4024,4070,4118,4168,4219,4268,4315,4366,4413,4466,4515,4580,4631,4682,4729,4798,4852,4908,4969,5028,5094,5160,5209,5264,5329,5382,5431,5499,5561,5639,5706,5785", "endColumns": "74,76,71,71,53,42,48,46,50,48,60,44,48,44,48,56,60,52,52,56,64,77,77,46,48,48,46,56,54,45,43,45,43,45,43,47,43,58,47,41,45,47,66,72,66,63,59,59,55,64,65,59,65,62,60,51,45,51,66,71,54,75,61,69,71,67,77,71,49,45,47,49,50,48,46,50,46,52,48,64,50,50,46,68,53,55,60,58,65,65,48,54,64,52,48,67,61,77,66,78,69", "endOffsets": "125,202,274,346,400,443,492,539,590,639,700,745,794,839,888,945,1006,1059,1112,1169,1234,1312,1390,1437,1486,1535,1582,1639,1694,1740,1784,1830,1874,1920,1964,2012,2056,2115,2163,2205,2251,2299,2366,2439,2506,2570,2630,2690,2746,2811,2877,2937,3003,3066,3127,3179,3225,3277,3344,3416,3471,3547,3609,3679,3751,3819,3897,3969,4019,4065,4113,4163,4214,4263,4310,4361,4408,4461,4510,4575,4626,4677,4724,4793,4847,4903,4964,5023,5089,5155,5204,5259,5324,5377,5426,5494,5556,5634,5701,5780,5850"}}]}]}