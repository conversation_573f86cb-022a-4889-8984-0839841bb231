// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    alias(libs.plugins.android.application) apply false
    alias(libs.plugins.kotlin.android) apply false
    alias(libs.plugins.kotlin.compose) apply false
}

// 全局编码配置
allprojects {
    tasks.withType(JavaCompile) {
        options.encoding = 'UTF-8'
    }

    tasks.withType(Test) {
        systemProperty 'file.encoding', 'UTF-8'
    }
}

// 设置JVM编码
System.setProperty('file.encoding', 'UTF-8')