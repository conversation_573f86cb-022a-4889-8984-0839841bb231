<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.wardrobe.app"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="35" />

    <!-- 照片相关权限 -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.CAMERA" />

    <!-- 如果您的应用需要访问网络，请取消注释以下权限 -->
    <!-- <uses-permission android:name="android.permission.INTERNET" /> -->


    <!-- 相机功能声明 -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <permission
        android:name="com.wardrobe.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.wardrobe.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- To allow posting notifications on Android 13 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <application
        android:name="com.wardrobe.app.WardrobeApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.MaterialComponents.DayNight.NoActionBar" >
        <activity
            android:name="com.wardrobe.app.ui.activities.MainActivity"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.wardrobe.app.ui.activities.AddClothingActivity"
            android:exported="false" />
        <activity
            android:name="com.wardrobe.app.ui.activities.CategorySelectActivity"
            android:exported="false" />
        <activity
            android:name="com.wardrobe.app.ui.activities.ColorSelectActivity"
            android:exported="false" />
        <activity
            android:name="com.wardrobe.app.ui.activities.OccasionSelectActivity"
            android:exported="false" />
        <activity
            android:name="com.wardrobe.app.ui.activities.ClothingDetailActivity"
            android:exported="false" />

        <!-- 如果您有 BatchAddClothingActivity，请确保它已声明 -->
        <activity
            android:name="com.wardrobe.app.ui.activities.BatchAddClothingActivity"
            android:exported="false" />

        <!-- FileProvider for secure file sharing -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.wardrobe.app.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- 请确保您应用中所有其他的 Activity, Service, Receiver, Provider 都在此处声明 -->

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.wardrobe.app.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>
        <provider
            android:name="leakcanary.internal.LeakCanaryFileProvider"
            android:authorities="com.squareup.leakcanary.fileprovider.com.wardrobe.app"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/leak_canary_file_paths" />
        </provider>

        <activity
            android:name="leakcanary.internal.activity.LeakActivity"
            android:exported="true"
            android:icon="@mipmap/leak_canary_icon"
            android:label="@string/leak_canary_display_activity_label"
            android:taskAffinity="com.squareup.leakcanary.com.wardrobe.app"
            android:theme="@style/leak_canary_LeakCanary.Base" >
            <intent-filter android:label="@string/leak_canary_import_hprof_file" >
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="file" />
                <data android:scheme="content" />
                <data android:mimeType="*/*" />
                <data android:host="*" />
                <data android:pathPattern=".*\\.hprof" />
                <data android:pathPattern=".*\\..*\\.hprof" />
                <data android:pathPattern=".*\\..*\\..*\\.hprof" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\.hprof" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.hprof" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\.hprof" />
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\..*\\..*\\.hprof" />
                <!--
            Since hprof isn't a standard MIME type, we have to declare such patterns.
            Most file providers will generate URIs including their own package name,
            which contains `.` characters that must be explicitly escaped in pathPattern.
            @see https://stackoverflow.com/a/31028507/703646
                -->
            </intent-filter>
        </activity>

        <activity-alias
            android:name="leakcanary.internal.activity.LeakLauncherActivity"
            android:banner="@drawable/leak_canary_tv_icon"
            android:enabled="@bool/leak_canary_add_launcher_icon"
            android:exported="true"
            android:icon="@mipmap/leak_canary_icon"
            android:label="@string/leak_canary_display_activity_label"
            android:targetActivity="leakcanary.internal.activity.LeakActivity"
            android:taskAffinity="com.squareup.leakcanary.com.wardrobe.app"
            android:theme="@style/leak_canary_LeakCanary.Base" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
                <!-- Android TV launcher intent -->
                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>
        </activity-alias>

        <activity
            android:name="leakcanary.internal.RequestPermissionActivity"
            android:excludeFromRecents="true"
            android:icon="@mipmap/leak_canary_icon"
            android:label="@string/leak_canary_storage_permission_activity_label"
            android:taskAffinity="com.squareup.leakcanary.com.wardrobe.app"
            android:theme="@style/leak_canary_Theme.Transparent" />

        <receiver android:name="leakcanary.internal.NotificationReceiver" />

        <provider
            android:name="leakcanary.internal.MainProcessAppWatcherInstaller"
            android:authorities="com.wardrobe.app.leakcanary-installer"
            android:enabled="@bool/leak_canary_watcher_auto_install"
            android:exported="false" />
        <provider
            android:name="leakcanary.internal.PlumberInstaller"
            android:authorities="com.wardrobe.app.plumber-installer"
            android:enabled="@bool/leak_canary_plumber_auto_install"
            android:exported="false" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>