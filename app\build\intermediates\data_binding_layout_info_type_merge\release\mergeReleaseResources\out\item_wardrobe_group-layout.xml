<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_wardrobe_group" modulePackage="com.wardrobe.app" filePath="app\src\main\res\layout\item_wardrobe_group.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_wardrobe_group_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="45" endOffset="14"/></Target><Target id="@+id/tv_category_title" view="TextView"><Expressions/><location startLine="16" startOffset="8" endLine="24" endOffset="38"/></Target><Target id="@+id/tv_item_count" view="TextView"><Expressions/><location startLine="26" startOffset="8" endLine="32" endOffset="37"/></Target><Target id="@+id/rv_clothing_items" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="37" startOffset="4" endLine="43" endOffset="34"/></Target></Targets></Layout>