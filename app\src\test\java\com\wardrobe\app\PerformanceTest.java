package com.wardrobe.app;

import android.content.Context;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.wardrobe.app.data.ClothingManager;
import com.wardrobe.app.model.ClothingItem;
import com.wardrobe.app.utils.ImageOptimizer;
import com.wardrobe.app.utils.MemoryOptimizer;
import com.wardrobe.app.utils.PerformanceMonitor;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;

/**
 * 性能测试类
 * 测试应用的各项性能指标
 */
@RunWith(AndroidJUnit4.class)
public class PerformanceTest {
    
    private Context context;
    private PerformanceMonitor performanceMonitor;
    private ClothingManager clothingManager;
    private MemoryOptimizer memoryOptimizer;
    
    @Before
    public void setUp() {
        context = ApplicationProvider.getApplicationContext();
        performanceMonitor = PerformanceMonitor.getInstance(context);
        clothingManager = ClothingManager.getInstance(context);
        memoryOptimizer = MemoryOptimizer.getInstance(context);
    }
    
    /**
     * 测试数据加载性能
     */
    @Test
    public void testDataLoadingPerformance() {
        performanceMonitor.startOperation("DataLoading", "Performance");
        
        // 模拟加载大量数据
        List<ClothingItem> items = clothingManager.getClothingList();
        
        performanceMonitor.endOperation("DataLoading", "Performance");
        
        // 验证数据加载时间不超过1秒
        // 这里可以通过PerformanceMonitor获取实际耗时进行断言
        assertNotNull("数据加载不应为空", items);
    }
    
    /**
     * 测试内存使用情况
     */
    @Test
    public void testMemoryUsage() {
        MemoryOptimizer.MemoryInfo initialMemory = memoryOptimizer.getCurrentMemoryInfo();
        
        // 创建大量对象测试内存使用
        List<ClothingItem> testItems = new ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            ClothingItem item = new ClothingItem();
            item.setName("测试衣物 " + i);
            item.setCategory("测试分类");
            item.setColor("测试颜色");
            testItems.add(item);
        }
        
        MemoryOptimizer.MemoryInfo afterMemory = memoryOptimizer.getCurrentMemoryInfo();
        
        // 验证内存使用在合理范围内
        long memoryIncrease = afterMemory.usedMemory - initialMemory.usedMemory;
        assertTrue("内存增长应在合理范围内", memoryIncrease < 50 * 1024 * 1024); // 50MB
        
        // 清理测试数据
        testItems.clear();
        System.gc();
    }
    
    /**
     * 测试图片处理性能
     */
    @Test
    public void testImageProcessingPerformance() throws InterruptedException {
        ImageOptimizer imageOptimizer = ImageOptimizer.getInstance(context);
        CountDownLatch latch = new CountDownLatch(1);
        
        performanceMonitor.startOperation("ImageProcessing", "Performance");
        
        // 模拟图片处理（这里需要实际的图片文件）
        // 由于测试环境限制，这里只测试基本功能
        
        performanceMonitor.endOperation("ImageProcessing", "Performance");
        
        // 等待异步操作完成
        boolean completed = latch.await(5, TimeUnit.SECONDS);
        assertTrue("图片处理应在5秒内完成", completed);
    }
    
    /**
     * 测试并发操作性能
     */
    @Test
    public void testConcurrentOperations() throws InterruptedException {
        int threadCount = 10;
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        performanceMonitor.startOperation("ConcurrentOperations", "Performance");
        
        // 启动多个线程同时操作
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            new Thread(() -> {
                try {
                    // 模拟并发数据操作
                    ClothingItem item = new ClothingItem();
                    item.setName("并发测试 " + threadId);
                    item.setCategory("测试");
                    item.setColor("红色");
                    
                    // 模拟一些处理时间
                    Thread.sleep(100);
                    
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    latch.countDown();
                }
            }).start();
        }
        
        // 等待所有线程完成
        boolean completed = latch.await(10, TimeUnit.SECONDS);
        
        performanceMonitor.endOperation("ConcurrentOperations", "Performance");
        
        assertTrue("并发操作应在10秒内完成", completed);
    }
    
    /**
     * 测试启动时间性能
     */
    @Test
    public void testStartupPerformance() {
        // 记录启动时间
        PerformanceMonitor.recordAppStartTime();
        
        // 模拟应用初始化过程
        performanceMonitor.startOperation("AppInitialization", "Startup");
        
        // 模拟服务初始化
        try {
            Thread.sleep(500); // 模拟初始化耗时
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        performanceMonitor.endOperation("AppInitialization", "Startup");
        PerformanceMonitor.recordFirstActivityTime();
        
        // 验证启动时间合理
        // 实际项目中可以通过PerformanceMonitor获取具体启动时间
    }
    
    /**
     * 测试内存泄漏检测
     */
    @Test
    public void testMemoryLeakDetection() {
        MemoryOptimizer.MemoryInfo beforeMemory = memoryOptimizer.getCurrentMemoryInfo();
        
        // 创建可能导致内存泄漏的对象
        List<Object> potentialLeaks = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            potentialLeaks.add(new Object());
        }
        
        // 检查内存泄漏风险
        boolean hasLeakRisk = memoryOptimizer.checkMemoryLeakRisk();
        
        // 清理对象
        potentialLeaks.clear();
        potentialLeaks = null;
        System.gc();
        
        // 等待GC完成
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        MemoryOptimizer.MemoryInfo afterMemory = memoryOptimizer.getCurrentMemoryInfo();
        
        // 验证内存是否正确释放
        assertFalse("不应检测到严重的内存泄漏风险", 
                afterMemory.usagePercentage > 90);
    }
    
    /**
     * 测试UI响应性能
     */
    @Test
    public void testUIResponsiveness() {
        performanceMonitor.startOperation("UIOperation", "UI");
        
        // 模拟UI操作
        try {
            // 模拟UI更新操作
            Thread.sleep(50); // 模拟UI渲染时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        performanceMonitor.endOperation("UIOperation", "UI");
        
        // UI操作应该很快完成
        // 实际测试中可以验证具体的响应时间
    }
    
    /**
     * 性能基准测试
     */
    @Test
    public void testPerformanceBenchmark() {
        // 运行一系列基准测试
        performanceMonitor.startOperation("Benchmark", "Performance");
        
        // CPU密集型操作测试
        long start = System.currentTimeMillis();
        for (int i = 0; i < 1000000; i++) {
            Math.sqrt(i);
        }
        long cpuTime = System.currentTimeMillis() - start;
        
        // 内存分配测试
        start = System.currentTimeMillis();
        List<byte[]> memoryTest = new ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            memoryTest.add(new byte[1024]); // 1KB
        }
        long memoryTime = System.currentTimeMillis() - start;
        
        performanceMonitor.endOperation("Benchmark", "Performance");
        
        // 验证性能在可接受范围内
        assertTrue("CPU操作应在合理时间内完成", cpuTime < 5000); // 5秒
        assertTrue("内存分配应在合理时间内完成", memoryTime < 1000); // 1秒
        
        // 清理测试数据
        memoryTest.clear();
    }
}
