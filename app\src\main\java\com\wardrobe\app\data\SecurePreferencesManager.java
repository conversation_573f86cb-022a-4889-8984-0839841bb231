package com.wardrobe.app.data;

import android.content.Context;
import android.content.SharedPreferences;
import androidx.security.crypto.EncryptedSharedPreferences;
import androidx.security.crypto.MasterKey;
import com.wardrobe.app.utils.Logger;

import java.io.IOException;
import java.security.GeneralSecurityException;

/**
 * 安全的SharedPreferences管理器
 * 使用EncryptedSharedPreferences加密存储敏感数据
 * 提供线程安全的数据读写操作
 */
public class SecurePreferencesManager {
    
    private static final String TAG = "SecurePreferencesManager";
    private static final String SECURE_PREFS_NAME = "wardrobe_secure_prefs";
    
    private static volatile SecurePreferencesManager instance;
    private static final Object LOCK = new Object();
    
    private final SharedPreferences encryptedPrefs;
    private final Object operationLock = new Object();
    
    private SecurePreferencesManager(Context context) throws GeneralSecurityException, IOException {
        // 创建或获取主密钥
        MasterKey masterKey = new MasterKey.Builder(context.getApplicationContext())
                .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
                .build();
        
        // 创建加密的SharedPreferences
        encryptedPrefs = EncryptedSharedPreferences.create(
                context.getApplicationContext(),
                SECURE_PREFS_NAME,
                masterKey,
                EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
        );
        
        Logger.d(TAG, "SecurePreferencesManager 初始化完成");
    }
    
    /**
     * 获取SecurePreferencesManager实例
     * 
     * @param context 上下文
     * @return SecurePreferencesManager实例
     * @throws SecurityException 如果加密初始化失败
     */
    public static SecurePreferencesManager getInstance(Context context) throws SecurityException {
        if (instance == null) {
            synchronized (LOCK) {
                if (instance == null) {
                    try {
                        instance = new SecurePreferencesManager(context);
                    } catch (GeneralSecurityException | IOException e) {
                        Logger.e(TAG, "初始化加密存储失败", e);
                        throw new SecurityException("无法初始化安全存储", e);
                    }
                }
            }
        }
        return instance;
    }
    
    /**
     * 安全地写入字符串数据
     * 
     * @param key 键
     * @param value 值
     * @return 是否写入成功
     */
    public boolean writeSecureString(String key, String value) {
        synchronized (operationLock) {
            try {
                SharedPreferences.Editor editor = encryptedPrefs.edit();
                editor.putString(key, value);
                boolean success = editor.commit();
                
                if (success) {
                    Logger.d(TAG, "安全写入数据成功: " + key);
                } else {
                    Logger.e(TAG, "安全写入数据失败: " + key);
                }
                
                return success;
            } catch (Exception e) {
                Logger.e(TAG, "安全写入数据异常: " + key, e);
                return false;
            }
        }
    }
    
    /**
     * 安全地读取字符串数据
     * 
     * @param key 键
     * @param defaultValue 默认值
     * @return 读取的值或默认值
     */
    public String readSecureString(String key, String defaultValue) {
        synchronized (operationLock) {
            try {
                String value = encryptedPrefs.getString(key, defaultValue);
                Logger.d(TAG, "安全读取数据成功: " + key);
                return value;
            } catch (Exception e) {
                Logger.e(TAG, "安全读取数据异常: " + key, e);
                return defaultValue;
            }
        }
    }
    
    /**
     * 安全地写入布尔值
     * 
     * @param key 键
     * @param value 值
     * @return 是否写入成功
     */
    public boolean writeSecureBoolean(String key, boolean value) {
        synchronized (operationLock) {
            try {
                SharedPreferences.Editor editor = encryptedPrefs.edit();
                editor.putBoolean(key, value);
                boolean success = editor.commit();
                
                if (success) {
                    Logger.d(TAG, "安全写入布尔值成功: " + key);
                } else {
                    Logger.e(TAG, "安全写入布尔值失败: " + key);
                }
                
                return success;
            } catch (Exception e) {
                Logger.e(TAG, "安全写入布尔值异常: " + key, e);
                return false;
            }
        }
    }
    
    /**
     * 安全地读取布尔值
     * 
     * @param key 键
     * @param defaultValue 默认值
     * @return 读取的值或默认值
     */
    public boolean readSecureBoolean(String key, boolean defaultValue) {
        synchronized (operationLock) {
            try {
                boolean value = encryptedPrefs.getBoolean(key, defaultValue);
                Logger.d(TAG, "安全读取布尔值成功: " + key);
                return value;
            } catch (Exception e) {
                Logger.e(TAG, "安全读取布尔值异常: " + key, e);
                return defaultValue;
            }
        }
    }
    
    /**
     * 安全地写入整数
     * 
     * @param key 键
     * @param value 值
     * @return 是否写入成功
     */
    public boolean writeSecureInt(String key, int value) {
        synchronized (operationLock) {
            try {
                SharedPreferences.Editor editor = encryptedPrefs.edit();
                editor.putInt(key, value);
                boolean success = editor.commit();
                
                if (success) {
                    Logger.d(TAG, "安全写入整数成功: " + key);
                } else {
                    Logger.e(TAG, "安全写入整数失败: " + key);
                }
                
                return success;
            } catch (Exception e) {
                Logger.e(TAG, "安全写入整数异常: " + key, e);
                return false;
            }
        }
    }
    
    /**
     * 安全地读取整数
     * 
     * @param key 键
     * @param defaultValue 默认值
     * @return 读取的值或默认值
     */
    public int readSecureInt(String key, int defaultValue) {
        synchronized (operationLock) {
            try {
                int value = encryptedPrefs.getInt(key, defaultValue);
                Logger.d(TAG, "安全读取整数成功: " + key);
                return value;
            } catch (Exception e) {
                Logger.e(TAG, "安全读取整数异常: " + key, e);
                return defaultValue;
            }
        }
    }
    
    /**
     * 安全地删除指定键的数据
     * 
     * @param key 要删除的键
     * @return 是否删除成功
     */
    public boolean removeSecure(String key) {
        synchronized (operationLock) {
            try {
                SharedPreferences.Editor editor = encryptedPrefs.edit();
                editor.remove(key);
                boolean success = editor.commit();
                
                if (success) {
                    Logger.d(TAG, "安全删除数据成功: " + key);
                } else {
                    Logger.e(TAG, "安全删除数据失败: " + key);
                }
                
                return success;
            } catch (Exception e) {
                Logger.e(TAG, "安全删除数据异常: " + key, e);
                return false;
            }
        }
    }
    
    /**
     * 安全地清除所有数据
     * 
     * @return 是否清除成功
     */
    public boolean clearSecure() {
        synchronized (operationLock) {
            try {
                SharedPreferences.Editor editor = encryptedPrefs.edit();
                editor.clear();
                boolean success = editor.commit();
                
                if (success) {
                    Logger.d(TAG, "安全清除所有数据成功");
                } else {
                    Logger.e(TAG, "安全清除所有数据失败");
                }
                
                return success;
            } catch (Exception e) {
                Logger.e(TAG, "安全清除所有数据异常", e);
                return false;
            }
        }
    }
    
    /**
     * 检查是否包含指定键
     * 
     * @param key 键
     * @return 是否包含该键
     */
    public boolean containsSecure(String key) {
        synchronized (operationLock) {
            try {
                boolean contains = encryptedPrefs.contains(key);
                Logger.d(TAG, "检查键存在性: " + key + " = " + contains);
                return contains;
            } catch (Exception e) {
                Logger.e(TAG, "检查键存在性异常: " + key, e);
                return false;
            }
        }
    }
    
    /**
     * 获取所有键的数量
     * 
     * @return 键的数量
     */
    public int getSecureKeyCount() {
        synchronized (operationLock) {
            try {
                int count = encryptedPrefs.getAll().size();
                Logger.d(TAG, "安全存储键数量: " + count);
                return count;
            } catch (Exception e) {
                Logger.e(TAG, "获取安全存储键数量异常", e);
                return 0;
            }
        }
    }
}
