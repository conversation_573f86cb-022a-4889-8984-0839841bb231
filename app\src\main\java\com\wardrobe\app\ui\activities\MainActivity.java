package com.wardrobe.app.ui.activities;

import android.os.Bundle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.wardrobe.app.R;
import com.wardrobe.app.ui.fragments.CalendarFragment;
import com.wardrobe.app.ui.fragments.OutfitFragment;
import com.wardrobe.app.ui.fragments.ProfileFragment;
import com.wardrobe.app.ui.fragments.WardrobeFragment;

/**
 * 主Activity
 * 负责管理底部导航和Fragment切换
 * 优化了Fragment缓存和内存管理
 */
public class MainActivity extends AppCompatActivity {

    private static final String TAG = "MainActivity";
    private FragmentManager fragmentManager;
    private BottomNavigationView bottomNavigation;
    
    // Fragment缓存，避免重复创建
    private WardrobeFragment wardrobeFragment;
    private OutfitFragment outfitFragment;
    private CalendarFragment calendarFragment;
    private ProfileFragment profileFragment;
    
    // 当前显示的Fragment标签
    private String currentFragmentTag = "wardrobe";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // 初始化Fragment管理器
        fragmentManager = getSupportFragmentManager();

        // 初始化底部导航栏
        initBottomNavigation();
        
        // 恢复Fragment状态或加载默认Fragment
        if (savedInstanceState != null) {
            currentFragmentTag = savedInstanceState.getString("current_fragment", "wardrobe");
        }
        
        // 延迟加载默认Fragment，提升启动速度
        getWindow().getDecorView().post(() -> {
            loadFragmentByTag(currentFragmentTag);
        });
    }

    /**
     * 初始化底部导航栏
     */
    private void initBottomNavigation() {
        bottomNavigation = findViewById(R.id.bottom_navigation);

        // 设置导航栏点击监听器
        bottomNavigation.setOnItemSelectedListener(item -> {
            String fragmentTag = null;
            int itemId = item.getItemId();

            if (itemId == R.id.nav_wardrobe) {
                fragmentTag = "wardrobe";
            } else if (itemId == R.id.nav_outfit) {
                fragmentTag = "outfit";
            } else if (itemId == R.id.nav_calendar) {
                fragmentTag = "calendar";
            } else if (itemId == R.id.nav_profile) {
                fragmentTag = "profile";
            }

            if (fragmentTag != null && !fragmentTag.equals(currentFragmentTag)) {
                loadFragmentByTag(fragmentTag);
                currentFragmentTag = fragmentTag;
                return true;
            }
            return false;
        });
    }

    /**
     * 根据标签加载Fragment
     * @param fragmentTag Fragment标签
     */
    private void loadFragmentByTag(String fragmentTag) {
        Fragment fragment = getFragmentByTag(fragmentTag);
        if (fragment != null) {
            loadFragment(fragment, fragmentTag);
        }
    }

    /**
     * 根据标签获取Fragment实例
     * @param fragmentTag Fragment标签
     * @return Fragment实例
     */
    private Fragment getFragmentByTag(String fragmentTag) {
        // 先从FragmentManager中查找已存在的Fragment
        Fragment existingFragment = fragmentManager.findFragmentByTag(fragmentTag);
        if (existingFragment != null) {
            return existingFragment;
        }

        // 如果不存在，创建新的Fragment实例
        switch (fragmentTag) {
            case "wardrobe":
                if (wardrobeFragment == null) {
                    wardrobeFragment = new WardrobeFragment();
                }
                return wardrobeFragment;
            case "outfit":
                if (outfitFragment == null) {
                    outfitFragment = new OutfitFragment();
                }
                return outfitFragment;
            case "calendar":
                if (calendarFragment == null) {
                    calendarFragment = new CalendarFragment();
                }
                return calendarFragment;
            case "profile":
                if (profileFragment == null) {
                    profileFragment = new ProfileFragment();
                }
                return profileFragment;
            default:
                return new WardrobeFragment();
        }
    }

    /**
     * 加载Fragment的方法
     * 优化了Fragment切换性能
     * @param fragment 要加载的Fragment
     * @param tag Fragment标签
     */
    private void loadFragment(Fragment fragment, String tag) {
        FragmentTransaction transaction = fragmentManager.beginTransaction();
        
        // 隐藏所有Fragment
        hideAllFragments(transaction);
        
        // 显示目标Fragment
        if (fragment.isAdded()) {
            transaction.show(fragment);
        } else {
            transaction.add(R.id.fragment_container, fragment, tag);
        }
        
        // 设置转场动画
        transaction.setCustomAnimations(
            R.anim.fade_in,    // enter
            R.anim.fade_out,   // exit
            R.anim.fade_in,    // popEnter
            R.anim.fade_out    // popExit
        );
        
        // 添加到回退栈，但不立即执行
        transaction.addToBackStack(tag);
        
        // 提交事务
        transaction.commitAllowingStateLoss();
    }

    /**
     * 隐藏所有Fragment
     * @param transaction Fragment事务
     */
    private void hideAllFragments(FragmentTransaction transaction) {
        if (wardrobeFragment != null && wardrobeFragment.isAdded()) {
            transaction.hide(wardrobeFragment);
        }
        if (outfitFragment != null && outfitFragment.isAdded()) {
            transaction.hide(outfitFragment);
        }
        if (calendarFragment != null && calendarFragment.isAdded()) {
            transaction.hide(calendarFragment);
        }
        if (profileFragment != null && profileFragment.isAdded()) {
            transaction.hide(profileFragment);
        }
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        // 保存当前Fragment状态
        outState.putString("current_fragment", currentFragmentTag);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 清理Fragment引用，避免内存泄漏
        wardrobeFragment = null;
        outfitFragment = null;
        calendarFragment = null;
        profileFragment = null;
    }

    @Override
    public void onBackPressed() {
        // 如果回退栈中有Fragment，则返回上一个Fragment
        if (fragmentManager.getBackStackEntryCount() > 1) {
            fragmentManager.popBackStack();
            // 更新当前Fragment标签
            Fragment currentFragment = fragmentManager.findFragmentById(R.id.fragment_container);
            if (currentFragment != null) {
                currentFragmentTag = currentFragment.getTag();
            }
        } else {
            super.onBackPressed();
        }
    }
}
