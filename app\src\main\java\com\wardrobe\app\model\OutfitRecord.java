package com.wardrobe.app.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 穿搭记录模型
 * 记录用户某一天的完整穿搭信息
 */
public class OutfitRecord implements Parcelable {
    
    private String id;
    private Date date;                    // 穿搭日期
    private String occasion;              // 场合
    private String weather;               // 天气
    private String mood;                  // 心情
    private List<String> clothingItemIds; // 衣物ID列表
    private String notes;                 // 备注
    private int rating;                   // 满意度评分 (1-5)
    private long timestamp;               // 创建时间戳
    private String imageUri;              // 整体搭配照片
    private List<String> tags;            // 标签
    
    // 默认构造函数
    public OutfitRecord() {
        this.id = UUID.randomUUID().toString();
        this.date = new Date();
        this.clothingItemIds = new ArrayList<>();
        this.tags = new ArrayList<>();
        this.timestamp = System.currentTimeMillis();
        this.rating = 0;
    }
    
    // 完整构造函数
    public OutfitRecord(Date date, String occasion, String weather, String mood,
                       List<String> clothingItemIds, String notes, int rating) {
        this();
        this.date = date;
        this.occasion = occasion;
        this.weather = weather;
        this.mood = mood;
        this.clothingItemIds = clothingItemIds != null ? new ArrayList<>(clothingItemIds) : new ArrayList<>();
        this.notes = notes;
        this.rating = rating;
    }
    
    // Parcelable构造函数
    protected OutfitRecord(Parcel in) {
        id = in.readString();
        long dateTime = in.readLong();
        date = dateTime != -1 ? new Date(dateTime) : null;
        occasion = in.readString();
        weather = in.readString();
        mood = in.readString();
        clothingItemIds = in.createStringArrayList();
        notes = in.readString();
        rating = in.readInt();
        timestamp = in.readLong();
        imageUri = in.readString();
        tags = in.createStringArrayList();
    }
    
    // Getters
    public String getId() { return id; }
    public Date getDate() { return date; }
    public String getOccasion() { return occasion; }
    public String getWeather() { return weather; }
    public String getMood() { return mood; }
    public List<String> getClothingItemIds() { return clothingItemIds; }
    public String getNotes() { return notes; }
    public int getRating() { return rating; }
    public long getTimestamp() { return timestamp; }
    public String getImageUri() { return imageUri; }
    public List<String> getTags() { return tags; }
    
    // Setters
    public void setId(String id) { this.id = id; }
    public void setDate(Date date) { this.date = date; }
    public void setOccasion(String occasion) { this.occasion = occasion; }
    public void setWeather(String weather) { this.weather = weather; }
    public void setMood(String mood) { this.mood = mood; }
    public void setClothingItemIds(List<String> clothingItemIds) { 
        this.clothingItemIds = clothingItemIds != null ? new ArrayList<>(clothingItemIds) : new ArrayList<>(); 
    }
    public void setNotes(String notes) { this.notes = notes; }
    public void setRating(int rating) { this.rating = Math.max(0, Math.min(5, rating)); }
    public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    public void setImageUri(String imageUri) { this.imageUri = imageUri; }
    public void setTags(List<String> tags) { 
        this.tags = tags != null ? new ArrayList<>(tags) : new ArrayList<>(); 
    }
    
    // 业务方法
    
    /**
     * 添加衣物到穿搭
     */
    public void addClothingItem(String clothingItemId) {
        if (clothingItemId != null && !clothingItemIds.contains(clothingItemId)) {
            clothingItemIds.add(clothingItemId);
        }
    }
    
    /**
     * 从穿搭中移除衣物
     */
    public void removeClothingItem(String clothingItemId) {
        clothingItemIds.remove(clothingItemId);
    }
    
    /**
     * 检查是否包含指定衣物
     */
    public boolean containsClothingItem(String clothingItemId) {
        return clothingItemIds.contains(clothingItemId);
    }
    
    /**
     * 获取穿搭中的衣物数量
     */
    public int getClothingItemCount() {
        return clothingItemIds.size();
    }
    
    /**
     * 添加标签
     */
    public void addTag(String tag) {
        if (tag != null && !tag.trim().isEmpty() && !tags.contains(tag)) {
            tags.add(tag.trim());
        }
    }
    
    /**
     * 移除标签
     */
    public void removeTag(String tag) {
        tags.remove(tag);
    }
    
    /**
     * 检查是否有效的穿搭记录
     */
    public boolean isValid() {
        return date != null && 
               clothingItemIds != null && 
               !clothingItemIds.isEmpty() &&
               rating >= 0 && rating <= 5;
    }
    
    /**
     * 获取日期的字符串表示
     */
    public String getDateString() {
        if (date == null) return "";
        return android.text.format.DateFormat.format("yyyy-MM-dd", date).toString();
    }
    
    /**
     * 获取显示用的简短描述
     */
    public String getDisplayDescription() {
        StringBuilder desc = new StringBuilder();
        if (occasion != null && !occasion.isEmpty()) {
            desc.append(occasion);
        }
        if (weather != null && !weather.isEmpty()) {
            if (desc.length() > 0) desc.append(" · ");
            desc.append(weather);
        }
        if (clothingItemIds != null) {
            if (desc.length() > 0) desc.append(" · ");
            desc.append(clothingItemIds.size()).append("件衣物");
        }
        return desc.toString();
    }
    
    // Parcelable实现
    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(id);
        dest.writeLong(date != null ? date.getTime() : -1);
        dest.writeString(occasion);
        dest.writeString(weather);
        dest.writeString(mood);
        dest.writeStringList(clothingItemIds);
        dest.writeString(notes);
        dest.writeInt(rating);
        dest.writeLong(timestamp);
        dest.writeString(imageUri);
        dest.writeStringList(tags);
    }
    
    @Override
    public int describeContents() {
        return 0;
    }
    
    public static final Creator<OutfitRecord> CREATOR = new Creator<OutfitRecord>() {
        @Override
        public OutfitRecord createFromParcel(Parcel in) {
            return new OutfitRecord(in);
        }
        
        @Override
        public OutfitRecord[] newArray(int size) {
            return new OutfitRecord[size];
        }
    };
    
    // equals和hashCode
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        OutfitRecord that = (OutfitRecord) obj;
        return id != null ? id.equals(that.id) : that.id == null;
    }
    
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
    
    @Override
    public String toString() {
        return "OutfitRecord{" +
                "id='" + id + '\'' +
                ", date=" + date +
                ", occasion='" + occasion + '\'' +
                ", weather='" + weather + '\'' +
                ", clothingItemCount=" + (clothingItemIds != null ? clothingItemIds.size() : 0) +
                ", rating=" + rating +
                '}';
    }
}
