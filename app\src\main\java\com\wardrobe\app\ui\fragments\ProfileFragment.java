package com.wardrobe.app.ui.fragments;

import android.app.AlertDialog;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

import com.wardrobe.app.R;
import com.wardrobe.app.data.ClothingManager;
import com.wardrobe.app.model.ClothingItem;
import com.wardrobe.app.utils.ImageManager;
import com.wardrobe.app.utils.ErrorUtils;
import com.wardrobe.app.viewmodel.ProfileViewModel;
import com.wardrobe.app.databinding.FragmentProfileBinding;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 个人资料页面
 * 提供存储空间管理、应用信息等功能
 */
public class ProfileFragment extends Fragment {
    
    private static final String TAG = "ProfileFragment";

    // DataBinding和ViewModel
    private FragmentProfileBinding binding;
    private ProfileViewModel profileViewModel;

    // 保留兼容性
    private ClothingManager clothingManager;
    private SharedPreferences preferences;
    private TextView tvThemeMode;
    private TextView tvLanguage;
    
    public ProfileFragment() {
        // Required empty public constructor
    }
    
    public static ProfileFragment newInstance() {
        return new ProfileFragment();
    }
    
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        clothingManager = ClothingManager.getInstance(requireContext());
    }
    
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_profile, container, false);

        // 初始化ViewModel
        profileViewModel = new ViewModelProvider(this).get(ProfileViewModel.class);

        return binding.getRoot();
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // 设置DataBinding
        binding.setViewModel(profileViewModel);
        binding.setLifecycleOwner(this);

        preferences = requireContext().getSharedPreferences("wardrobe_prefs", Context.MODE_PRIVATE);

        setupObservers();
        setupListeners();

        // 加载数据
        profileViewModel.loadAllData();
    }

    /**
     * 设置ViewModel观察者
     */
    private void setupObservers() {
        // 观察存储信息
        profileViewModel.getStorageInfo().observe(getViewLifecycleOwner(), storageInfo -> {
            if (storageInfo != null) {
                binding.setStorageInfo(storageInfo);
            }
        });

        // 观察应用统计
        profileViewModel.getAppStatistics().observe(getViewLifecycleOwner(), appStats -> {
            if (appStats != null) {
                binding.setAppStats(appStats);
            }
        });

        // 观察用户设置
        profileViewModel.getUserSettings().observe(getViewLifecycleOwner(), userSettings -> {
            if (userSettings != null) {
                binding.setUserSettings(userSettings);
                updateUIFromSettings(userSettings);
            }
        });

        // 观察错误消息
        profileViewModel.getErrorMessage().observe(getViewLifecycleOwner(), error -> {
            if (error != null && !error.isEmpty()) {
                Toast.makeText(requireContext(), error, Toast.LENGTH_SHORT).show();
            }
        });

        // 观察成功消息
        profileViewModel.getSuccessMessage().observe(getViewLifecycleOwner(), message -> {
            if (message != null && !message.isEmpty()) {
                Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show();
            }
        });
    }

    /**
     * 根据设置更新UI
     */
    private void updateUIFromSettings(ProfileViewModel.UserSettings settings) {
        // 这里可以更新开关状态等UI元素
        // 由于我们使用了DataBinding，大部分更新会自动进行
    }

    private void initViews(View view) {
        tvStorageInfo = view.findViewById(R.id.tv_storage_info);
        tvImageCount = view.findViewById(R.id.tv_image_count);
        tvClothingCount = view.findViewById(R.id.tv_clothing_count);
        btnCleanupImages = view.findViewById(R.id.btn_cleanup_images);
        btnCompressImages = view.findViewById(R.id.btn_compress_images);
        btnCleanupUnused = view.findViewById(R.id.btn_cleanup_unused);
        
        switchDarkMode = view.findViewById(R.id.switch_dark_mode);
        switchNotifications = view.findViewById(R.id.switch_notifications);
        switchAutoBackup = view.findViewById(R.id.switch_auto_backup);
        tvThemeMode = view.findViewById(R.id.tv_theme_mode);
        tvLanguage = view.findViewById(R.id.tv_language);
        
        // 设置点击事件
        view.findViewById(R.id.layout_theme).setOnClickListener(v -> showThemeDialog());
        view.findViewById(R.id.layout_language).setOnClickListener(v -> showLanguageDialog());
        view.findViewById(R.id.layout_about).setOnClickListener(v -> showAboutDialog());
        view.findViewById(R.id.layout_feedback).setOnClickListener(v -> showFeedbackDialog());
    }
    
    private void setupListeners() {
        // 使用ViewModel处理存储管理
        if (binding.btnCleanupImages != null) {
            binding.btnCleanupImages.setOnClickListener(v -> profileViewModel.cleanupImages());
        }
        if (binding.btnCompressImages != null) {
            binding.btnCompressImages.setOnClickListener(v -> profileViewModel.compressImages());
        }
        if (binding.btnCleanupUnused != null) {
            binding.btnCleanupUnused.setOnClickListener(v -> profileViewModel.cleanupUnusedFiles());
        }
        
        // 设置开关监听器（如果需要额外的UI逻辑）
        // 大部分设置更新现在通过ViewModel处理
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (binding != null) {
            binding.unbind();
            binding = null;
        }
    }

    private void updateStorageInfo() {
        try {
            Context context = requireContext();
            
            // 获取存储信息
            ImageManager.StorageInfo storageInfo = ImageManager.getStorageInfo(context);
            
            // 获取衣物数量
            List<ClothingItem> clothingList = clothingManager.getClothingList();
            int clothingCount = clothingList.size();
            
            // 更新UI
            tvStorageInfo.setText("存储空间: " + storageInfo.getReadableSize());
            tvImageCount.setText("图片文件: " + storageInfo.getFileCount() + " 个");
            tvClothingCount.setText("衣物数量: " + clothingCount + " 件");
            
            // 根据存储情况调整按钮状态
            boolean hasImages = storageInfo.getFileCount() > 0;
            btnCleanupImages.setEnabled(hasImages);
            btnCompressImages.setEnabled(hasImages);
            btnCleanupUnused.setEnabled(hasImages);
            
        } catch (Exception e) {
            ErrorUtils.logError("更新存储信息时发生错误", e);
            ErrorUtils.showUserError(requireContext(), getString(R.string.error_get_storage_info));
        }
    }
    
    private void showCleanupAllDialog() {
        new AlertDialog.Builder(requireContext())
                .setTitle("清理所有图片")
                .setMessage("这将删除所有图片文件，衣物将失去图片显示。此操作不可撤销，确定继续吗？")
                .setPositiveButton("确定", (dialog, which) -> cleanupAllImages())
                .setNegativeButton("取消", null)
                .show();
    }
    
    private void cleanupAllImages() {
        try {
            int cleanedCount = ImageManager.cleanupAllImages(requireContext());
            ErrorUtils.showUserError(requireContext(), getString(R.string.success_cleanup_all_images, cleanedCount));
            updateStorageInfo();
        } catch (Exception e) {
            ErrorUtils.logError("清理所有图片时发生错误", e);
            ErrorUtils.showUserError(requireContext(), getString(R.string.error_cleanup_images, e.getMessage()));
        }
    }
    
    private void compressAllImages() {
        try {
            btnCompressImages.setEnabled(false);
            btnCompressImages.setText(getString(R.string.compressing));
            
            // 在后台线程执行压缩
            new Thread(() -> {
                try {
                    int compressedCount = ImageManager.compressAllImages(requireContext());
                    
                    requireActivity().runOnUiThread(() -> {
                        btnCompressImages.setEnabled(true);
                        btnCompressImages.setText(getString(R.string.compress_all_images));
                        ErrorUtils.showUserError(requireContext(), getString(R.string.success_compress_images, compressedCount));
                        updateStorageInfo();
                    });
                } catch (Exception e) {
                    ErrorUtils.logError("压缩图片时发生错误", e);
                    requireActivity().runOnUiThread(() -> {
                        btnCompressImages.setEnabled(true);
                        btnCompressImages.setText(getString(R.string.compress_all_images));
                        ErrorUtils.showUserError(requireContext(), getString(R.string.error_compress_images, e.getMessage()));
                    });
                }
            }).start();
            
        } catch (Exception e) {
            ErrorUtils.logError("启动压缩任务时发生错误", e);
            btnCompressImages.setEnabled(true);
            btnCompressImages.setText(getString(R.string.compress_all_images));
            ErrorUtils.showUserError(requireContext(), getString(R.string.error_start_compress, e.getMessage()));
        }
    }
    
    private void cleanupUnusedImages() {
        try {
            // 获取正在使用的图片路径
            List<ClothingItem> clothingList = clothingManager.getClothingList();
            List<String> usedImagePaths = clothingList.stream()
                    .map(ClothingItem::getImageUri)
                    .filter(path -> path != null && !path.isEmpty())
                    .collect(Collectors.toList());
            
            int cleanedCount = ImageManager.cleanupUnusedImages(requireContext(), usedImagePaths);
            ErrorUtils.showUserError(requireContext(), getString(R.string.success_cleanup_unused_images, cleanedCount));
            updateStorageInfo();
        } catch (Exception e) {
            ErrorUtils.logError("清理未使用图片时发生错误", e);
            ErrorUtils.showUserError(requireContext(), getString(R.string.error_cleanup_images, e.getMessage()));
        }
    }
    
    private void loadSettings() {
        // 加载保存的设置
        switchDarkMode.setChecked(preferences.getBoolean("dark_mode", false));
        switchNotifications.setChecked(preferences.getBoolean("notifications", true));
        switchAutoBackup.setChecked(preferences.getBoolean("auto_backup", true));
        
        // 加载主题模式
        String themeMode = preferences.getString("theme_mode", "跟随系统");
        tvThemeMode.setText(themeMode);
        
        // 加载语言设置
        String language = preferences.getString("language", "简体中文");
        tvLanguage.setText(language);
    }
    
    private void showThemeDialog() {
        String[] themes = {"跟随系统", "浅色模式", "深色模式"};
        String currentTheme = preferences.getString("theme_mode", "跟随系统");
        int currentIndex = 0;
        
        for (int i = 0; i < themes.length; i++) {
            if (themes[i].equals(currentTheme)) {
                currentIndex = i;
                break;
            }
        }
        
        new androidx.appcompat.app.AlertDialog.Builder(requireContext())
                .setTitle("选择主题")
                .setSingleChoiceItems(themes, currentIndex, (dialog, which) -> {
                    String selectedTheme = themes[which];
                    preferences.edit().putString("theme_mode", selectedTheme).apply();
                    tvThemeMode.setText(selectedTheme);
                    dialog.dismiss();
                    // TODO: 应用主题
                })
                .setNegativeButton("取消", null)
                .show();
    }
    
    private void showLanguageDialog() {
        String[] languages = {"简体中文", "English", "日本語"};
        String currentLanguage = preferences.getString("language", "简体中文");
        int currentIndex = 0;
        
        for (int i = 0; i < languages.length; i++) {
            if (languages[i].equals(currentLanguage)) {
                currentIndex = i;
                break;
            }
        }
        
        new androidx.appcompat.app.AlertDialog.Builder(requireContext())
                .setTitle("选择语言")
                .setSingleChoiceItems(languages, currentIndex, (dialog, which) -> {
                    String selectedLanguage = languages[which];
                    preferences.edit().putString("language", selectedLanguage).apply();
                    tvLanguage.setText(selectedLanguage);
                    dialog.dismiss();
                    // TODO: 应用语言切换
                })
                .setNegativeButton("取消", null)
                .show();
    }
    
    private void showAboutDialog() {
        new androidx.appcompat.app.AlertDialog.Builder(requireContext())
                .setTitle("关于")
                .setMessage("衣柜应用 v1.0.0\n\n一个简洁优雅的衣物管理应用")
                .setPositiveButton("确定", null)
                .show();
    }
    
    private void showFeedbackDialog() {
        new androidx.appcompat.app.AlertDialog.Builder(requireContext())
                .setTitle("反馈")
                .setMessage("如有问题或建议，请通过以下方式联系我们：\n\n邮箱：<EMAIL>")
                .setPositiveButton("确定", null)
                .show();
    }
    
    @Override
    public void onResume() {
        super.onResume();
        updateStorageInfo();
    }
}