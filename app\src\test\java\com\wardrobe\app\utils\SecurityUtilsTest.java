package com.wardrobe.app.utils;

import android.content.Context;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;

import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * SecurityUtils单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class SecurityUtilsTest {

    @Mock
    private Context mockContext;

    private File tempFile;

    @Before
    public void setUp() throws IOException {
        // 创建临时文件用于测试
        tempFile = File.createTempFile("test", ".jpg");
        tempFile.deleteOnExit();
    }

    // 辅助方法：创建重复字符串
    private String repeat(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    @Test
    public void testValidateName_ValidName() {
        try (MockedStatic<TextUtils> mockedTextUtils = mockStatic(TextUtils.class)) {
            mockedTextUtils.when(() -> TextUtils.isEmpty(any())).thenAnswer(invocation -> {
                Object arg = invocation.getArgument(0);
                return arg == null || arg.toString().isEmpty();
            });
            assertTrue("有效名称应该通过验证", SecurityUtils.validateName("测试衣物"));
            assertTrue("短名称应该通过验证", SecurityUtils.validateName("T恤"));
            assertTrue("最大长度名称应该通过验证", SecurityUtils.validateName(repeat("a", 100)));
        }
    }

    @Test
    public void testValidateName_InvalidName() {
        try (MockedStatic<TextUtils> mockedTextUtils = mockStatic(TextUtils.class)) {
            mockedTextUtils.when(() -> TextUtils.isEmpty(any())).thenAnswer(invocation -> {
                Object arg = invocation.getArgument(0);
                return arg == null || arg.toString().isEmpty();
            });
            assertFalse("空名称应该失败", SecurityUtils.validateName(""));
            assertFalse("null名称应该失败", SecurityUtils.validateName(null));
            assertFalse("超长名称应该失败", SecurityUtils.validateName(repeat("a", 101)));
        }
    }

    @Test
    public void testValidateDescription_ValidDescription() {
        // 不依赖TextUtils
        assertTrue("null描述应该通过验证", SecurityUtils.validateDescription(null));
        assertTrue("空描述应该通过验证", SecurityUtils.validateDescription(""));
        assertTrue("短描述应该通过验证", SecurityUtils.validateDescription("这是一件很棒的衣物"));
        assertTrue("最大长度描述应该通过验证", SecurityUtils.validateDescription(repeat("a", 500)));
    }

    @Test
    public void testValidateDescription_InvalidDescription() {
        // 不依赖TextUtils
        assertFalse("超长描述应该失败", SecurityUtils.validateDescription(repeat("a", 501)));
    }

    @Test
    public void testValidateImagePath_ValidPath() {
        try (MockedStatic<TextUtils> mockedTextUtils = mockStatic(TextUtils.class)) {
            mockedTextUtils.when(() -> TextUtils.isEmpty(any())).thenReturn(false);
            assertTrue("有效图片路径应该通过验证", SecurityUtils.validateImagePath(tempFile.getAbsolutePath()));
        }
    }

    @Test
    public void testValidateImagePath_InvalidPath() {
        try (MockedStatic<TextUtils> mockedTextUtils = mockStatic(TextUtils.class)) {
            mockedTextUtils.when(() -> TextUtils.isEmpty(any())).thenAnswer(invocation -> {
                Object arg = invocation.getArgument(0);
                return arg == null || arg.toString().isEmpty();
            });
            assertFalse("空路径应该失败", SecurityUtils.validateImagePath(""));
            assertFalse("null路径应该失败", SecurityUtils.validateImagePath(null));
            assertFalse("不存在的文件应该失败", SecurityUtils.validateImagePath("/path/to/nonexistent/file.jpg"));
        }
    }

    @Test
    public void testValidateColor_ValidColor() {
        try (MockedStatic<TextUtils> mockedTextUtils = mockStatic(TextUtils.class)) {
            mockedTextUtils.when(() -> TextUtils.isEmpty(any())).thenAnswer(invocation -> {
                Object arg = invocation.getArgument(0);
                return arg == null || arg.toString().isEmpty();
            });
            assertTrue("null颜色应该通过验证", SecurityUtils.validateColor(null));
            assertTrue("空颜色应该通过验证", SecurityUtils.validateColor(""));
            assertTrue("有效十六进制颜色应该通过验证", SecurityUtils.validateColor("#FF0000"));
            assertTrue("小写十六进制颜色应该通过验证", SecurityUtils.validateColor("#ff0000"));
            assertTrue("混合大小写十六进制颜色应该通过验证", SecurityUtils.validateColor("#Ff0000"));
        }
    }

    @Test
    public void testValidateColor_InvalidColor() {
        try (MockedStatic<TextUtils> mockedTextUtils = mockStatic(TextUtils.class)) {
            mockedTextUtils.when(() -> TextUtils.isEmpty(any())).thenReturn(false);
            assertFalse("无效颜色格式应该失败", SecurityUtils.validateColor("red"));
            assertFalse("缺少#的颜色应该失败", SecurityUtils.validateColor("FF0000"));
            assertFalse("长度不对的颜色应该失败", SecurityUtils.validateColor("#FF00"));
            assertFalse("包含无效字符的颜色应该失败", SecurityUtils.validateColor("#FF000G"));
        }
    }

    @Test
    public void testValidateFileName_ValidFileName() {
        try (MockedStatic<TextUtils> mockedTextUtils = mockStatic(TextUtils.class)) {
            mockedTextUtils.when(() -> TextUtils.isEmpty(any())).thenReturn(false);
            assertTrue("有效文件名应该通过验证", SecurityUtils.validateFileName("test.jpg"));
            assertTrue("带下划线的文件名应该通过验证", SecurityUtils.validateFileName("test_file.jpg"));
            assertTrue("带点的文件名应该通过验证", SecurityUtils.validateFileName("test.file.jpg"));
            assertTrue("带连字符的文件名应该通过验证", SecurityUtils.validateFileName("test-file.jpg"));
        }
    }

    @Test
    public void testValidateFileName_InvalidFileName() {
        try (MockedStatic<TextUtils> mockedTextUtils = mockStatic(TextUtils.class)) {
            mockedTextUtils.when(() -> TextUtils.isEmpty(any())).thenAnswer(invocation -> {
                Object arg = invocation.getArgument(0);
                return arg == null || arg.toString().isEmpty();
            });
            assertFalse("空文件名应该失败", SecurityUtils.validateFileName(""));
            assertFalse("null文件名应该失败", SecurityUtils.validateFileName(null));
            assertFalse("包含特殊字符的文件名应该失败", SecurityUtils.validateFileName("test<file>.jpg"));
            assertFalse("包含空格的文件名应该失败", SecurityUtils.validateFileName("test file.jpg"));
        }
    }

    @Test
    public void testGenerateSafeFileName() {
        try (MockedStatic<TextUtils> mockedTextUtils = mockStatic(TextUtils.class)) {
            mockedTextUtils.when(() -> TextUtils.isEmpty(any())).thenAnswer(invocation -> {
                Object arg = invocation.getArgument(0);
                return arg == null || arg.toString().isEmpty();
            });
            String originalName = "test<file>.jpg";
            String safeName = SecurityUtils.generateSafeFileName(originalName);
            assertNotNull("生成的安全文件名不应为null", safeName);
            assertTrue("生成的文件名应该是安全的", SecurityUtils.validateFileName(safeName));
            assertTrue("生成的文件名应该包含原始扩展名", safeName.endsWith(".jpg"));
        }
    }

    @Test
    public void testGenerateSafeFileName_EmptyInput() {
        try (MockedStatic<TextUtils> mockedTextUtils = mockStatic(TextUtils.class);
             MockedStatic<Base64> mockedBase64 = mockStatic(Base64.class)) {
            mockedTextUtils.when(() -> TextUtils.isEmpty(any())).thenAnswer(invocation -> {
                Object arg = invocation.getArgument(0);
                return arg == null || arg.toString().isEmpty();
            });
            mockedBase64.when(() -> Base64.encodeToString(any(byte[].class), anyInt())).thenReturn("randomString");
            String safeName = SecurityUtils.generateSafeFileName("");
            assertNotNull("空输入应该生成随机文件名", safeName);
            assertTrue("生成的文件名应该是安全的", SecurityUtils.validateFileName(safeName));
        }
    }

    @Test
    public void testGenerateRandomFileName() {
        try (MockedStatic<Base64> mockedBase64 = mockStatic(Base64.class)) {
            mockedBase64.when(() -> Base64.encodeToString(any(byte[].class), anyInt())).thenReturn("randomString1", "randomString2");
            String fileName1 = SecurityUtils.generateRandomFileName();
            String fileName2 = SecurityUtils.generateRandomFileName();
            assertNotNull("随机文件名不应为null", fileName1);
            assertNotNull("随机文件名不应为null", fileName2);
            assertTrue("随机文件名应该以.jpg结尾", fileName1.endsWith(".jpg"));
            assertTrue("随机文件名应该以.jpg结尾", fileName2.endsWith(".jpg"));
            assertNotEquals("两次生成的随机文件名应该不同", fileName1, fileName2);
        }
    }

    @Test
    public void testCalculateFileMD5() {
        try (MockedStatic<Log> mockedLog = mockStatic(Log.class)) {
            mockedLog.when(() -> Log.e(anyString(), anyString(), any(Exception.class))).thenReturn(0);
            try {
                java.io.FileWriter writer = new java.io.FileWriter(tempFile);
                writer.write("test content");
                writer.close();
                String md5 = SecurityUtils.calculateFileMD5(tempFile);
                assertNotNull("MD5值不应为null", md5);
                assertEquals("MD5值长度应该是32位", 32, md5.length());
                String md5Again = SecurityUtils.calculateFileMD5(tempFile);
                assertEquals("相同文件的MD5值应该一致", md5, md5Again);
            } catch (IOException e) {
                fail("测试文件操作失败: " + e.getMessage());
            }
        }
    }

    @Test
    public void testCalculateFileMD5_NonExistentFile() {
        try (MockedStatic<Log> mockedLog = mockStatic(Log.class)) {
            mockedLog.when(() -> Log.e(anyString(), anyString(), any(Exception.class))).thenReturn(0);
            File nonExistentFile = new File("/path/to/nonexistent/file");
            String md5 = SecurityUtils.calculateFileMD5(nonExistentFile);
            assertNull("不存在文件的MD5值应该为null", md5);
        }
    }

    @Test
    @Ignore("JVM环境下无法Mock Cipher/SecretKey，建议在真机或androidTest下测试")
    public void testEncryptAndDecryptData() {
        try (MockedStatic<TextUtils> mockedTextUtils = mockStatic(TextUtils.class);
             MockedStatic<Log> mockedLog = mockStatic(Log.class);
             MockedStatic<Base64> mockedBase64 = mockStatic(Base64.class)) {
            mockedTextUtils.when(() -> TextUtils.isEmpty(any())).thenReturn(false);
            mockedLog.when(() -> Log.e(anyString(), anyString(), any(Exception.class))).thenReturn(0);
            mockedBase64.when(() -> Base64.encodeToString(any(byte[].class), anyInt())).thenReturn("encryptedData");
            mockedBase64.when(() -> Base64.decode(anyString(), anyInt())).thenReturn("敏感数据".getBytes());
            String originalData = "敏感数据";
            String key = "testKey123";
            String encryptedData = SecurityUtils.encryptData(originalData, key);
            assertNotNull("加密后的数据不应为null", encryptedData);
            assertEquals("encryptedData", encryptedData);
            String decryptedData = SecurityUtils.decryptData(encryptedData, key);
            assertNotNull("解密后的数据不应为null", decryptedData);
            assertEquals("解密后的数据应该与原始数据相同", originalData, decryptedData);
        }
    }

    @Test
    public void testEncryptData_InvalidInput() {
        try (MockedStatic<Log> mockedLog = mockStatic(Log.class);
             MockedStatic<TextUtils> mockedTextUtils = mockStatic(TextUtils.class)) {
            mockedLog.when(() -> Log.e(anyString(), anyString(), any(Exception.class))).thenReturn(0);
            mockedTextUtils.when(() -> TextUtils.isEmpty(any())).thenAnswer(invocation -> {
                Object arg = invocation.getArgument(0);
                return arg == null || arg.toString().isEmpty();
            });
            assertNull("null数据应该返回null", SecurityUtils.encryptData(null, "key"));
            assertNull("null密钥应该返回null", SecurityUtils.encryptData("data", null));
        }
    }

    @Test
    public void testDecryptData_InvalidInput() {
        try (MockedStatic<Log> mockedLog = mockStatic(Log.class);
             MockedStatic<TextUtils> mockedTextUtils = mockStatic(TextUtils.class)) {
            mockedLog.when(() -> Log.e(anyString(), anyString(), any(Exception.class))).thenReturn(0);
            mockedTextUtils.when(() -> TextUtils.isEmpty(any())).thenAnswer(invocation -> {
                Object arg = invocation.getArgument(0);
                return arg == null || arg.toString().isEmpty();
            });
            assertNull("null数据应该返回null", SecurityUtils.decryptData(null, "key"));
            assertNull("null密钥应该返回null", SecurityUtils.decryptData("data", null));
        }
    }

    @Test
    public void testSecureDeleteFile() {
        try (MockedStatic<Log> mockedLog = mockStatic(Log.class)) {
            // Mock Log.e
            mockedLog.when(() -> Log.e(anyString(), anyString(), any(Exception.class))).thenReturn(0);
            
            // 创建测试文件
            File testFile = new File(tempFile.getParent(), "test_delete.txt");
            try {
                // 使用传统IO方式写入文件
                java.io.FileWriter writer = new java.io.FileWriter(testFile);
                writer.write("test content for deletion");
                writer.close();
                
                // 验证文件存在
                assertTrue("测试文件应该存在", testFile.exists());
                
                // 安全删除
                boolean deleted = SecurityUtils.secureDeleteFile(testFile);
                assertTrue("文件应该被成功删除", deleted);
                assertFalse("文件应该不存在", testFile.exists());
            } catch (IOException e) {
                fail("测试文件操作失败: " + e.getMessage());
            }
        }
    }

    @Test
    public void testSecureDeleteFile_NonExistentFile() {
        try (MockedStatic<Log> mockedLog = mockStatic(Log.class)) {
            // Mock Log.e
            mockedLog.when(() -> Log.e(anyString(), anyString(), any(Exception.class))).thenReturn(0);
            
            File nonExistentFile = new File("/path/to/nonexistent/file");
            boolean deleted = SecurityUtils.secureDeleteFile(nonExistentFile);
            assertTrue("不存在文件的删除操作应该返回true", deleted);
        }
    }

    @Test
    public void testValidateClothingData_ValidData() {
        try (MockedStatic<TextUtils> mockedTextUtils = mockStatic(TextUtils.class)) {
            // Mock TextUtils.isEmpty
            mockedTextUtils.when(() -> TextUtils.isEmpty("测试衣物")).thenReturn(false);
            mockedTextUtils.when(() -> TextUtils.isEmpty("这是一件很棒的衣物")).thenReturn(false);
            mockedTextUtils.when(() -> TextUtils.isEmpty(tempFile.getAbsolutePath())).thenReturn(false);
            
            SecurityUtils.ValidationResult result = SecurityUtils.validateClothingData(
                    "测试衣物", "这是一件很棒的衣物", tempFile.getAbsolutePath());
            
            assertTrue("有效数据应该通过验证", result.isValid());
            assertEquals("有效数据应该没有错误信息", "验证通过", result.toString());
        }
    }

    @Test
    public void testValidateClothingData_InvalidName() {
        try (MockedStatic<TextUtils> mockedTextUtils = mockStatic(TextUtils.class)) {
            // Mock TextUtils.isEmpty
            mockedTextUtils.when(() -> TextUtils.isEmpty("")).thenReturn(true);
            mockedTextUtils.when(() -> TextUtils.isEmpty("描述")).thenReturn(false);
            mockedTextUtils.when(() -> TextUtils.isEmpty(tempFile.getAbsolutePath())).thenReturn(false);
            
            SecurityUtils.ValidationResult result = SecurityUtils.validateClothingData(
                    "", "描述", tempFile.getAbsolutePath());
            
            assertFalse("无效名称应该失败验证", result.isValid());
            assertTrue("应该包含名称错误信息", result.getErrorMessage().contains("衣物名称"));
        }
    }

    @Test
    public void testValidateClothingData_InvalidDescription() {
        try (MockedStatic<TextUtils> mockedTextUtils = mockStatic(TextUtils.class)) {
            // Mock TextUtils.isEmpty
            mockedTextUtils.when(() -> TextUtils.isEmpty("测试衣物")).thenReturn(false);
            mockedTextUtils.when(() -> TextUtils.isEmpty(tempFile.getAbsolutePath())).thenReturn(false);
            
            SecurityUtils.ValidationResult result = SecurityUtils.validateClothingData(
                    "测试衣物", repeat("a", 501), tempFile.getAbsolutePath());
            
            assertFalse("无效描述应该失败验证", result.isValid());
            assertTrue("应该包含描述错误信息", result.getErrorMessage().contains("描述长度"));
        }
    }

    @Test
    public void testValidationResult() {
        SecurityUtils.ValidationResult result = new SecurityUtils.ValidationResult();
        
        // 初始状态
        assertTrue("初始状态应该有效", result.isValid());
        assertEquals("初始错误信息应该为空", "", result.getErrorMessage());
        
        // 添加错误
        result.addError("错误1");
        assertFalse("有错误时应该无效", result.isValid());
        assertEquals("错误信息应该正确", "错误1", result.getErrorMessage());
        
        // 添加多个错误
        result.addError("错误2");
        assertTrue("多个错误信息应该包含换行符", result.getErrorMessage().contains("\n"));
        assertTrue("应该包含所有错误信息", result.getErrorMessage().contains("错误1"));
        assertTrue("应该包含所有错误信息", result.getErrorMessage().contains("错误2"));
    }
} 