<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="background_primary">#1C1B1F</color>
    <color name="background_secondary">#1C1B1F</color>
    <color name="background_tertiary">#49454F</color>
    <color name="borderColor">#938F99</color>
    <color name="buttonBackground">#D0BCFF</color>
    <color name="buttonTextColor">#381E72</color>
    <color name="cardBackground">#1C1B1F</color>
    <color name="colorBackground">#1C1B1F</color>
    <color name="colorError">#FFB4AB</color>
    <color name="colorOnBackground">#E6E1E5</color>
    <color name="colorOnError">#690005</color>
    <color name="colorOnPrimary">#381E72</color>
    <color name="colorOnSecondary">#332D41</color>
    <color name="colorOnSurface">#E6E1E5</color>
    <color name="colorPrimary">#D0BCFF</color>
    <color name="colorPrimaryVariant">#4F378B</color>
    <color name="colorSecondary">#CCC2DC</color>
    <color name="colorSecondaryVariant">#4A4458</color>
    <color name="colorSurface">#1C1B1F</color>
    <color name="containerBackground">#1C1B1F</color>
    <color name="dividerColor">#49454F</color>
    <color name="navigationBarColor">#1C1B1F</color>
    <color name="rippleColor">#1FD0BCFF</color>
    <color name="selectedItemBackground">#4F378B</color>
    <color name="statusBarColor">#1C1B1F</color>
    <color name="textColorHint">#938F99</color>
    <color name="textColorPrimary">#E6E1E5</color>
    <color name="textColorSecondary">#CAC4D0</color>
    <color name="text_inverse">#1C1B1F</color>
    <color name="text_primary">#E6E1E5</color>
    <color name="text_secondary">#CAC4D0</color>
    <color name="text_tertiary">#938F99</color>
    <style name="AppTheme" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryVariant">@color/colorPrimaryVariant</item>
        <item name="colorOnPrimary">@color/colorOnPrimary</item>
        
        
        <item name="colorSecondary">@color/colorSecondary</item>
        <item name="colorSecondaryVariant">@color/colorSecondaryVariant</item>
        <item name="colorOnSecondary">@color/colorOnSecondary</item>
        
        
        <item name="android:colorBackground">@color/colorBackground</item>
        <item name="colorOnBackground">@color/colorOnBackground</item>
        
        
        <item name="colorSurface">@color/colorSurface</item>
        <item name="colorOnSurface">@color/colorOnSurface</item>
        
        
        <item name="colorError">@color/colorError</item>
        <item name="colorOnError">@color/colorOnError</item>
        
        
        <item name="android:statusBarColor">@color/statusBarColor</item>
        <item name="android:windowLightStatusBar">false</item>
        
        
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:windowLightNavigationBar">false</item>
        
        
        <item name="android:textColorPrimary">@color/textColorPrimary</item>
        <item name="android:textColorSecondary">@color/textColorSecondary</item>
        <item name="android:textColorHint">@color/textColorHint</item>
        
        
        <item name="android:forceDarkAllowed">true</item>
    </style>
    <style name="ButtonStyle" parent="Widget.Material3.Button">
        <item name="android:background">@color/buttonBackground</item>
        <item name="android:textColor">@color/buttonTextColor</item>
        <item name="backgroundTint">@color/buttonBackground</item>
    </style>
    <style name="CardStyle" parent="Widget.Material3.CardView.Elevated">
        <item name="cardBackgroundColor">@color/cardBackground</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style>
</resources>