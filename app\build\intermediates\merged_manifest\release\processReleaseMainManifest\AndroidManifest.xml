<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.wardrobe.app"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="35" />

    <!-- 照片相关权限 -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.CAMERA" />

    <!-- 如果您的应用需要访问网络，请取消注释以下权限 -->
    <!-- <uses-permission android:name="android.permission.INTERNET" /> -->


    <!-- 相机功能声明 -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <permission
        android:name="com.wardrobe.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.wardrobe.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.wardrobe.app.WardrobeApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.MaterialComponents.DayNight.NoActionBar" >
        <activity
            android:name="com.wardrobe.app.ui.activities.MainActivity"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.wardrobe.app.ui.activities.AddClothingActivity"
            android:exported="false" />
        <activity
            android:name="com.wardrobe.app.ui.activities.CategorySelectActivity"
            android:exported="false" />
        <activity
            android:name="com.wardrobe.app.ui.activities.ColorSelectActivity"
            android:exported="false" />
        <activity
            android:name="com.wardrobe.app.ui.activities.OccasionSelectActivity"
            android:exported="false" />
        <activity
            android:name="com.wardrobe.app.ui.activities.ClothingDetailActivity"
            android:exported="false" />

        <!-- 如果您有 BatchAddClothingActivity，请确保它已声明 -->
        <activity
            android:name="com.wardrobe.app.ui.activities.BatchAddClothingActivity"
            android:exported="false" />

        <!-- FileProvider for secure file sharing -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.wardrobe.app.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- 请确保您应用中所有其他的 Activity, Service, Receiver, Provider 都在此处声明 -->

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.wardrobe.app.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>