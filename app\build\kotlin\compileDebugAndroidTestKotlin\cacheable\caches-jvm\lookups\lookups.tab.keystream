  
targetContext android.app.Instrumentation  packageName android.content.Context  
AndroidJUnit4 androidx.test.ext.junit.runners  InstrumentationRegistry androidx.test.platform.app  getInstrumentation 2androidx.test.platform.app.InstrumentationRegistry  
AndroidJUnit4 com.wardrobe.app  ExampleInstrumentedTest com.wardrobe.app  InstrumentationRegistry com.wardrobe.app  RunWith com.wardrobe.app  Test com.wardrobe.app  assertEquals com.wardrobe.app  InstrumentationRegistry (com.wardrobe.app.ExampleInstrumentedTest  assertEquals (com.wardrobe.app.ExampleInstrumentedTest  
AndroidJUnit4 	org.junit  InstrumentationRegistry 	org.junit  RunWith 	org.junit  Test 	org.junit  assertEquals 	org.junit  assertEquals org.junit.Assert  RunWith org.junit.runner                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 