<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_clothing_pro" modulePackage="com.wardrobe.app" filePath="app\src\main\res\layout\item_clothing_pro.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_clothing_pro_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="47" endOffset="51"/></Target><Target id="@+id/clothing_image" view="ImageView"><Expressions/><location startLine="17" startOffset="8" endLine="22" endOffset="62"/></Target><Target id="@+id/clothing_name" view="TextView"><Expressions/><location startLine="24" startOffset="8" endLine="33" endOffset="37"/></Target><Target id="@+id/clothing_category" view="TextView"><Expressions/><location startLine="35" startOffset="8" endLine="43" endOffset="36"/></Target></Targets></Layout>