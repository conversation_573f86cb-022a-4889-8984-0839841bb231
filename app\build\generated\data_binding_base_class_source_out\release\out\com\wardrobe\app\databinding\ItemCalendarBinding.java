// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemCalendarBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final Button btnViewDetail;

  @NonNull
  public final TextView tvDate;

  @NonNull
  public final TextView tvDayOfWeek;

  @NonNull
  public final TextView tvOutfitDescription;

  private ItemCalendarBinding(@NonNull CardView rootView, @NonNull Button btnViewDetail,
      @NonNull TextView tvDate, @NonNull TextView tvDayOfWeek,
      @NonNull TextView tvOutfitDescription) {
    this.rootView = rootView;
    this.btnViewDetail = btnViewDetail;
    this.tvDate = tvDate;
    this.tvDayOfWeek = tvDayOfWeek;
    this.tvOutfitDescription = tvOutfitDescription;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemCalendarBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemCalendarBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_calendar, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemCalendarBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_view_detail;
      Button btnViewDetail = ViewBindings.findChildViewById(rootView, id);
      if (btnViewDetail == null) {
        break missingId;
      }

      id = R.id.tv_date;
      TextView tvDate = ViewBindings.findChildViewById(rootView, id);
      if (tvDate == null) {
        break missingId;
      }

      id = R.id.tv_day_of_week;
      TextView tvDayOfWeek = ViewBindings.findChildViewById(rootView, id);
      if (tvDayOfWeek == null) {
        break missingId;
      }

      id = R.id.tv_outfit_description;
      TextView tvOutfitDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvOutfitDescription == null) {
        break missingId;
      }

      return new ItemCalendarBinding((CardView) rootView, btnViewDetail, tvDate, tvDayOfWeek,
          tvOutfitDescription);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
