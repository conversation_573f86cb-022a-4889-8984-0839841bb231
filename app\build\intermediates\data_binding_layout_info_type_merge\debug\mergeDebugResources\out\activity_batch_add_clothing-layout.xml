<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_batch_add_clothing" modulePackage="com.wardrobe.app" filePath="app\src\main\res\layout\activity_batch_add_clothing.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_batch_add_clothing_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="98" endOffset="14"/></Target><Target id="@+id/batch_category_selector_view" tag="layout/activity_batch_add_clothing_0" include="layout_category_selector"><Expressions/><location startLine="20" startOffset="4" endLine="20" endOffset="103"/></Target><Target id="@+id/edit_notes_batch" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="29" startOffset="8" endLine="36" endOffset="33"/></Target><Target id="@+id/edit_story_batch" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="44" startOffset="8" endLine="51" endOffset="33"/></Target><Target id="@+id/edit_tags_batch" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="59" startOffset="8" endLine="64" endOffset="37"/></Target><Target id="@+id/btn_select_images_batch" view="Button"><Expressions/><location startLine="68" startOffset="4" endLine="74" endOffset="49"/></Target><Target id="@+id/grid_view_selected_images" view="GridView"><Expressions/><location startLine="76" startOffset="4" endLine="87" endOffset="43"/></Target><Target id="@+id/btn_save_batch" view="Button"><Expressions/><location startLine="90" startOffset="4" endLine="96" endOffset="49"/></Target></Targets></Layout>