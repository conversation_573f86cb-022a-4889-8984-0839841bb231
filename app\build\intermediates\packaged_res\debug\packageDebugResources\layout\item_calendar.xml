<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardBackgroundColor="@color/surface_primary"
    app:cardCornerRadius="16dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="20dp">

        <!-- 日期信息 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="12月19日"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/tv_day_of_week"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="周四"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

        </LinearLayout>

        <!-- 穿搭描述 -->
        <TextView
            android:id="@+id/tv_outfit_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:text="白色T恤 + 牛仔裤 + 运动鞋"
            android:textColor="@color/text_secondary"
            android:textSize="14sp" />

        <!-- 操作按钮 -->
        <Button
            android:id="@+id/btn_view_detail"
            style="@style/ButtonTertiary"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:text="查看"
            android:textSize="12sp" />

    </LinearLayout>

</androidx.cardview.widget.CardView> 