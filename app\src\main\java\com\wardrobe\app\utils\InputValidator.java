package com.wardrobe.app.utils;

import android.text.TextUtils;
import java.util.regex.Pattern;

/**
 * 输入验证工具类
 * 提供各种输入数据的验证和清理功能
 * 防止恶意输入和数据注入攻击
 */
public class InputValidator {
    
    private static final String TAG = "InputValidator";
    
    // 正则表达式模式
    private static final Pattern CLOTHING_NAME_PATTERN = Pattern.compile("^[\\u4e00-\\u9fa5a-zA-Z0-9\\s\\-_()（）]{1,50}$");
    private static final Pattern BRAND_NAME_PATTERN = Pattern.compile("^[\\u4e00-\\u9fa5a-zA-Z0-9\\s\\-_&.]{1,30}$");
    private static final Pattern CATEGORY_PATTERN = Pattern.compile("^[\\u4e00-\\u9fa5a-zA-Z0-9\\s]{1,20}$");
    private static final Pattern COLOR_PATTERN = Pattern.compile("^[\\u4e00-\\u9fa5a-zA-Z0-9\\s]{1,15}$");
    private static final Pattern SIZE_PATTERN = Pattern.compile("^[a-zA-Z0-9\\s]{1,10}$");
    private static final Pattern DESCRIPTION_PATTERN = Pattern.compile("^[\\u4e00-\\u9fa5a-zA-Z0-9\\s\\-_.,!?()（）]{0,200}$");
    
    // 危险字符模式
    private static final Pattern DANGEROUS_CHARS = Pattern.compile("[<>\"'&;]");
    private static final Pattern SQL_INJECTION = Pattern.compile("(?i)(select|insert|update|delete|drop|create|alter|exec|union|script)");
    
    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String errorMessage;
        private final String cleanedInput;
        
        public ValidationResult(boolean valid, String errorMessage, String cleanedInput) {
            this.valid = valid;
            this.errorMessage = errorMessage;
            this.cleanedInput = cleanedInput;
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public String getCleanedInput() {
            return cleanedInput;
        }
    }
    
    /**
     * 验证衣物名称
     * 
     * @param name 衣物名称
     * @return 验证结果
     */
    public static ValidationResult validateClothingName(String name) {
        if (TextUtils.isEmpty(name)) {
            return new ValidationResult(false, "衣物名称不能为空", "");
        }
        
        String cleaned = cleanInput(name);
        
        if (cleaned.length() > 50) {
            return new ValidationResult(false, "衣物名称不能超过50个字符", cleaned.substring(0, 50));
        }
        
        if (!CLOTHING_NAME_PATTERN.matcher(cleaned).matches()) {
            return new ValidationResult(false, "衣物名称包含无效字符", cleaned);
        }
        
        if (containsDangerousContent(cleaned)) {
            return new ValidationResult(false, "衣物名称包含不安全内容", cleaned);
        }
        
        return new ValidationResult(true, null, cleaned);
    }
    
    /**
     * 验证品牌名称
     * 
     * @param brand 品牌名称
     * @return 验证结果
     */
    public static ValidationResult validateBrandName(String brand) {
        if (TextUtils.isEmpty(brand)) {
            return new ValidationResult(true, null, ""); // 品牌可以为空
        }
        
        String cleaned = cleanInput(brand);
        
        if (cleaned.length() > 30) {
            return new ValidationResult(false, "品牌名称不能超过30个字符", cleaned.substring(0, 30));
        }
        
        if (!BRAND_NAME_PATTERN.matcher(cleaned).matches()) {
            return new ValidationResult(false, "品牌名称包含无效字符", cleaned);
        }
        
        if (containsDangerousContent(cleaned)) {
            return new ValidationResult(false, "品牌名称包含不安全内容", cleaned);
        }
        
        return new ValidationResult(true, null, cleaned);
    }
    
    /**
     * 验证分类名称
     * 
     * @param category 分类名称
     * @return 验证结果
     */
    public static ValidationResult validateCategory(String category) {
        if (TextUtils.isEmpty(category)) {
            return new ValidationResult(false, "分类不能为空", "");
        }
        
        String cleaned = cleanInput(category);
        
        if (cleaned.length() > 20) {
            return new ValidationResult(false, "分类名称不能超过20个字符", cleaned.substring(0, 20));
        }
        
        if (!CATEGORY_PATTERN.matcher(cleaned).matches()) {
            return new ValidationResult(false, "分类名称包含无效字符", cleaned);
        }
        
        if (containsDangerousContent(cleaned)) {
            return new ValidationResult(false, "分类名称包含不安全内容", cleaned);
        }
        
        return new ValidationResult(true, null, cleaned);
    }
    
    /**
     * 验证颜色名称
     * 
     * @param color 颜色名称
     * @return 验证结果
     */
    public static ValidationResult validateColor(String color) {
        if (TextUtils.isEmpty(color)) {
            return new ValidationResult(true, null, ""); // 颜色可以为空
        }
        
        String cleaned = cleanInput(color);
        
        if (cleaned.length() > 15) {
            return new ValidationResult(false, "颜色名称不能超过15个字符", cleaned.substring(0, 15));
        }
        
        if (!COLOR_PATTERN.matcher(cleaned).matches()) {
            return new ValidationResult(false, "颜色名称包含无效字符", cleaned);
        }
        
        if (containsDangerousContent(cleaned)) {
            return new ValidationResult(false, "颜色名称包含不安全内容", cleaned);
        }
        
        return new ValidationResult(true, null, cleaned);
    }
    
    /**
     * 验证尺寸
     * 
     * @param size 尺寸
     * @return 验证结果
     */
    public static ValidationResult validateSize(String size) {
        if (TextUtils.isEmpty(size)) {
            return new ValidationResult(true, null, ""); // 尺寸可以为空
        }
        
        String cleaned = cleanInput(size);
        
        if (cleaned.length() > 10) {
            return new ValidationResult(false, "尺寸不能超过10个字符", cleaned.substring(0, 10));
        }
        
        if (!SIZE_PATTERN.matcher(cleaned).matches()) {
            return new ValidationResult(false, "尺寸包含无效字符", cleaned);
        }
        
        if (containsDangerousContent(cleaned)) {
            return new ValidationResult(false, "尺寸包含不安全内容", cleaned);
        }
        
        return new ValidationResult(true, null, cleaned);
    }
    
    /**
     * 验证描述信息
     * 
     * @param description 描述信息
     * @return 验证结果
     */
    public static ValidationResult validateDescription(String description) {
        if (TextUtils.isEmpty(description)) {
            return new ValidationResult(true, null, ""); // 描述可以为空
        }
        
        String cleaned = cleanInput(description);
        
        if (cleaned.length() > 200) {
            return new ValidationResult(false, "描述不能超过200个字符", cleaned.substring(0, 200));
        }
        
        if (!DESCRIPTION_PATTERN.matcher(cleaned).matches()) {
            return new ValidationResult(false, "描述包含无效字符", cleaned);
        }
        
        if (containsDangerousContent(cleaned)) {
            return new ValidationResult(false, "描述包含不安全内容", cleaned);
        }
        
        return new ValidationResult(true, null, cleaned);
    }
    
    /**
     * 验证价格
     * 
     * @param priceStr 价格字符串
     * @return 验证结果
     */
    public static ValidationResult validatePrice(String priceStr) {
        if (TextUtils.isEmpty(priceStr)) {
            return new ValidationResult(true, null, "0"); // 价格可以为空，默认为0
        }
        
        String cleaned = priceStr.trim();
        
        try {
            double price = Double.parseDouble(cleaned);
            if (price < 0) {
                return new ValidationResult(false, "价格不能为负数", "0");
            }
            if (price > 999999.99) {
                return new ValidationResult(false, "价格不能超过999999.99", "999999.99");
            }
            return new ValidationResult(true, null, String.valueOf(price));
        } catch (NumberFormatException e) {
            return new ValidationResult(false, "价格格式不正确", "0");
        }
    }
    
    /**
     * 清理输入字符串
     * 移除首尾空格，替换危险字符
     * 
     * @param input 输入字符串
     * @return 清理后的字符串
     */
    public static String cleanInput(String input) {
        if (input == null) {
            return "";
        }
        
        // 移除首尾空格
        String cleaned = input.trim();
        
        // 替换危险字符
        cleaned = cleaned.replaceAll("[<>\"'&;]", "");
        
        // 移除多余的空格
        cleaned = cleaned.replaceAll("\\s+", " ");
        
        return cleaned;
    }
    
    /**
     * 检查是否包含危险内容
     * 
     * @param input 输入字符串
     * @return 是否包含危险内容
     */
    private static boolean containsDangerousContent(String input) {
        if (input == null) {
            return false;
        }
        
        String lowerInput = input.toLowerCase();
        
        // 检查SQL注入模式
        if (SQL_INJECTION.matcher(lowerInput).find()) {
            Logger.w(TAG, "检测到可能的SQL注入尝试: " + input);
            return true;
        }
        
        // 检查脚本注入
        if (lowerInput.contains("script") || lowerInput.contains("javascript")) {
            Logger.w(TAG, "检测到可能的脚本注入尝试: " + input);
            return true;
        }
        
        return false;
    }
    
    /**
     * 验证文件路径安全性
     * 
     * @param filePath 文件路径
     * @return 是否安全
     */
    public static boolean isFilePathSafe(String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            return false;
        }
        
        // 检查路径遍历攻击
        if (filePath.contains("..") || filePath.contains("./") || filePath.contains("\\")) {
            Logger.w(TAG, "检测到不安全的文件路径: " + filePath);
            return false;
        }
        
        // 检查绝对路径
        if (filePath.startsWith("/") && !filePath.startsWith("/android_asset/")) {
            Logger.w(TAG, "检测到绝对路径访问: " + filePath);
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证ID格式
     * 
     * @param id ID字符串
     * @return 验证结果
     */
    public static ValidationResult validateId(String id) {
        if (TextUtils.isEmpty(id)) {
            return new ValidationResult(false, "ID不能为空", "");
        }
        
        String cleaned = id.trim();
        
        // ID应该是UUID格式或数字格式
        if (!cleaned.matches("^[a-fA-F0-9\\-]{36}$") && !cleaned.matches("^\\d+$")) {
            return new ValidationResult(false, "ID格式不正确", cleaned);
        }
        
        return new ValidationResult(true, null, cleaned);
    }
}
