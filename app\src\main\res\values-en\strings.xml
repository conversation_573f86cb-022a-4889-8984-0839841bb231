<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Wardrobe Manager</string>

    <!-- Add/Edit Clothing Activity -->
    <string name="title_add_new_clothing">Add New Clothing</string>
    <string name="title_edit_clothing">Edit Clothing</string>
    <string name="button_save">Save</string>
    <string name="hint_clothing_name">Clothing Name</string>

    <!-- Sections -->
    <string name="section_required">Required Information</string>
    <string name="section_optional">Optional Information</string>

    <!-- Labels -->
    <string name="label_category">Category</string>
    <string name="label_color">Color</string>
    <string name="label_occasion">Occasion</string>

    <!-- Placeholders -->
    <string name="category_placeholder">Select Category</string>
    <string name="color_placeholder">Select Color</string>
    <string name="occasion_placeholder">Select Occasion</string>
    <string name="category_primary_placeholder">Select Main Category</string>
    <string name="category_secondary_placeholder">Select Subcategory</string>
    <string name="placeholder_select">Please Select</string>

    <!-- Dialog Titles -->
    <string name="dialog_title_select_color">Select Color</string>
    <string name="dialog_title_select_occasion">Select Occasion</string>
    <string name="dialog_title_select_photo">Select Photo</string>

    <!-- Buttons -->
    <string name="button_cancel">Cancel</string>
    <string name="button_confirm">Confirm</string>
    <string name="button_delete">Delete</string>
    <string name="button_edit">Edit</string>
    <string name="button_add">Add</string>
    <string name="button_search">Search</string>
    <string name="button_filter">Filter</string>
    <string name="button_clear">Clear</string>
    <string name="button_select_all">Select All</string>
    <string name="button_deselect_all">Deselect All</string>

    <!-- Navigation -->
    <string name="nav_wardrobe">Wardrobe</string>
    <string name="nav_outfit">Outfit</string>
    <string name="nav_calendar">Calendar</string>
    <string name="nav_profile">Profile</string>

    <!-- Categories -->
    <string name="category_tops">Tops</string>
    <string name="category_bottoms">Bottoms</string>
    <string name="category_dresses">Dresses</string>
    <string name="category_outerwear">Outerwear</string>
    <string name="category_shoes">Shoes</string>
    <string name="category_accessories">Accessories</string>
    <string name="category_underwear">Underwear</string>
    <string name="category_suits">Suits</string>
    <string name="category_sets">Sets</string>
    <string name="category_workwear">Workwear</string>

    <!-- Colors -->
    <string name="color_red">Red</string>
    <string name="color_blue">Blue</string>
    <string name="color_green">Green</string>
    <string name="color_yellow">Yellow</string>
    <string name="color_black">Black</string>
    <string name="color_white">White</string>
    <string name="color_gray">Gray</string>
    <string name="color_brown">Brown</string>
    <string name="color_pink">Pink</string>
    <string name="color_purple">Purple</string>
    <string name="color_orange">Orange</string>
    <string name="color_navy">Navy</string>

    <!-- Occasions -->
    <string name="occasion_casual">Casual</string>
    <string name="occasion_formal">Formal</string>
    <string name="occasion_work">Work</string>
    <string name="occasion_party">Party</string>
    <string name="occasion_sports">Sports</string>
    <string name="occasion_travel">Travel</string>
    <string name="occasion_date">Date</string>
    <string name="occasion_home">Home</string>
    <string name="occasion_outdoor">Outdoor</string>
    <string name="occasion_daily">Daily</string>

    <!-- Messages -->
    <string name="message_save_success">Saved successfully</string>
    <string name="message_delete_success">Deleted successfully</string>
    <string name="message_add_success">Added successfully</string>
    <string name="message_update_success">Updated successfully</string>
    <string name="message_operation_failed">Operation failed</string>
    <string name="message_network_error">Network connection failed</string>
    <string name="message_permission_denied">Permission denied</string>
    <string name="message_no_data">No data available</string>
    <string name="message_loading">Loading...</string>
    <string name="message_search_no_results">No search results found</string>

    <!-- Validation Messages -->
    <string name="validation_name_required">Clothing name is required</string>
    <string name="validation_category_required">Category is required</string>
    <string name="validation_name_too_long">Name is too long</string>
    <string name="validation_invalid_input">Invalid input</string>

    <!-- Photo Selection -->
    <string name="photo_take_photo">Take Photo</string>
    <string name="photo_choose_gallery">Choose from Gallery</string>
    <string name="photo_remove">Remove Photo</string>

    <!-- Search and Filter -->
    <string name="search_hint">Search clothing...</string>
    <string name="filter_by_category">Filter by Category</string>
    <string name="filter_by_color">Filter by Color</string>
    <string name="filter_by_occasion">Filter by Occasion</string>
    <string name="filter_clear_all">Clear All Filters</string>

    <!-- Settings -->
    <string name="settings_language">Language</string>
    <string name="settings_theme">Theme</string>
    <string name="settings_notifications">Notifications</string>
    <string name="settings_privacy">Privacy</string>
    <string name="settings_about">About</string>

    <!-- Error Messages -->
    <string name="error_generic">An error occurred</string>
    <string name="error_network">Network error</string>
    <string name="error_permission">Permission required</string>
    <string name="error_file_not_found">File not found</string>
    <string name="error_invalid_data">Invalid data</string>

    <!-- Accessibility -->
    <string name="accessibility_add_button">Add new clothing item</string>
    <string name="accessibility_search_button">Search clothing</string>
    <string name="accessibility_filter_button">Filter clothing</string>
    <string name="accessibility_clothing_image">Clothing item image</string>
</resources>
