package com.wardrobe.app.ui.binding;

import android.graphics.drawable.Drawable;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.databinding.BindingAdapter;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CircleCrop;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.wardrobe.app.R;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * DataBinding适配器
 * 提供自定义的绑定方法，简化布局文件中的数据绑定
 * 符合Android DataBinding最佳实践
 */
public class BindingAdapters {
    
    // ==================== 图片加载适配器 ====================
    
    /**
     * 加载图片到ImageView
     */
    @BindingAdapter("imageUrl")
    public static void loadImage(ImageView imageView, String imageUrl) {
        if (imageUrl != null && !imageUrl.isEmpty()) {
            Glide.with(imageView.getContext())
                    .load(imageUrl)
                    .placeholder(R.drawable.ic_clothing_placeholder)
                    .error(R.drawable.ic_clothing_placeholder)
                    .into(imageView);
        } else {
            imageView.setImageResource(R.drawable.ic_clothing_placeholder);
        }
    }
    
    /**
     * 加载圆形图片
     */
    @BindingAdapter("circleImageUrl")
    public static void loadCircleImage(ImageView imageView, String imageUrl) {
        if (imageUrl != null && !imageUrl.isEmpty()) {
            Glide.with(imageView.getContext())
                    .load(imageUrl)
                    .transform(new CircleCrop())
                    .placeholder(R.drawable.ic_clothing_placeholder)
                    .error(R.drawable.ic_clothing_placeholder)
                    .into(imageView);
        } else {
            imageView.setImageResource(R.drawable.ic_clothing_placeholder);
        }
    }
    
    /**
     * 加载圆角图片
     */
    @BindingAdapter("roundedImageUrl")
    public static void loadRoundedImage(ImageView imageView, String imageUrl) {
        if (imageUrl != null && !imageUrl.isEmpty()) {
            Glide.with(imageView.getContext())
                    .load(imageUrl)
                    .transform(new RoundedCorners(16))
                    .placeholder(R.drawable.ic_clothing_placeholder)
                    .error(R.drawable.ic_clothing_placeholder)
                    .into(imageView);
        } else {
            imageView.setImageResource(R.drawable.ic_clothing_placeholder);
        }
    }
    
    /**
     * 设置图片资源
     */
    @BindingAdapter("srcCompat")
    public static void setImageResource(ImageView imageView, int resourceId) {
        if (resourceId != 0) {
            imageView.setImageResource(resourceId);
        }
    }
    
    /**
     * 设置图片资源（Drawable）
     */
    @BindingAdapter("srcDrawable")
    public static void setImageDrawable(ImageView imageView, Drawable drawable) {
        imageView.setImageDrawable(drawable);
    }
    
    // ==================== 文本显示适配器 ====================
    
    /**
     * 格式化日期显示
     */
    @BindingAdapter("dateText")
    public static void setDateText(TextView textView, Date date) {
        if (date != null) {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy年MM月dd日", Locale.getDefault());
            textView.setText(formatter.format(date));
        } else {
            textView.setText("");
        }
    }
    
    /**
     * 格式化日期时间显示
     */
    @BindingAdapter("dateTimeText")
    public static void setDateTimeText(TextView textView, Date date) {
        if (date != null) {
            SimpleDateFormat formatter = new SimpleDateFormat("MM月dd日 HH:mm", Locale.getDefault());
            textView.setText(formatter.format(date));
        } else {
            textView.setText("");
        }
    }
    
    /**
     * 显示评分星级
     */
    @BindingAdapter("ratingText")
    public static void setRatingText(TextView textView, int rating) {
        StringBuilder stars = new StringBuilder();
        for (int i = 0; i < 5; i++) {
            if (i < rating) {
                stars.append("★");
            } else {
                stars.append("☆");
            }
        }
        textView.setText(stars.toString());
    }
    
    /**
     * 显示数量文本
     */
    @BindingAdapter("countText")
    public static void setCountText(TextView textView, int count) {
        textView.setText(String.valueOf(count));
    }
    
    /**
     * 显示百分比文本
     */
    @BindingAdapter("percentageText")
    public static void setPercentageText(TextView textView, double percentage) {
        textView.setText(String.format(Locale.getDefault(), "%.1f%%", percentage * 100));
    }
    
    /**
     * 显示文件大小
     */
    @BindingAdapter("fileSizeText")
    public static void setFileSizeText(TextView textView, long sizeInBytes) {
        String[] units = {"B", "KB", "MB", "GB"};
        double size = sizeInBytes;
        int unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        textView.setText(String.format(Locale.getDefault(), "%.1f %s", size, units[unitIndex]));
    }
    
    // ==================== 视图状态适配器 ====================
    
    /**
     * 设置视图可见性
     */
    @BindingAdapter("visibleIf")
    public static void setVisibleIf(View view, boolean visible) {
        view.setVisibility(visible ? View.VISIBLE : View.GONE);
    }
    
    /**
     * 设置视图可见性（反向）
     */
    @BindingAdapter("goneIf")
    public static void setGoneIf(View view, boolean gone) {
        view.setVisibility(gone ? View.GONE : View.VISIBLE);
    }
    
    /**
     * 设置视图不可见性
     */
    @BindingAdapter("invisibleIf")
    public static void setInvisibleIf(View view, boolean invisible) {
        view.setVisibility(invisible ? View.INVISIBLE : View.VISIBLE);
    }
    
    /**
     * 设置视图启用状态
     */
    @BindingAdapter("enabledIf")
    public static void setEnabledIf(View view, boolean enabled) {
        view.setEnabled(enabled);
        view.setAlpha(enabled ? 1.0f : 0.5f);
    }
    
    // ==================== RecyclerView适配器 ====================
    
    /**
     * 设置RecyclerView的数据
     */
    @BindingAdapter("items")
    public static void setRecyclerViewItems(RecyclerView recyclerView, List<?> items) {
        if (recyclerView.getAdapter() instanceof BindableAdapter) {
            ((BindableAdapter<?>) recyclerView.getAdapter()).setItems(items);
        }
    }
    
    /**
     * 可绑定的适配器接口
     */
    public interface BindableAdapter<T> {
        void setItems(List<T> items);
    }
    
    // ==================== 动画适配器 ====================
    
    /**
     * 淡入动画
     */
    @BindingAdapter("fadeIn")
    public static void setFadeIn(View view, boolean fadeIn) {
        if (fadeIn) {
            view.setAlpha(0f);
            view.animate()
                    .alpha(1f)
                    .setDuration(300)
                    .start();
        }
    }
    
    /**
     * 滑入动画
     */
    @BindingAdapter("slideIn")
    public static void setSlideIn(View view, boolean slideIn) {
        if (slideIn) {
            view.setTranslationY(100f);
            view.animate()
                    .translationY(0f)
                    .setDuration(300)
                    .start();
        }
    }
    
    /**
     * 缩放动画
     */
    @BindingAdapter("scaleIn")
    public static void setScaleIn(View view, boolean scaleIn) {
        if (scaleIn) {
            view.setScaleX(0.8f);
            view.setScaleY(0.8f);
            view.animate()
                    .scaleX(1f)
                    .scaleY(1f)
                    .setDuration(300)
                    .start();
        }
    }
    
    // ==================== 颜色和样式适配器 ====================
    
    /**
     * 设置背景颜色
     */
    @BindingAdapter("backgroundColor")
    public static void setBackgroundColor(View view, int color) {
        if (color != 0) {
            view.setBackgroundColor(color);
        }
    }
    
    /**
     * 设置文本颜色
     */
    @BindingAdapter("textColor")
    public static void setTextColor(TextView textView, int color) {
        if (color != 0) {
            textView.setTextColor(color);
        }
    }
    
    /**
     * 设置选中状态
     */
    @BindingAdapter("selected")
    public static void setSelected(View view, boolean selected) {
        view.setSelected(selected);
    }
    
    /**
     * 设置激活状态
     */
    @BindingAdapter("activated")
    public static void setActivated(View view, boolean activated) {
        view.setActivated(activated);
    }
    
    // ==================== 列表和标签适配器 ====================
    
    /**
     * 显示标签列表
     */
    @BindingAdapter("tagList")
    public static void setTagList(TextView textView, List<String> tags) {
        if (tags != null && !tags.isEmpty()) {
            StringBuilder tagText = new StringBuilder();
            for (int i = 0; i < tags.size(); i++) {
                if (i > 0) tagText.append(" · ");
                tagText.append(tags.get(i));
            }
            textView.setText(tagText.toString());
        } else {
            textView.setText("");
        }
    }
    
    /**
     * 显示空状态文本
     */
    @BindingAdapter("emptyText")
    public static void setEmptyText(TextView textView, boolean isEmpty) {
        if (isEmpty) {
            textView.setText("暂无数据");
            textView.setVisibility(View.VISIBLE);
        } else {
            textView.setVisibility(View.GONE);
        }
    }
    
    /**
     * 显示加载状态
     */
    @BindingAdapter("loading")
    public static void setLoading(View view, boolean loading) {
        if (loading) {
            view.setVisibility(View.VISIBLE);
            // 可以添加旋转动画
            view.animate()
                    .rotation(360f)
                    .setDuration(1000)
                    .withEndAction(() -> {
                        if (view.getVisibility() == View.VISIBLE) {
                            view.setRotation(0f);
                            setLoading(view, true); // 继续动画
                        }
                    })
                    .start();
        } else {
            view.setVisibility(View.GONE);
            view.clearAnimation();
        }
    }
}
