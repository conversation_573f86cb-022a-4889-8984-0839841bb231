<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - SecurityUtilsTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>SecurityUtilsTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/com.wardrobe.app.utils.html">com.wardrobe.app.utils</a> &gt; SecurityUtilsTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">24</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">1</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.577s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox skipped" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Tests</a>
</li>
<li>
<a href="#tab1">Standard error</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">testCalculateFileMD5</td>
<td class="success">0.007s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testCalculateFileMD5_NonExistentFile</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testDecryptData_InvalidInput</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="skipped">testEncryptAndDecryptData</td>
<td class="skipped">-</td>
<td class="skipped">ignored</td>
</tr>
<tr>
<td class="success">testEncryptData_InvalidInput</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGenerateRandomFileName</td>
<td class="success">0.026s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGenerateSafeFileName</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testGenerateSafeFileName_EmptyInput</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testSecureDeleteFile</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testSecureDeleteFile_NonExistentFile</td>
<td class="success">0.029s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testValidateClothingData_InvalidDescription</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testValidateClothingData_InvalidName</td>
<td class="success">0.005s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testValidateClothingData_ValidData</td>
<td class="success">0.012s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testValidateColor_InvalidColor</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testValidateColor_ValidColor</td>
<td class="success">0.004s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testValidateDescription_InvalidDescription</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testValidateDescription_ValidDescription</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testValidateFileName_InvalidFileName</td>
<td class="success">0.449s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testValidateFileName_ValidFileName</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testValidateImagePath_InvalidPath</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testValidateImagePath_ValidPath</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testValidateName_InvalidName</td>
<td class="success">0.003s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testValidateName_ValidName</td>
<td class="success">0.002s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">testValidationResult</td>
<td class="success">0.001s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div id="tab1" class="tab">
<h2>Standard error</h2>
<span class="code">
<pre>WARNING: A Java agent has been loaded dynamically (C:\Users\<USER>\.gradle\caches\8.11.1\transforms\597f97b58f5d3034e974f4bc3b5fd56f\transformed\jetified-byte-buddy-agent-1.14.8.jar)
WARNING: If a serviceability tool is in use, please run with -XX:+EnableDynamicAgentLoading to hide this warning
WARNING: If a serviceability tool is not in use, please run with -Djdk.instrument.traceUsage for more information
WARNING: Dynamic loading of agents will be disallowed by default in a future release
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.11.1</a> at 2025年6月25日 20:57:37</p>
</div>
</div>
</body>
</html>
