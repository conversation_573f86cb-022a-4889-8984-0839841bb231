<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_wardrobe" modulePackage="com.wardrobe.app" filePath="app\src\main\res\layout\fragment_wardrobe.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_wardrobe_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="185" endOffset="53"/></Target><Target id="@+id/multi_select_toolbar" tag="layout/fragment_wardrobe_0" include="multi_select_toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="11" endOffset="47"/></Target><Target id="@+id/et_search" view="EditText"><Expressions/><location startLine="36" startOffset="12" endLine="49" endOffset="38"/></Target><Target id="@+id/iv_clear_search" view="ImageView"><Expressions/><location startLine="51" startOffset="12" endLine="59" endOffset="87"/></Target><Target id="@+id/chip_group_filter" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="93" startOffset="12" endLine="98" endOffset="46"/></Target><Target id="@+id/group_recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="103" startOffset="8" endLine="110" endOffset="58"/></Target><Target id="@+id/tv_loading_state" view="LinearLayout"><Expressions/><location startLine="113" startOffset="8" endLine="134" endOffset="22"/></Target><Target id="@+id/tv_empty_state" view="LinearLayout"><Expressions/><location startLine="137" startOffset="8" endLine="170" endOffset="22"/></Target><Target id="@+id/fab_add_clothing" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="175" startOffset="4" endLine="183" endOffset="33"/></Target></Targets></Layout>