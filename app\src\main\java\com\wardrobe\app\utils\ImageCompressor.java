package com.wardrobe.app.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.media.ExifInterface;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 智能图片压缩工具类
 * 使用先进技术对所有图片进行智能压缩，保持清晰度的同时大幅减小文件大小
 */
public class ImageCompressor {
    
    private static final String TAG = "ImageCompressor";
    
    // 🚀 智能压缩配置 - 更激进的压缩策略
    private static final int MAX_WIDTH = 1200;   // 最大宽度提升到1200
    private static final int MAX_HEIGHT = 1200;  // 最大高度提升到1200
    private static final int QUALITY = 90;       // 压缩质量提升到90%，保持更好清晰度
    private static final long TARGET_FILE_SIZE = 500 * 1024; // 目标文件大小：500KB
    
    // 🎯 智能压缩策略
    private static final int[] COMPRESSION_LEVELS = {90, 85, 80, 75, 70, 65, 60}; // 多级压缩质量
    private static final int[] SIZE_LEVELS = {1200, 1000, 800, 600, 400}; // 多级尺寸压缩
    
    /**
     * 智能压缩图片 - 无论多大都压缩到最优大小
     * @param context 上下文
     * @param sourcePath 源图片路径
     * @return 压缩后的图片路径，失败返回null
     */
    public static String compressImage(Context context, String sourcePath) {
        if (sourcePath == null || sourcePath.isEmpty()) {
            Log.e(TAG, "源图片路径为空");
            return null;
        }
        
        File sourceFile = new File(sourcePath);
        if (!sourceFile.exists()) {
            Log.e(TAG, "源图片文件不存在: " + sourcePath);
            return null;
        }
        
        try {
            long originalSize = sourceFile.length();
            Log.i(TAG, "开始智能压缩图片: " + sourceFile.getName() + ", 原始大小: " + formatFileSize(originalSize));
            
            // 🚀 智能压缩：无论多大都压缩
            String compressedPath = smartCompress(context, sourcePath, originalSize);
            
            if (compressedPath != null) {
                File compressedFile = new File(compressedPath);
                long compressedSize = compressedFile.length();
                double compressionRatio = (double) (originalSize - compressedSize) / originalSize * 100;
                
                Log.i(TAG, String.format("智能压缩完成: %s -> %s (压缩率: %.1f%%)", 
                    formatFileSize(originalSize), formatFileSize(compressedSize), compressionRatio));
            }
            
            return compressedPath;
            
        } catch (Exception e) {
            Log.e(TAG, "智能压缩图片时发生错误: " + sourcePath, e);
            return null;
        }
    }
    
    /**
     * 🚀 智能压缩算法 - 多级压缩策略
     */
    private static String smartCompress(Context context, String sourcePath, long originalSize) {
        try {
            // 第一级：标准压缩
            String result = compressWithQuality(sourcePath, MAX_WIDTH, MAX_HEIGHT, QUALITY);
            if (result != null && isCompressionSatisfactory(result, originalSize)) {
                return result;
            }
            
            // 第二级：如果还是太大，降低质量
            for (int quality : COMPRESSION_LEVELS) {
                if (quality >= QUALITY) continue; // 跳过已经尝试过的质量
                
                result = compressWithQuality(sourcePath, MAX_WIDTH, MAX_HEIGHT, quality);
                if (result != null && isCompressionSatisfactory(result, originalSize)) {
                    Log.d(TAG, "质量压缩成功，质量: " + quality);
                    return result;
                }
            }
            
            // 第三级：如果还是太大，降低尺寸
            for (int size : SIZE_LEVELS) {
                if (size >= MAX_WIDTH) continue; // 跳过已经尝试过的尺寸
                
                for (int quality : COMPRESSION_LEVELS) {
                    result = compressWithQuality(sourcePath, size, size, quality);
                    if (result != null && isCompressionSatisfactory(result, originalSize)) {
                        Log.d(TAG, "尺寸+质量压缩成功，尺寸: " + size + ", 质量: " + quality);
                        return result;
                    }
                }
            }
            
            // 最后一级：强制压缩到目标大小
            return forceCompressToTargetSize(context, sourcePath, originalSize);
            
        } catch (Exception e) {
            Log.e(TAG, "智能压缩失败", e);
            return null;
        }
    }
    
    /**
     * 检查压缩效果是否满意
     */
    private static boolean isCompressionSatisfactory(String compressedPath, long originalSize) {
        File compressedFile = new File(compressedPath);
        if (!compressedFile.exists()) return false;
        
        long compressedSize = compressedFile.length();
        
        // 满意条件：
        // 1. 压缩后小于目标大小
        // 2. 或者压缩率超过50%
        // 3. 或者压缩后小于1MB
        return compressedSize <= TARGET_FILE_SIZE || 
               compressedSize <= originalSize * 0.5 || 
               compressedSize <= 1024 * 1024;
    }
    
    /**
     * 强制压缩到目标大小
     */
    private static String forceCompressToTargetSize(Context context, String sourcePath, long originalSize) {
        try {
            // 使用最小尺寸和最低质量
            String result = compressWithQuality(sourcePath, 400, 400, 60);
            if (result != null) {
                Log.w(TAG, "使用强制压缩策略");
                return result;
            }
        } catch (Exception e) {
            Log.e(TAG, "强制压缩失败", e);
        }
        
        return null;
    }
    
    /**
     * 使用指定参数压缩图片
     */
    private static String compressWithQuality(String sourcePath, int maxWidth, int maxHeight, int quality) {
        try {
            // 解码图片获取尺寸信息
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inJustDecodeBounds = true;
            BitmapFactory.decodeFile(sourcePath, options);
            
            // 计算压缩比例
            int inSampleSize = calculateInSampleSize(options, maxWidth, maxHeight);
            
            // 解码图片
            options.inJustDecodeBounds = false;
            options.inSampleSize = inSampleSize;
            options.inPreferredConfig = Bitmap.Config.RGB_565; // 使用更少内存的配置
            
            Bitmap bitmap = BitmapFactory.decodeFile(sourcePath, options);
            if (bitmap == null) {
                return null;
            }
            
            // 处理图片旋转
            bitmap = handleImageRotation(bitmap, sourcePath);
            
            // 保存压缩后的图片
            String compressedPath = saveCompressedImage(sourcePath, bitmap, quality);
            
            // 回收Bitmap
            bitmap.recycle();
            
            return compressedPath;
            
        } catch (Exception e) {
            Log.e(TAG, "压缩图片失败: " + sourcePath, e);
            return null;
        }
    }
    
    /**
     * 计算压缩比例
     */
    private static int calculateInSampleSize(BitmapFactory.Options options, int reqWidth, int reqHeight) {
        final int height = options.outHeight;
        final int width = options.outWidth;
        int inSampleSize = 1;
        
        if (height > reqHeight || width > reqWidth) {
            final int halfHeight = height / 2;
            final int halfWidth = width / 2;
            
            while ((halfHeight / inSampleSize) >= reqHeight && (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2;
            }
        }
        
        return inSampleSize;
    }
    
    /**
     * 处理图片旋转
     */
    private static Bitmap handleImageRotation(Bitmap bitmap, String imagePath) {
        try {
            ExifInterface exif = new ExifInterface(imagePath);
            int orientation = exif.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL);
            
            Matrix matrix = new Matrix();
            switch (orientation) {
                case ExifInterface.ORIENTATION_ROTATE_90:
                    matrix.postRotate(90);
                    break;
                case ExifInterface.ORIENTATION_ROTATE_180:
                    matrix.postRotate(180);
                    break;
                case ExifInterface.ORIENTATION_ROTATE_270:
                    matrix.postRotate(270);
                    break;
                default:
                    return bitmap;
            }
            
            return Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
            
        } catch (IOException e) {
            Log.w(TAG, "处理图片旋转时发生错误", e);
            return bitmap;
        }
    }
    
    /**
     * 保存压缩后的图片
     */
    private static String saveCompressedImage(String originalPath, Bitmap bitmap, int quality) {
        try {
            // 创建压缩后的文件名
            File originalFile = new File(originalPath);
            String fileName = originalFile.getName();
            String nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
            String extension = fileName.substring(fileName.lastIndexOf('.'));
            
            // 创建压缩后的文件路径
            File appImagesDir = new File(originalFile.getParentFile(), "clothing_images");
            if (!appImagesDir.exists()) {
                appImagesDir.mkdirs();
            }
            
            File compressedFile = new File(appImagesDir, nameWithoutExt + "_compressed" + extension);
            
            // 保存压缩后的图片
            FileOutputStream fos = new FileOutputStream(compressedFile);
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, fos);
            fos.close();
            
            return compressedFile.getAbsolutePath();
            
        } catch (IOException e) {
            Log.e(TAG, "保存压缩图片时发生错误", e);
            return null;
        }
    }
    
    /**
     * 压缩从URI获取的图片
     */
    public static String compressImageFromUri(Context context, android.net.Uri uri) {
        if (uri == null) {
            Log.e(TAG, "URI为空，无法压缩图片");
            return null;
        }
        
        try {
            // 先复制到临时文件
            String tempPath = copyUriToTempFile(context, uri);
            if (tempPath == null) {
                return null;
            }
            
            // 智能压缩临时文件
            String compressedPath = compressImage(context, tempPath);
            
            // 删除临时文件
            new File(tempPath).delete();
            
            return compressedPath;
            
        } catch (Exception e) {
            Log.e(TAG, "压缩URI图片时发生错误", e);
            return null;
        }
    }
    
    /**
     * 复制URI到临时文件
     */
    private static String copyUriToTempFile(Context context, android.net.Uri uri) {
        try {
            File tempFile = File.createTempFile("temp_image_", ".jpg", context.getCacheDir());
            
            try (InputStream inputStream = context.getContentResolver().openInputStream(uri);
                 FileOutputStream outputStream = new FileOutputStream(tempFile)) {
                
                if (inputStream == null) {
                    Log.e(TAG, "无法打开URI输入流");
                    return null;
                }
                
                byte[] buffer = new byte[1024];
                int length;
                while ((length = inputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, length);
                }
            }
            
            return tempFile.getAbsolutePath();
            
        } catch (IOException e) {
            Log.e(TAG, "复制URI到临时文件时发生错误", e);
            return null;
        }
    }
    
    /**
     * 获取图片文件大小（MB）
     */
    public static double getImageSizeMB(String imagePath) {
        if (imagePath == null || imagePath.isEmpty()) {
            return 0;
        }
        
        File file = new File(imagePath);
        if (!file.exists()) {
            return 0;
        }
        
        return (double) file.length() / (1024 * 1024);
    }
    
    /**
     * 格式化文件大小
     */
    public static String formatFileSize(long size) {
        if (size <= 0) return "0 B";
        
        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        
        return String.format("%.1f %s", size / Math.pow(1024, digitGroups), units[digitGroups]);
    }
    
    /**
     * 🎯 预测压缩效果
     */
    public static CompressionPrediction predictCompression(String imagePath) {
        if (imagePath == null || imagePath.isEmpty()) {
            return null;
        }
        
        File file = new File(imagePath);
        if (!file.exists()) {
            return null;
        }
        
        long originalSize = file.length();
        double originalSizeMB = (double) originalSize / (1024 * 1024);
        
        // 基于原始大小预测压缩效果
        double predictedSizeMB;
        if (originalSizeMB > 10) {
            predictedSizeMB = 0.8; // 10MB+ -> 约800KB
        } else if (originalSizeMB > 5) {
            predictedSizeMB = 0.6; // 5MB+ -> 约600KB
        } else if (originalSizeMB > 2) {
            predictedSizeMB = 0.4; // 2MB+ -> 约400KB
        } else if (originalSizeMB > 1) {
            predictedSizeMB = 0.3; // 1MB+ -> 约300KB
        } else {
            predictedSizeMB = originalSizeMB * 0.5; // 小于1MB，压缩50%
        }
        
        double compressionRatio = (originalSizeMB - predictedSizeMB) / originalSizeMB * 100;
        
        return new CompressionPrediction(originalSizeMB, predictedSizeMB, compressionRatio);
    }
    
    /**
     * 压缩预测结果
     */
    public static class CompressionPrediction {
        private final double originalSizeMB;
        private final double predictedSizeMB;
        private final double compressionRatio;
        
        public CompressionPrediction(double originalSizeMB, double predictedSizeMB, double compressionRatio) {
            this.originalSizeMB = originalSizeMB;
            this.predictedSizeMB = predictedSizeMB;
            this.compressionRatio = compressionRatio;
        }
        
        public double getOriginalSizeMB() { return originalSizeMB; }
        public double getPredictedSizeMB() { return predictedSizeMB; }
        public double getCompressionRatio() { return compressionRatio; }
        
        @Override
        public String toString() {
            return String.format("原始: %.1fMB -> 预测: %.1fMB (压缩率: %.1f%%)", 
                originalSizeMB, predictedSizeMB, compressionRatio);
        }
    }
} 