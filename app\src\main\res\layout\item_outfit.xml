<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardBackgroundColor="@color/surface_primary"
    app:cardCornerRadius="16dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- 搭配名称 -->
        <TextView
            android:id="@+id/tv_outfit_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="搭配建议 1"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- 搭配描述 -->
        <TextView
            android:id="@+id/tv_outfit_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="白色T恤 + 牛仔裤 + 运动鞋"
            android:textColor="@color/text_secondary"
            android:textSize="14sp" />

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_save_outfit"
                style="@style/ButtonSecondary"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="保存搭配"
                android:textSize="14sp" />

            <Button
                android:id="@+id/btn_share_outfit"
                style="@style/ButtonTertiary"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:text="分享"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView> 