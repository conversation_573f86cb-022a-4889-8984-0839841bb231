<dependencies>
  <compile
      roots=":@@:app::debug,androidx.test.ext:junit:1.1.5@aar,androidx.test.espresso:espresso-core:3.5.1@aar,androidx.test:rules:1.5.0@aar,androidx.test:runner:1.5.2@aar,androidx.databinding:viewbinding:8.10.1@aar,com.google.android.material:material:1.12.0@aar,com.github.QuadFlask:colorpicker:0.0.13@aar,androidx.appcompat:appcompat-resources:1.7.1@aar,androidx.appcompat:appcompat:1.7.1@aar,com.github.bumptech.glide:glide:4.16.0@aar,androidx.viewpager2:viewpager2:1.0.0@aar,androidx.fragment:fragment:1.5.4@aar,androidx.fragment:fragment:1.5.4@aar,androidx.activity:activity:1.10.1@aar,androidx.test:core:1.5.0@aar,androidx.drawerlayout:drawerlayout:1.1.1@aar,androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar,androidx.dynamicanimation:dynamicanimation:1.0.0@aar,androidx.transition:transition:1.5.0@aar,androidx.vectordrawable:vectordrawable-animated:1.1.0@aar,androidx.vectordrawable:vectordrawable:1.1.0@aar,androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar,androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar,androidx.legacy:legacy-support-core-utils:1.0.0@aar,androidx.loader:loader:1.0.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-livedata:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar,androidx.core:core-ktx:1.13.0@aar,androidx.viewpager:viewpager:1.0.0@aar,androidx.recyclerview:recyclerview:1.1.0@aar,androidx.customview:customview:1.1.0@aar,androidx.core:core:1.13.0@aar,androidx.core:core:1.13.0@aar,androidx.lifecycle:lifecycle-runtime:2.7.0@aar,androidx.lifecycle:lifecycle-common:2.7.0@jar,androidx.lifecycle:lifecycle-livedata-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar,androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar,com.squareup.leakcanary:leakcanary-android:2.12@aar,com.squareup.leakcanary:leakcanary-object-watcher-android:2.12@aar,androidx.savedstate:savedstate:1.2.1@aar,androidx.test.services:storage:1.4.2@aar,androidx.test:monitor:1.6.1@aar,androidx.test:annotation:1.0.1@aar,androidx.annotation:annotation-experimental:1.4.0@aar,androidx.core:core-viewtree:1.0.0@aar,androidx.security:security-crypto:1.1.0-alpha06@aar,androidx.cursoradapter:cursoradapter:1.0.0@aar,androidx.cardview:cardview:1.0.0@aar,com.github.bumptech.glide:gifdecoder:4.16.0@aar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.versionedparcelable:versionedparcelable:1.1.1@aar,androidx.collection:collection:1.1.0@jar,androidx.interpolator:interpolator:1.0.0@aar,androidx.arch.core:core-runtime:2.2.0@aar,androidx.arch.core:core-common:2.2.0@jar,androidx.documentfile:documentfile:1.0.0@aar,androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar,androidx.print:print:1.0.0@aar,androidx.annotation:annotation-jvm:1.8.1@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20@jar,org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,androidx.constraintlayout:constraintlayout:2.2.1@aar,com.google.code.gson:gson:2.10.1@jar,junit:junit:4.13.2@jar,org.hamcrest:hamcrest-integration:1.3@jar,org.hamcrest:hamcrest-library:1.3@jar,org.hamcrest:hamcrest-core:1.3@jar,org.jetbrains:annotations:23.0.0@jar,androidx.tracing:tracing:1.0.0@aar,com.google.code.findbugs:jsr305:2.0.2@jar,com.google.guava:listenablefuture:1.0@jar,androidx.test.espresso:espresso-idling-resource:3.5.1@aar,com.squareup:javawriter:2.1.1@jar,javax.inject:javax.inject:1@jar,com.squareup.leakcanary:leakcanary-android-core:2.12@aar,androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar,com.github.bumptech.glide:disklrucache:4.16.0@jar,com.github.bumptech.glide:annotations:4.16.0@jar,androidx.exifinterface:exifinterface:1.3.6@aar,com.squareup.leakcanary:shark-android:2.12@jar,com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.12@aar,com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.12@aar,com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.12@aar,com.squareup.leakcanary:shark:2.12@jar,com.squareup.leakcanary:leakcanary-object-watcher:2.12@jar,com.squareup.leakcanary:leakcanary-android-utils:2.12@aar,com.squareup.leakcanary:shark-graph:2.12@jar,com.squareup.leakcanary:shark-hprof:2.12@jar,com.squareup.leakcanary:shark-log:2.12@jar">
    <dependency
        name=":@@:app::debug"
        simpleName="artifacts::app"/>
    <dependency
        name="androidx.test.ext:junit:1.1.5@aar"
        simpleName="androidx.test.ext:junit"/>
    <dependency
        name="androidx.test.espresso:espresso-core:3.5.1@aar"
        simpleName="androidx.test.espresso:espresso-core"/>
    <dependency
        name="androidx.test:rules:1.5.0@aar"
        simpleName="androidx.test:rules"/>
    <dependency
        name="androidx.test:runner:1.5.2@aar"
        simpleName="androidx.test:runner"/>
    <dependency
        name="androidx.databinding:viewbinding:8.10.1@aar"
        simpleName="androidx.databinding:viewbinding"/>
    <dependency
        name="com.google.android.material:material:1.12.0@aar"
        simpleName="com.google.android.material:material"/>
    <dependency
        name="com.github.QuadFlask:colorpicker:0.0.13@aar"
        simpleName="com.github.QuadFlask:colorpicker"/>
    <dependency
        name="androidx.appcompat:appcompat-resources:1.7.1@aar"
        simpleName="androidx.appcompat:appcompat-resources"/>
    <dependency
        name="androidx.appcompat:appcompat:1.7.1@aar"
        simpleName="androidx.appcompat:appcompat"/>
    <dependency
        name="com.github.bumptech.glide:glide:4.16.0@aar"
        simpleName="com.github.bumptech.glide:glide"/>
    <dependency
        name="androidx.viewpager2:viewpager2:1.0.0@aar"
        simpleName="androidx.viewpager2:viewpager2"/>
    <dependency
        name="androidx.fragment:fragment:1.5.4@aar"
        simpleName="androidx.fragment:fragment"/>
    <dependency
        name="androidx.activity:activity:1.10.1@aar"
        simpleName="androidx.activity:activity"/>
    <dependency
        name="androidx.test:core:1.5.0@aar"
        simpleName="androidx.test:core"/>
    <dependency
        name="androidx.drawerlayout:drawerlayout:1.1.1@aar"
        simpleName="androidx.drawerlayout:drawerlayout"/>
    <dependency
        name="androidx.coordinatorlayout:coordinatorlayout:1.1.0@aar"
        simpleName="androidx.coordinatorlayout:coordinatorlayout"/>
    <dependency
        name="androidx.dynamicanimation:dynamicanimation:1.0.0@aar"
        simpleName="androidx.dynamicanimation:dynamicanimation"/>
    <dependency
        name="androidx.transition:transition:1.5.0@aar"
        simpleName="androidx.transition:transition"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable-animated"/>
    <dependency
        name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
        simpleName="androidx.vectordrawable:vectordrawable"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-core-ktx"/>
    <dependency
        name="androidx.legacy:legacy-support-core-utils:1.0.0@aar"
        simpleName="androidx.legacy:legacy-support-core-utils"/>
    <dependency
        name="androidx.loader:loader:1.0.0@aar"
        simpleName="androidx.loader:loader"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-savedstate"/>
    <dependency
        name="androidx.core:core-ktx:1.13.0@aar"
        simpleName="androidx.core:core-ktx"/>
    <dependency
        name="androidx.viewpager:viewpager:1.0.0@aar"
        simpleName="androidx.viewpager:viewpager"/>
    <dependency
        name="androidx.recyclerview:recyclerview:1.1.0@aar"
        simpleName="androidx.recyclerview:recyclerview"/>
    <dependency
        name="androidx.customview:customview:1.1.0@aar"
        simpleName="androidx.customview:customview"/>
    <dependency
        name="androidx.core:core:1.13.0@aar"
        simpleName="androidx.core:core"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.lifecycle:lifecycle-livedata-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-livedata-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-viewmodel-ktx"/>
    <dependency
        name="androidx.lifecycle:lifecycle-runtime-ktx:2.7.0@aar"
        simpleName="androidx.lifecycle:lifecycle-runtime-ktx"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-android:2.12@aar"
        simpleName="com.squareup.leakcanary:leakcanary-android"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-object-watcher-android:2.12@aar"
        simpleName="com.squareup.leakcanary:leakcanary-object-watcher-android"/>
    <dependency
        name="androidx.savedstate:savedstate:1.2.1@aar"
        simpleName="androidx.savedstate:savedstate"/>
    <dependency
        name="androidx.test.services:storage:1.4.2@aar"
        simpleName="androidx.test.services:storage"/>
    <dependency
        name="androidx.test:monitor:1.6.1@aar"
        simpleName="androidx.test:monitor"/>
    <dependency
        name="androidx.test:annotation:1.0.1@aar"
        simpleName="androidx.test:annotation"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="androidx.core:core-viewtree:1.0.0@aar"
        simpleName="androidx.core:core-viewtree"/>
    <dependency
        name="androidx.security:security-crypto:1.1.0-alpha06@aar"
        simpleName="androidx.security:security-crypto"/>
    <dependency
        name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
        simpleName="androidx.cursoradapter:cursoradapter"/>
    <dependency
        name="androidx.cardview:cardview:1.0.0@aar"
        simpleName="androidx.cardview:cardview"/>
    <dependency
        name="com.github.bumptech.glide:gifdecoder:4.16.0@aar"
        simpleName="com.github.bumptech.glide:gifdecoder"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
        simpleName="androidx.versionedparcelable:versionedparcelable"/>
    <dependency
        name="androidx.collection:collection:1.1.0@jar"
        simpleName="androidx.collection:collection"/>
    <dependency
        name="androidx.interpolator:interpolator:1.0.0@aar"
        simpleName="androidx.interpolator:interpolator"/>
    <dependency
        name="androidx.arch.core:core-runtime:2.2.0@aar"
        simpleName="androidx.arch.core:core-runtime"/>
    <dependency
        name="androidx.arch.core:core-common:2.2.0@jar"
        simpleName="androidx.arch.core:core-common"/>
    <dependency
        name="androidx.documentfile:documentfile:1.0.0@aar"
        simpleName="androidx.documentfile:documentfile"/>
    <dependency
        name="androidx.localbroadcastmanager:localbroadcastmanager:1.0.0@aar"
        simpleName="androidx.localbroadcastmanager:localbroadcastmanager"/>
    <dependency
        name="androidx.print:print:1.0.0@aar"
        simpleName="androidx.print:print"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk8"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib-jdk7"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="androidx.constraintlayout:constraintlayout:2.2.1@aar"
        simpleName="androidx.constraintlayout:constraintlayout"/>
    <dependency
        name="com.google.code.gson:gson:2.10.1@jar"
        simpleName="com.google.code.gson:gson"/>
    <dependency
        name="junit:junit:4.13.2@jar"
        simpleName="junit:junit"/>
    <dependency
        name="org.hamcrest:hamcrest-integration:1.3@jar"
        simpleName="org.hamcrest:hamcrest-integration"/>
    <dependency
        name="org.hamcrest:hamcrest-library:1.3@jar"
        simpleName="org.hamcrest:hamcrest-library"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="com.google.code.findbugs:jsr305:2.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="androidx.test.espresso:espresso-idling-resource:3.5.1@aar"
        simpleName="androidx.test.espresso:espresso-idling-resource"/>
    <dependency
        name="com.squareup:javawriter:2.1.1@jar"
        simpleName="com.squareup:javawriter"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-android-core:2.12@aar"
        simpleName="com.squareup.leakcanary:leakcanary-android-core"/>
    <dependency
        name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
        simpleName="androidx.resourceinspection:resourceinspection-annotation"/>
    <dependency
        name="com.github.bumptech.glide:disklrucache:4.16.0@jar"
        simpleName="com.github.bumptech.glide:disklrucache"/>
    <dependency
        name="com.github.bumptech.glide:annotations:4.16.0@jar"
        simpleName="com.github.bumptech.glide:annotations"/>
    <dependency
        name="androidx.exifinterface:exifinterface:1.3.6@aar"
        simpleName="androidx.exifinterface:exifinterface"/>
    <dependency
        name="com.squareup.leakcanary:shark-android:2.12@jar"
        simpleName="com.squareup.leakcanary:shark-android"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.12@aar"
        simpleName="com.squareup.leakcanary:leakcanary-object-watcher-android-androidx"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.12@aar"
        simpleName="com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.12@aar"
        simpleName="com.squareup.leakcanary:leakcanary-object-watcher-android-core"/>
    <dependency
        name="com.squareup.leakcanary:shark:2.12@jar"
        simpleName="com.squareup.leakcanary:shark"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-object-watcher:2.12@jar"
        simpleName="com.squareup.leakcanary:leakcanary-object-watcher"/>
    <dependency
        name="com.squareup.leakcanary:leakcanary-android-utils:2.12@aar"
        simpleName="com.squareup.leakcanary:leakcanary-android-utils"/>
    <dependency
        name="com.squareup.leakcanary:shark-graph:2.12@jar"
        simpleName="com.squareup.leakcanary:shark-graph"/>
    <dependency
        name="com.squareup.leakcanary:shark-hprof:2.12@jar"
        simpleName="com.squareup.leakcanary:shark-hprof"/>
    <dependency
        name="com.squareup.leakcanary:shark-log:2.12@jar"
        simpleName="com.squareup.leakcanary:shark-log"/>
  </compile>
  <package
      roots="androidx.test.ext:junit:1.1.5@aar,androidx.test.espresso:espresso-core:3.5.1@aar,androidx.test:rules:1.5.0@aar,androidx.test:runner:1.5.2@aar,androidx.test:core:1.5.0@aar,androidx.test.services:storage:1.4.2@aar,androidx.test:monitor:1.6.1@aar,androidx.test:annotation:1.0.1@aar,androidx.tracing:tracing:1.0.0@aar,androidx.lifecycle:lifecycle-common:2.7.0@jar,androidx.concurrent:concurrent-futures:1.1.0@jar,androidx.annotation:annotation-jvm:1.8.1@jar,androidx.annotation:annotation-experimental:1.4.0@aar,org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar,org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar,org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar,org.jetbrains:annotations:23.0.0@jar,com.google.guava:listenablefuture:1.0@jar,junit:junit:4.13.2@jar,androidx.test.espresso:espresso-idling-resource:3.5.1@aar,com.squareup:javawriter:2.1.1@jar,javax.inject:javax.inject:1@jar,org.hamcrest:hamcrest-integration:1.3@jar,org.hamcrest:hamcrest-library:1.3@jar,com.google.code.findbugs:jsr305:2.0.2@jar,org.hamcrest:hamcrest-core:1.3@jar">
    <dependency
        name="androidx.test.ext:junit:1.1.5@aar"
        simpleName="androidx.test.ext:junit"/>
    <dependency
        name="androidx.test.espresso:espresso-core:3.5.1@aar"
        simpleName="androidx.test.espresso:espresso-core"/>
    <dependency
        name="androidx.test:rules:1.5.0@aar"
        simpleName="androidx.test:rules"/>
    <dependency
        name="androidx.test:runner:1.5.2@aar"
        simpleName="androidx.test:runner"/>
    <dependency
        name="androidx.test:core:1.5.0@aar"
        simpleName="androidx.test:core"/>
    <dependency
        name="androidx.test.services:storage:1.4.2@aar"
        simpleName="androidx.test.services:storage"/>
    <dependency
        name="androidx.test:monitor:1.6.1@aar"
        simpleName="androidx.test:monitor"/>
    <dependency
        name="androidx.test:annotation:1.0.1@aar"
        simpleName="androidx.test:annotation"/>
    <dependency
        name="androidx.tracing:tracing:1.0.0@aar"
        simpleName="androidx.tracing:tracing"/>
    <dependency
        name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
        simpleName="androidx.lifecycle:lifecycle-common"/>
    <dependency
        name="androidx.concurrent:concurrent-futures:1.1.0@jar"
        simpleName="androidx.concurrent:concurrent-futures"/>
    <dependency
        name="androidx.annotation:annotation-jvm:1.8.1@jar"
        simpleName="androidx.annotation:annotation-jvm"/>
    <dependency
        name="androidx.annotation:annotation-experimental:1.4.0@aar"
        simpleName="androidx.annotation:annotation-experimental"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-android"/>
    <dependency
        name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
        simpleName="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm"/>
    <dependency
        name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
        simpleName="org.jetbrains.kotlin:kotlin-stdlib"/>
    <dependency
        name="org.jetbrains:annotations:23.0.0@jar"
        simpleName="org.jetbrains:annotations"/>
    <dependency
        name="com.google.guava:listenablefuture:1.0@jar"
        simpleName="com.google.guava:listenablefuture"/>
    <dependency
        name="junit:junit:4.13.2@jar"
        simpleName="junit:junit"/>
    <dependency
        name="androidx.test.espresso:espresso-idling-resource:3.5.1@aar"
        simpleName="androidx.test.espresso:espresso-idling-resource"/>
    <dependency
        name="com.squareup:javawriter:2.1.1@jar"
        simpleName="com.squareup:javawriter"/>
    <dependency
        name="javax.inject:javax.inject:1@jar"
        simpleName="javax.inject:javax.inject"/>
    <dependency
        name="org.hamcrest:hamcrest-integration:1.3@jar"
        simpleName="org.hamcrest:hamcrest-integration"/>
    <dependency
        name="org.hamcrest:hamcrest-library:1.3@jar"
        simpleName="org.hamcrest:hamcrest-library"/>
    <dependency
        name="com.google.code.findbugs:jsr305:2.0.2@jar"
        simpleName="com.google.code.findbugs:jsr305"/>
    <dependency
        name="org.hamcrest:hamcrest-core:1.3@jar"
        simpleName="org.hamcrest:hamcrest-core"/>
  </package>
</dependencies>
