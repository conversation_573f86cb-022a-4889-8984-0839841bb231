<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Disabled State -->
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="@color/fillTertiary" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    <!-- Pressed State -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#0062CC" /> <!-- Darker shade of systemBlue -->
            <corners android:radius="8dp" />
        </shape>
    </item>
    <!-- Default State -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/systemBlue" />
            <corners android:radius="8dp" />
        </shape>
    </item>
</selector> 