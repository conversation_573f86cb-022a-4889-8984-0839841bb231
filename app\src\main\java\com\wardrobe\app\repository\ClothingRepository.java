package com.wardrobe.app.repository;

import android.content.Context;
import android.util.Log;
import com.wardrobe.app.model.ClothingItem;
import com.wardrobe.app.data.ClothingDataSource;
import com.wardrobe.app.data.SharedPreferencesClothingDataSource;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import com.wardrobe.app.utils.DataException;
import com.wardrobe.app.utils.ErrorUtils;

/**
 * 衣物数据仓库
 * 作为业务逻辑和数据访问层之间的桥梁
 * 提供统一的数据访问接口，支持异步操作
 */
public class ClothingRepository {
    private static final String TAG = "ClothingRepository";
    
    private final ClothingDataSource dataSource;
    private final ExecutorService executorService;
    
    // 回调接口定义
    public interface DataCallback<T> {
        void onSuccess(T result);
        void onError(String error);
    }
    
    public interface BooleanCallback {
        void onSuccess(boolean result);
        void onError(String error);
    }
    
    public interface IntegerCallback {
        void onSuccess(int result);
        void onError(String error);
    }

    /**
     * 构造函数
     * @param context 应用上下文
     */
    public ClothingRepository(Context context) {
        this.dataSource = new SharedPreferencesClothingDataSource(context);
        this.executorService = Executors.newFixedThreadPool(2);
    }

    // --- 同步操作 ---

    /**
     * 获取所有衣物列表（同步）
     * @return 衣物列表
     */
    public List<ClothingItem> getAllClothingItems() {
        try {
            return dataSource.getAllClothingItems();
        } catch (DataException e) {
            ErrorUtils.logError("获取衣物列表失败", e);
            return new java.util.ArrayList<>();
        }
    }

    /**
     * 根据ID获取衣物（同步）
     * @param id 衣物ID
     * @return 衣物项目
     */
    public ClothingItem getClothingItemById(String id) {
        return dataSource.getClothingItemById(id);
    }

    /**
     * 根据分类获取衣物列表（同步）
     * @param category 分类名称
     * @return 该分类下的衣物列表
     */
    public List<ClothingItem> getClothingItemsByCategory(String category) {
        try {
            return dataSource.getClothingItemsByCategory(category);
        } catch (Exception e) {
            ErrorUtils.logError("获取分类衣物失败", e);
            return new java.util.ArrayList<>();
        }
    }

    /**
     * 搜索衣物（同步）
     * @param query 搜索关键词
     * @return 匹配的衣物列表
     */
    public List<ClothingItem> searchClothingItems(String query) {
        try {
            return dataSource.searchClothingItems(query);
        } catch (Exception e) {
            ErrorUtils.logError("搜索衣物失败", e);
            return new java.util.ArrayList<>();
        }
    }

    /**
     * 获取指定分类的衣物数量（同步）
     * @param category 分类名称
     * @return 该分类下的衣物数量
     */
    public int getClothingCountByCategory(String category) {
        try {
            return dataSource.getClothingCountByCategory(category);
        } catch (Exception e) {
            ErrorUtils.logError("获取分类数量失败", e);
            return 0;
        }
    }

    /**
     * 获取总衣物数量（同步）
     * @return 总衣物数量
     */
    public int getTotalClothingCount() {
        try {
            return dataSource.getTotalClothingCount();
        } catch (Exception e) {
            ErrorUtils.logError("获取总数量失败", e);
            return 0;
        }
    }

    /**
     * 检查衣物是否存在（同步）
     * @param id 衣物ID
     * @return 是否存在
     */
    public boolean exists(String id) {
        return dataSource.exists(id);
    }

    // --- 异步操作 ---

    /**
     * 异步获取所有衣物列表
     * @param callback 回调接口
     */
    public void getAllClothingItemsAsync(DataCallback<List<ClothingItem>> callback) {
        executorService.execute(() -> {
            try {
                List<ClothingItem> result = dataSource.getAllClothingItems();
                callback.onSuccess(result);
            } catch (DataException e) {
                ErrorUtils.logError("异步获取衣物列表失败", e);
                callback.onError("获取衣物列表失败，请稍后重试");
            } catch (Exception e) {
                ErrorUtils.logError("异步获取衣物列表未知错误", e);
                callback.onError("获取衣物列表时发生未知错误");
            }
        });
    }

    /**
     * 异步添加衣物项目
     * @param item 要添加的衣物项目
     * @param callback 回调接口
     */
    public void addClothingItemAsync(ClothingItem item, BooleanCallback callback) {
        executorService.execute(() -> {
            try {
                boolean result = dataSource.addClothingItem(item);
                callback.onSuccess(result);
            } catch (DataException e) {
                ErrorUtils.logError("异步添加衣物失败", e);
                callback.onError("添加衣物失败，请检查信息完整性");
            } catch (Exception e) {
                ErrorUtils.logError("异步添加衣物未知错误", e);
                callback.onError("添加衣物时发生未知错误");
            }
        });
    }

    /**
     * 异步批量添加衣物项目
     * @param items 要添加的衣物列表
     * @param callback 回调接口
     */
    public void addMultipleClothingItemsAsync(List<ClothingItem> items, IntegerCallback callback) {
        executorService.execute(() -> {
            try {
                int result = dataSource.addMultipleClothingItems(items);
                callback.onSuccess(result);
            } catch (DataException e) {
                ErrorUtils.logError("异步批量添加衣物失败", e);
                callback.onError("批量添加衣物失败，请检查信息完整性");
            } catch (Exception e) {
                ErrorUtils.logError("异步批量添加衣物未知错误", e);
                callback.onError("批量添加衣物时发生未知错误");
            }
        });
    }

    /**
     * 异步更新衣物项目
     * @param item 要更新的衣物项目
     * @param callback 回调接口
     */
    public void updateClothingItemAsync(ClothingItem item, BooleanCallback callback) {
        executorService.execute(() -> {
            try {
                boolean result = dataSource.updateClothingItem(item);
                callback.onSuccess(result);
            } catch (DataException e) {
                ErrorUtils.logError("异步更新衣物失败", e);
                callback.onError("更新衣物失败，请检查信息完整性");
            } catch (Exception e) {
                ErrorUtils.logError("异步更新衣物未知错误", e);
                callback.onError("更新衣物时发生未知错误");
            }
        });
    }

    /**
     * 异步删除衣物项目
     * @param id 要删除的衣物ID
     * @param callback 回调接口
     */
    public void deleteClothingItemAsync(String id, BooleanCallback callback) {
        executorService.execute(() -> {
            try {
                boolean result = dataSource.deleteClothingItem(id);
                callback.onSuccess(result);
            } catch (DataException e) {
                ErrorUtils.logError("异步删除衣物失败", e);
                callback.onError("删除衣物失败，请重试");
            } catch (Exception e) {
                ErrorUtils.logError("异步删除衣物未知错误", e);
                callback.onError("删除衣物时发生未知错误");
            }
        });
    }

    /**
     * 异步批量删除衣物项目
     * @param ids 要删除的衣物ID列表
     * @param callback 回调接口
     */
    public void deleteMultipleClothingItemsAsync(List<String> ids, IntegerCallback callback) {
        executorService.execute(() -> {
            try {
                int result = dataSource.deleteMultipleClothingItems(ids);
                callback.onSuccess(result);
            } catch (DataException e) {
                ErrorUtils.logError("异步批量删除衣物失败", e);
                callback.onError("批量删除衣物失败，请重试");
            } catch (Exception e) {
                ErrorUtils.logError("异步批量删除衣物未知错误", e);
                callback.onError("批量删除衣物时发生未知错误");
            }
        });
    }

    /**
     * 异步搜索衣物
     * @param query 搜索关键词
     * @param callback 回调接口
     */
    public void searchClothingItemsAsync(String query, DataCallback<List<ClothingItem>> callback) {
        executorService.execute(() -> {
            try {
                List<ClothingItem> result = dataSource.searchClothingItems(query);
                callback.onSuccess(result);
            } catch (DataException e) {
                ErrorUtils.logError("异步搜索衣物失败", e);
                callback.onError("搜索衣物失败，请重试");
            } catch (Exception e) {
                ErrorUtils.logError("异步搜索衣物未知错误", e);
                callback.onError("搜索衣物时发生未知错误");
            }
        });
    }

    /**
     * 异步清理所有数据
     * @param callback 回调接口
     */
    public void clearAllDataAsync(BooleanCallback callback) {
        executorService.execute(() -> {
            try {
                boolean result = dataSource.clearAllData();
                callback.onSuccess(result);
            } catch (DataException e) {
                ErrorUtils.logError("异步清理数据失败", e);
                callback.onError("清理数据失败，请重试");
            } catch (Exception e) {
                ErrorUtils.logError("异步清理数据未知错误", e);
                callback.onError("清理数据时发生未知错误");
            }
        });
    }

    /**
     * 获取数据统计信息
     * @return 统计信息字符串
     */
    public String getStatistics() {
        return dataSource.getStatistics();
    }

    /**
     * 关闭资源
     * 在Activity或Fragment销毁时调用
     */
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }

    /**
     * 检查是否已关闭
     * @return 是否已关闭
     */
    public boolean isShutdown() {
        return executorService == null || executorService.isShutdown();
    }
} 