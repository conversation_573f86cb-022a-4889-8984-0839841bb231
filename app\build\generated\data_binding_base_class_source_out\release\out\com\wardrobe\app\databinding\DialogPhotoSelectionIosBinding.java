// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogPhotoSelectionIosBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView buttonCancel;

  @NonNull
  public final TextView buttonChooseGallery;

  @NonNull
  public final TextView buttonTakePhoto;

  @NonNull
  public final TextView textTitle;

  private DialogPhotoSelectionIosBinding(@NonNull LinearLayout rootView,
      @NonNull TextView buttonCancel, @NonNull TextView buttonChooseGallery,
      @NonNull TextView buttonTakePhoto, @NonNull TextView textTitle) {
    this.rootView = rootView;
    this.buttonCancel = buttonCancel;
    this.buttonChooseGallery = buttonChooseGallery;
    this.buttonTakePhoto = buttonTakePhoto;
    this.textTitle = textTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogPhotoSelectionIosBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogPhotoSelectionIosBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_photo_selection_ios, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogPhotoSelectionIosBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_cancel;
      TextView buttonCancel = ViewBindings.findChildViewById(rootView, id);
      if (buttonCancel == null) {
        break missingId;
      }

      id = R.id.button_choose_gallery;
      TextView buttonChooseGallery = ViewBindings.findChildViewById(rootView, id);
      if (buttonChooseGallery == null) {
        break missingId;
      }

      id = R.id.button_take_photo;
      TextView buttonTakePhoto = ViewBindings.findChildViewById(rootView, id);
      if (buttonTakePhoto == null) {
        break missingId;
      }

      id = R.id.text_title;
      TextView textTitle = ViewBindings.findChildViewById(rootView, id);
      if (textTitle == null) {
        break missingId;
      }

      return new DialogPhotoSelectionIosBinding((LinearLayout) rootView, buttonCancel,
          buttonChooseGallery, buttonTakePhoto, textTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
