package com.wardrobe.app;

import org.junit.Test;
import static org.junit.Assert.*;

import java.util.Arrays;
import java.util.List;
import com.wardrobe.app.model.ClothingItem;

public class ClothingItemTest {

    @Test
    public void testClothingItemCreation() {
        // Given
        String name = "黑色T恤";
        String category = "上装";
        String subcategory = "T恤";
        String color = "黑色";
        String occasion = "日常";
        String imageUri = "file:///path/to/image.jpg";
        String notes = "纯棉材质";
        String story = "在商场买的";
        List<String> tags = Arrays.asList("夏季", "休闲", "纯棉");

        // When
        ClothingItem item = new ClothingItem(name, category, subcategory, color, occasion, 
                                           imageUri, notes, story, tags);

        // Then
        assertNotNull(item.getId());
        assertEquals(name, item.getName());
        assertEquals(category, item.getCategory());
        assertEquals(subcategory, item.getSubcategory());
        assertEquals(color, item.getColor());
        assertEquals(occasion, item.getOccasion());
        assertEquals(imageUri, item.getImageUri());
        assertEquals(notes, item.getNotes());
        assertEquals(story, item.getStory());
        assertEquals(tags, item.getTags());
    }

    @Test
    public void testClothingItemWithNullTags() {
        // Given
        String name = "蓝色牛仔裤";
        String category = "下装";
        String subcategory = "牛仔裤";
        String color = "蓝色";
        String occasion = "休闲";
        String imageUri = "file:///path/to/jeans.jpg";
        String notes = "弹力材质";
        String story = "网购的";
        List<String> tags = null;

        // When
        ClothingItem item = new ClothingItem(name, category, subcategory, color, occasion, 
                                           imageUri, notes, story, tags);

        // Then
        assertNotNull(item.getTags());
        assertTrue(item.getTags().isEmpty());
    }

    @Test
    public void testClothingItemWithStringConstructor() {
        // Given
        String id = "test-id-123";
        String name = "红色连衣裙";
        String category = "裙装";
        String subcategory = "连衣裙";
        String color = "红色";
        String occasion = "正式";
        String imageUri = "file:///path/to/dress.jpg";
        String notes = "丝绸材质";
        String story = "生日礼物";
        String tagsString = "夏季,正式,丝绸";

        // When
        ClothingItem item = new ClothingItem(id, name, category, subcategory, color, occasion, 
                                           imageUri, notes, story, tagsString);

        // Then
        assertEquals(id, item.getId());
        assertEquals(name, item.getName());
        assertEquals(category, item.getCategory());
        assertEquals(subcategory, item.getSubcategory());
        assertEquals(color, item.getColor());
        assertEquals(occasion, item.getOccasion());
        assertEquals(imageUri, item.getImageUri());
        assertEquals(notes, item.getNotes());
        assertEquals(story, item.getStory());
        assertEquals(Arrays.asList("夏季", "正式", "丝绸"), item.getTags());
    }

    @Test
    public void testClothingItemEquals() {
        // Given
        ClothingItem item1 = new ClothingItem("T恤", "上装", "T恤", "白色", "日常", 
                                            "file:///path/to/tshirt.jpg", "纯棉", "网购", 
                                            Arrays.asList("夏季", "休闲"));
        
        ClothingItem item2 = new ClothingItem("T恤", "上装", "T恤", "白色", "日常", 
                                            "file:///path/to/tshirt.jpg", "纯棉", "网购", 
                                            Arrays.asList("夏季", "休闲"));

        // When & Then
        assertNotEquals(item1, item2); // 不同的ID，应该不相等
        
        // 设置相同的ID
        item2 = new ClothingItem(item1.getId(), "T恤", "上装", "T恤", "白色", "日常", 
                               "file:///path/to/tshirt.jpg", "纯棉", "网购", 
                               "夏季,休闲");
        
        assertEquals(item1, item2);
    }

    @Test
    public void testClothingItemHashCode() {
        // Given
        ClothingItem item1 = new ClothingItem("T恤", "上装", "T恤", "白色", "日常", 
                                            "file:///path/to/tshirt.jpg", "纯棉", "网购", 
                                            Arrays.asList("夏季", "休闲"));
        
        ClothingItem item2 = new ClothingItem(item1.getId(), "T恤", "上装", "T恤", "白色", "日常", 
                                            "file:///path/to/tshirt.jpg", "纯棉", "网购", 
                                            "夏季,休闲");

        // When & Then
        assertEquals(item1.hashCode(), item2.hashCode());
    }

    @Test
    public void testClothingItemSetters() {
        // Given
        ClothingItem item = new ClothingItem("T恤", "上装", "T恤", "白色", "日常", 
                                           "file:///path/to/tshirt.jpg", "纯棉", "网购", 
                                           Arrays.asList("夏季", "休闲"));

        // When
        item.setName("新T恤");
        item.setCategory("下装");
        item.setSubcategory("短裤");
        item.setColor("黑色");
        item.setOccasion("运动");
        item.setImageUri("file:///path/to/new.jpg");
        item.setNotes("新备注");
        item.setStory("新故事");
        item.setTags(Arrays.asList("冬季", "运动"));

        // Then
        assertEquals("新T恤", item.getName());
        assertEquals("下装", item.getCategory());
        assertEquals("短裤", item.getSubcategory());
        assertEquals("黑色", item.getColor());
        assertEquals("运动", item.getOccasion());
        assertEquals("file:///path/to/new.jpg", item.getImageUri());
        assertEquals("新备注", item.getNotes());
        assertEquals("新故事", item.getStory());
        assertEquals(Arrays.asList("冬季", "运动"), item.getTags());
    }

    @Test
    public void testClothingItemToString() {
        // Given
        ClothingItem item = new ClothingItem("T恤", "上装", "T恤", "白色", "日常", 
                                           "file:///path/to/tshirt.jpg", "纯棉", "网购", 
                                           Arrays.asList("夏季", "休闲"));

        // When
        String result = item.toString();

        // Then
        assertTrue(result.contains("ClothingItem{"));
        assertTrue(result.contains("name='T恤'"));
        assertTrue(result.contains("category='上装'"));
        assertTrue(result.contains("subcategory='T恤'"));
        assertTrue(result.contains("color='白色'"));
        assertTrue(result.contains("occasion='日常'"));
        assertTrue(result.contains("imageUri='file:///path/to/tshirt.jpg'"));
        assertTrue(result.contains("notes='纯棉'"));
        assertTrue(result.contains("story='网购'"));
        assertTrue(result.contains("tags=[夏季, 休闲]"));
    }
} 