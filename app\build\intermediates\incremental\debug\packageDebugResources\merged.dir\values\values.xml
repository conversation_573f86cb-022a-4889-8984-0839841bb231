<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent_primary">#007AFF</color>
    <color name="background_primary">#FFFFFF</color>
    <color name="background_secondary">#F2F2F7</color>
    <color name="black">#FF000000</color>
    <color name="border_primary">#D1D1D6</color>
    <color name="destructive">#FF3B30</color>
    <color name="destructiveRed">#FF3B30</color>
    <color name="fillTertiary">#D1D1D6</color>
    <color name="labelPrimary">#000000</color>
    <color name="labelQuaternary">#3C3C43</color>
    <color name="labelSecondary">#3C3C43</color>
    <color name="labelTertiary">#3C3C43</color>
    <color name="opaqueSeparator">#C6C6C8</color>
    <color name="placeholderText">#8E8E93</color>
    <color name="quaternarySystemGroupedBackground">#FFFFFF</color>
    <color name="secondarySystemGroupedBackground">#F2F2F7</color>
    <color name="separator">#3C3C43</color>
    <color name="surface_primary">#FFFFFF</color>
    <color name="systemBackground">#FFFFFF</color>
    <color name="systemBlue">#007AFF</color>
    <color name="systemGray">#8E8E93</color>
    <color name="systemGray2">#AEAEB2</color>
    <color name="systemGray3">#C7C7CC</color>
    <color name="systemGray4">#D1D1D6</color>
    <color name="systemGray5">#E5E5EA</color>
    <color name="systemGray6">#F2F2F7</color>
    <color name="systemGroupedBackground">#F2F2F7</color>
    <color name="systemRed">#FF3B30</color>
    <color name="text_primary">#000000</color>
    <color name="text_secondary">#8E8E93</color>
    <color name="text_tertiary">#C7C7CC</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">WardrobeApp</string>
    <string name="button_save">保存</string>
    <string name="category_accessories">配饰</string>
    <string name="category_bottoms">下装</string>
    <string name="category_dresses">连衣裙</string>
    <string name="category_outerwear">外套</string>
    <string name="category_placeholder">请选择分类</string>
    <string name="category_primary_placeholder">请选择主分类</string>
    <string name="category_secondary_placeholder">请选择子分类</string>
    <string name="category_shoes">鞋子</string>
    <string name="category_tops">上衣</string>
    <string name="color_beige">米色</string>
    <string name="color_black">黑色</string>
    <string name="color_blue">蓝色</string>
    <string name="color_brown">棕色</string>
    <string name="color_custom">自定义</string>
    <string name="color_gray">灰色</string>
    <string name="color_green">绿色</string>
    <string name="color_khaki">卡其色</string>
    <string name="color_navy">藏青色</string>
    <string name="color_orange">橙色</string>
    <string name="color_pink">粉色</string>
    <string name="color_placeholder">请选择颜色</string>
    <string name="color_purple">紫色</string>
    <string name="color_red">红色</string>
    <string name="color_white">白色</string>
    <string name="color_yellow">黄色</string>
    <string name="compress_all_images">压缩所有图片</string>
    <string name="compressing">压缩中...</string>
    <string name="dialog_button_cancel">取消</string>
    <string name="dialog_option_camera">拍照</string>
    <string name="dialog_option_gallery">从相册选择</string>
    <string name="dialog_title_select_color">选择颜色</string>
    <string name="dialog_title_select_occasion">选择场合</string>
    <string name="dialog_title_select_photo">选择图片</string>
    <string name="error_batch_add_all_failed">全部失败，共%1$d张图片未添加</string>
    <string name="error_cleanup_images">清理失败: %1$s</string>
    <string name="error_compress_images">压缩失败: %1$s</string>
    <string name="error_get_storage_info">获取存储信息失败</string>
    <string name="error_incomplete_selection_batch">请先选择完整的分类</string>
    <string name="error_load_data">加载数据失败</string>
    <string name="error_main_category_missing_batch">主分类缺失，无法批量添加</string>
    <string name="error_no_images_selected_batch">请至少选择一张图片</string>
    <string name="error_start_compress">启动压缩失败: %1$s</string>
    <string name="hint_clothing_name">衣物名称</string>
    <string name="images_selected_count_batch">已选择%1$d张图片</string>
    <string name="info_no_items_to_add_batch">没有可添加的衣物</string>
    <string name="label_category">分类</string>
    <string name="label_color">颜色</string>
    <string name="label_occasion">场合</string>
    <string name="occasion_casual">休闲</string>
    <string name="occasion_daily">日常</string>
    <string name="occasion_date">约会</string>
    <string name="occasion_formal">正式</string>
    <string name="occasion_home">居家</string>
    <string name="occasion_outdoor">户外</string>
    <string name="occasion_party">派对</string>
    <string name="occasion_placeholder">请选择场合</string>
    <string name="occasion_sport">运动</string>
    <string name="occasion_sports">运动</string>
    <string name="occasion_travel">旅行</string>
    <string name="occasion_work">工作</string>
    <string name="placeholder_select">请选择</string>
    <string name="section_optional">可选信息</string>
    <string name="section_required">必填信息</string>
    <string name="success_batch_add_clothing_summary">成功添加%1$d件%2$s</string>
    <string name="success_batch_add_clothing_summary_with_failures">成功添加%1$d件%2$s，%3$d张图片失败</string>
    <string name="success_cleanup_all_images">清理完成，删除了 %1$d 个图片文件</string>
    <string name="success_cleanup_unused_images">清理完成，删除了 %1$d 个未使用的图片文件</string>
    <string name="success_compress_images">压缩完成，处理了 %1$d 个图片文件</string>
    <string name="title_add_new_clothing">添加新衣物</string>
    <string name="title_batch_add_prefix">批量添加：%1$s</string>
    <string name="title_edit_clothing">编辑衣物</string>
    <string name="toast_create_image_file_failed">无法创建图片文件</string>
    <string name="toast_image_selection_cancelled">已取消选择图片</string>
    <string name="toast_load_failed">加载失败</string>
    <string name="toast_permission_denied">权限被拒绝，无法继续操作</string>
    <string name="toast_permission_granted_retry">权限已授予，请重试操作</string>
    <string name="toast_permissions_denied">权限被拒绝，无法选择图片</string>
    <string name="toast_save_failed">保存失败</string>
    <string name="toast_save_successful">保存成功</string>
    <string name="toast_select_image_first">请先选择一张图片</string>
    <style name="AppModalStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@drawable/dialog_rounded_background</item>
    </style>
    <style name="Base.Theme.WardrobeApp" parent="Theme.Material3.DayNight.NoActionBar">
        
    </style>
    <style name="BottomSheetDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomSheetStyle</item>
    </style>
    <style name="BottomSheetStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
    </style>
    <style name="ButtonDestructive" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background_destructive</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:minHeight">44dp</item>
        <item name="android:textAllCaps">false</item>
    </style>
    <style name="ButtonPrimary" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background_prominent</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:minHeight">44dp</item>
        <item name="android:textAllCaps">false</item>
    </style>
    <style name="ButtonSecondary" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background</item>
        <item name="android:textColor">@color/systemBlue</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:minHeight">44dp</item>
        <item name="android:textAllCaps">false</item>
    </style>
    <style name="ButtonTertiary" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background</item>
        <item name="android:textColor">@color/labelSecondary</item>
        <item name="android:textSize">14sp</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:minHeight">32dp</item>
        <item name="android:textAllCaps">false</item>
    </style>
    <style name="DestructiveButton" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background_destructive</item>
        <item name="android:textColor">@color/destructiveRed</item>
        <item name="android:textSize">17sp</item>
    </style>
    <style name="ProminentButton" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background_prominent</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">17sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="SectionHeader">
        <item name="android:textSize">13sp</item>
        <item name="android:textColor">@color/systemGray</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingBottom">8dp</item>
    </style>
    <style name="Separator">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0.5dp</item>
        <item name="android:background">@color/separator</item>
    </style>
    <style name="TextAppearance.App.Base" parent="">
        <item name="android:textColor">@color/labelPrimary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.App.Body" parent="TextAppearance.App.Base">
        <item name="android:textSize">17sp</item>
    </style>
    <style name="TextAppearance.App.Callout" parent="TextAppearance.App.Base">
        <item name="android:textSize">16sp</item>
    </style>
    <style name="TextAppearance.App.Caption1" parent="TextAppearance.App.Base">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/labelSecondary</item>
    </style>
    <style name="TextAppearance.App.Caption2" parent="TextAppearance.App.Base">
        <item name="android:textSize">11sp</item>
        <item name="android:textColor">@color/labelTertiary</item>
    </style>
    <style name="TextAppearance.App.Footnote" parent="TextAppearance.App.Base">
        <item name="android:textSize">13sp</item>
        <item name="android:textColor">@color/labelSecondary</item>
    </style>
    <style name="TextAppearance.App.Headline" parent="TextAppearance.App.Base">
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textSize">17sp</item>
    </style>
    <style name="TextAppearance.App.LargeTitle" parent="TextAppearance.App.Base">
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:textSize">34sp</item>
    </style>
    <style name="TextAppearance.App.Subheadline" parent="TextAppearance.App.Base">
        <item name="android:textSize">15sp</item>
        <item name="android:textColor">@color/labelSecondary</item>
    </style>
    <style name="TextAppearance.App.Title1" parent="TextAppearance.App.Base">
        <item name="android:textSize">28sp</item>
    </style>
    <style name="TextAppearance.App.Title2" parent="TextAppearance.App.Base">
        <item name="android:textSize">22sp</item>
    </style>
    <style name="TextAppearance.App.Title3" parent="TextAppearance.App.Base">
        <item name="android:textSize">20sp</item>
    </style>
    <style name="Theme.WardrobeApp" parent="Base.Theme.WardrobeApp"/>
    <style name="Theme.WardrobeApp.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="Theme.WardrobeApp.BottomSheetDialog" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/AppModalStyle</item>
    </style>
    <style name="Theme.WardrobeApp.Button.Destructive" parent="ButtonDestructive"/>
    <style name="Theme.WardrobeApp.Button.Prominent" parent="ButtonPrimary"/>
    <style name="Theme.WardrobeApp.Dialog" parent="Theme.Material3.DayNight.Dialog.Alert">
        <item name="colorPrimary">@color/systemBlue</item>
        <item name="android:background">@color/secondarySystemGroupedBackground</item>
        <item name="android:windowBackground">@drawable/dialog_rounded_background</item>
    </style>
    <style name="Theme.WardrobeApp.EditTextStyle" parent="Widget.AppCompat.EditText">
        <item name="android:textColor">@color/labelPrimary</item>
        <item name="android:textColorHint">@color/placeholderText</item>
        <item name="android:textSize">16sp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
    </style>
    <style name="Theme.WardrobeApp.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.WardrobeApp.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/>
    <style name="iOSChevron">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_alignParentEnd">true</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:src">@drawable/ic_chevron_right</item>
        
    </style>
    <style name="iOSGroupedBackground">
        <item name="android:background">@color/white</item>
        <item name="android:orientation">vertical</item>
    </style>
    <style name="iOSGroupedBackground.Bottom" parent="iOSGroupedBackground">
        <item name="android:background">@drawable/ios_style_group_background_bottom</item>
    </style>
    <style name="iOSGroupedBackground.Middle" parent="iOSGroupedBackground">
        <item name="android:background">@drawable/ios_style_group_background_middle</item>
    </style>
    <style name="iOSGroupedBackground.Single" parent="iOSGroupedBackground">
        <item name="android:background">@drawable/ios_style_group_background_single</item>
    </style>
    <style name="iOSGroupedBackground.Top" parent="iOSGroupedBackground">
        <item name="android:background">@drawable/ios_style_group_background_top</item>
    </style>
    <style name="iOSLabel">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_alignParentStart">true</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:textColor">@color/labelPrimary</item>
        <item name="android:textSize">17sp</item>
    </style>
    <style name="iOSListItem">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:minHeight">44dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">16dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
        <item name="android:background">@drawable/secondary_system_grouped_background_ripple</item>
    </style>
    <style name="iOSValue">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:textColor">@color/systemGray</item>
        <item name="android:textSize">17sp</item>
        <item name="android:layout_marginEnd">8dp</item>
    </style>
</resources>