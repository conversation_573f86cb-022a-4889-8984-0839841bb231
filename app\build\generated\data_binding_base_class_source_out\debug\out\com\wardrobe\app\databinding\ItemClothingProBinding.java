// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemClothingProBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView clothingCategory;

  @NonNull
  public final ImageView clothingImage;

  @NonNull
  public final TextView clothingName;

  private ItemClothingProBinding(@NonNull MaterialCardView rootView,
      @NonNull TextView clothingCategory, @NonNull ImageView clothingImage,
      @NonNull TextView clothingName) {
    this.rootView = rootView;
    this.clothingCategory = clothingCategory;
    this.clothingImage = clothingImage;
    this.clothingName = clothingName;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemClothingProBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemClothingProBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_clothing_pro, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemClothingProBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.clothing_category;
      TextView clothingCategory = ViewBindings.findChildViewById(rootView, id);
      if (clothingCategory == null) {
        break missingId;
      }

      id = R.id.clothing_image;
      ImageView clothingImage = ViewBindings.findChildViewById(rootView, id);
      if (clothingImage == null) {
        break missingId;
      }

      id = R.id.clothing_name;
      TextView clothingName = ViewBindings.findChildViewById(rootView, id);
      if (clothingName == null) {
        break missingId;
      }

      return new ItemClothingProBinding((MaterialCardView) rootView, clothingCategory,
          clothingImage, clothingName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
