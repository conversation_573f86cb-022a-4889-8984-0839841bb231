<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="120dp"
    android:layout_height="160dp"
    android:layout_margin="8dp"
    app:cardBackgroundColor="@color/surface_primary"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <!-- 衣物图片 -->
            <ImageView
                android:id="@+id/iv_clothing"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:scaleType="centerCrop"
                android:src="@drawable/ic_wardrobe"
                android:contentDescription="衣物图片" />

            <!-- 衣物名称 -->
            <TextView
                android:id="@+id/tv_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="8dp"
                android:text="衣物名称"
                android:textColor="@color/text_primary"
                android:textSize="12sp"
                android:maxLines="2"
                android:ellipsize="end"
                android:gravity="center" />

        </LinearLayout>

        <!-- 选择覆盖层 -->
        <View
            android:id="@+id/selection_overlay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/accent_primary"
            android:alpha="0.3"
            android:visibility="gone" />

        <!-- 选中状态图标 -->
        <ImageView
            android:id="@+id/iv_selected_icon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="top|end"
            android:layout_margin="8dp"
            android:src="@drawable/ic_check_circle"
            android:tint="@color/white"
            android:visibility="gone" />

    </FrameLayout>

</androidx.cardview.widget.CardView> 