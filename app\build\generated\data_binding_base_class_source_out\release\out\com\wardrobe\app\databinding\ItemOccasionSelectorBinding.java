// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemOccasionSelectorBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView occasionIcon;

  @NonNull
  public final TextView occasionName;

  private ItemOccasionSelectorBinding(@NonNull LinearLayout rootView,
      @NonNull ImageView occasionIcon, @NonNull TextView occasionName) {
    this.rootView = rootView;
    this.occasionIcon = occasionIcon;
    this.occasionName = occasionName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemOccasionSelectorBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemOccasionSelectorBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_occasion_selector, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemOccasionSelectorBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.occasion_icon;
      ImageView occasionIcon = ViewBindings.findChildViewById(rootView, id);
      if (occasionIcon == null) {
        break missingId;
      }

      id = R.id.occasion_name;
      TextView occasionName = ViewBindings.findChildViewById(rootView, id);
      if (occasionName == null) {
        break missingId;
      }

      return new ItemOccasionSelectorBinding((LinearLayout) rootView, occasionIcon, occasionName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
