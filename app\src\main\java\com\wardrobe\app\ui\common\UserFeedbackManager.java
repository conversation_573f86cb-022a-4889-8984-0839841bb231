package com.wardrobe.app.ui.common;

import android.app.Activity;
import android.content.Context;
import android.view.View;
import android.widget.Toast;
import androidx.annotation.StringRes;
import com.google.android.material.snackbar.Snackbar;
import com.wardrobe.app.utils.Logger;

/**
 * 用户反馈管理器
 * 统一管理Toast、Snackbar等用户反馈提示
 * 提供不同类型的反馈方式和样式
 */
public class UserFeedbackManager {
    
    private static final String TAG = "UserFeedbackManager";
    private static volatile UserFeedbackManager instance;
    private static final Object LOCK = new Object();
    
    /**
     * 反馈类型枚举
     */
    public enum FeedbackType {
        SUCCESS,    // 成功
        ERROR,      // 错误
        WARNING,    // 警告
        INFO        // 信息
    }
    
    /**
     * 反馈持续时间
     */
    public enum Duration {
        SHORT(Toast.LENGTH_SHORT, Snackbar.LENGTH_SHORT),
        LONG(Toast.LENGTH_LONG, Snackbar.LENGTH_LONG),
        INDEFINITE(Toast.LENGTH_LONG, Snackbar.LENGTH_INDEFINITE);
        
        private final int toastDuration;
        private final int snackbarDuration;
        
        Duration(int toastDuration, int snackbarDuration) {
            this.toastDuration = toastDuration;
            this.snackbarDuration = snackbarDuration;
        }
        
        public int getToastDuration() {
            return toastDuration;
        }
        
        public int getSnackbarDuration() {
            return snackbarDuration;
        }
    }
    
    private UserFeedbackManager() {
        Logger.d(TAG, "UserFeedbackManager 初始化");
    }
    
    /**
     * 获取UserFeedbackManager实例
     * 
     * @return UserFeedbackManager实例
     */
    public static UserFeedbackManager getInstance() {
        if (instance == null) {
            synchronized (LOCK) {
                if (instance == null) {
                    instance = new UserFeedbackManager();
                }
            }
        }
        return instance;
    }
    
    // ==================== Toast 方法 ====================
    
    /**
     * 显示成功Toast
     * 
     * @param context 上下文
     * @param message 消息
     */
    public void showSuccessToast(Context context, String message) {
        showToast(context, message, FeedbackType.SUCCESS, Duration.SHORT);
    }
    
    /**
     * 显示错误Toast
     * 
     * @param context 上下文
     * @param message 消息
     */
    public void showErrorToast(Context context, String message) {
        showToast(context, message, FeedbackType.ERROR, Duration.LONG);
    }
    
    /**
     * 显示警告Toast
     * 
     * @param context 上下文
     * @param message 消息
     */
    public void showWarningToast(Context context, String message) {
        showToast(context, message, FeedbackType.WARNING, Duration.SHORT);
    }
    
    /**
     * 显示信息Toast
     * 
     * @param context 上下文
     * @param message 消息
     */
    public void showInfoToast(Context context, String message) {
        showToast(context, message, FeedbackType.INFO, Duration.SHORT);
    }
    
    /**
     * 显示Toast（字符串资源版本）
     * 
     * @param context 上下文
     * @param messageRes 消息资源ID
     * @param type 反馈类型
     */
    public void showToast(Context context, @StringRes int messageRes, FeedbackType type) {
        showToast(context, context.getString(messageRes), type, Duration.SHORT);
    }
    
    /**
     * 显示Toast
     * 
     * @param context 上下文
     * @param message 消息
     * @param type 反馈类型
     * @param duration 持续时间
     */
    public void showToast(Context context, String message, FeedbackType type, Duration duration) {
        if (context == null || message == null || message.trim().isEmpty()) {
            return;
        }
        
        try {
            Toast toast = Toast.makeText(context, message, duration.getToastDuration());
            toast.show();
            
            Logger.d(TAG, String.format("显示Toast: %s [%s]", message, type));
        } catch (Exception e) {
            Logger.e(TAG, "显示Toast失败", e);
        }
    }
    
    // ==================== Snackbar 方法 ====================
    
    /**
     * 显示成功Snackbar
     * 
     * @param view 父视图
     * @param message 消息
     */
    public void showSuccessSnackbar(View view, String message) {
        showSnackbar(view, message, FeedbackType.SUCCESS, Duration.SHORT, null, null);
    }
    
    /**
     * 显示错误Snackbar
     * 
     * @param view 父视图
     * @param message 消息
     */
    public void showErrorSnackbar(View view, String message) {
        showSnackbar(view, message, FeedbackType.ERROR, Duration.LONG, null, null);
    }
    
    /**
     * 显示带重试按钮的错误Snackbar
     * 
     * @param view 父视图
     * @param message 消息
     * @param retryAction 重试动作
     */
    public void showErrorSnackbarWithRetry(View view, String message, Runnable retryAction) {
        showSnackbar(view, message, FeedbackType.ERROR, Duration.INDEFINITE, "重试", retryAction);
    }
    
    /**
     * 显示警告Snackbar
     * 
     * @param view 父视图
     * @param message 消息
     */
    public void showWarningSnackbar(View view, String message) {
        showSnackbar(view, message, FeedbackType.WARNING, Duration.SHORT, null, null);
    }
    
    /**
     * 显示信息Snackbar
     * 
     * @param view 父视图
     * @param message 消息
     */
    public void showInfoSnackbar(View view, String message) {
        showSnackbar(view, message, FeedbackType.INFO, Duration.SHORT, null, null);
    }
    
    /**
     * 显示带动作的Snackbar
     * 
     * @param view 父视图
     * @param message 消息
     * @param actionText 动作文本
     * @param action 动作
     */
    public void showSnackbarWithAction(View view, String message, String actionText, Runnable action) {
        showSnackbar(view, message, FeedbackType.INFO, Duration.LONG, actionText, action);
    }
    
    /**
     * 显示Snackbar
     * 
     * @param view 父视图
     * @param message 消息
     * @param type 反馈类型
     * @param duration 持续时间
     * @param actionText 动作文本
     * @param action 动作
     */
    public void showSnackbar(View view, String message, FeedbackType type, Duration duration, 
                           String actionText, Runnable action) {
        if (view == null || message == null || message.trim().isEmpty()) {
            return;
        }
        
        try {
            Snackbar snackbar = Snackbar.make(view, message, duration.getSnackbarDuration());
            
            // 设置动作按钮
            if (actionText != null && action != null) {
                snackbar.setAction(actionText, v -> {
                    try {
                        action.run();
                    } catch (Exception e) {
                        Logger.e(TAG, "Snackbar动作执行失败", e);
                    }
                });
            }
            
            // 根据类型设置样式（如果需要的话）
            // 这里可以根据type设置不同的背景色或文字色
            
            snackbar.show();
            
            Logger.d(TAG, String.format("显示Snackbar: %s [%s]", message, type));
        } catch (Exception e) {
            Logger.e(TAG, "显示Snackbar失败", e);
        }
    }
    
    // ==================== 便捷方法 ====================
    
    /**
     * 显示操作成功反馈
     * 
     * @param context 上下文
     * @param operation 操作名称
     */
    public void showOperationSuccess(Context context, String operation) {
        showSuccessToast(context, operation + "成功");
    }
    
    /**
     * 显示操作失败反馈
     * 
     * @param context 上下文
     * @param operation 操作名称
     */
    public void showOperationError(Context context, String operation) {
        showErrorToast(context, operation + "失败，请重试");
    }
    
    /**
     * 显示网络错误反馈
     * 
     * @param context 上下文
     */
    public void showNetworkError(Context context) {
        showErrorToast(context, "网络连接失败，请检查网络设置");
    }
    
    /**
     * 显示权限错误反馈
     * 
     * @param context 上下文
     * @param permission 权限名称
     */
    public void showPermissionError(Context context, String permission) {
        showWarningToast(context, "需要" + permission + "权限才能继续操作");
    }
    
    /**
     * 显示数据为空反馈
     * 
     * @param view 父视图
     * @param dataType 数据类型
     */
    public void showEmptyData(View view, String dataType) {
        showInfoSnackbar(view, "暂无" + dataType + "数据");
    }
    
    /**
     * 显示保存成功反馈
     * 
     * @param context 上下文
     */
    public void showSaveSuccess(Context context) {
        showOperationSuccess(context, "保存");
    }
    
    /**
     * 显示删除成功反馈
     * 
     * @param context 上下文
     */
    public void showDeleteSuccess(Context context) {
        showOperationSuccess(context, "删除");
    }
    
    /**
     * 显示添加成功反馈
     * 
     * @param context 上下文
     */
    public void showAddSuccess(Context context) {
        showOperationSuccess(context, "添加");
    }
    
    /**
     * 显示更新成功反馈
     * 
     * @param context 上下文
     */
    public void showUpdateSuccess(Context context) {
        showOperationSuccess(context, "更新");
    }
    
    /**
     * 显示加载失败反馈（带重试）
     * 
     * @param view 父视图
     * @param retryAction 重试动作
     */
    public void showLoadErrorWithRetry(View view, Runnable retryAction) {
        showErrorSnackbarWithRetry(view, "加载失败", retryAction);
    }
    
    /**
     * 显示输入验证错误
     * 
     * @param context 上下文
     * @param fieldName 字段名称
     * @param errorMessage 错误信息
     */
    public void showValidationError(Context context, String fieldName, String errorMessage) {
        showWarningToast(context, fieldName + ": " + errorMessage);
    }
    
    /**
     * 显示功能开发中提示
     * 
     * @param context 上下文
     */
    public void showFeatureInDevelopment(Context context) {
        showInfoToast(context, "该功能正在开发中，敬请期待");
    }
}
