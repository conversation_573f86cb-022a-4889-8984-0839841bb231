package com.wardrobe.app.ui.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.wardrobe.app.R;
import com.wardrobe.app.model.CategoryItem;
import java.util.List;

public class CategoryListAdapter extends RecyclerView.Adapter<CategoryListAdapter.ViewHolder> {

    private final Context context;
    private final List<CategoryItem> items;
    private final OnItemClickListener listener;

    public interface OnItemClickListener {
        void onItemClick(CategoryItem item);
    }

    public CategoryListAdapter(Context context, List<CategoryItem> items, OnItemClickListener listener) {
        this.context = context;
        this.items = items;
        this.listener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.list_item_selector_row, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        CategoryItem item = items.get(position);
        holder.bind(item, listener);
    }

    @Override
    public int getItemCount() {
        return items.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView icon;
        TextView name;
        ImageView checkmark;

        ViewHolder(View itemView) {
            super(itemView);
            icon = itemView.findViewById(R.id.item_icon);
            name = itemView.findViewById(R.id.item_name);
            checkmark = itemView.findViewById(R.id.item_checkmark);
        }

        void bind(final CategoryItem item, final OnItemClickListener listener) {
            name.setText(item.getName());
            
            if (item.getIconName() != null && !item.getIconName().isEmpty()) {
                int resId = itemView.getContext().getResources().getIdentifier(item.getIconName(), "drawable", itemView.getContext().getPackageName());
                if (resId != 0) {
                    icon.setImageResource(resId);
                    icon.setVisibility(View.VISIBLE);
                } else {
                    icon.setVisibility(View.GONE);
                }
            } else {
                icon.setVisibility(View.GONE);
            }

            // You can add logic for the checkmark visibility if needed
            checkmark.setVisibility(View.GONE);

            itemView.setOnClickListener(v -> listener.onItemClick(item));
        }
    }
}