package com.wardrobe.app.repository;

import android.content.Context;

/**
 * Repository工厂类
 * 统一管理所有Repository的创建和生命周期
 * 确保Repository的单例模式和资源管理
 */
public class RepositoryFactory {
    
    private static volatile RepositoryFactory instance;
    private static final Object LOCK = new Object();
    
    // Repository实例
    private ClothingRepository clothingRepository;
    private OutfitRepository outfitRepository;
    
    private final Context applicationContext;
    
    private RepositoryFactory(Context context) {
        this.applicationContext = context.getApplicationContext();
    }
    
    /**
     * 获取工厂单例实例
     */
    public static RepositoryFactory getInstance(Context context) {
        if (instance == null) {
            synchronized (LOCK) {
                if (instance == null) {
                    instance = new RepositoryFactory(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * 获取衣物Repository
     */
    public ClothingRepository getClothingRepository() {
        if (clothingRepository == null) {
            synchronized (LOCK) {
                if (clothingRepository == null) {
                    clothingRepository = new ClothingRepository(applicationContext);
                }
            }
        }
        return clothingRepository;
    }
    
    /**
     * 获取穿搭Repository
     */
    public OutfitRepository getOutfitRepository() {
        if (outfitRepository == null) {
            synchronized (LOCK) {
                if (outfitRepository == null) {
                    outfitRepository = new OutfitRepository(applicationContext);
                }
            }
        }
        return outfitRepository;
    }
    
    /**
     * 清理所有Repository资源
     * 在应用退出时调用
     */
    public void shutdown() {
        if (clothingRepository != null) {
            clothingRepository.shutdown();
            clothingRepository = null;
        }
        
        if (outfitRepository != null) {
            outfitRepository.shutdown();
            outfitRepository = null;
        }
    }
    
    /**
     * 重置工厂实例
     * 主要用于测试
     */
    public static void resetInstance() {
        if (instance != null) {
            instance.shutdown();
            instance = null;
        }
    }
}
