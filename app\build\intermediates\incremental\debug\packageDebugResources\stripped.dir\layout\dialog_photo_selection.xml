<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/dialog_background"
    android:padding="0dp">

    <!-- 🍎 苹果风格弹窗标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="选择照片方式"
        android:textSize="20sp"
        android:textColor="#1D1D1F"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:padding="24dp"
        android:paddingBottom="16dp" />

    <!-- 🍎 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#8E8E93"
        android:alpha="0.3"
        android:layout_marginLeft="24dp"
        android:layout_marginRight="24dp" />

    <!-- 🍎 拍摄照片选项 -->
    <LinearLayout
        android:id="@+id/option_camera"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="24dp"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true">

        <!-- 使用emoji作为图标，确保兼容性 -->
        <TextView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:text="📷"
            android:textSize="24sp"
            android:gravity="center"
            android:layout_marginEnd="16dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="拍摄照片"
            android:textSize="17sp"
            android:textColor="#1D1D1F"
            android:fontFamily="sans-serif" />

    </LinearLayout>

    <!-- 🍎 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#8E8E93"
        android:alpha="0.3"
        android:layout_marginLeft="24dp"
        android:layout_marginRight="24dp" />

    <!-- 🍎 从相册选择选项 -->
    <LinearLayout
        android:id="@+id/option_gallery"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="24dp"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true">

        <!-- 使用emoji作为图标，确保兼容性 -->
        <TextView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:text="🖼️"
            android:textSize="24sp"
            android:gravity="center"
            android:layout_marginEnd="16dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="从相册选择"
            android:textSize="17sp"
            android:textColor="#1D1D1F"
            android:fontFamily="sans-serif" />

    </LinearLayout>

    <!-- 🍎 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#8E8E93"
        android:alpha="0.3"
        android:layout_marginLeft="24dp"
        android:layout_marginRight="24dp" />

    <!-- 🍎 取消按钮 -->
    <TextView
        android:id="@+id/option_cancel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="取消"
        android:textSize="17sp"
        android:textColor="#8E8E93"
        android:fontFamily="sans-serif"
        android:gravity="center"
        android:padding="20dp"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true" />

</LinearLayout>
