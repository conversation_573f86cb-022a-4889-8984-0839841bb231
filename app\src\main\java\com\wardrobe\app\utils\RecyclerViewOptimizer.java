package com.wardrobe.app.utils;

import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.RecyclerView;
import com.wardrobe.app.model.ClothingItem;
import java.util.List;

/**
 * RecyclerView优化工具
 * 提供DiffUtil实现和性能优化建议
 */
public class RecyclerViewOptimizer {
    
    private static final String TAG = "RecyclerViewOptimizer";
    
    /**
     * 衣物列表DiffUtil回调
     */
    public static class ClothingItemDiffCallback extends DiffUtil.Callback {
        
        private final List<ClothingItem> oldList;
        private final List<ClothingItem> newList;
        
        public ClothingItemDiffCallback(List<ClothingItem> oldList, List<ClothingItem> newList) {
            this.oldList = oldList;
            this.newList = newList;
        }
        
        @Override
        public int getOldListSize() {
            return oldList.size();
        }
        
        @Override
        public int getNewListSize() {
            return newList.size();
        }
        
        @Override
        public boolean areItemsTheSame(int oldItemPosition, int newItemPosition) {
            ClothingItem oldItem = oldList.get(oldItemPosition);
            ClothingItem newItem = newList.get(newItemPosition);
            return oldItem.getId().equals(newItem.getId());
        }
        
        @Override
        public boolean areContentsTheSame(int oldItemPosition, int newItemPosition) {
            ClothingItem oldItem = oldList.get(oldItemPosition);
            ClothingItem newItem = newList.get(newItemPosition);
            
            // 比较所有重要字段
            return oldItem.equals(newItem) &&
                   oldItem.getName().equals(newItem.getName()) &&
                   oldItem.getCategory().equals(newItem.getCategory()) &&
                   oldItem.getSubcategory().equals(newItem.getSubcategory()) &&
                   oldItem.getColor().equals(newItem.getColor()) &&
                   oldItem.getOccasion().equals(newItem.getOccasion()) &&
                   oldItem.getImageUri().equals(newItem.getImageUri()) &&
                   oldItem.getNotes().equals(newItem.getNotes()) &&
                   oldItem.getStory().equals(newItem.getStory()) &&
                   oldItem.getTimestamp() == newItem.getTimestamp();
        }
        
        @Override
        public Object getChangePayload(int oldItemPosition, int newItemPosition) {
            ClothingItem oldItem = oldList.get(oldItemPosition);
            ClothingItem newItem = newList.get(newItemPosition);
            
            // 创建变更载荷，只更新变化的部分
            ClothingItemChangePayload payload = new ClothingItemChangePayload();
            
            if (!oldItem.getName().equals(newItem.getName())) {
                payload.nameChanged = true;
            }
            if (!oldItem.getCategory().equals(newItem.getCategory())) {
                payload.categoryChanged = true;
            }
            if (!oldItem.getSubcategory().equals(newItem.getSubcategory())) {
                payload.subcategoryChanged = true;
            }
            if (!oldItem.getColor().equals(newItem.getColor())) {
                payload.colorChanged = true;
            }
            if (!oldItem.getOccasion().equals(newItem.getOccasion())) {
                payload.occasionChanged = true;
            }
            if (!oldItem.getImageUri().equals(newItem.getImageUri())) {
                payload.imageChanged = true;
            }
            if (!oldItem.getNotes().equals(newItem.getNotes())) {
                payload.notesChanged = true;
            }
            if (!oldItem.getStory().equals(newItem.getStory())) {
                payload.storyChanged = true;
            }
            if (oldItem.getTimestamp() != newItem.getTimestamp()) {
                payload.timestampChanged = true;
            }
            
            return payload.hasChanges() ? payload : null;
        }
    }
    
    /**
     * 变更载荷类
     */
    public static class ClothingItemChangePayload {
        public boolean nameChanged = false;
        public boolean categoryChanged = false;
        public boolean subcategoryChanged = false;
        public boolean colorChanged = false;
        public boolean occasionChanged = false;
        public boolean imageChanged = false;
        public boolean notesChanged = false;
        public boolean storyChanged = false;
        public boolean timestampChanged = false;

        public boolean hasChanges() {
            return nameChanged || categoryChanged || subcategoryChanged || colorChanged ||
                   occasionChanged || imageChanged || notesChanged || storyChanged || timestampChanged;
        }
    }
    
    /**
     * 异步计算DiffResult
     * 
     * @param oldList 旧列表
     * @param newList 新列表
     * @param callback 回调接口
     */
    public static void calculateDiffAsync(List<ClothingItem> oldList, List<ClothingItem> newList, 
                                        DiffResultCallback callback) {
        AsyncTaskManager.getInstance().executeBackground(() -> {
            long startTime = System.currentTimeMillis();
            
            ClothingItemDiffCallback diffCallback = new ClothingItemDiffCallback(oldList, newList);
            DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(diffCallback);
            
            long executionTime = System.currentTimeMillis() - startTime;
            Logger.d(TAG, "DiffUtil计算完成，耗时: " + executionTime + "ms, 列表大小: " + 
                    oldList.size() + " -> " + newList.size());
            
            return diffResult;
            
        }, new AsyncTaskManager.TaskCallback<DiffUtil.DiffResult>() {
            @Override
            public void onSuccess(DiffUtil.DiffResult result) {
                callback.onDiffCalculated(result, newList);
            }
            
            @Override
            public void onError(Exception error) {
                Logger.e(TAG, "DiffUtil计算失败", error);
                callback.onDiffError(error);
            }
        });
    }
    
    /**
     * DiffResult回调接口
     */
    public interface DiffResultCallback {
        void onDiffCalculated(DiffUtil.DiffResult diffResult, List<ClothingItem> newList);
        void onDiffError(Exception error);
    }
    
    /**
     * RecyclerView性能优化配置
     * 
     * @param recyclerView RecyclerView实例
     */
    public static void optimizeRecyclerView(RecyclerView recyclerView) {
        // 设置固定大小，如果RecyclerView的大小不会改变
        recyclerView.setHasFixedSize(true);
        
        // 设置回收视图池
        RecyclerView.RecycledViewPool recycledViewPool = new RecyclerView.RecycledViewPool();
        recycledViewPool.setMaxRecycledViews(0, 20); // 设置ViewType 0的最大回收数量
        recyclerView.setRecycledViewPool(recycledViewPool);
        
        // 设置预取数量
        if (recyclerView.getLayoutManager() instanceof androidx.recyclerview.widget.LinearLayoutManager) {
            ((androidx.recyclerview.widget.LinearLayoutManager) recyclerView.getLayoutManager())
                    .setInitialPrefetchItemCount(4);
        }
        
        Logger.d(TAG, "RecyclerView性能优化配置完成");
    }
    
    /**
     * 基础优化适配器抽象类
     */
    public static abstract class OptimizedAdapter<VH extends RecyclerView.ViewHolder> 
            extends RecyclerView.Adapter<VH> {
        
        private static final String TAG = "OptimizedAdapter";
        protected List<ClothingItem> items;
        
        public OptimizedAdapter(List<ClothingItem> items) {
            this.items = items;
            setHasStableIds(true); // 启用稳定ID
        }
        
        @Override
        public long getItemId(int position) {
            return items.get(position).getId().hashCode();
        }
        
        /**
         * 使用DiffUtil更新数据
         * 
         * @param newItems 新数据列表
         */
        public void updateItems(List<ClothingItem> newItems) {
            RecyclerViewOptimizer.calculateDiffAsync(this.items, newItems, new DiffResultCallback() {
                @Override
                public void onDiffCalculated(DiffUtil.DiffResult diffResult, List<ClothingItem> newList) {
                    items = newList;
                    diffResult.dispatchUpdatesTo(OptimizedAdapter.this);
                    Logger.d(TAG, "适配器数据更新完成");
                }
                
                @Override
                public void onDiffError(Exception error) {
                    // 降级到全量更新
                    items = newItems;
                    notifyDataSetChanged();
                    Logger.w(TAG, "DiffUtil失败，使用全量更新");
                }
            });
        }
        
        /**
         * 处理部分更新
         * 
         * @param holder ViewHolder
         * @param position 位置
         * @param payloads 载荷列表
         */
        @Override
        public void onBindViewHolder(VH holder, int position, List<Object> payloads) {
            if (payloads.isEmpty()) {
                // 全量更新
                onBindViewHolder(holder, position);
            } else {
                // 部分更新
                for (Object payload : payloads) {
                    if (payload instanceof ClothingItemChangePayload) {
                        onBindViewHolderWithPayload(holder, position, (ClothingItemChangePayload) payload);
                    }
                }
            }
        }
        
        /**
         * 处理带载荷的绑定
         * 
         * @param holder ViewHolder
         * @param position 位置
         * @param payload 变更载荷
         */
        protected abstract void onBindViewHolderWithPayload(VH holder, int position, 
                                                           ClothingItemChangePayload payload);
        
        @Override
        public int getItemCount() {
            return items.size();
        }
        
        /**
         * 获取指定位置的数据项
         * 
         * @param position 位置
         * @return 数据项
         */
        public ClothingItem getItem(int position) {
            return items.get(position);
        }
        
        /**
         * 添加单个项目
         * 
         * @param item 项目
         */
        public void addItem(ClothingItem item) {
            items.add(0, item);
            notifyItemInserted(0);
        }
        
        /**
         * 移除指定位置的项目
         * 
         * @param position 位置
         */
        public void removeItem(int position) {
            if (position >= 0 && position < items.size()) {
                items.remove(position);
                notifyItemRemoved(position);
            }
        }
        
        /**
         * 移除指定项目
         * 
         * @param item 项目
         */
        public void removeItem(ClothingItem item) {
            int position = items.indexOf(item);
            if (position != -1) {
                removeItem(position);
            }
        }
        
        /**
         * 清空所有项目
         */
        public void clearItems() {
            int size = items.size();
            items.clear();
            notifyItemRangeRemoved(0, size);
        }
    }
    
    /**
     * 性能监控器
     */
    public static class PerformanceMonitor {
        private long scrollStartTime;
        private int scrollDistance;
        
        public void onScrollStart() {
            scrollStartTime = System.currentTimeMillis();
            scrollDistance = 0;
        }
        
        public void onScrolling(int dx, int dy) {
            scrollDistance += Math.abs(dx) + Math.abs(dy);
        }
        
        public void onScrollEnd() {
            long scrollTime = System.currentTimeMillis() - scrollStartTime;
            if (scrollTime > 0) {
                float scrollSpeed = scrollDistance / (float) scrollTime;
                Logger.d(TAG, String.format("滚动性能: 距离=%dpx, 时间=%dms, 速度=%.2fpx/ms", 
                        scrollDistance, scrollTime, scrollSpeed));
            }
        }
    }
}
