package com.wardrobe.app.ui.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.wardrobe.app.R;
import com.wardrobe.app.data.ClothingManager;
import com.wardrobe.app.model.ClothingItem;
import com.wardrobe.app.model.OutfitRecord;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.UUID;

/**
 * 搭配推荐Fragment
 * 显示智能搭配建议
 */
public class OutfitFragment extends Fragment {
    
    private static final String TAG = "OutfitFragment";

    // UI组件
    private RecyclerView recyclerView;
    private TextView tvEmptyState;
    private TextView tvSuggestion;
    
    // 数据
    private ClothingManager clothingManager;
    private OutfitAdapter outfitAdapter;
    private List<OutfitRecord> outfitList;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 初始化数据
        clothingManager = ClothingManager.getInstance(requireContext());
        outfitList = new ArrayList<>();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_outfit, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initViews(view);
        setupRecyclerView();
        generateOutfitSuggestions();
    }
    
    private void initViews(View view) {
        recyclerView = view.findViewById(R.id.recycler_view_outfits);
        tvEmptyState = view.findViewById(R.id.tv_empty_state);
        tvSuggestion = view.findViewById(R.id.tv_suggestion);
    }

    private void setupRecyclerView() {
        outfitAdapter = new OutfitAdapter(outfitList);
        recyclerView.setLayoutManager(new LinearLayoutManager(requireContext()));
        recyclerView.setAdapter(outfitAdapter);
    }

    /**
     * 生成搭配建议
     */
    private void generateOutfitSuggestions() {
        try {
            List<ClothingItem> allClothing = clothingManager.getClothingList();
            
            if (allClothing.isEmpty()) {
                showEmptyState();
                return;
            }

            // 生成搭配建议
            outfitList.clear();
            
            // 按类别分组衣物
            List<ClothingItem> tops = filterByCategory(allClothing, "上衣");
            List<ClothingItem> bottoms = filterByCategory(allClothing, "下装");
            List<ClothingItem> outerwear = filterByCategory(allClothing, "外套");
            List<ClothingItem> shoes = filterByCategory(allClothing, "鞋子");

            // 生成搭配组合
            Random random = new Random();
            int suggestionCount = Math.min(5, Math.min(tops.size(), bottoms.size()));
            
            for (int i = 0; i < suggestionCount; i++) {
                OutfitRecord outfit = new OutfitRecord();
                outfit.setId(UUID.randomUUID().toString());
                outfit.setDate(new Date());
                outfit.setOccasion("日常");
                outfit.setNotes("搭配建议 " + (i + 1));
                
                // 添加衣物ID到列表中
                List<String> clothingIds = new ArrayList<>();
                if (!tops.isEmpty()) {
                    clothingIds.add(tops.get(random.nextInt(tops.size())).getId());
                }
                if (!bottoms.isEmpty()) {
                    clothingIds.add(bottoms.get(random.nextInt(bottoms.size())).getId());
                }
                if (!outerwear.isEmpty() && random.nextBoolean()) {
                    clothingIds.add(outerwear.get(random.nextInt(outerwear.size())).getId());
                }
                if (!shoes.isEmpty()) {
                    clothingIds.add(shoes.get(random.nextInt(shoes.size())).getId());
                }
                
                outfit.setClothingItemIds(clothingIds);
                outfitList.add(outfit);
            }

            outfitAdapter.notifyDataSetChanged();
            hideEmptyState();
            
            // 更新建议文本
            tvSuggestion.setText("为您推荐 " + outfitList.size() + " 套搭配");
            
        } catch (Exception e) {
            Toast.makeText(requireContext(), "生成搭配建议失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
            showEmptyState();
        }
    }

    /**
     * 按类别筛选衣物
     */
    private List<ClothingItem> filterByCategory(List<ClothingItem> items, String category) {
        List<ClothingItem> filtered = new ArrayList<>();
        for (ClothingItem item : items) {
            if (category.equals(item.getCategory())) {
                filtered.add(item);
            }
        }
        return filtered;
    }

    private void showEmptyState() {
        tvEmptyState.setVisibility(View.VISIBLE);
        recyclerView.setVisibility(View.GONE);
        tvSuggestion.setText("添加一些衣物后我们会为您推荐搭配");
    }

    private void hideEmptyState() {
        tvEmptyState.setVisibility(View.GONE);
        recyclerView.setVisibility(View.VISIBLE);
    }

    /**
     * 搭配适配器
     */
    private static class OutfitAdapter extends RecyclerView.Adapter<OutfitAdapter.OutfitViewHolder> {
        
        private List<OutfitRecord> outfitList;

        public OutfitAdapter(List<OutfitRecord> outfitList) {
            this.outfitList = outfitList;
        }

        @NonNull
        @Override
        public OutfitViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_outfit, parent, false);
            return new OutfitViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull OutfitViewHolder holder, int position) {
            OutfitRecord outfit = outfitList.get(position);

            // 设置搭配信息
            holder.tvOutfitName.setText(outfit.getNotes());

            // 显示包含的衣物数量和场合
            int itemCount = outfit.getClothingItemIds() != null ? outfit.getClothingItemIds().size() : 0;
            String description = "场合: " + outfit.getOccasion() + " | 包含 " + itemCount + " 件衣物";
            holder.tvOutfitDescription.setText(description);
        }

        @Override
        public int getItemCount() {
            return outfitList.size();
        }

        static class OutfitViewHolder extends RecyclerView.ViewHolder {
            TextView tvOutfitName;
            TextView tvOutfitDescription;

            public OutfitViewHolder(@NonNull View itemView) {
                super(itemView);
                tvOutfitName = itemView.findViewById(R.id.tv_outfit_name);
                tvOutfitDescription = itemView.findViewById(R.id.tv_outfit_description);
            }
        }
    }
}
