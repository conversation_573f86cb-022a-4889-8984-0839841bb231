package com.wardrobe.app.ui.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.wardrobe.app.R;
import com.wardrobe.app.data.ClothingManager;
import com.wardrobe.app.model.ClothingItem;
import com.wardrobe.app.viewmodel.OutfitViewModel;
import com.wardrobe.app.databinding.FragmentOutfitBinding;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 搭配页面
 * 提供智能搭配推荐和手动搭配功能
 */
public class OutfitFragment extends Fragment {

    private static final String TAG = "OutfitFragment";

    // DataBinding
    private FragmentOutfitBinding binding;

    // ViewModel
    private OutfitViewModel outfitViewModel;

    // 保留兼容性
    @Deprecated
    private ClothingManager clothingManager;
    private OutfitAdapter outfitAdapter;
    private List<OutfitViewModel.OutfitRecommendation> recommendationList;

    public OutfitFragment() {
        // Required empty public constructor
    }

    public static OutfitFragment newInstance() {
        return new OutfitFragment();
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 初始化ViewModel
        outfitViewModel = new ViewModelProvider(this).get(OutfitViewModel.class);

        // 保留兼容性
        clothingManager = ClothingManager.getInstance(requireContext());
        recommendationList = new ArrayList<>();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_outfit, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // 设置DataBinding
        binding.setViewModel(outfitViewModel);
        binding.setLifecycleOwner(this);

        setupRecyclerView();
        setupObservers();

        // 加载数据
        outfitViewModel.loadAllData();
    }

    private void setupRecyclerView() {
        outfitAdapter = new OutfitAdapter(recommendationList);
        binding.recyclerView.setLayoutManager(new LinearLayoutManager(requireContext()));
        binding.recyclerView.setAdapter(outfitAdapter);
    }

    /**
     * 设置ViewModel观察者
     */
    private void setupObservers() {
        // 观察推荐列表
        outfitViewModel.getRecommendations().observe(getViewLifecycleOwner(), recommendations -> {
            if (recommendations != null) {
                recommendationList.clear();
                recommendationList.addAll(recommendations);
                outfitAdapter.notifyDataSetChanged();

                // 更新DataBinding变量
                binding.setIsEmpty(recommendations.isEmpty());
            }
        });

        // 观察加载状态
        outfitViewModel.getIsLoading().observe(getViewLifecycleOwner(), isLoading -> {
            if (isLoading != null) {
                binding.setIsLoading(isLoading);
            }
        });

        // 观察错误消息
        outfitViewModel.getErrorMessage().observe(getViewLifecycleOwner(), error -> {
            if (error != null && !error.isEmpty()) {
                Toast.makeText(requireContext(), error, Toast.LENGTH_SHORT).show();
            }
        });

        // 观察成功消息
        outfitViewModel.getSuccessMessage().observe(getViewLifecycleOwner(), message -> {
            if (message != null && !message.isEmpty()) {
                Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void generateOutfitSuggestions() {
        try {
            List<ClothingItem> allClothing = clothingManager.getClothingList();
            
            if (allClothing.isEmpty()) {
                showEmptyState();
                return;
            }

            // 生成搭配建议
            outfitList.clear();
            
            // 按类别分组衣物
            List<ClothingItem> tops = filterByCategory(allClothing, "上衣");
            List<ClothingItem> bottoms = filterByCategory(allClothing, "下装");
            List<ClothingItem> outerwear = filterByCategory(allClothing, "外套");
            List<ClothingItem> shoes = filterByCategory(allClothing, "鞋子");
            List<ClothingItem> accessories = filterByCategory(allClothing, "配饰");

            // 生成搭配组合
            Random random = new Random();
            int suggestionCount = Math.min(5, Math.min(tops.size(), bottoms.size()));
            
            for (int i = 0; i < suggestionCount; i++) {
                OutfitItem outfit = new OutfitItem();
                outfit.setName("搭配建议 " + (i + 1));
                
                if (!tops.isEmpty()) {
                    outfit.setTop(tops.get(random.nextInt(tops.size())));
                }
                if (!bottoms.isEmpty()) {
                    outfit.setBottom(bottoms.get(random.nextInt(bottoms.size())));
                }
                if (!outerwear.isEmpty() && random.nextBoolean()) {
                    outfit.setOuterwear(outerwear.get(random.nextInt(outerwear.size())));
                }
                if (!shoes.isEmpty()) {
                    outfit.setShoes(shoes.get(random.nextInt(shoes.size())));
                }
                if (!accessories.isEmpty() && random.nextBoolean()) {
                    outfit.setAccessory(accessories.get(random.nextInt(accessories.size())));
                }
                
                outfitList.add(outfit);
            }

            outfitAdapter.notifyDataSetChanged();
            hideEmptyState();
            
        } catch (Exception e) {
            showEmptyState();
            Toast.makeText(requireContext(), "生成搭配建议失败", Toast.LENGTH_SHORT).show();
        }
    }

    private List<ClothingItem> filterByCategory(List<ClothingItem> items, String category) {
        List<ClothingItem> filtered = new ArrayList<>();
        for (ClothingItem item : items) {
            if (category.equals(item.getCategory())) {
                filtered.add(item);
            }
        }
        return filtered;
    }

    // 状态显示现在通过DataBinding自动处理

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (binding != null) {
            binding.unbind();
            binding = null;
        }
    }

    private void hideEmptyState() {
        tvEmptyState.setVisibility(View.GONE);
        recyclerView.setVisibility(View.VISIBLE);
        tvSuggestion.setVisibility(View.VISIBLE);
    }

    @Override
    public void onResume() {
        super.onResume();
        generateOutfitSuggestions();
    }

    /**
     * 搭配项数据类
     */
    public static class OutfitItem {
        private String name;
        private ClothingItem top;
        private ClothingItem bottom;
        private ClothingItem outerwear;
        private ClothingItem shoes;
        private ClothingItem accessory;

        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public ClothingItem getTop() { return top; }
        public void setTop(ClothingItem top) { this.top = top; }
        
        public ClothingItem getBottom() { return bottom; }
        public void setBottom(ClothingItem bottom) { this.bottom = bottom; }
        
        public ClothingItem getOuterwear() { return outerwear; }
        public void setOuterwear(ClothingItem outerwear) { this.outerwear = outerwear; }
        
        public ClothingItem getShoes() { return shoes; }
        public void setShoes(ClothingItem shoes) { this.shoes = shoes; }
        
        public ClothingItem getAccessory() { return accessory; }
        public void setAccessory(ClothingItem accessory) { this.accessory = accessory; }
    }

    /**
     * 搭配列表适配器
     */
    private static class OutfitAdapter extends RecyclerView.Adapter<OutfitAdapter.ViewHolder> {

        private List<OutfitViewModel.OutfitRecommendation> recommendationList;

        public OutfitAdapter(List<OutfitViewModel.OutfitRecommendation> recommendationList) {
            this.recommendationList = recommendationList;
        }

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_outfit, parent, false);
            return new ViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            OutfitViewModel.OutfitRecommendation recommendation = recommendationList.get(position);
            holder.tvOutfitName.setText(recommendation.getName());
            
            StringBuilder description = new StringBuilder();
            if (outfit.getTop() != null) {
                description.append(outfit.getTop().getName());
            }
            if (outfit.getBottom() != null) {
                if (description.length() > 0) description.append(" + ");
                description.append(outfit.getBottom().getName());
            }
            if (outfit.getOuterwear() != null) {
                if (description.length() > 0) description.append(" + ");
                description.append(outfit.getOuterwear().getName());
            }
            if (outfit.getShoes() != null) {
                if (description.length() > 0) description.append(" + ");
                description.append(outfit.getShoes().getName());
            }
            
            holder.tvOutfitDescription.setText(recommendation.getDisplayDescription());

            // 添加点击事件
            holder.itemView.setOnClickListener(v -> {
                Toast.makeText(v.getContext(), "选择了: " + recommendation.getName(), Toast.LENGTH_SHORT).show();
            });
        }

        @Override
        public int getItemCount() {
            return recommendationList.size();
        }

        static class ViewHolder extends RecyclerView.ViewHolder {
            TextView tvOutfitName;
            TextView tvOutfitDescription;

            ViewHolder(View itemView) {
                super(itemView);
                tvOutfitName = itemView.findViewById(R.id.tv_outfit_name);
                tvOutfitDescription = itemView.findViewById(R.id.tv_outfit_description);
            }
        }
    }
}