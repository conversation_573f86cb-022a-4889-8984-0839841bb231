<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/multi_select_toolbar"
    android:layout_width="match_parent"
    android:layout_height="56dp"
    android:background="@color/surface_primary"
    android:elevation="8dp"
    android:orientation="horizontal"
    android:paddingHorizontal="16dp"
    android:gravity="center_vertical"
    android:visibility="gone">

    <!-- 取消按钮 -->
    <TextView
        android:id="@+id/btn_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="取消"
        android:textColor="@color/text_secondary"
        android:textSize="16sp"
        android:padding="8dp"
        android:background="?android:attr/selectableItemBackground" />

    <!-- 选中数量 -->
    <TextView
        android:id="@+id/tv_selected_count"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="已选择 0 项"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:gravity="center" />

    <!-- 全选按钮 -->
    <TextView
        android:id="@+id/btn_select_all"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="全选"
        android:textColor="@color/accent_primary"
        android:textSize="16sp"
        android:padding="8dp"
        android:layout_marginEnd="16dp"
        android:background="?android:attr/selectableItemBackground" />

    <!-- 删除按钮 -->
    <TextView
        android:id="@+id/btn_delete"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="删除"
        android:textColor="@color/destructive"
        android:textSize="16sp"
        android:padding="8dp"
        android:background="?android:attr/selectableItemBackground" />

</LinearLayout> 