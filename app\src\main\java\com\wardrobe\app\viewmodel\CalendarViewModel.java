package com.wardrobe.app.viewmodel;

import android.app.Application;
import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.wardrobe.app.model.OutfitRecord;
import com.wardrobe.app.model.ClothingItem;
import com.wardrobe.app.repository.ClothingRepository;
import com.wardrobe.app.repository.OutfitRepository;
import com.wardrobe.app.di.ServiceLocator;
import com.wardrobe.app.utils.Logger;
import com.wardrobe.app.utils.GlobalExceptionHandler;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 日历页面ViewModel
 * 管理穿搭记录的数据和业务逻辑
 */
public class CalendarViewModel extends AndroidViewModel {
    
    private static final String TAG = "CalendarViewModel";
    
    // Repository
    private final ClothingRepository clothingRepository;
    private final OutfitRepository outfitRepository;
    private final GlobalExceptionHandler exceptionHandler;
    
    // LiveData
    private final MutableLiveData<List<OutfitRecord>> outfitRecords = new MutableLiveData<>();
    private final MutableLiveData<List<ClothingItem>> availableClothing = new MutableLiveData<>();
    private final MutableLiveData<OutfitRecord> selectedOutfit = new MutableLiveData<>();
    private final MutableLiveData<Date> selectedDate = new MutableLiveData<>();
    private final MutableLiveData<Boolean> isLoading = new MutableLiveData<>();
    private final MutableLiveData<String> errorMessage = new MutableLiveData<>();
    private final MutableLiveData<Map<String, Integer>> monthlyStats = new MutableLiveData<>();
    
    // 内部数据
    private List<OutfitRecord> allOutfitRecords = new ArrayList<>();
    private List<ClothingItem> allClothingItems = new ArrayList<>();
    
    public CalendarViewModel(@NonNull Application application) {
        super(application);

        // 使用ServiceLocator获取Repository
        ServiceLocator serviceLocator = ServiceLocator.getInstance(application);
        this.clothingRepository = serviceLocator.getService(ClothingRepository.class);
        this.outfitRepository = serviceLocator.getService(OutfitRepository.class);
        this.exceptionHandler = GlobalExceptionHandler.getInstance();
        
        // 初始化默认值
        selectedDate.setValue(new Date());
        isLoading.setValue(false);
        
        Logger.d(TAG, "CalendarViewModel 初始化完成");
    }
    
    // ==================== LiveData Getters ====================
    
    public LiveData<List<OutfitRecord>> getOutfitRecords() {
        return outfitRecords;
    }
    
    public LiveData<List<ClothingItem>> getAvailableClothing() {
        return availableClothing;
    }
    
    public LiveData<OutfitRecord> getSelectedOutfit() {
        return selectedOutfit;
    }
    
    public LiveData<Date> getSelectedDate() {
        return selectedDate;
    }
    
    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }
    
    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }
    
    public LiveData<Map<String, Integer>> getMonthlyStats() {
        return monthlyStats;
    }
    
    // ==================== 数据加载方法 ====================
    
    /**
     * 加载所有数据
     */
    public void loadAllData() {
        Logger.enter(TAG, "loadAllData");
        isLoading.setValue(true);
        
        // 先加载衣物数据
        loadClothingItems();
        
        // 然后加载穿搭记录
        loadOutfitRecords();
    }
    
    /**
     * 加载衣物数据
     */
    private void loadClothingItems() {
        clothingRepository.getAllClothingItemsAsync(new ClothingRepository.DataCallback<List<ClothingItem>>() {
            @Override
            public void onSuccess(List<ClothingItem> items) {
                allClothingItems = items;
                availableClothing.setValue(items);
                Logger.d(TAG, "加载了 " + items.size() + " 件衣物");
            }
            
            @Override
            public void onError(String error) {
                exceptionHandler.handleException("加载衣物数据", new Exception(error), false);
                errorMessage.setValue("加载衣物数据失败: " + error);
            }
        });
    }
    
    /**
     * 加载穿搭记录
     */
    private void loadOutfitRecords() {
        outfitRepository.getAllOutfitRecordsAsync(new OutfitRepository.DataCallback<List<OutfitRecord>>() {
            @Override
            public void onSuccess(List<OutfitRecord> records) {
                allOutfitRecords = records;
                outfitRecords.setValue(records);
                calculateMonthlyStats();
                isLoading.setValue(false);
                Logger.d(TAG, "加载了 " + records.size() + " 条穿搭记录");
            }

            @Override
            public void onError(String error) {
                exceptionHandler.handleException("加载穿搭记录", new Exception(error), false);
                errorMessage.setValue("加载穿搭记录失败: " + error);

                // 如果加载失败，生成模拟数据作为后备
                generateMockOutfitRecords();
                isLoading.setValue(false);
            }
        });
    }
    
    /**
     * 生成模拟穿搭记录数据
     */
    private void generateMockOutfitRecords() {
        List<OutfitRecord> mockRecords = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        
        // 生成最近30天的模拟数据
        for (int i = 0; i < 30; i++) {
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            
            // 不是每天都有记录
            if (i % 3 == 0) continue;
            
            OutfitRecord record = new OutfitRecord();
            record.setDate(new Date(calendar.getTimeInMillis()));
            record.setOccasion(getRandomOccasion());
            record.setWeather(getRandomWeather());
            record.setMood(getRandomMood());
            record.setRating((int) (Math.random() * 5) + 1);
            
            // 随机选择一些衣物
            List<String> clothingIds = getRandomClothingIds();
            record.setClothingItemIds(clothingIds);
            
            mockRecords.add(record);
        }
        
        allOutfitRecords = mockRecords;
        outfitRecords.setValue(mockRecords);
        
        // 计算月度统计
        calculateMonthlyStats();
        
        Logger.d(TAG, "生成了 " + mockRecords.size() + " 条模拟穿搭记录");
    }
    
    // ==================== 业务逻辑方法 ====================
    
    /**
     * 选择日期
     */
    public void selectDate(Date date) {
        selectedDate.setValue(date);
        
        // 查找该日期的穿搭记录
        OutfitRecord outfitForDate = findOutfitForDate(date);
        selectedOutfit.setValue(outfitForDate);
        
        Logger.d(TAG, "选择日期: " + date + ", 找到穿搭记录: " + (outfitForDate != null));
    }
    
    /**
     * 创建新的穿搭记录
     */
    public void createOutfitRecord(Date date, String occasion, String weather,
                                  List<String> clothingItemIds) {
        Logger.enter(TAG, "createOutfitRecord");

        OutfitRecord newRecord = new OutfitRecord();
        newRecord.setDate(date);
        newRecord.setOccasion(occasion);
        newRecord.setWeather(weather);
        newRecord.setClothingItemIds(clothingItemIds);
        newRecord.setRating(3); // 默认评分

        // 使用Repository保存
        outfitRepository.addOutfitRecordAsync(newRecord, new OutfitRepository.BooleanCallback() {
            @Override
            public void onSuccess(boolean result) {
                // 添加到本地列表
                allOutfitRecords.add(0, newRecord);
                outfitRecords.setValue(new ArrayList<>(allOutfitRecords));

                // 如果是当前选中的日期，更新选中的穿搭
                Date currentSelectedDate = selectedDate.getValue();
                if (currentSelectedDate != null && isSameDay(date, currentSelectedDate)) {
                    selectedOutfit.setValue(newRecord);
                }

                // 重新计算统计数据
                calculateMonthlyStats();

                Logger.d(TAG, "创建穿搭记录成功");
            }

            @Override
            public void onError(String error) {
                exceptionHandler.handleException("创建穿搭记录", new Exception(error), false);
                errorMessage.setValue("创建穿搭记录失败: " + error);
            }
        });
    }
    
    /**
     * 更新穿搭记录
     */
    public void updateOutfitRecord(OutfitRecord record) {
        Logger.enter(TAG, "updateOutfitRecord");

        outfitRepository.updateOutfitRecordAsync(record, new OutfitRepository.BooleanCallback() {
            @Override
            public void onSuccess(boolean result) {
                // 在列表中找到并更新
                for (int i = 0; i < allOutfitRecords.size(); i++) {
                    if (allOutfitRecords.get(i).getId().equals(record.getId())) {
                        allOutfitRecords.set(i, record);
                        break;
                    }
                }

                outfitRecords.setValue(new ArrayList<>(allOutfitRecords));

                // 如果是当前选中的穿搭，更新选中状态
                OutfitRecord currentSelected = selectedOutfit.getValue();
                if (currentSelected != null && currentSelected.getId().equals(record.getId())) {
                    selectedOutfit.setValue(record);
                }

                // 重新计算统计数据
                calculateMonthlyStats();

                Logger.d(TAG, "更新穿搭记录成功");
            }

            @Override
            public void onError(String error) {
                exceptionHandler.handleException("更新穿搭记录", new Exception(error), false);
                errorMessage.setValue("更新穿搭记录失败: " + error);
            }
        });
    }
    
    /**
     * 删除穿搭记录
     */
    public void deleteOutfitRecord(String recordId) {
        Logger.enter(TAG, "deleteOutfitRecord");
        
        allOutfitRecords.removeIf(record -> record.getId().equals(recordId));
        outfitRecords.setValue(new ArrayList<>(allOutfitRecords));
        
        // 如果删除的是当前选中的穿搭，清空选中状态
        OutfitRecord currentSelected = selectedOutfit.getValue();
        if (currentSelected != null && currentSelected.getId().equals(recordId)) {
            selectedOutfit.setValue(null);
        }
        
        // 重新计算统计数据
        calculateMonthlyStats();
        
        Logger.d(TAG, "删除穿搭记录成功");
    }
    
    /**
     * 获取指定月份的穿搭记录
     */
    public List<OutfitRecord> getOutfitRecordsForMonth(int year, int month) {
        List<OutfitRecord> monthlyRecords = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        
        for (OutfitRecord record : allOutfitRecords) {
            calendar.setTime(record.getDate());
            if (calendar.get(Calendar.YEAR) == year && 
                calendar.get(Calendar.MONTH) == month) {
                monthlyRecords.add(record);
            }
        }
        
        return monthlyRecords;
    }
    
    // ==================== 辅助方法 ====================
    
    /**
     * 查找指定日期的穿搭记录
     */
    private OutfitRecord findOutfitForDate(Date date) {
        for (OutfitRecord record : allOutfitRecords) {
            if (isSameDay(record.getDate(), date)) {
                return record;
            }
        }
        return null;
    }
    
    /**
     * 判断两个日期是否是同一天
     */
    private boolean isSameDay(Date date1, Date date2) {
        if (date1 == null || date2 == null) return false;
        
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal1.setTime(date1);
        cal2.setTime(date2);
        
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
               cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR);
    }
    
    /**
     * 计算月度统计数据
     */
    private void calculateMonthlyStats() {
        Map<String, Integer> stats = new HashMap<>();
        
        // 统计总记录数
        stats.put("total_records", allOutfitRecords.size());
        
        // 统计本月记录数
        Calendar calendar = Calendar.getInstance();
        int currentMonth = calendar.get(Calendar.MONTH);
        int currentYear = calendar.get(Calendar.YEAR);
        
        int thisMonthCount = 0;
        int totalRating = 0;
        
        for (OutfitRecord record : allOutfitRecords) {
            calendar.setTime(record.getDate());
            if (calendar.get(Calendar.YEAR) == currentYear && 
                calendar.get(Calendar.MONTH) == currentMonth) {
                thisMonthCount++;
                totalRating += record.getRating();
            }
        }
        
        stats.put("this_month_records", thisMonthCount);
        stats.put("average_rating", thisMonthCount > 0 ? totalRating / thisMonthCount : 0);
        
        monthlyStats.setValue(stats);
    }
    
    // 模拟数据生成辅助方法
    private String getRandomOccasion() {
        String[] occasions = {"工作", "休闲", "约会", "运动", "聚会", "购物", "旅行"};
        return occasions[(int) (Math.random() * occasions.length)];
    }
    
    private String getRandomWeather() {
        String[] weathers = {"晴天", "阴天", "雨天", "多云", "雪天"};
        return weathers[(int) (Math.random() * weathers.length)];
    }
    
    private String getRandomMood() {
        String[] moods = {"开心", "平静", "兴奋", "疲惫", "焦虑", "放松"};
        return moods[(int) (Math.random() * moods.length)];
    }
    
    private List<String> getRandomClothingIds() {
        List<String> ids = new ArrayList<>();
        if (allClothingItems.isEmpty()) return ids;
        
        int count = (int) (Math.random() * 4) + 2; // 2-5件衣物
        for (int i = 0; i < count && i < allClothingItems.size(); i++) {
            int randomIndex = (int) (Math.random() * allClothingItems.size());
            String id = allClothingItems.get(randomIndex).getId();
            if (!ids.contains(id)) {
                ids.add(id);
            }
        }
        
        return ids;
    }
    
    @Override
    protected void onCleared() {
        super.onCleared();
        // Repository由ServiceLocator管理，不需要手动关闭
        Logger.d(TAG, "CalendarViewModel 已清理");
    }
}
