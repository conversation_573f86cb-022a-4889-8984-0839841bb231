package com.wardrobe.app.utils;

import android.content.Context;
import android.util.Log;

import com.wardrobe.app.model.ClothingItem;

import java.io.File;
import java.util.List;

/**
 * 图片管理工具类
 * 提供存储空间管理、图片清理等功能
 */
public class ImageManager {
    
    private static final String TAG = "ImageManager";
    
    /**
     * 获取应用图片存储目录
     */
    public static File getImagesDirectory(Context context) {
        return new File(context.getFilesDir(), "clothing_images");
    }
    
    /**
     * 获取图片存储空间使用情况
     */
    public static StorageInfo getStorageInfo(Context context) {
        File imagesDir = getImagesDirectory(context);
        long totalSize = 0;
        int fileCount = 0;
        
        if (imagesDir.exists() && imagesDir.isDirectory()) {
            File[] files = imagesDir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile() && isImageFile(file.getName())) {
                        totalSize += file.length();
                        fileCount++;
                    }
                }
            }
        }
        
        return new StorageInfo(totalSize, fileCount);
    }
    
    /**
     * 清理未使用的图片文件
     * @param context 上下文
     * @param usedImagePaths 正在使用的图片路径列表
     * @return 清理的文件数量
     */
    public static int cleanupUnusedImages(Context context, List<String> usedImagePaths) {
        File imagesDir = getImagesDirectory(context);
        int cleanedCount = 0;
        
        if (!imagesDir.exists() || !imagesDir.isDirectory()) {
            return 0;
        }
        
        File[] files = imagesDir.listFiles();
        if (files == null) {
            return 0;
        }
        
        for (File file : files) {
            if (file.isFile() && isImageFile(file.getName())) {
                String filePath = file.getAbsolutePath();
                boolean isUsed = false;
                
                // 检查文件是否正在被使用
                for (String usedPath : usedImagePaths) {
                    if (filePath.equals(usedPath)) {
                        isUsed = true;
                        break;
                    }
                }
                
                // 删除未使用的文件
                if (!isUsed) {
                    if (file.delete()) {
                        cleanedCount++;
                        Log.i(TAG, "清理未使用的图片文件: " + file.getName());
                    } else {
                        Log.w(TAG, "无法删除文件: " + file.getName());
                    }
                }
            }
        }
        
        Log.i(TAG, "清理完成，删除了 " + cleanedCount + " 个未使用的图片文件");
        return cleanedCount;
    }
    
    /**
     * 清理所有图片文件（危险操作）
     * @param context 上下文
     * @return 清理的文件数量
     */
    public static int cleanupAllImages(Context context) {
        File imagesDir = getImagesDirectory(context);
        int cleanedCount = 0;
        
        if (!imagesDir.exists() || !imagesDir.isDirectory()) {
            return 0;
        }
        
        File[] files = imagesDir.listFiles();
        if (files == null) {
            return 0;
        }
        
        for (File file : files) {
            if (file.isFile() && isImageFile(file.getName())) {
                if (file.delete()) {
                    cleanedCount++;
                    Log.i(TAG, "删除图片文件: " + file.getName());
                } else {
                    Log.w(TAG, "无法删除文件: " + file.getName());
                }
            }
        }
        
        Log.i(TAG, "清理完成，删除了 " + cleanedCount + " 个图片文件");
        return cleanedCount;
    }
    
    /**
     * 压缩所有图片文件
     * @param context 上下文
     * @return 压缩的文件数量
     */
    public static int compressAllImages(Context context) {
        File imagesDir = getImagesDirectory(context);
        int compressedCount = 0;
        
        if (!imagesDir.exists() || !imagesDir.isDirectory()) {
            return 0;
        }
        
        File[] files = imagesDir.listFiles();
        if (files == null) {
            return 0;
        }
        
        for (File file : files) {
            if (file.isFile() && isImageFile(file.getName()) && !file.getName().contains("_compressed")) {
                String originalPath = file.getAbsolutePath();
                String compressedPath = ImageCompressor.compressImage(context, originalPath);
                
                if (compressedPath != null && !compressedPath.equals(originalPath)) {
                    // 删除原文件，保留压缩后的文件
                    if (file.delete()) {
                        compressedCount++;
                        Log.i(TAG, "压缩并替换图片文件: " + file.getName());
                    } else {
                        Log.w(TAG, "无法删除原文件: " + file.getName());
                    }
                }
            }
        }
        
        Log.i(TAG, "压缩完成，处理了 " + compressedCount + " 个图片文件");
        return compressedCount;
    }
    
    /**
     * 检查是否为图片文件
     */
    private static boolean isImageFile(String fileName) {
        if (fileName == null) {
            return false;
        }
        
        String lowerFileName = fileName.toLowerCase();
        return lowerFileName.endsWith(".jpg") || 
               lowerFileName.endsWith(".jpeg") || 
               lowerFileName.endsWith(".png") || 
               lowerFileName.endsWith(".webp");
    }
    
    /**
     * 获取文件大小的可读字符串
     */
    public static String getReadableFileSize(long size) {
        if (size <= 0) return "0 B";
        
        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        
        return String.format("%.1f %s", size / Math.pow(1024, digitGroups), units[digitGroups]);
    }
    
    /**
     * 存储信息类
     */
    public static class StorageInfo {
        private final long totalSize;
        private final int fileCount;
        
        public StorageInfo(long totalSize, int fileCount) {
            this.totalSize = totalSize;
            this.fileCount = fileCount;
        }
        
        public long getTotalSize() {
            return totalSize;
        }
        
        public int getFileCount() {
            return fileCount;
        }
        
        public String getReadableSize() {
            return getReadableFileSize(totalSize);
        }
        
        public double getSizeMB() {
            return (double) totalSize / (1024 * 1024);
        }
    }
} 