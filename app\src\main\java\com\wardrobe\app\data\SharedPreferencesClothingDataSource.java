package com.wardrobe.app.data;

import android.content.Context;
import android.content.SharedPreferences;
import android.net.Uri;
import android.util.Log;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.wardrobe.app.model.ClothingItem;
import com.wardrobe.app.utils.DataException;
import static com.wardrobe.app.data.DataConstants.*;
import java.io.File;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SharedPreferences数据源实现
 * 使用SharedPreferences存储衣物数据，实现ClothingDataSource接口
 */
public class SharedPreferencesClothingDataSource implements ClothingDataSource {
    private static final String TAG = "SharedPrefsDataSource";
    // 使用统一的数据常量
    // private static final String PREFS_NAME = "wardrobe_data"; // 使用 DataConstants.PREFS_NAME
    // private static final String CLOTHING_KEY = "clothing_list"; // 使用 DataConstants.CLOTHING_LIST_KEY
    // private static final int MAX_CLOTHING_ITEMS = 1000; // 使用 DataConstants.MAX_CLOTHING_ITEMS

    private final SharedPreferences sharedPreferences;
    private final Gson gson;

    public SharedPreferencesClothingDataSource(Context context) {
        this.sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.gson = new GsonBuilder()
                .setPrettyPrinting()
                .serializeNulls()
                .create();
    }

    @Override
    public List<ClothingItem> getAllClothingItems() throws DataException {
        try {
            String json = sharedPreferences.getString(CLOTHING_LIST_KEY, "");
            if (json.isEmpty()) {
                return new ArrayList<>();
            }
            
            Type type = new TypeToken<List<ClothingItem>>(){}.getType();
            List<ClothingItem> result = gson.fromJson(json, type);
            return result != null ? result : new ArrayList<>();
        } catch (Exception e) {
            Log.e(TAG, "读取衣物列表失败", e);
            throw new DataException("读取衣物列表失败", e);
        }
    }

    @Override
    public ClothingItem getClothingItemById(String id) {
        if (id == null || id.trim().isEmpty()) {
            return null;
        }
        
        try {
            return getAllClothingItems().stream()
                    .filter(item -> id.equals(item.getId()))
                    .findFirst()
                    .orElse(null);
        } catch (DataException e) {
            Log.e(TAG, "获取衣物失败", e);
            return null;
        }
    }

    @Override
    public boolean addClothingItem(ClothingItem item) throws DataException {
        try {
            validateClothingItem(item);
            
            List<ClothingItem> clothingList = getAllClothingItems();
            if (clothingList.size() >= MAX_CLOTHING_ITEMS) {
                Log.w(TAG, "衣物数量已达到上限: " + MAX_CLOTHING_ITEMS);
                throw new DataException("衣物数量已达到上限");
            }
            
            clothingList.add(0, item);
            saveClothingList(clothingList);
            Log.d(TAG, "成功添加衣物: " + item.getId());
            return true;
        } catch (DataException e) {
            throw e;
        } catch (Exception e) {
            Log.e(TAG, "添加衣物失败", e);
            throw new DataException("添加衣物失败", e);
        }
    }

    @Override
    public int addMultipleClothingItems(List<ClothingItem> items) throws DataException {
        if (items == null || items.isEmpty()) {
            return 0;
        }

        try {
            // 验证所有衣物项目
            for (ClothingItem item : items) {
                validateClothingItem(item);
            }

            List<ClothingItem> currentList = getAllClothingItems();
            if (currentList.size() + items.size() > MAX_CLOTHING_ITEMS) {
                Log.w(TAG, "添加后衣物数量将超过上限: " + MAX_CLOTHING_ITEMS);
                throw new DataException("添加后衣物数量将超过上限");
            }

            currentList.addAll(0, items);
            saveClothingList(currentList);
            Log.d(TAG, "批量添加了 " + items.size() + " 件衣物");
            return items.size();
        } catch (DataException e) {
            throw e;
        } catch (Exception e) {
            Log.e(TAG, "批量添加衣物失败", e);
            throw new DataException("批量添加衣物失败", e);
        }
    }

    @Override
    public boolean updateClothingItem(ClothingItem item) throws DataException {
        try {
            validateClothingItem(item);
            
            List<ClothingItem> clothingList = getAllClothingItems();
            boolean found = false;
            
            for (int i = 0; i < clothingList.size(); i++) {
                if (clothingList.get(i).getId().equals(item.getId())) {
                    clothingList.set(i, item);
                    found = true;
                    break;
                }
            }
            
            if (found) {
                saveClothingList(clothingList);
                Log.d(TAG, "衣物已更新: " + item.getId());
                return true;
            } else {
                Log.w(TAG, "未找到要更新的衣物: " + item.getId());
                throw new DataException("未找到要更新的衣物");
            }
        } catch (DataException e) {
            throw e;
        } catch (Exception e) {
            Log.e(TAG, "更新衣物失败", e);
            throw new DataException("更新衣物失败", e);
        }
    }

    @Override
    public boolean deleteClothingItem(String id) throws DataException {
        if (id == null || id.trim().isEmpty()) {
            return false;
        }

        try {
            List<ClothingItem> clothingList = getAllClothingItems();
            ClothingItem itemToDelete = getClothingItemById(id);
            
            if (itemToDelete != null) {
                clothingList.removeIf(item -> item.getId().equals(id));
                saveClothingList(clothingList);
                deleteImageFile(itemToDelete);
                Log.d(TAG, "成功删除衣物: " + id);
                return true;
            } else {
                Log.w(TAG, "未找到要删除的衣物: " + id);
                throw new DataException("未找到要删除的衣物");
            }
        } catch (DataException e) {
            throw e;
        } catch (Exception e) {
            Log.e(TAG, "删除衣物失败", e);
            throw new DataException("删除衣物失败", e);
        }
    }

    @Override
    public int deleteMultipleClothingItems(List<String> ids) throws DataException {
        if (ids == null || ids.isEmpty()) {
            return 0;
        }

        try {
            List<ClothingItem> currentList = getAllClothingItems();
            int originalSize = currentList.size();
            
            // 删除图片文件
            for (String id : ids) {
                ClothingItem item = getClothingItemById(id);
                if (item != null) {
                    deleteImageFile(item);
                }
            }
            
            // 从列表中移除
            currentList.removeIf(item -> ids.contains(item.getId()));
            int deletedCount = originalSize - currentList.size();
            
            saveClothingList(currentList);
            Log.d(TAG, "批量删除了 " + deletedCount + " 件衣物");
            return deletedCount;
        } catch (DataException e) {
            throw e;
        } catch (Exception e) {
            Log.e(TAG, "批量删除衣物失败", e);
            throw new DataException("批量删除衣物失败", e);
        }
    }

    @Override
    public List<ClothingItem> getClothingItemsByCategory(String category) {
        if (category == null) {
            return new ArrayList<>();
        }
        
        if ("全部".equals(category)) {
            try {
                return getAllClothingItems();
            } catch (DataException e) {
                Log.e(TAG, "获取全部衣物失败", e);
                return new ArrayList<>();
            }
        }

        try {
            return getAllClothingItems().stream()
                    .filter(item -> item.getCategory() != null && item.getCategory().equals(category))
                    .collect(Collectors.toList());
        } catch (DataException e) {
            Log.e(TAG, "按分类获取衣物失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ClothingItem> searchClothingItems(String query) throws DataException {
        if (query == null || query.trim().isEmpty()) {
            return getAllClothingItems();
        }

        String lowerQuery = query.toLowerCase().trim();
        try {
            return getAllClothingItems().stream()
                    .filter(item -> matchesSearchQuery(item, lowerQuery))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            Log.e(TAG, "搜索衣物失败", e);
            throw new DataException("搜索衣物时发生错误: " + e.getMessage(), e);
        }
    }

    @Override
    public int getClothingCountByCategory(String category) {
        return getClothingItemsByCategory(category).size();
    }

    @Override
    public int getTotalClothingCount() {
        try {
            return getAllClothingItems().size();
        } catch (DataException e) {
            Log.e(TAG, "获取总衣物数量失败", e);
            return 0;
        }
    }

    @Override
    public boolean exists(String id) {
        return getClothingItemById(id) != null;
    }

    @Override
    public boolean clearAllData() throws DataException {
        try {
            sharedPreferences.edit().clear().apply();
            Log.d(TAG, "所有衣物数据已清理");
            return true;
        } catch (Exception e) {
            Log.e(TAG, "清理数据失败", e);
            throw new DataException("清理数据失败", e);
        }
    }

    @Override
    public String getStatistics() {
        try {
            List<ClothingItem> items = getAllClothingItems();
            return String.format("总衣物数量: %d, 最大容量: %d", items.size(), MAX_CLOTHING_ITEMS);
        } catch (DataException e) {
            Log.e(TAG, "获取统计信息失败", e);
            return "统计信息获取失败";
        }
    }

    /**
     * 保存衣物列表到SharedPreferences
     */
    private void saveClothingList(List<ClothingItem> clothingList) {
        try {
            String json = gson.toJson(clothingList);
            sharedPreferences.edit()
                    .putString(CLOTHING_LIST_KEY, json)
                    .apply();
            Log.d(TAG, "衣物列表保存成功，共 " + clothingList.size() + " 件衣物");
        } catch (Exception e) {
            Log.e(TAG, "保存衣物列表失败", e);
            throw new RuntimeException("保存衣物数据失败", e);
        }
    }

    /**
     * 验证衣物项目数据的有效性
     */
    private void validateClothingItem(ClothingItem item) {
        if (item == null) {
            throw new IllegalArgumentException("衣物项目不能为空");
        }
        
        if (item.getId() == null || item.getId().trim().isEmpty()) {
            throw new IllegalArgumentException("衣物ID不能为空");
        }
        
        if (item.getName() == null || item.getName().trim().isEmpty()) {
            throw new IllegalArgumentException("衣物名称不能为空");
        }
        
        if (item.getCategory() == null || item.getCategory().trim().isEmpty()) {
            throw new IllegalArgumentException("衣物分类不能为空");
        }
    }

    /**
     * 删除衣物对应的图片文件
     */
    private void deleteImageFile(ClothingItem item) {
        if (item == null || item.getImageUri() == null || item.getImageUri().isEmpty()) {
            Log.w(TAG, "图片路径为空，无法删除文件。 衣物ID: " + (item != null ? item.getId() : "null"));
            return;
        }
        
        try {
            Uri uri = Uri.parse(item.getImageUri());
            if ("file".equals(uri.getScheme())) {
                File fileToDelete = new File(uri.getPath());
                if (fileToDelete.exists()) {
                    if (fileToDelete.delete()) {
                        Log.i(TAG, "图片文件删除成功: " + fileToDelete.getAbsolutePath());
                    } else {
                        Log.w(TAG, "图片文件删除失败: " + fileToDelete.getAbsolutePath());
                    }
                } else {
                    Log.w(TAG, "图片文件不存在，无法删除: " + fileToDelete.getAbsolutePath());
                }
            } else {
                Log.w(TAG, "不支持的图片路径 scheme: " + uri.getScheme() + " 衣物ID: " + item.getId());
            }
        } catch (Exception e) {
            Log.e(TAG, "删除图片文件时发生错误: " + item.getImageUri(), e);
        }
    }

    /**
     * 检查衣物是否匹配搜索查询
     */
    private boolean matchesSearchQuery(ClothingItem item, String lowerQuery) {
        return (item.getName() != null && item.getName().toLowerCase().contains(lowerQuery)) ||
               (item.getCategory() != null && item.getCategory().toLowerCase().contains(lowerQuery)) ||
               (item.getSubcategory() != null && item.getSubcategory().toLowerCase().contains(lowerQuery)) ||
               (item.getColor() != null && item.getColor().toLowerCase().contains(lowerQuery)) ||
               (item.getOccasion() != null && item.getOccasion().toLowerCase().contains(lowerQuery)) ||
               (item.getNotes() != null && item.getNotes().toLowerCase().contains(lowerQuery)) ||
               (item.getStory() != null && item.getStory().toLowerCase().contains(lowerQuery)) ||
               (item.getTags() != null && item.getTags().stream()
                   .anyMatch(tag -> tag != null && tag.toLowerCase().contains(lowerQuery)));
    }
} 