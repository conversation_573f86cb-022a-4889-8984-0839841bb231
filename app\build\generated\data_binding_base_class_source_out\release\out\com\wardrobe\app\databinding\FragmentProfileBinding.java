// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentProfileBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnCleanupImages;

  @NonNull
  public final Button btnCleanupUnused;

  @NonNull
  public final Button btnCompressImages;

  @NonNull
  public final SwitchMaterial switchAutoBackup;

  @NonNull
  public final SwitchMaterial switchDarkMode;

  @NonNull
  public final SwitchMaterial switchNotifications;

  @NonNull
  public final TextView tvClothingCount;

  @NonNull
  public final TextView tvImageCount;

  @NonNull
  public final TextView tvStorageInfo;

  private FragmentProfileBinding(@NonNull ScrollView rootView, @NonNull Button btnCleanupImages,
      @NonNull Button btnCleanupUnused, @NonNull Button btnCompressImages,
      @NonNull SwitchMaterial switchAutoBackup, @NonNull SwitchMaterial switchDarkMode,
      @NonNull SwitchMaterial switchNotifications, @NonNull TextView tvClothingCount,
      @NonNull TextView tvImageCount, @NonNull TextView tvStorageInfo) {
    this.rootView = rootView;
    this.btnCleanupImages = btnCleanupImages;
    this.btnCleanupUnused = btnCleanupUnused;
    this.btnCompressImages = btnCompressImages;
    this.switchAutoBackup = switchAutoBackup;
    this.switchDarkMode = switchDarkMode;
    this.switchNotifications = switchNotifications;
    this.tvClothingCount = tvClothingCount;
    this.tvImageCount = tvImageCount;
    this.tvStorageInfo = tvStorageInfo;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentProfileBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentProfileBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_profile, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentProfileBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_cleanup_images;
      Button btnCleanupImages = ViewBindings.findChildViewById(rootView, id);
      if (btnCleanupImages == null) {
        break missingId;
      }

      id = R.id.btn_cleanup_unused;
      Button btnCleanupUnused = ViewBindings.findChildViewById(rootView, id);
      if (btnCleanupUnused == null) {
        break missingId;
      }

      id = R.id.btn_compress_images;
      Button btnCompressImages = ViewBindings.findChildViewById(rootView, id);
      if (btnCompressImages == null) {
        break missingId;
      }

      id = R.id.switch_auto_backup;
      SwitchMaterial switchAutoBackup = ViewBindings.findChildViewById(rootView, id);
      if (switchAutoBackup == null) {
        break missingId;
      }

      id = R.id.switch_dark_mode;
      SwitchMaterial switchDarkMode = ViewBindings.findChildViewById(rootView, id);
      if (switchDarkMode == null) {
        break missingId;
      }

      id = R.id.switch_notifications;
      SwitchMaterial switchNotifications = ViewBindings.findChildViewById(rootView, id);
      if (switchNotifications == null) {
        break missingId;
      }

      id = R.id.tv_clothing_count;
      TextView tvClothingCount = ViewBindings.findChildViewById(rootView, id);
      if (tvClothingCount == null) {
        break missingId;
      }

      id = R.id.tv_image_count;
      TextView tvImageCount = ViewBindings.findChildViewById(rootView, id);
      if (tvImageCount == null) {
        break missingId;
      }

      id = R.id.tv_storage_info;
      TextView tvStorageInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvStorageInfo == null) {
        break missingId;
      }

      return new FragmentProfileBinding((ScrollView) rootView, btnCleanupImages, btnCleanupUnused,
          btnCompressImages, switchAutoBackup, switchDarkMode, switchNotifications, tvClothingCount,
          tvImageCount, tvStorageInfo);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
