http://schemas.android.com/apk/res-auto;;${\:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/slide_out_right.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/slide_out_left.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/slide_in_right.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/slide_in_left.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/fade_in.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/slide_up.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/scale_in.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/fade_out.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/color/bottom_nav_color_selector.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_formal_pants.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_occasion_home.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_empty_box.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_check_circle.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_vest.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_rain_boots.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sport_pants.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/selected_item_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_boots.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_spaghetti_dress.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_clothing_placeholder.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_formal_dress.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/dialog_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ios_style_group_background_middle.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_thermal.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_camisole.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/search_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/edit_text_background_ios.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_add.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_profile.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_mermaid_skirt.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_chevron_right.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_calendar.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_functional.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/dotted_border_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_bra.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pleated_skirt.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_replace_photo.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_bag.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ios_style_group_background_single.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/option_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jeans.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_camisole_dress.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/secondary_system_grouped_background_ripple.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_occasion_sports.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_briefs.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_workwear.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_straight_skirt.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pajamas.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_occasion_casual.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/button_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sport_shoes.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_tracksuit.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_flared_skirt.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_occasion_date.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_outfit.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sneakers.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_socks.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/dialog_rounded_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_occasion_outdoor.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_skirt_bottom.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_hoodie.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_outerwear.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_slit_skirt.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_wardrobe.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_occasion_formal.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jumpsuit.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sandals.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/spinner_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/color_circle_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_bottoms.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_polo.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_occasion_party.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jewelry.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ios_style_group_background_top.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_occasion_daily.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_skirt.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_shorts.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/button_background_prominent.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pencil_skirt.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/replace_icon_background.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_traditional.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sets.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_shirt.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_accessories.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_scarf.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_heels.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_coat.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jacket.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_dress.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_tuxedo.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_shoes.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_canvas_shoes.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_belt.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_evening_dress.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_clear.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_suit.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_tops.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_underwear.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_suits.png,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ios_style_group_background_bottom.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_occasion_work.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_hat.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_formal_shoes.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/button_background_destructive.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_dresses.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_error.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_occasion_travel.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_launcher_foreground.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_wedding.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sweater.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_tshirt.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_search.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_outer_vest.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pants.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_other_acc.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_batch_add_clothing.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_occasion_selector.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_color_selector.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_color_spinner.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_calendar.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_photo_selection_ios.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_clothing.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_outfit.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_clothing_horizontal.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_wardrobe.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_outfit.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_empty.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_calendar.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_wardrobe_group.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_clothing_detail.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_color_selection.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_image_fullscreen.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_photo_selection.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_error.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_profile.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/spinner_item_with_icon.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/list_item_selector_row.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_category_selector.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/bottom_navigation_menu.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_category_selection.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_category_select.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_color_select.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_occasion_select.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/multi_select_toolbar.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_occasion_selection.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_recyclerview.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_category_selector.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_clothing_pro.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_loading.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/action_mode_menu.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/menu.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher_round.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-mdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-xxxhdpi/ic_launcher.webp,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values-en/strings.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/styles.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/file_paths.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+anim:slide_out_right,0,F;slide_out_left,1,F;slide_in_right,2,F;slide_in_left,3,F;fade_in,4,F;slide_up,5,F;scale_in,6,F;fade_out,7,F;+color:text_primary_ios,8,V9318f9,2e00931927,;"#000000";text_primary,8,V400500e13,2e00500e3d,;"#1C1B1F";colorError,9,V4001702b4,2c001702dc,;"#CF6679";colorOnError,9,V4001802e1,2e0018030b,;"#000000";md_theme_light_surfaceVariant,8,V4001b053b,3f001b0576,;"#E7E0EC";md_theme_dark_onSecondaryContainer,8,V4002a080e,44002a084e,;"#E8DEF8";selectedItemBackground,9,V4003005db,3a00300611,;"#33BB86FC";colorOnSecondary,9,V4000c0179,32000c01a7,;"#000000";md_theme_dark_outline,8,V4003d0ba9,37003d0bdc,;"#938F99";md_theme_light_background,8,V40017044d,3b00170484,;"#FFFBFE";cardBackground,9,V400240461,300024048d,;"#2C2C2C";placeholderText,8,V400841655,**********,;"#8E8E93";md_theme_dark_onPrimaryContainer,8,V400250711,420025074f,;"#EADDFF";primary_dark,8,V400420c64,2e00420c8e,;"#4F378B";md_theme_light_onSecondaryContainer,8,V4000b0214,45000b0255,;"#1D192B";black_alpha_54,8,V400611047,3200611075,;"#8A000000";colorPrimary,9,V400050063,2e0005008d,;"#BB86FC";md_theme_dark_onSecondary,8,V40028078f,3b002807c6,;"#332D41";systemGroupedBackground,8,V400751384,39007513b9,;"#F2F2F7";pressed,8,V4005b0f78,29005b0f9d,;"#E8DEF8";md_theme_light_onTertiaryContainer,8,V400100314,4400100354,;"#31111D";background_secondary,8,V4004c0d93,36004c0dc5,;"#F7F2FA";navigationBarColor,9,V4002d058f,34002d05bf,;"#121212";md_theme_dark_surfaceVariant,8,V4003a0b28,3e003a0b62,;"#49454F";background_primary_ios,8,V9218c4,34009218f8,;"#FFFFFF";md_theme_light_primary,8,V40003005d,3800030091,;"#6750A4";md_theme_dark_onBackground,8,V400370a79,3c00370ab1,;"#E6E1E5";primary,8,V400410c3a,2900410c5f,;"#6750A4";md_theme_light_errorContainer,8,V4001403ca,3f00140405,;"#FFDAD6";category_bottoms,8,V4006d11fc,32006d122a,;"#F3E5F5";labelPrimary,8,V4008716cf,2e008716f9,;"#000000";colorSurface,9,V40013023e,2e00130268,;"#1E1E1E";md_theme_dark_errorContainer,8,V4003309bd,3e003309f7,;"#93000A";colorSecondary,9,V4000a0110,30000a013c,;"#03DAC6";white_alpha_12,8,V40062107a,32006210a8,;"#1FFFFFFF";destructiveRed,8,V40095194b,3000951977,;"#FF3B30";disabled,8,V4005c0fa2,2a005c0fc8,;"#F7F2FA";text_secondary_ios,8,V4007613be,34007613ee,;"#8E8E93";bottom_nav_color_selector,10,F;systemBlue,8,V4007a1474,2c007a149c,;"#007AFF";category_accessories,8,V400701295,36007012c7,;"#FCE4EC";colorBackground,9,V4000f01c2,31000f01ef,;"#121212";black,8,V4007212fd,2900721322,;"#FF000000";systemGray6,8,V400801586,2d008015af,;"#F2F2F7";text_tertiary,8,V400520e73,2f00520e9e,;"#79747E";category_other,8,V4007112cc,30007112f8,;"#F5F5F5";systemGray2,8,V4007c14ce,2d007c14f7,;"#AEAEB2";md_theme_light_onSurfaceVariant,8,V4001c057b,41001c05b8,;"#49454F";category_tops,8,V4006c11cc,2f006c11f7,;"#E3F2FD";systemGray3,8,V4007d14fc,2d007d1525,;"#C7C7CC";systemGray4,8,V4007e152a,2d007e1553,;"#D1D1D6";destructive,8,V400851687,2d008516b0,;"#FF3B30";md_theme_light_primaryContainer,8,V4000500d1,410005010e,;"#EADDFF";systemGray5,8,V4007f1558,2d007f1581,;"#E5E5EA";category_shoes,8,V4006f1264,30006f1290,;"#FFF3E0";accent_primary,8,V911897,2c009118c3,;"#007AFF";containerBackground,9,V400250492,35002504c3,;"#1E1E1E";md_theme_light_onSurface,8,V4001a0500,3a001a0536,;"#1C1B1F";md_theme_light_onPrimary,8,V400040096,3a000400cc,;"#FFFFFF";md_theme_dark_primary,8,V40022065e,3700220691,;"#D0BCFF";systemGray,8,V4007b14a1,2c007b14c9,;"#8E8E93";background_primary,8,V4004b0d5e,34004b0d8e,;"#FFFBFE";quaternarySystemGroupedBackground,8,V8e17f3,3f008e1832,;"#FFFFFF";md_theme_dark_background,8,V400360a3e,3a00360a74,;"#1C1B1F";black_alpha_24,8,V400601014,3200601042,;"#3D000000";md_theme_dark_surface,8,V400380ab6,3700380ae9,;"#1C1B1F";text_secondary,8,V400510e42,3000510e6e,;"#49454F";borderColor,9,V400210417,2f00210442,;"#33FFFFFF";md_theme_dark_secondaryContainer,8,V4002907cb,4200290809,;"#4A4458";border_primary,8,V90186a,2c00901896,;"#D1D1D6";textColorSecondary,9,V4001c0364,36001c0396,;"#B3FFFFFF";text_inverse,8,V400530ea3,2e00530ecd,;"#FFFFFF";white,8,V400731327,290073134c,;"#FFFFFFFF";text_tertiary_ios,8,V4007713f3,3300771422,;"#C7C7CC";warning,8,V400470cfa,2900470d1f,;"#FF9800";surface_primary,8,V400781427,3100781454,;"#FFFFFF";md_theme_light_onErrorContainer,8,V40015040a,4100150447,;"#410002";black_alpha_12,8,V4005f0fe1,32005f100f,;"#1F000000";selected,8,V4005a0f4d,2a005a0f73,;"#EADDFF";info,8,V400480d24,2600480d46,;"#2196F3";dividerColor,9,V4002003e6,3000200412,;"#33FFFFFF";border,8,V400570f11,2800570f35,;"#79747E";systemBackground,8,V400741351,320074137f,;"#FFFFFF";overlay,8,V40069118b,2b006911b2,;"#80000000";md_theme_light_secondary,8,V400080158,3a0008018e,;"#625B71";md_theme_light_surface,8,V4001904c7,38001904fb,;"#FFFBFE";labelTertiary,8,V40089172f,2f0089175a,;"#3C3C43";shimmer_highlight,8,V400681157,3300681186,;"#FFFFFF";separator,8,V4008115b4,2b008115db,;"#3C3C43";colorOnSurface,9,V40014026d,3000140299,;"#FFFFFF";md_theme_light_outline,8,V4001e05be,38001e05f2,;"#79747E";systemRed,8,V8d17cb,27008d17f2,;"#FF3B30";md_theme_dark_tertiaryContainer,8,V4002e08c8,41002e0905,;"#633B48";white_alpha_24,8,V4006310ad,32006310db,;"#3DFFFFFF";opaqueSeparator,8,V4008215e0,310082160d,;"#C6C6C8";fillTertiary,8,V8c17a0,2a008c17ca,;"#D1D1D6";colorSecondaryVariant,9,V4000b0141,37000b0174,;"#018786";textColorHint,9,V4001d039b,31001d03c8,;"#66FFFFFF";buttonBackground,9,V4002804df,320028050d,;"#BB86FC";success,8,V400460cd0,2900460cf5,;"#4CAF50";md_theme_dark_onTertiaryContainer,8,V4002f090a,43002f0949,;"#FFD8E4";colorOnPrimary,9,V4000700c8,30000700f4,;"#000000";md_theme_light_outlineVariant,8,V4001f05f7,3f001f0632,;"#CAC4D0";background_secondary_ios,8,V8f1833,36008f1869,;"#F2F2F7";md_theme_dark_tertiary,8,V4002c0854,38002c0888,;"#EFB8C8";md_theme_light_secondaryContainer,8,V4000a01d0,43000a020f,;"#E8DEF8";md_theme_light_error,8,V40012035a,360012038c,;"#BA1A1A";labelQuaternary,8,V4008a175f,31008a178c,;"#3C3C43";md_theme_dark_onError,8,V400320985,37003209b8,;"#690005";labelSecondary,8,V4008816fe,300088172a,;"#3C3C43";md_theme_light_onTertiary,8,V4000e0295,3b000e02cc,;"#FFFFFF";md_theme_light_onPrimaryContainer,8,V400060113,4300060152,;"#21005D";md_theme_light_tertiaryContainer,8,V4000f02d1,42000f030f,;"#FFD8E4";background_tertiary,8,V4004d0dca,35004d0dfb,;"#E7E0EC";category_outerwear,8,V4006e122f,34006e125f,;"#E8F5E8";md_theme_dark_secondary,8,V400270755,390027078a,;"#CCC2DC";secondarySystemGroupedBackground,8,V400831612,4200831650,;"#F2F2F7";shimmer_base,8,V400671128,2e00671152,;"#F0F0F0";md_theme_dark_error,8,V40031094f,3500310980,;"#FFB4AB";md_theme_dark_onSurfaceVariant,8,V4003b0b67,40003b0ba3,;"#CAC4D0";md_theme_light_onBackground,8,V400180489,3d001804c2,;"#1C1B1F";md_theme_dark_onSurface,8,V400390aee,3900390b23,;"#E6E1E5";statusBarColor,9,V4002c055e,30002c058a,;"#000000";divider,8,V400560ee7,2900560f0c,;"#CAC4D0";rippleColor,9,V400310616,2f00310641,;"#33FFFFFF";buttonTextColor,9,V400290512,310029053f,;"#000000";md_theme_dark_onPrimary,8,V400230696,39002306cb,;"#381E72";white_alpha_54,8,V4006410e0,320064110e,;"#8AFFFFFF";colorPrimaryVariant,9,V400060092,35000600c3,;"#3700B3";md_theme_dark_onErrorContainer,8,V4003409fc,4000340a38,;"#FFDAD6";md_theme_dark_primaryContainer,8,V4002406d0,400024070c,;"#4F378B";md_theme_dark_onTertiary,8,V4002d088d,3a002d08c3,;"#492532";md_theme_light_tertiary,8,V4000d025b,39000d0290,;"#7D5260";textColorPrimary,9,V4001b0331,32001b035f,;"#FFFFFF";md_theme_light_onError,8,V400130391,38001303c5,;"#FFFFFF";accent,8,V400430c93,2800430cb7,;"#D0BCFF";md_theme_light_onSecondary,8,V400090193,3c000901cb,;"#FFFFFF";colorOnBackground,9,V4001001f4,3300100223,;"#FFFFFF";md_theme_dark_outlineVariant,8,V4003e0be1,3e003e0c1b,;"#49454F";+drawable:ic_subcategory_formal_pants,11,F;ic_occasion_home,12,F;ic_empty_box,13,F;ic_check_circle,14,F;ic_subcategory_vest,15,F;ic_subcategory_rain_boots,16,F;ic_subcategory_sport_pants,17,F;selected_item_background,18,F;ic_subcategory_boots,19,F;ic_subcategory_spaghetti_dress,20,F;ic_clothing_placeholder,21,F;ic_subcategory_formal_dress,22,F;dialog_background,23,F;ios_style_group_background_middle,24,F;ic_subcategory_thermal,25,F;ic_subcategory_camisole,26,F;search_background,27,F;edit_text_background_ios,28,F;ic_add,29,F;ic_profile,30,F;ic_subcategory_mermaid_skirt,31,F;ic_launcher_background,32,F;ic_chevron_right,33,F;ic_calendar,34,F;ic_subcategory_functional,35,F;dotted_border_background,36,F;ic_subcategory_bra,37,F;ic_subcategory_pleated_skirt,38,F;ic_replace_photo,39,F;ic_subcategory_bag,40,F;ios_style_group_background_single,41,F;option_background,42,F;ic_subcategory_jeans,43,F;ic_subcategory_camisole_dress,44,F;secondary_system_grouped_background_ripple,45,F;ic_occasion_sports,46,F;ic_subcategory_briefs,47,F;ic_category_workwear,48,F;ic_subcategory_straight_skirt,49,F;ic_subcategory_pajamas,50,F;ic_occasion_casual,51,F;button_background,52,F;ic_subcategory_sport_shoes,53,F;ic_subcategory_tracksuit,54,F;ic_subcategory_flared_skirt,55,F;ic_occasion_date,56,F;ic_outfit,57,F;ic_subcategory_sneakers,58,F;ic_subcategory_socks,59,F;dialog_rounded_background,60,F;ic_occasion_outdoor,61,F;ic_subcategory_skirt_bottom,62,F;ic_subcategory_hoodie,63,F;ic_category_outerwear,64,F;ic_subcategory_slit_skirt,65,F;ic_wardrobe,66,F;ic_occasion_formal,67,F;ic_subcategory_jumpsuit,68,F;ic_subcategory_sandals,69,F;spinner_background,70,F;color_circle_background,71,F;ic_category_bottoms,72,F;ic_subcategory_polo,73,F;ic_occasion_party,74,F;ic_subcategory_jewelry,75,F;ios_style_group_background_top,76,F;ic_occasion_daily,77,F;ic_subcategory_skirt,78,F;ic_subcategory_shorts,79,F;button_background_prominent,80,F;ic_subcategory_pencil_skirt,81,F;replace_icon_background,82,F;ic_subcategory_traditional,83,F;ic_category_sets,84,F;ic_subcategory_shirt,85,F;ic_category_accessories,86,F;ic_subcategory_scarf,87,F;ic_subcategory_heels,88,F;ic_subcategory_coat,89,F;ic_subcategory_jacket,90,F;ic_subcategory_dress,91,F;ic_category_tuxedo,92,F;ic_category_shoes,93,F;ic_subcategory_canvas_shoes,94,F;ic_subcategory_belt,95,F;ic_subcategory_evening_dress,96,F;ic_clear,97,F;ic_subcategory_suit,98,F;ic_category_tops,99,F;ic_category_underwear,100,F;ic_category_suits,101,F;ios_style_group_background_bottom,102,F;ic_occasion_work,103,F;ic_subcategory_hat,104,F;ic_subcategory_formal_shoes,105,F;button_background_destructive,106,F;ic_category_dresses,107,F;ic_error,108,F;ic_occasion_travel,109,F;ic_launcher_foreground,110,F;ic_subcategory_wedding,111,F;ic_subcategory_sweater,112,F;ic_subcategory_tshirt,113,F;ic_search,114,F;ic_subcategory_outer_vest,115,F;ic_subcategory_pants,116,F;ic_subcategory_other_acc,117,F;+id:batch_category_selector_view,118,F;occasion_icon,119,F;color_circle,120,F;color_circle,121,F;btn_view_detail,122,F;btn_save_batch,118,F;edit_notes_batch,118,F;text_title,123,F;image_container,124,F;button_replace_photo,124,F;text_view_color_value,124,F;tv_outfit_name,125,F;iv_selected_icon,126,F;group_recycler_view,127,F;layout_empty_state,128,F;tv_empty_message,129,F;tv_empty_state,130,F;tv_empty_state,128,F;tv_empty_state,127,F;bottom_navigation,131,F;tv_item_count,132,F;btn_batch_add,133,F;grid_view_selected_images,118,F;color_recycler_view,134,F;fullscreen_image_view,135,F;option_cancel,136,F;recycler_view_outfits,128,F;tv_error_message,137,F;fab_add_clothing,127,F;iv_error,137,F;tv_storage_info,138,F;spinner_icon,139,F;iv_clear_search,127,F;tv_name,126,F;selection_overlay,126,F;tv_error_title,137,F;label_text,140,F;category_icon,141,F;color_name,120,F;occasion_name,119,F;nav_wardrobe,142,F;edit_story_batch,118,F;nav_calendar,142,F;category_recycler_view,143,F;btn_back,144,F;btn_back,145,F;btn_back,146,F;button_choose_gallery,123,F;et_search,127,F;tv_day_of_week,122,F;button_save,124,F;chip_group_filter,127,F;nav_profile,142,F;tv_date,122,F;nav_outfit,142,F;btn_delete,147,F;occasion_recycler_view,148,F;spinner_text,121,F;spinner_text,139,F;tv_suggestion,128,F;tv_empty_title,129,F;iv_clothing,126,F;item_name,140,F;text_view_occasion_value,124,F;btn_compress_images,138,F;btn_select_all,147,F;option_gallery,136,F;btn_cleanup_images,138,F;item_checkmark,140,F;btn_cancel,147,F;fragment_container,131,F;switch_notifications,138,F;recycler_view,144,F;recycler_view,133,F;recycler_view,145,F;recycler_view,146,F;recycler_view,149,F;recycler_view,130,F;btn_cleanup_unused,138,F;item_icon,140,F;switch_auto_backup,138,F;btn_select_images_batch,118,F;tv_title,144,F;tv_title,145,F;tv_title,146,F;tv_title,130,F;tv_title,128,F;empty_view,133,F;btn_save_outfit,125,F;color_preview_circle,120,F;text_view_category_value,124,F;tv_clothing_count,138,F;count_view,133,F;category_selector,150,F;tv_loading_state,127,F;tv_current_date,130,F;tv_outfit_description,122,F;tv_outfit_description,125,F;layout_category,124,F;option_camera,136,F;close_button,135,F;layout_occasion,124,F;clothing_name,151,F;button_cancel,123,F;btn_retry,137,F;rv_clothing_items,132,F;subcategory_selector,150,F;progress_bar,152,F;clothing_image_view,124,F;edit_tags_batch,118,F;switch_dark_mode,138,F;tv_selected_count,147,F;btn_add_color,145,F;image_placeholder,124,F;iv_empty,129,F;button_take_photo,123,F;nav_bar,144,F;nav_bar,145,F;nav_bar,146,F;tv_image_count,138,F;action_delete_multiple,153,F;btn_share_outfit,125,F;multi_select_toolbar,127,F;multi_select_toolbar,147,F;clothing_image,151,F;value_text,140,F;layout_color,124,F;category_title,133,F;tv_category_title,132,F;color_selector,150,F;bottom_nav_container,131,F;bottom_nav_container,131,F;tv_category,141,F;clothing_category,151,F;occasion_selector,150,F;edit_text_name,124,F;+layout:activity_clothing_detail,133,F;item_clothing_pro,151,F;activity_color_select,145,F;dialog_occasion_selection,148,F;layout_category_selector,150,F;activity_category_select,144,F;fragment_outfit,128,F;item_occasion_selector,119,F;dialog_recyclerview,149,F;activity_occasion_select,146,F;activity_main,131,F;dialog_photo_selection_ios,123,F;activity_add_clothing,124,F;spinner_item_with_icon,139,F;list_item_selector_row,140,F;item_outfit,125,F;fragment_profile,138,F;item_category_selector,141,F;dialog_image_fullscreen,135,F;dialog_category_selection,143,F;item_calendar,122,F;layout_error,137,F;activity_batch_add_clothing,118,F;layout_loading,152,F;layout_empty,129,F;fragment_wardrobe,127,F;multi_select_toolbar,147,F;dialog_color_selection,134,F;item_wardrobe_group,132,F;dialog_photo_selection,136,F;item_color_spinner,121,F;item_clothing_horizontal,126,F;fragment_calendar,130,F;item_color_selector,120,F;+menu:bottom_navigation_menu,142,F;action_mode_menu,153,F;menu,154,F;+mipmap:ic_launcher_round,155,F;ic_launcher_round,156,F;ic_launcher_round,157,F;ic_launcher_round,158,F;ic_launcher_round,159,F;ic_launcher_round,160,F;ic_launcher,161,F;ic_launcher,162,F;ic_launcher,163,F;ic_launcher,164,F;ic_launcher,165,F;ic_launcher,166,F;+string:button_filter,167,V4007914af,2c007914d7,;"筛选";button_filter,168,V400270698,30002706c4,;"Filter";settings_theme,167,V400a41b7e,2d00a41ba7,;"主题";settings_theme,168,V40078153d,3000781569,;"Theme";section_optional,167,V4000b0180,31000b01ad,;"可选信息";section_optional,168,V4000c01db,41000c0218,;"Optional Information";dialog_option_camera,167,V4001e0490,33001e04bf,;"拍照";dialog_title_select_occasion,167,V4001c0417,3d001c0450,;"选择场合";dialog_title_select_occasion,168,V4001d04d8,48001d051c,;"Select Occasion";dialog_button_cancel,167,V4002004fc,330020052b,;"取消";color_beige,167,V400631172,2a00631198,;"米色";error_incomplete_selection_batch,167,V4003b0a69,46003b0aab,;"请先选择完整的分类";color_placeholder,167,V4001402a0,33001402cf,;"请选择颜色";color_placeholder,168,V400150324,3a0015035a,;"Select Color";color_pink,167,V4005f10c5,29005f10ea,;"粉色";color_pink,168,V400470bf1,2b00470c18,;"Pink";accessibility_filter_button,167,V400b31ddb,3c00b31e13,;"筛选衣物";accessibility_filter_button,168,V400871812,4700871855,;"Filter clothing";toast_select_image_first,167,V4002605ed,3d00260626,;"请先选择一张图片";compressing,167,V4002f07c1,2e002f07eb,;"压缩中...";toast_load_failed,167,V4002505ba,32002505e8,;"加载失败";success_batch_add_clothing_summary_with_failures,167,V4003d0afd,64003d0b5d,;"成功添加%1$d件%2$s，%3$d张图片失败";color_custom,167,V400570f6e,2c00570f96,;"自定义";error_generic,167,V400ae1d22,2e00ae1d4c,;"发生错误";error_generic,168,V4007e1632,3b007e1669,;"An error occurred";dialog_title_select_color,167,V4001b03dc,3a001b0412,;"选择颜色";dialog_title_select_color,168,V4001c0495,42001c04d3,;"Select Color";button_clear,167,V4007a14dc,2b007a1503,;"清除";button_clear,168,V4002806c9,2e002806f3,;"Clear";photo_choose_gallery,167,V4009819c3,36009819f5,;"从相册选择";photo_choose_gallery,168,V4006c131a,44006c135a,;"Choose from Gallery";category_primary_placeholder,167,V40016030b,3f00160346,;"请选择主分类";category_primary_placeholder,168,V4001703a0,4d001703e9,;"Select Main Category";filter_by_color,167,V4009e1aa1,31009e1ace,;"按颜色筛选";filter_by_color,168,V400721432,3b00721469,;"Filter by Color";color_black,167,V400580f9b,2a00580fc1,;"黑色";color_black,168,V400430b3b,2d00430b64,;"Black";filter_clear_all,167,V400a01b08,3300a01b37,;"清除所有筛选";filter_clear_all,168,V4007414b0,3e007414ea,;"Clear All Filters";label_occasion,167,V400100220,2d00100249,;"场合";label_occasion,168,V400110294,33001102c3,;"Occasion";message_no_data,167,V4008c17d9,30008c1805,;"暂无数据";message_no_data,168,V4006010b9,3d006010f2,;"No data available";error_cleanup_images,167,V4002e0785,3b002e07bc,;"清理失败\: %1$s";error_get_storage_info,167,V4002c06fe,3b002c0735,;"获取存储信息失败";error_main_category_missing_batch,167,V400370959,4a0037099f,;"主分类缺失，无法批量添加";nav_wardrobe,167,V4006d12e7,2b006d130e,;"衣橱";nav_wardrobe,168,V4002d0787,31002d07b4,;"Wardrobe";error_no_images_selected_batch,167,V4003a0a24,44003a0a64,;"请至少选择一张图片";success_batch_add_clothing_summary,167,V4003c0ab0,4c003c0af8,;"成功添加%1$d件%2$s";label_category,167,V4000e01c7,2d000e01f0,;"分类";label_category,168,V4000f0232,33000f0261,;"Category";nav_calendar,167,V4006f133d,2b006f1364,;"日历";nav_calendar,168,V4002f07e7,31002f0814,;"Calendar";message_save_success,167,V4008716bc,35008716ed,;"保存成功";message_save_success,168,V400590ec8,4300590f07,;"Saved successfully";category_bottoms,167,V400430c34,2f00430c5f,;"下装";category_bottoms,168,V400340891,34003408c1,;"Bottoms";button_save,167,V4000600d8,2a000600fe,;"保存";button_save,168,V400070118,2c00070140,;"Save";button_search,167,V400781482,2c007814aa,;"搜索";button_search,168,V400260667,3000260693,;"Search";nav_profile,167,V400701369,2a0070138f,;"个人";nav_profile,168,V400300819,2f00300844,;"Profile";compress_all_images,167,V4003007f0,3600300822,;"压缩所有图片";message_network_error,167,V4008a1764,38008a1798,;"网络连接失败";message_network_error,168,V4005e1025,4b005e106c,;"Network connection failed";nav_outfit,167,V4006e1313,29006e1338,;"搭配";nav_outfit,168,V4002e07b9,2d002e07e2,;"Outfit";occasion_daily,167,V400530ee5,2d00530f0e,;"日常";occasion_daily,168,V400560e80,3000560eac,;"Daily";title_edit_clothing,167,V4000500a3,34000500d3,;"编辑衣物";title_edit_clothing,168,V4000600da,3d00060113,;"Edit Clothing";message_operation_failed,167,V40089172a,390089175f,;"操作失败";message_operation_failed,168,V4005d0fdf,45005d1020,;"Operation failed";category_secondary_placeholder,167,V40017034b,4100170388,;"请选择子分类";category_secondary_placeholder,168,V4001803ee,4d00180437,;"Select Subcategory";color_brown,167,V400621147,2a0062116d,;"棕色";color_brown,168,V400460bc3,2d00460bec,;"Brown";occasion_outdoor,167,V400540f13,2f00540f3e,;"户外";occasion_outdoor,168,V400550e4b,3400550e7b,;"Outdoor";settings_privacy,167,V400a61be2,2f00a61c0d,;"隐私";settings_privacy,168,V4007a15af,34007a15df,;"Privacy";color_blue,167,V4005d106f,29005d1094,;"蓝色";color_blue,168,V400400ab1,2b00400ad8,;"Blue";toast_save_successful,167,V400230550,3600230582,;"保存成功";error_start_compress,167,V4003308ac,3d003308e5,;"启动压缩失败\: %1$s";category_accessories,167,V400470cf5,3300470d24,;"配饰";category_accessories,168,V400380965,3c0038099d,;"Accessories";label_color,167,V4000f01f5,2a000f021b,;"颜色";label_color,168,V400100266,2d0010028f,;"Color";color_white,167,V400590fc6,2a00590fec,;"白色";color_white,168,V400440b69,2d00440b92,;"White";color_gray,167,V4005a0ff1,29005a1016,;"灰色";color_gray,168,V400450b97,2b00450bbe,;"Gray";toast_save_failed,167,V400240587,32002405b5,;"保存失败";search_hint,167,V4009c1a3c,2f009c1a67,;"搜索衣物...";search_hint,168,V4007013b5,3a007013eb,;"Search clothing...";occasion_sport,167,V4004e0e00,2d004e0e29,;"运动";color_green,167,V4005c1044,2a005c106a,;"绿色";color_green,168,V400410add,2d00410b06,;"Green";message_add_success,167,V40085164f,340085167f,;"添加成功";message_add_success,168,V4005b0f54,42005b0f92,;"Added successfully";category_tops,167,V400420c07,2c00420c2f,;"上衣";category_tops,168,V400330862,2e0033088c,;"Tops";message_loading,167,V4008d180a,32008d1838,;"加载中...";message_loading,168,V4006110f7,3600611129,;"Loading...";validation_name_too_long,167,V40093190c,3900931941,;"名称过长";validation_name_too_long,168,V40067123b,450067127c,;"Name is too long";occasion_travel,167,V400500e5c,2e00500e86,;"旅行";occasion_travel,168,V400520dba,3200520de8,;"Travel";message_permission_denied,167,V4008b179d,3b008b17d4,;"权限被拒绝";message_permission_denied,168,V4005f1071,47005f10b4,;"Permission denied";app_name,167,V400010010,300001003c,;"WardrobeApp";app_name,168,V400020037,3500020068,;"Wardrobe Manager";category_shoes,167,V400460cc7,2d00460cf0,;"鞋子";category_shoes,168,V400370934,3000370960,;"Shoes";occasion_placeholder,167,V4001502d4,3600150306,;"请选择场合";occasion_placeholder,168,V40016035f,400016039b,;"Select Occasion";error_load_data,167,V4002b06cb,32002b06f9,;"加载数据失败";validation_name_required,167,V40091188e,3d009118c7,;"衣物名称不能为空";validation_name_required,168,V40065119e,4e006511e8,;"Clothing name is required";placeholder_select,167,V40018038d,32001803bb,;"请选择";placeholder_select,168,V40019043c,3c00190474,;"Please Select";accessibility_add_button,167,V400b11d63,3a00b11d99,;"添加新衣物";accessibility_add_button,168,V40085177f,4a008517c5,;"Add new clothing item";button_add,167,V4007313a5,29007313ca,;"添加";button_add,168,V40025063c,2a00250662,;"Add";accessibility_clothing_image,167,V400b41e18,3d00b41e51,;"衣物图片";accessibility_clothing_image,168,V40088185a,4c008818a2,;"Clothing item image";section_required,167,V4000a014e,31000a017b,;"必填信息";section_required,168,V4000b0199,41000b01d6,;"Required Information";hint_clothing_name,167,V400070103,3300070132,;"衣物名称";hint_clothing_name,168,V400080145,3c0008017d,;"Clothing Name";error_batch_add_all_failed,167,V4003e0b62,47003e0ba5,;"全部失败，共%1$d张图片未添加";category_workwear,167,V40082160c,3100821639,;"工作服";category_workwear,168,V4003c0a3b,36003c0a6d,;"Workwear";title_add_new_clothing,167,V40004006a,380004009e,;"添加新衣物";title_add_new_clothing,168,V400050096,43000500d5,;"Add New Clothing";message_delete_success,167,V400861684,37008616b7,;"删除成功";message_delete_success,168,V4005a0f0c,47005a0f4f,;"Deleted successfully";validation_category_required,167,V4009218cc,3f00921907,;"分类不能为空";validation_category_required,168,V4006611ed,4d00661236,;"Category is required";error_network,167,V400aa1c53,3000aa1c7f,;"网络连接失败";error_network,168,V4007f166e,37007f16a1,;"Network error";images_selected_count_batch,167,V4003909e1,4200390a1f,;"已选择%1$d张图片";color_orange,167,V40061111b,2b00611142,;"橙色";color_orange,168,V400490c4d,2f00490c78,;"Orange";error_permission,167,V400ab1c84,3200ab1cb2,;"权限被拒绝";error_permission,168,V4008016a6,40008016e2,;"Permission required";message_search_no_results,167,V4008e183d,3d008e1876,;"未找到搜索结果";message_search_no_results,168,V40062112e,4d00621177,;"No search results found";category_underwear,167,V4008115da,3100811607,;"内衣";category_underwear,168,V4003909a2,38003909d6,;"Underwear";category_suits,167,V4008015ac,2d008015d5,;"西装";category_suits,168,V4003a09db,30003a0a07,;"Suits";color_red,167,V4005b101b,28005b103f,;"红色";color_red,168,V4003f0a87,29003f0aac,;"Red";validation_invalid_input,167,V400941946,390094197b,;"输入无效";validation_invalid_input,168,V400681281,42006812bf,;"Invalid input";message_update_success,167,V4008816f2,3700881725,;"更新成功";message_update_success,168,V4005c0f97,47005c0fda,;"Updated successfully";photo_take_photo,167,V400971993,2f009719be,;"拍照";photo_take_photo,168,V4006b12e2,37006b1315,;"Take Photo";color_yellow,167,V4005e1099,2b005e10c0,;"黄色";color_yellow,168,V400420b0b,2f00420b36,;"Yellow";error_file_not_found,167,V400ac1cb7,3600ac1ce9,;"文件未找到";error_file_not_found,168,V4008116e7,3f00811722,;"File not found";color_khaki,167,V40064119d,2b006411c4,;"卡其色";accessibility_search_button,167,V400b21d9e,3c00b21dd6,;"搜索衣物";accessibility_search_button,168,V4008617ca,470086180d,;"Search clothing";filter_by_category,167,V4009d1a6c,34009d1a9c,;"按分类筛选";filter_by_category,168,V4007113f0,410071142d,;"Filter by Category";toast_create_image_file_failed,167,V4006a1292,43006a12d1,;"无法创建图片文件";occasion_work,167,V4004a0d46,2c004a0d6e,;"工作";occasion_work,168,V4004f0d27,2e004f0d51,;"Work";dialog_option_gallery,167,V4001f04c4,37001f04f7,;"从相册选择";occasion_formal,167,V4004c0da2,2e004c0dcc,;"正式";occasion_formal,168,V4004e0cf4,32004e0d22,;"Formal";success_cleanup_unused_images,167,V4003408ea,5100340937,;"清理完成，删除了 %1$d 个未使用的图片文件";button_cancel,167,V4007413cf,2c007413f7,;"取消";button_cancel,168,V40021057a,30002105a6,;"Cancel";category_sets,167,V4007f157f,2c007f15a7,;"套装";category_sets,168,V4003b0a0c,2e003b0a36,;"Sets";toast_permissions_denied,167,V40028066f,41002806ac,;"权限被拒绝，无法选择图片";title_batch_add_prefix,167,V4003809a4,3c003809dc,;"批量添加：%1$s";occasion_party,167,V4004f0e2e,2d004f0e57,;"派对";occasion_party,168,V400500d56,3000500d82,;"Party";category_outerwear,167,V400450c95,3100450cc2,;"外套";category_outerwear,168,V4003608fb,380036092f,;"Outerwear";color_navy,167,V4006511c9,2a006511ef,;"藏青色";color_navy,168,V4004a0c7d,2b004a0ca4,;"Navy";settings_about,167,V400a71c12,2d00a71c3b,;"关于";settings_about,168,V4007b15e4,30007b1610,;"About";toast_permission_denied,167,V400691251,400069128d,;"权限被拒绝，无法继续操作";occasion_date,167,V400520eb8,2c00520ee0,;"约会";occasion_date,168,V400530ded,2e00530e17,;"Date";toast_image_selection_cancelled,167,V40027062b,430027066a,;"已取消选择图片";occasion_casual,167,V4004b0d73,2e004b0d9d,;"休闲";occasion_casual,168,V4004d0cc1,32004d0cef,;"Casual";button_confirm,167,V4007513fc,2d00751425,;"确认";button_confirm,168,V4002205ab,32002205d9,;"Confirm";error_invalid_data,167,V400ad1cee,3300ad1d1d,;"数据无效";error_invalid_data,168,V400821727,3b0082175e,;"Invalid data";info_no_items_to_add_batch,167,V4003f0baa,3f003f0be5,;"没有可添加的衣物";category_dresses,167,V400440c64,3000440c90,;"连衣裙";category_dresses,168,V4003508c6,34003508f6,;"Dresses";button_delete,167,V40076142a,2c00761452,;"删除";button_delete,168,V4002305de,300023060a,;"Delete";dialog_title_select_photo,167,V4001d0455,3a001d048b,;"选择图片";dialog_title_select_photo,168,V4001e0521,42001e055f,;"Select Photo";filter_by_occasion,167,V4009f1ad3,34009f1b03,;"按场合筛选";filter_by_occasion,168,V40073146e,41007314ab,;"Filter by Occasion";toast_permission_granted_retry,167,V40068120a,460068124c,;"权限已授予，请重试操作";settings_notifications,167,V400a51bac,3500a51bdd,;"通知";settings_notifications,168,V40079156e,40007915aa,;"Notifications";success_compress_images,167,V400310827,470031086a,;"压缩完成，处理了 %1$d 个图片文件";occasion_home,167,V400510e8b,2c00510eb3,;"居家";occasion_home,168,V400540e1c,2e00540e46,;"Home";error_compress_images,167,V40032086f,3c003208a7,;"压缩失败\: %1$s";settings_language,167,V400a31b4d,3000a31b79,;"语言";settings_language,168,V400771506,3600771538,;"Language";color_purple,167,V4006010ef,2b00601116,;"紫色";color_purple,168,V400480c1d,2f00480c48,;"Purple";photo_remove,167,V4009919fa,2d00991a23,;"移除照片";photo_remove,168,V4006d135f,35006d1390,;"Remove Photo";success_cleanup_all_images,167,V4002d073a,4a002d0780,;"清理完成，删除了 %1$d 个图片文件";category_placeholder,167,V400130269,360013029b,;"请选择分类";category_placeholder,168,V4001402e3,400014031f,;"Select Category";button_edit,167,V400771457,2a0077147d,;"编辑";button_edit,168,V40024060f,2c00240637,;"Edit";occasion_sports,167,V4004d0dd1,2e004d0dfb,;"运动";occasion_sports,168,V400510d87,3200510db5,;"Sports";button_select_all,167,V4007b1508,30007b1534,;"全选";button_select_all,168,V4002906f8,380029072c,;"Select All";button_deselect_all,167,V4007c1539,34007c1569,;"取消全选";button_deselect_all,168,V4002a0731,3c002a0769,;"Deselect All";+style:ButtonTertiary,169,V400a41fc6,c00af2253,;DWidget.AppCompat.Button,android\:background:@drawable/button_background,android\:textColor:@color/labelSecondary,android\:textSize:14sp,android\:fontFamily:sans-serif,android\:paddingStart:16dp,android\:paddingEnd:16dp,android\:paddingTop:8dp,android\:paddingBottom:8dp,android\:minHeight:32dp,android\:textAllCaps:false,;iOSLabel,169,V4002206b4,c00290850,;Nandroid\:layout_width:wrap_content,android\:layout_height:wrap_content,android\:layout_alignParentStart:true,android\:layout_centerVertical:true,android\:textColor:@color/labelPrimary,android\:textSize:17sp,;TextAppearance.App.Headline,169,V40071153e,c0074160c,;DTextAppearance.App.Base,android\:fontFamily:sans-serif-medium,android\:textSize:17sp,;TextAppearance.App.Body,169,V400751612,c0077169a,;DTextAppearance.App.Base,android\:textSize:17sp,;ProminentButton,169,V4004c0e18,c00510f5e,;DWidget.AppCompat.Button,android\:background:@drawable/button_background_prominent,android\:textColor:@color/white,android\:textSize:17sp,android\:textStyle:bold,;TextAppearance.App.Footnote,169,V4007f180b,c008218dc,;DTextAppearance.App.Base,android\:textSize:13sp,android\:textColor:@color/labelSecondary,;TextAppearance.WardrobeApp.TitleMedium,170,V40092208a,c0094213b,;DTextAppearance.Material3.TitleMedium,android\:fontFamily:sans-serif-medium,;ButtonDestructive,169,V400b02259,c00bb24f5,;DWidget.AppCompat.Button,android\:background:@drawable/button_background_destructive,android\:textColor:@color/white,android\:textSize:16sp,android\:fontFamily:sans-serif-medium,android\:paddingStart:24dp,android\:paddingEnd:24dp,android\:paddingTop:12dp,android\:paddingBottom:12dp,android\:minHeight:44dp,android\:textAllCaps:false,;Base.Theme.WardrobeApp,169,V4005910c1,c005d11a7,;DTheme.WardrobeApp.Material3,windowActionBar:false,windowNoTitle:true,;ButtonPrimary,169,V4008c1a91,c00971d27,;DWidget.AppCompat.Button,android\:background:@drawable/button_background_prominent,android\:textColor:@color/white,android\:textSize:16sp,android\:fontFamily:sans-serif-medium,android\:paddingStart:24dp,android\:paddingEnd:24dp,android\:paddingTop:12dp,android\:paddingBottom:12dp,android\:minHeight:44dp,android\:textAllCaps:false,;iOSValue,169,V4002a0856,c003109e8,;Nandroid\:layout_width:wrap_content,android\:layout_height:wrap_content,android\:layout_centerVertical:true,android\:textColor:@color/systemGray,android\:textSize:17sp,android\:layout_marginEnd:8dp,;AppTheme,171,V40003004b,c002906fa,;DTheme.Material3.DayNight,colorPrimary:@color/colorPrimary,colorPrimaryVariant:@color/colorPrimaryVariant,colorOnPrimary:@color/colorOnPrimary,colorSecondary:@color/colorSecondary,colorSecondaryVariant:@color/colorSecondaryVariant,colorOnSecondary:@color/colorOnSecondary,colorOnBackground:@color/colorOnBackground,colorSurface:@color/colorSurface,colorOnSurface:@color/colorOnSurface,colorError:@color/colorError,colorOnError:@color/colorOnError,android\:colorBackground:@color/colorBackground,android\:statusBarColor:@color/statusBarColor,android\:windowLightStatusBar:false,android\:navigationBarColor:@color/navigationBarColor,android\:windowLightNavigationBar:false,android\:textColorPrimary:@color/textColorPrimary,android\:textColorSecondary:@color/textColorSecondary,android\:textColorHint:@color/textColorHint,android\:forceDarkAllowed:true,;ButtonStyle,171,V4002c071a,c00300831,;DWidget.Material3.Button,android\:background:@color/buttonBackground,android\:textColor:@color/buttonTextColor,backgroundTint:@color/buttonBackground,;TextAppearance.App.Callout,169,V4007816a0,c007a172b,;DTextAppearance.App.Base,android\:textSize:16sp,;TextAppearance.WardrobeApp.LabelSmall,170,V400ae2568,c00b02617,;DTextAppearance.Material3.LabelSmall,android\:fontFamily:sans-serif-medium,;Theme.WardrobeApp.PopupOverlay,169,V400d32a15,5900d32a6a,;DThemeOverlay.AppCompat.Light,;Widget.WardrobeApp.Button.Outlined,170,V400bb27be,c00c12957,;DWidget.Material3.Button.OutlinedButton,android\:textAppearance:@style/TextAppearance.WardrobeApp.LabelLarge,android\:minHeight:48dp,android\:paddingStart:24dp,android\:paddingEnd:24dp,cornerRadius:12dp,;Animation.WardrobeApp.Activity,170,V400df2ea9,c00e4305e,;D@android\:style/Animation.Activity,android\:activityOpenEnterAnimation:@anim/slide_in_right,android\:activityOpenExitAnimation:@anim/slide_out_left,android\:activityCloseEnterAnimation:@anim/slide_in_left,android\:activityCloseExitAnimation:@anim/slide_out_right,;Theme.WardrobeApp.AppBarOverlay,169,V400d229b0,6300d22a0f,;DThemeOverlay.AppCompat.Dark.ActionBar,;Widget.WardrobeApp.CardView,170,V400ca2ace,c00ce2bc7,;DWidget.Material3.CardView.Elevated,cardCornerRadius:16dp,cardElevation:4dp,android\:layout_margin:8dp,;TextAppearance.App.Base,169,V4006011f7,c006312b3,;Eandroid\:textColor:@color/labelPrimary,android\:fontFamily:sans-serif,;Theme.WardrobeApp.Material3,170,V400050070,c004d1295,;DTheme.Material3.DayNight,colorPrimary:@color/md_theme_light_primary,colorOnPrimary:@color/md_theme_light_onPrimary,colorPrimaryContainer:@color/md_theme_light_primaryContainer,colorOnPrimaryContainer:@color/md_theme_light_onPrimaryContainer,colorSecondary:@color/md_theme_light_secondary,colorOnSecondary:@color/md_theme_light_onSecondary,colorSecondaryContainer:@color/md_theme_light_secondaryContainer,colorOnSecondaryContainer:@color/md_theme_light_onSecondaryContainer,colorTertiary:@color/md_theme_light_tertiary,colorOnTertiary:@color/md_theme_light_onTertiary,colorTertiaryContainer:@color/md_theme_light_tertiaryContainer,colorOnTertiaryContainer:@color/md_theme_light_onTertiaryContainer,colorError:@color/md_theme_light_error,colorOnError:@color/md_theme_light_onError,colorErrorContainer:@color/md_theme_light_errorContainer,colorOnErrorContainer:@color/md_theme_light_onErrorContainer,colorOnBackground:@color/md_theme_light_onBackground,colorSurface:@color/md_theme_light_surface,colorOnSurface:@color/md_theme_light_onSurface,colorSurfaceVariant:@color/md_theme_light_surfaceVariant,colorOnSurfaceVariant:@color/md_theme_light_onSurfaceVariant,colorOutline:@color/md_theme_light_outline,colorOutlineVariant:@color/md_theme_light_outlineVariant,textAppearanceHeadlineLarge:@style/TextAppearance.WardrobeApp.HeadlineLarge,textAppearanceHeadlineMedium:@style/TextAppearance.WardrobeApp.HeadlineMedium,textAppearanceHeadlineSmall:@style/TextAppearance.WardrobeApp.HeadlineSmall,textAppearanceTitleLarge:@style/TextAppearance.WardrobeApp.TitleLarge,textAppearanceTitleMedium:@style/TextAppearance.WardrobeApp.TitleMedium,textAppearanceTitleSmall:@style/TextAppearance.WardrobeApp.TitleSmall,textAppearanceBodyLarge:@style/TextAppearance.WardrobeApp.BodyLarge,textAppearanceBodyMedium:@style/TextAppearance.WardrobeApp.BodyMedium,textAppearanceBodySmall:@style/TextAppearance.WardrobeApp.BodySmall,textAppearanceLabelLarge:@style/TextAppearance.WardrobeApp.LabelLarge,textAppearanceLabelMedium:@style/TextAppearance.WardrobeApp.LabelMedium,textAppearanceLabelSmall:@style/TextAppearance.WardrobeApp.LabelSmall,materialButtonStyle:@style/Widget.WardrobeApp.Button,materialCardViewStyle:@style/Widget.WardrobeApp.CardView,chipStyle:@style/Widget.WardrobeApp.Chip,textInputStyle:@style/Widget.WardrobeApp.TextInputLayout,android\:colorBackground:@color/md_theme_light_background,android\:statusBarColor:@android\:color/transparent,android\:navigationBarColor:@android\:color/transparent,android\:windowLightStatusBar:true,android\:windowLightNavigationBar:true,android\:windowDrawsSystemBarBackgrounds:true,android\:windowTranslucentStatus:false,android\:windowTranslucentNavigation:false,android\:windowAnimationStyle:@style/Animation.WardrobeApp.Activity,android\:windowEnterAnimation:@anim/slide_in_right,android\:windowExitAnimation:@anim/slide_out_left,;Widget.WardrobeApp.Button,170,V400b32633,c00b927b4,;DWidget.Material3.Button,android\:textAppearance:@style/TextAppearance.WardrobeApp.LabelLarge,android\:minHeight:48dp,android\:paddingStart:24dp,android\:paddingEnd:24dp,cornerRadius:12dp,;SectionHeader,169,V400430cb8,c00490dfa,;Nandroid\:textSize:13sp,android\:textColor:@color/systemGray,android\:textAllCaps:true,android\:paddingStart:16dp,android\:paddingBottom:8dp,;Theme.WardrobeApp.Material3.Dark,170,V4005012b1,c007f1d86,;DTheme.Material3.DayNight,colorPrimary:@color/md_theme_dark_primary,colorOnPrimary:@color/md_theme_dark_onPrimary,colorPrimaryContainer:@color/md_theme_dark_primaryContainer,colorOnPrimaryContainer:@color/md_theme_dark_onPrimaryContainer,colorSecondary:@color/md_theme_dark_secondary,colorOnSecondary:@color/md_theme_dark_onSecondary,colorSecondaryContainer:@color/md_theme_dark_secondaryContainer,colorOnSecondaryContainer:@color/md_theme_dark_onSecondaryContainer,colorTertiary:@color/md_theme_dark_tertiary,colorOnTertiary:@color/md_theme_dark_onTertiary,colorTertiaryContainer:@color/md_theme_dark_tertiaryContainer,colorOnTertiaryContainer:@color/md_theme_dark_onTertiaryContainer,colorError:@color/md_theme_dark_error,colorOnError:@color/md_theme_dark_onError,colorErrorContainer:@color/md_theme_dark_errorContainer,colorOnErrorContainer:@color/md_theme_dark_onErrorContainer,colorOnBackground:@color/md_theme_dark_onBackground,colorSurface:@color/md_theme_dark_surface,colorOnSurface:@color/md_theme_dark_onSurface,colorSurfaceVariant:@color/md_theme_dark_surfaceVariant,colorOnSurfaceVariant:@color/md_theme_dark_onSurfaceVariant,colorOutline:@color/md_theme_dark_outline,colorOutlineVariant:@color/md_theme_dark_outlineVariant,android\:colorBackground:@color/md_theme_dark_background,android\:statusBarColor:@android\:color/transparent,android\:navigationBarColor:@android\:color/transparent,android\:windowLightStatusBar:false,android\:windowLightNavigationBar:false,android\:windowDrawsSystemBarBackgrounds:true,android\:windowTranslucentStatus:false,android\:windowTranslucentNavigation:false,;TextAppearance.WardrobeApp.BodyMedium,170,V4009e22a2,c00a0234a,;DTextAppearance.Material3.BodyMedium,android\:fontFamily:sans-serif,;BottomSheetStyle,169,V400c025aa,c00c2264b,;DWidget.Design.BottomSheet.Modal,android\:background:@android\:color/transparent,;TextAppearance.WardrobeApp.HeadlineMedium,170,V400861e5d,c00881f14,;DTextAppearance.Material3.HeadlineMedium,android\:fontFamily:sans-serif-medium,;DestructiveButton,169,V400520f64,c00561083,;DWidget.AppCompat.Button,android\:background:@drawable/button_background_destructive,android\:textColor:@color/destructiveRed,android\:textSize:17sp,;iOSGroupedBackground.Middle,169,V4000a01e4,c000c0296,;DiOSGroupedBackground,android\:background:@drawable/ios_style_group_background_middle,;iOSGroupedBackground.Bottom,169,V4000d029c,c000f034e,;DiOSGroupedBackground,android\:background:@drawable/ios_style_group_background_bottom,;TextAppearance.WardrobeApp.HeadlineLarge,170,V400821da2,c00841e57,;DTextAppearance.Material3.HeadlineLarge,android\:fontFamily:sans-serif-medium,;TextAppearance.App.LargeTitle,169,V4006412b9,c00671388,;DTextAppearance.App.Base,android\:fontFamily:sans-serif-light,android\:textSize:34sp,;TextAppearance.WardrobeApp.TitleLarge,170,V4008e1fd5,c00902084,;DTextAppearance.Material3.TitleLarge,android\:fontFamily:sans-serif-medium,;TextAppearance.App.Caption1,169,V4008318e2,c008619b3,;DTextAppearance.App.Base,android\:textSize:12sp,android\:textColor:@color/labelSecondary,;TextAppearance.WardrobeApp.LabelMedium,170,V400aa24b1,c00ac2562,;DTextAppearance.Material3.LabelMedium,android\:fontFamily:sans-serif-medium,;iOSListItem,169,V400150430,c001f0682,;Nandroid\:layout_width:match_parent,android\:layout_height:wrap_content,android\:minHeight:44dp,android\:gravity:center_vertical,android\:paddingLeft:16dp,android\:paddingRight:16dp,android\:clickable:true,android\:focusable:true,android\:background:@drawable/secondary_system_grouped_background_ripple,;TextAppearance.App.Caption2,169,********b9,c008a1a89,;DTextAppearance.App.Base,android\:textSize:11sp,android\:textColor:@color/labelTertiary,;CardStyle,171,V400330851,c00370947,;DWidget.Material3.CardView.Elevated,cardBackgroundColor:@color/cardBackground,cardElevation:4dp,cardCornerRadius:8dp,;iOSChevron,169,V4003209ee,c00390b94,;Nandroid\:layout_width:wrap_content,android\:layout_height:wrap_content,android\:layout_alignParentEnd:true,android\:layout_centerVertical:true,android\:src:@drawable/ic_chevron_right,;BottomSheetDialogTheme,169,V400bd24fd,c00bf25a4,;DTheme.Design.Light.BottomSheetDialog,bottomSheetStyle:@style/BottomSheetStyle,;iOSGroupedBackground.Top,169,V400070132,c000901de,;DiOSGroupedBackground,android\:background:@drawable/ios_style_group_background_top,;Theme.WardrobeApp.Dialog,169,V400c32651,c00c727a3,;DTheme.Material3.DayNight.Dialog.Alert,colorPrimary:@color/systemBlue,android\:background:@color/secondarySystemGroupedBackground,android\:windowBackground:@drawable/dialog_rounded_background,;Widget.WardrobeApp.TextInputLayout,170,V400d62cf6,c00dc2e8d,;DWidget.Material3.TextInputLayout.OutlinedBox,boxCornerRadiusTopStart:12dp,boxCornerRadiusTopEnd:12dp,boxCornerRadiusBottomStart:12dp,boxCornerRadiusBottomEnd:12dp,android\:layout_marginBottom:16dp,;TextAppearance.WardrobeApp.BodySmall,170,V400a22350,c00a423f6,;DTextAppearance.Material3.BodySmall,android\:fontFamily:sans-serif,;Theme.WardrobeApp.NoActionBar,169,V400c827a9,c00cb2846,;NwindowActionBar:false,windowNoTitle:true,;TextAppearance.WardrobeApp.LabelLarge,170,V400a623fc,c00a824ab,;DTextAppearance.Material3.LabelLarge,android\:fontFamily:sans-serif-medium,;Theme.WardrobeApp.Button.Prominent,169,V400dd2c6e,4e00dd2cb8,;DButtonPrimary,;TextAppearance.WardrobeApp.HeadlineSmall,170,V4008a1f1a,c008c1fcf,;DTextAppearance.Material3.HeadlineSmall,android\:fontFamily:sans-serif-medium,;Widget.WardrobeApp.Button.Text,170,V400c32961,c00c82ac4,;DWidget.Material3.Button.TextButton,android\:textAppearance:@style/TextAppearance.WardrobeApp.LabelLarge,android\:minHeight:48dp,android\:paddingStart:24dp,android\:paddingEnd:24dp,;iOSGroupedBackground,169,V400030084,c0006012c,;Nandroid\:background:@color/white,android\:orientation:vertical,;TextAppearance.WardrobeApp.TitleSmall,170,V400962141,c009821f0,;DTextAppearance.Material3.TitleSmall,android\:fontFamily:sans-serif-medium,;Theme.WardrobeApp.BottomSheetDialog,169,V400cc284c,c00ce28fd,;DTheme.Design.Light.BottomSheetDialog,bottomSheetStyle:@style/AppModalStyle,;Theme.WardrobeApp.EditTextStyle,169,V400d42a70,c00dc2c68,;DWidget.AppCompat.EditText,android\:textColor:@color/labelPrimary,android\:textColorHint:@color/placeholderText,android\:textSize:16sp,android\:paddingTop:12dp,android\:paddingBottom:12dp,android\:paddingStart:16dp,android\:paddingEnd:16dp,;TextAppearance.App.Title3,169,V4006e14ae,c00701538,;DTextAppearance.App.Base,android\:textSize:20sp,;TextAppearance.App.Title2,169,V4006b141e,c006d14a8,;DTextAppearance.App.Base,android\:textSize:22sp,;TextAppearance.WardrobeApp.BodyLarge,170,V4009a21f6,c009c229c,;DTextAppearance.Material3.BodyLarge,android\:fontFamily:sans-serif,;TextAppearance.App.Subheadline,169,V4007b1731,c007e1805,;DTextAppearance.App.Base,android\:textSize:15sp,android\:textColor:@color/labelSecondary,;TextAppearance.App.Title1,169,V40068138e,c006a1418,;DTextAppearance.App.Base,android\:textSize:28sp,;AppModalStyle,169,V400cf2903,c00d129aa,;DWidget.Design.BottomSheet.Modal,android\:background:@drawable/dialog_rounded_background,;ButtonSecondary,169,V400981d2d,c00a31fc0,;DWidget.AppCompat.Button,android\:background:@drawable/button_background,android\:textColor:@color/systemBlue,android\:textSize:16sp,android\:fontFamily:sans-serif-medium,android\:paddingStart:24dp,android\:paddingEnd:24dp,android\:paddingTop:12dp,android\:paddingBottom:12dp,android\:minHeight:44dp,android\:textAllCaps:false,;Theme.WardrobeApp,169,V4005e11ad,46005e11ef,;DBase.Theme.WardrobeApp,;Widget.WardrobeApp.Chip,170,V400d02bd1,c00d42cec,;DWidget.Material3.Chip.Filter,chipCornerRadius:20dp,chipMinHeight:40dp,android\:textAppearance:@style/TextAppearance.WardrobeApp.LabelMedium,;iOSGroupedBackground.Single,169,V400100354,c00120406,;DiOSGroupedBackground,android\:background:@drawable/ios_style_group_background_single,;Separator,169,V4003c0bb4,c00400c93,;Nandroid\:layout_width:match_parent,android\:layout_height:0.5dp,android\:background:@color/separator,;Theme.WardrobeApp.Button.Destructive,169,V400de2cbe,5400de2d0e,;DButtonDestructive,;+xml:file_paths,172,F;data_extraction_rules,173,F;backup_rules,174,F;