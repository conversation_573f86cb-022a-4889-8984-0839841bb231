package com.wardrobe.app.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import androidx.annotation.StringRes;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * 本地化管理器
 * 提供多语言支持和本地化功能
 * 管理语言切换和本地化资源
 */
public class LocalizationManager {
    
    private static final String TAG = "LocalizationManager";
    private static volatile LocalizationManager instance;
    private static final Object LOCK = new Object();
    
    private static final String PREF_LANGUAGE = "selected_language";
    private static final String PREF_COUNTRY = "selected_country";
    
    /**
     * 支持的语言枚举
     */
    public enum SupportedLanguage {
        CHINESE_SIMPLIFIED("zh", "CN", "简体中文"),
        CHINESE_TRADITIONAL("zh", "TW", "繁體中文"),
        ENGLISH("en", "US", "English"),
        JAPANESE("ja", "JP", "日本語"),
        KOREAN("ko", "KR", "한국어");
        
        private final String language;
        private final String country;
        private final String displayName;
        
        SupportedLanguage(String language, String country, String displayName) {
            this.language = language;
            this.country = country;
            this.displayName = displayName;
        }
        
        public String getLanguage() { return language; }
        public String getCountry() { return country; }
        public String getDisplayName() { return displayName; }
        
        public Locale getLocale() {
            return new Locale(language, country);
        }
        
        public String getLanguageTag() {
            return language + "_" + country;
        }
        
        public static SupportedLanguage fromLocale(Locale locale) {
            for (SupportedLanguage lang : values()) {
                if (lang.language.equals(locale.getLanguage()) && 
                    lang.country.equals(locale.getCountry())) {
                    return lang;
                }
            }
            // 如果找不到完全匹配，尝试只匹配语言
            for (SupportedLanguage lang : values()) {
                if (lang.language.equals(locale.getLanguage())) {
                    return lang;
                }
            }
            return ENGLISH; // 默认返回英语
        }
        
        public static SupportedLanguage fromLanguageTag(String tag) {
            for (SupportedLanguage lang : values()) {
                if (lang.getLanguageTag().equals(tag)) {
                    return lang;
                }
            }
            return ENGLISH;
        }
    }
    
    private final SharedPreferences preferences;
    private SupportedLanguage currentLanguage;
    private final Map<String, String> fallbackStrings;
    
    private LocalizationManager(Context context) {
        this.preferences = context.getSharedPreferences("localization_prefs", Context.MODE_PRIVATE);
        this.fallbackStrings = new HashMap<>();
        
        // 初始化回退字符串
        initializeFallbackStrings();
        
        // 加载保存的语言设置
        loadSavedLanguage();
        
        Logger.d(TAG, "LocalizationManager 初始化，当前语言: " + currentLanguage.getDisplayName());
    }
    
    /**
     * 获取LocalizationManager实例
     * 
     * @param context 上下文
     * @return LocalizationManager实例
     */
    public static LocalizationManager getInstance(Context context) {
        if (instance == null) {
            synchronized (LOCK) {
                if (instance == null) {
                    instance = new LocalizationManager(context.getApplicationContext());
                }
            }
        }
        return instance;
    }
    
    /**
     * 初始化回退字符串
     */
    private void initializeFallbackStrings() {
        // 添加一些关键的回退字符串，以防资源文件缺失
        fallbackStrings.put("app_name", "衣橱管理");
        fallbackStrings.put("loading", "加载中...");
        fallbackStrings.put("error", "错误");
        fallbackStrings.put("success", "成功");
        fallbackStrings.put("cancel", "取消");
        fallbackStrings.put("confirm", "确认");
        fallbackStrings.put("retry", "重试");
        fallbackStrings.put("save", "保存");
        fallbackStrings.put("delete", "删除");
        fallbackStrings.put("add", "添加");
        fallbackStrings.put("edit", "编辑");
        fallbackStrings.put("search", "搜索");
        fallbackStrings.put("no_data", "暂无数据");
        fallbackStrings.put("network_error", "网络连接失败");
        fallbackStrings.put("permission_denied", "权限被拒绝");
    }
    
    /**
     * 加载保存的语言设置
     */
    private void loadSavedLanguage() {
        String savedLanguage = preferences.getString(PREF_LANGUAGE, null);
        String savedCountry = preferences.getString(PREF_COUNTRY, null);
        
        if (savedLanguage != null && savedCountry != null) {
            String languageTag = savedLanguage + "_" + savedCountry;
            currentLanguage = SupportedLanguage.fromLanguageTag(languageTag);
        } else {
            // 使用系统默认语言
            Locale systemLocale = Locale.getDefault();
            currentLanguage = SupportedLanguage.fromLocale(systemLocale);
        }
    }
    
    /**
     * 保存语言设置
     * 
     * @param language 语言
     */
    private void saveLanguage(SupportedLanguage language) {
        preferences.edit()
                .putString(PREF_LANGUAGE, language.getLanguage())
                .putString(PREF_COUNTRY, language.getCountry())
                .apply();
    }
    
    /**
     * 设置应用语言
     * 
     * @param context 上下文
     * @param language 语言
     */
    public void setLanguage(Context context, SupportedLanguage language) {
        if (language == currentLanguage) {
            return;
        }
        
        Logger.d(TAG, "切换语言: " + currentLanguage.getDisplayName() + " -> " + language.getDisplayName());
        
        currentLanguage = language;
        saveLanguage(language);
        
        // 更新应用配置
        updateConfiguration(context, language.getLocale());
    }
    
    /**
     * 更新配置
     * 
     * @param context 上下文
     * @param locale 区域设置
     */
    private void updateConfiguration(Context context, Locale locale) {
        Resources resources = context.getResources();
        Configuration configuration = resources.getConfiguration();
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            configuration.setLocale(locale);
        } else {
            configuration.locale = locale;
        }
        
        resources.updateConfiguration(configuration, resources.getDisplayMetrics());
    }
    
    /**
     * 获取当前语言
     * 
     * @return 当前语言
     */
    public SupportedLanguage getCurrentLanguage() {
        return currentLanguage;
    }
    
    /**
     * 获取当前区域设置
     * 
     * @return 当前区域设置
     */
    public Locale getCurrentLocale() {
        return currentLanguage.getLocale();
    }
    
    /**
     * 获取所有支持的语言
     * 
     * @return 支持的语言数组
     */
    public SupportedLanguage[] getSupportedLanguages() {
        return SupportedLanguage.values();
    }
    
    /**
     * 获取本地化字符串
     * 
     * @param context 上下文
     * @param resId 资源ID
     * @return 本地化字符串
     */
    public String getString(Context context, @StringRes int resId) {
        try {
            return context.getString(resId);
        } catch (Exception e) {
            Logger.w(TAG, "获取字符串资源失败: " + resId, e);
            return getFallbackString(context.getResources().getResourceEntryName(resId));
        }
    }
    
    /**
     * 获取本地化字符串（带参数）
     * 
     * @param context 上下文
     * @param resId 资源ID
     * @param formatArgs 格式化参数
     * @return 本地化字符串
     */
    public String getString(Context context, @StringRes int resId, Object... formatArgs) {
        try {
            return context.getString(resId, formatArgs);
        } catch (Exception e) {
            Logger.w(TAG, "获取格式化字符串资源失败: " + resId, e);
            String fallback = getFallbackString(context.getResources().getResourceEntryName(resId));
            try {
                return String.format(fallback, formatArgs);
            } catch (Exception formatException) {
                return fallback;
            }
        }
    }
    
    /**
     * 获取回退字符串
     * 
     * @param key 键
     * @return 回退字符串
     */
    private String getFallbackString(String key) {
        return fallbackStrings.getOrDefault(key, key);
    }
    
    /**
     * 检查是否为RTL语言
     * 
     * @return 是否为RTL语言
     */
    public boolean isRTL() {
        Locale locale = getCurrentLocale();
        return isRTL(locale);
    }
    
    /**
     * 检查指定区域设置是否为RTL
     * 
     * @param locale 区域设置
     * @return 是否为RTL
     */
    public static boolean isRTL(Locale locale) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            return Character.getDirectionality(locale.getDisplayName().charAt(0)) == Character.DIRECTIONALITY_RIGHT_TO_LEFT;
        }
        
        // 简单的RTL语言检测
        String language = locale.getLanguage();
        return "ar".equals(language) || "fa".equals(language) || "he".equals(language) || "ur".equals(language);
    }
    
    /**
     * 格式化数字
     * 
     * @param number 数字
     * @return 格式化后的数字字符串
     */
    public String formatNumber(Number number) {
        return java.text.NumberFormat.getInstance(getCurrentLocale()).format(number);
    }
    
    /**
     * 格式化日期
     * 
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    public String formatDate(java.util.Date date) {
        return java.text.DateFormat.getDateInstance(java.text.DateFormat.MEDIUM, getCurrentLocale()).format(date);
    }
    
    /**
     * 格式化时间
     * 
     * @param date 日期
     * @return 格式化后的时间字符串
     */
    public String formatTime(java.util.Date date) {
        return java.text.DateFormat.getTimeInstance(java.text.DateFormat.SHORT, getCurrentLocale()).format(date);
    }
    
    /**
     * 格式化日期时间
     * 
     * @param date 日期
     * @return 格式化后的日期时间字符串
     */
    public String formatDateTime(java.util.Date date) {
        return java.text.DateFormat.getDateTimeInstance(
                java.text.DateFormat.MEDIUM, 
                java.text.DateFormat.SHORT, 
                getCurrentLocale()).format(date);
    }
    
    /**
     * 获取语言显示名称
     * 
     * @param language 语言
     * @return 显示名称
     */
    public String getLanguageDisplayName(SupportedLanguage language) {
        return language.getDisplayName();
    }
    
    /**
     * 重置为系统默认语言
     * 
     * @param context 上下文
     */
    public void resetToSystemDefault(Context context) {
        Locale systemLocale = Locale.getDefault();
        SupportedLanguage systemLanguage = SupportedLanguage.fromLocale(systemLocale);
        setLanguage(context, systemLanguage);
    }
    
    /**
     * 清除保存的语言设置
     */
    public void clearSavedLanguage() {
        preferences.edit()
                .remove(PREF_LANGUAGE)
                .remove(PREF_COUNTRY)
                .apply();
    }
}
