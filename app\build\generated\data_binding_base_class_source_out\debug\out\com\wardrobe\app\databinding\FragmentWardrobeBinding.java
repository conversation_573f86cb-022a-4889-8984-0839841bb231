// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentWardrobeBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final ChipGroup chipGroupFilter;

  @NonNull
  public final EditText etSearch;

  @NonNull
  public final FloatingActionButton fabAddClothing;

  @NonNull
  public final RecyclerView groupRecyclerView;

  @NonNull
  public final ImageView ivClearSearch;

  @NonNull
  public final MultiSelectToolbarBinding multiSelectToolbar;

  @NonNull
  public final LinearLayout tvEmptyState;

  @NonNull
  public final LinearLayout tvLoadingState;

  private FragmentWardrobeBinding(@NonNull CoordinatorLayout rootView,
      @NonNull ChipGroup chipGroupFilter, @NonNull EditText etSearch,
      @NonNull FloatingActionButton fabAddClothing, @NonNull RecyclerView groupRecyclerView,
      @NonNull ImageView ivClearSearch, @NonNull MultiSelectToolbarBinding multiSelectToolbar,
      @NonNull LinearLayout tvEmptyState, @NonNull LinearLayout tvLoadingState) {
    this.rootView = rootView;
    this.chipGroupFilter = chipGroupFilter;
    this.etSearch = etSearch;
    this.fabAddClothing = fabAddClothing;
    this.groupRecyclerView = groupRecyclerView;
    this.ivClearSearch = ivClearSearch;
    this.multiSelectToolbar = multiSelectToolbar;
    this.tvEmptyState = tvEmptyState;
    this.tvLoadingState = tvLoadingState;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentWardrobeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentWardrobeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_wardrobe, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentWardrobeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.chip_group_filter;
      ChipGroup chipGroupFilter = ViewBindings.findChildViewById(rootView, id);
      if (chipGroupFilter == null) {
        break missingId;
      }

      id = R.id.et_search;
      EditText etSearch = ViewBindings.findChildViewById(rootView, id);
      if (etSearch == null) {
        break missingId;
      }

      id = R.id.fab_add_clothing;
      FloatingActionButton fabAddClothing = ViewBindings.findChildViewById(rootView, id);
      if (fabAddClothing == null) {
        break missingId;
      }

      id = R.id.group_recycler_view;
      RecyclerView groupRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (groupRecyclerView == null) {
        break missingId;
      }

      id = R.id.iv_clear_search;
      ImageView ivClearSearch = ViewBindings.findChildViewById(rootView, id);
      if (ivClearSearch == null) {
        break missingId;
      }

      id = R.id.multi_select_toolbar;
      View multiSelectToolbar = ViewBindings.findChildViewById(rootView, id);
      if (multiSelectToolbar == null) {
        break missingId;
      }
      MultiSelectToolbarBinding binding_multiSelectToolbar = MultiSelectToolbarBinding.bind(multiSelectToolbar);

      id = R.id.tv_empty_state;
      LinearLayout tvEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (tvEmptyState == null) {
        break missingId;
      }

      id = R.id.tv_loading_state;
      LinearLayout tvLoadingState = ViewBindings.findChildViewById(rootView, id);
      if (tvLoadingState == null) {
        break missingId;
      }

      return new FragmentWardrobeBinding((CoordinatorLayout) rootView, chipGroupFilter, etSearch,
          fabAddClothing, groupRecyclerView, ivClearSearch, binding_multiSelectToolbar,
          tvEmptyState, tvLoadingState);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
