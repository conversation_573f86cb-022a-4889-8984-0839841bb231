<androidx.core.widget.NestedScrollView
    android:layout_width="match_parent"
    android:layout_height="0dp"
    app:layout_constraintTop_toBottomOf="@id/toolbar"
    app:layout_constraintBottom_toTopOf="@id/bottom_bar_container">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Image Selection Area -->
        <FrameLayout
            android:id="@+id/select_image_button"
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:background="@drawable/dotted_border_background">

            <ImageView
                android:id="@+id/clothing_image_preview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:visibility="gone"
                android:contentDescription="@string/desc_clothing_image_preview" />

            <LinearLayout
                android:id="@+id/add_photo_placeholder"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:orientation="vertical"
                android:gravity="center">

                <ImageView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/ic_add"
                    app:tint="@color/systemBlue" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="@string/add_photo"
                    android:textColor="@color/systemBlue"
                    android:textSize="16sp" />
            </LinearLayout>

            <ImageView
                android:id="@+id/replace_image_icon"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_gravity="bottom|end"
                android:layout_margin="8dp"
                android:src="@drawable/ic_replace_photo"
                android:background="@drawable/replace_icon_background"
                android:padding="6dp"
                android:visibility="gone"
                android:elevation="4dp"
                android:contentDescription="@string/desc_replace_image" />

        </FrameLayout>

        <!-- Core Details Group -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="24dp"
            android:background="@drawable/ios_style_group_background_single"
            android:paddingVertical="8dp">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/clothing_name_edit_text"
                style="@style/Theme.WardrobeApp.EditTextStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:hint="@string/hint_clothing_name"
                android:paddingHorizontal="16dp"
                android:paddingVertical="12dp"
                android:inputType="text" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/separator_color"
                android:layout_marginHorizontal="16dp"/>

            <include
                android:id="@+id/category_selector_layout"
                layout="@layout/layout_category_selector" />

        </LinearLayout>

        <!-- Optional Details Group -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="24dp"
            android:background="@drawable/ios_style_group_background_single"
            android:paddingVertical="8dp">

             <!-- Color Selector Row -->
            <RelativeLayout
                android:id="@+id/color_selector_row"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="16dp"
                android:paddingVertical="12dp"
                android:background="?attr/selectableItemBackground">
                <TextView
                    android:id="@+id/color_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="颜色"
                    android:textSize="16sp"
                    android:textColor="@android:color/black"
                    android:layout_centerVertical="true"/>
                <TextView
                    android:id="@+id/color_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toStartOf="@+id/color_chevron"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="24dp"
                    android:text="请选择"
                    android:textSize="16sp"
                    android:textColor="@color/text_gray_light"/>
                <ImageView
                    android:id="@+id/color_chevron"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:src="@drawable/ic_chevron_right"/>
            </RelativeLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/separator_color"
                android:layout_marginHorizontal="16dp"/>

            <!-- Occasion Selector Row -->
            <RelativeLayout
                android:id="@+id/occasion_selector_row"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="16dp"
                android:paddingVertical="12dp"
                android:background="?attr/selectableItemBackground">
                <TextView
                    android:id="@+id/occasion_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="场合"
                    android:textSize="16sp"
                    android:textColor="@android:color/black"
                    android:layout_centerVertical="true"/>
                <TextView
                    android:id="@+id/occasion_value"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_toStartOf="@+id/occasion_chevron"
                    android:layout_alignParentEnd="true"
                    android:layout_marginEnd="24dp"
                    android:text="请选择"
                    android:textSize="16sp"
                    android:textColor="@color/text_gray_light"/>
                <ImageView
                    android:id="@+id/occasion_chevron"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:src="@drawable/ic_chevron_right"/>
            </RelativeLayout>

        </LinearLayout>

        <!-- Notes Group -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="24dp"
            android:background="@drawable/ios_style_group_background_single"
            android:padding="8dp">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/notes_edit_text"
                style="@style/Theme.WardrobeApp.EditTextStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="100dp"
                android:gravity="top"
                android:background="@android:color/transparent"
                android:hint="@string/hint_notes"
                android:padding="8dp"
                android:inputType="textMultiLine" />

        </LinearLayout>
        
         <!-- Story Group -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="24dp"
            android:background="@drawable/ios_style_group_background_single"
            android:padding="8dp">
            
            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/story_edit_text"
                style="@style/Theme.WardrobeApp.EditTextStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="120dp"
                android:gravity="top"
                android:background="@android:color/transparent"
                android:hint="@string/hint_story"
                android:padding="8dp"
                android:inputType="textMultiLine" />
        
        </LinearLayout>

    </LinearLayout>
</androidx.core.widget.NestedScrollView>

<LinearLayout
    android:id="@+id/bottom_bar_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    app:layout_constraintBottom_toBottomOf="parent">

    <Button
        android:id="@+id/save_clothing_button"
        style="@style/Theme.WardrobeApp.Button.Prominent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/save_clothing" />

    <Button
        android:id="@+id/btn_delete_item"
        style="@style/Theme.WardrobeApp.Button.Destructive"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/delete_item"
        android:visibility="gone"/>

</LinearLayout> 