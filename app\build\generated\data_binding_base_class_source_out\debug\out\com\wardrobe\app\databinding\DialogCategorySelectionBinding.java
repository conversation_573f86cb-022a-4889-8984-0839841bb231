// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogCategorySelectionBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final RecyclerView categoryRecyclerView;

  private DialogCategorySelectionBinding(@NonNull FrameLayout rootView,
      @NonNull RecyclerView categoryRecyclerView) {
    this.rootView = rootView;
    this.categoryRecyclerView = categoryRecyclerView;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogCategorySelectionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogCategorySelectionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_category_selection, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogCategorySelectionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.category_recycler_view;
      RecyclerView categoryRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (categoryRecyclerView == null) {
        break missingId;
      }

      return new DialogCategorySelectionBinding((FrameLayout) rootView, categoryRecyclerView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
