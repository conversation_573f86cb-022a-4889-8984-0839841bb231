package com.wardrobe.app.utils;

/**
 * 验证异常类
 * 用于表示输入验证失败的异常
 */
public class ValidationException extends Exception {
    
    private final String fieldName;
    private final String validationRule;
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     */
    public ValidationException(String message) {
        super(message);
        this.fieldName = null;
        this.validationRule = null;
    }
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     * @param cause 原因
     */
    public ValidationException(String message, Throwable cause) {
        super(message, cause);
        this.fieldName = null;
        this.validationRule = null;
    }
    
    /**
     * 构造函数
     * 
     * @param fieldName 字段名称
     * @param message 异常消息
     */
    public ValidationException(String fieldName, String message) {
        super(message);
        this.fieldName = fieldName;
        this.validationRule = null;
    }
    
    /**
     * 构造函数
     * 
     * @param fieldName 字段名称
     * @param validationRule 验证规则
     * @param message 异常消息
     */
    public ValidationException(String fieldName, String validationRule, String message) {
        super(message);
        this.fieldName = fieldName;
        this.validationRule = validationRule;
    }
    
    /**
     * 获取字段名称
     * 
     * @return 字段名称
     */
    public String getFieldName() {
        return fieldName;
    }
    
    /**
     * 获取验证规则
     * 
     * @return 验证规则
     */
    public String getValidationRule() {
        return validationRule;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        
        if (fieldName != null) {
            sb.append(" [字段: ").append(fieldName);
            if (validationRule != null) {
                sb.append(", 规则: ").append(validationRule);
            }
            sb.append("]");
        }
        
        sb.append(": ").append(getMessage());
        return sb.toString();
    }
}
