<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="12dp"
    app:cardElevation="0dp"
    app:strokeWidth="0.5dp"
    app:strokeColor="@color/separator"
    android:layout_margin="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/clothing_image"
            android:layout_width="match_parent"
            android:layout_height="150dp"
            android:scaleType="centerCrop"
            android:contentDescription="Clothing Item Image" />

        <TextView
            android:id="@+id/clothing_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="@style/TextAppearance.App.Footnote"
            android:textColor="@color/labelPrimary"
            android:paddingHorizontal="12dp"
            android:paddingTop="8dp"
            android:paddingBottom="4dp"
            android:text="Item Name"/>

        <TextView
            android:id="@+id/clothing_category"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="@style/TextAppearance.App.Caption2"
            android:textColor="@color/labelSecondary"
            android:paddingHorizontal="12dp"
            android:paddingBottom="8dp"
            android:text="Category"/>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
