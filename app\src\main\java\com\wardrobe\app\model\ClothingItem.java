package com.wardrobe.app.model;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

public class ClothingItem implements Parcelable {
    private String id;
    private String name; // 新增字段：衣物名称，与 AddClothingActivity 对应
    private String category; // 将 mainCategory 重命名为 category，与 CategorySelectorManager 对应
    private String subcategory; // 将 subCategory 重命名为 subcategory，与 CategorySelectorManager 对应
    private String color;
    private String occasion;
    private String imageUri; // 将 imagePath 重命名为 imageUri，与 AddClothingActivity 对应
    private String notes;
    private String story; // 已有字段
    private List<String> tags; // 已有字段
    private long timestamp; // 新增字段：时间戳

    // 新增：一个空的构造函数，用于在AddClothingActivity中简化新对象的创建
    public ClothingItem() {
        this.id = UUID.randomUUID().toString();
        this.timestamp = System.currentTimeMillis();
        this.tags = new ArrayList<>(); // 初始化列表以避免NullPointerException
    }

    // 主构造函数：用于创建新的ClothingItem (例如在 AddClothingActivity 中添加新衣物)
    // 参数顺序和类型与 AddClothingActivity 中的 new ClothingItem(...) 调用保持一致
    public ClothingItem(String name, String category, String subcategory, String color, String occasion,
                        String imageUri, String notes, String story, List<String> tags) {
        this.id = UUID.randomUUID().toString(); // 自动生成唯一ID
        this.name = name;
        this.category = category;
        this.subcategory = subcategory;
        this.color = color;
        this.occasion = occasion;
        this.imageUri = imageUri;
        this.notes = notes;
        this.story = story;
        this.tags = tags != null ? new ArrayList<>(tags) : new ArrayList<>(); // 确保tags列表不为null
        this.timestamp = System.currentTimeMillis(); // 在创建时设置时间戳
    }

    // 另一个构造函数：可能用于从持久化数据（如数据库）加载，或者用于 BatchAddClothingActivity
    // 这个构造函数接受一个 id，并且标签是逗号分隔的字符串
    public ClothingItem(String id, String name, String category, String subcategory, String color, String occasion,
                        String imageUri, String notes, String story, String tagsString) {
        this.id = id;
        this.name = name;
        this.category = category;
        this.subcategory = subcategory;
        this.color = color;
        this.occasion = occasion;
        this.imageUri = imageUri;
        this.notes = notes;
        this.story = story;
        this.tags = new ArrayList<>();
        if (tagsString != null && !tagsString.isEmpty()) {
            this.tags.addAll(Arrays.asList(tagsString.split(","))); // 假设标签是逗号分隔的字符串
        }
        this.timestamp = System.currentTimeMillis(); // 在创建时设置时间戳
    }

    // --- Getter方法 (获取信息) ---
    public String getId() { return id; }
    public String getName() { return name; } // 新增 getter
    public String getCategory() { return category; } // 修正 getter
    public String getSubcategory() { return subcategory; } // 修正 getter
    public String getColor() { return color; }
    public String getOccasion() { return occasion; }
    public String getImageUri() { return imageUri; } // 修正 getter
    public String getNotes() { return notes; }
    public String getStory() { return story; }
    public List<String> getTags() { return tags; }
    public long getTimestamp() { return timestamp; } // 新增 getter

    // --- Setter方法 (修改信息) ---
    public void setName(String name) { this.name = name; } // 新增 setter
    public void setCategory(String category) { this.category = category; } // 修正 setter
    public void setSubcategory(String subcategory) { this.subcategory = subcategory; } // 修正 setter
    public void setColor(String color) { this.color = color; }
    public void setOccasion(String occasion) { this.occasion = occasion; }
    public void setImageUri(String imageUri) { this.imageUri = imageUri; } // 修正 setter
    public void setNotes(String notes) { this.notes = notes; }
    public void setStory(String story) { this.story = story; }
    public void setTags(List<String> tags) { this.tags = tags != null ? new ArrayList<>(tags) : new ArrayList<>(); } // 确保 tags 不为 null
    public void setTimestamp(long timestamp) { this.timestamp = timestamp; } // 新增 setter
    public void setId(String id) {
        this.id = id;
    }

    // Parcelable implementation
    protected ClothingItem(Parcel in) {
        id = in.readString();
        name = in.readString(); // 读取 name
        category = in.readString(); // 读取 category
        subcategory = in.readString(); // 读取 subcategory
        color = in.readString();
        occasion = in.readString();
        imageUri = in.readString(); // 读取 imageUri
        notes = in.readString();
        story = in.readString();
        tags = in.createStringArrayList(); // 读取 List<String>
        timestamp = in.readLong(); // 读取 timestamp
    }

    public static final Creator<ClothingItem> CREATOR = new Creator<ClothingItem>() {
        @Override
        public ClothingItem createFromParcel(Parcel in) {
            return new ClothingItem(in);
        }

        @Override
        public ClothingItem[] newArray(int size) {
            return new ClothingItem[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(id);
        dest.writeString(name); // 写入 name
        dest.writeString(category); // 写入 category
        dest.writeString(subcategory); // 写入 subcategory
        dest.writeString(color);
        dest.writeString(occasion);
        dest.writeString(imageUri); // 写入 imageUri
        dest.writeString(notes);
        dest.writeString(story);
        dest.writeStringList(tags); // 写入 List<String>
        dest.writeLong(timestamp); // 写入 timestamp
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        ClothingItem that = (ClothingItem) obj;
        return id.equals(that.id) &&
               java.util.Objects.equals(name, that.name) &&
               java.util.Objects.equals(category, that.category) &&
               java.util.Objects.equals(subcategory, that.subcategory) &&
               java.util.Objects.equals(color, that.color) &&
               java.util.Objects.equals(occasion, that.occasion) &&
               java.util.Objects.equals(imageUri, that.imageUri) &&
               java.util.Objects.equals(notes, that.notes) &&
               java.util.Objects.equals(story, that.story) &&
               java.util.Objects.equals(tags, that.tags) &&
               timestamp == that.timestamp;
    }

    @Override
    public int hashCode() {
        return java.util.Objects.hash(id, name, category, subcategory, color, occasion, imageUri, notes, story, tags, timestamp);
    }

    @Override
    public String toString() {
        return "ClothingItem{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", category='" + category + '\'' +
                ", subcategory='" + subcategory + '\'' +
                ", color='" + color + '\'' +
                ", occasion='" + occasion + '\'' +
                ", imageUri='" + imageUri + '\'' +
                ", notes='" + notes + '\'' +
                ", story='" + story + '\'' +
                ", tags=" + tags +
                ", timestamp=" + timestamp +
                '}';
    }
}
