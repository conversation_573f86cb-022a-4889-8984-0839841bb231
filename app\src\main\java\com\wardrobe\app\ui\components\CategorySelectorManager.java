package com.wardrobe.app.ui.components;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.wardrobe.app.R;
import com.wardrobe.app.model.CategoryItem;
import com.wardrobe.app.ui.activities.CategorySelectActivity;
import com.wardrobe.app.ui.activities.ColorSelectActivity;
import com.wardrobe.app.ui.activities.OccasionSelectActivity;
import com.wardrobe.app.ui.adapters.CategoryListAdapter;
import com.wardrobe.app.ui.adapters.ColorListAdapter;
import com.wardrobe.app.ui.adapters.OccasionListAdapter;

import com.flask.colorpicker.ColorPickerView;
import com.flask.colorpicker.OnColorSelectedListener;
import com.flask.colorpicker.builder.ColorPickerClickListener;
import com.flask.colorpicker.builder.ColorPickerDialogBuilder;
import android.content.DialogInterface;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.wardrobe.app.utils.ErrorUtils;

/**
 * 四级联动分类选择管理器
 * 实现内嵌式级联选择：类别 -> 子类别 -> 颜色 -> 场合
 */
public class CategorySelectorManager implements View.OnClickListener {
    private static final String TAG = "CategorySelectorManager";
    
    private final Activity activity;
    private final View rootView;
    private final CategorySelectionListener listener;
    
    // UI component references
    private View categorySelector, subcategorySelector, colorSelector, occasionSelector;
    private TextView categoryLabel, subcategoryLabel, colorLabel, occasionLabel;
    private TextView categoryValue, subcategoryValue, colorValue, occasionValue;

    // 内嵌选择器容器
    private LinearLayout mainCategoryContainer, subcategoryContainer, colorContainer, occasionContainer;
    private RecyclerView mainCategoryRecyclerView, subcategoryRecyclerView, colorRecyclerView, occasionRecyclerView;

    private String selectedCategory, selectedSubcategory, selectedOccasion;
    private CategoryItem selectedColorItem;
    private int completedLevels = 0;
    
    public interface CategorySelectionListener {
        void onSelectionChanged(int completedLevels);
    }
    
    public CategorySelectorManager(Activity activity, View rootView, CategorySelectionListener listener) {
        this.activity = activity;
        this.rootView = rootView;
        this.listener = listener;
        initViews();
    }
    
    private void initViews() {
        // 修正：查找实际传入的include id（如batch_category_selector_view），或直接用rootView本身
        View categorySelectorLayout = rootView;
        if (categorySelectorLayout == null) {
            throw new IllegalStateException("无法找到四级联动选择器的布局。请确保activity_add_clothing.xml包含正确的include标签。");
        }

        // 从包含的布局中查找各个选择器
        categorySelector = categorySelectorLayout.findViewById(R.id.category_selector);
        subcategorySelector = categorySelectorLayout.findViewById(R.id.subcategory_selector);
        colorSelector = categorySelectorLayout.findViewById(R.id.color_selector);
        occasionSelector = categorySelectorLayout.findViewById(R.id.occasion_selector);

        // 检查是否找到了所有必要的视图
        if (categorySelector == null || subcategorySelector == null || 
            colorSelector == null || occasionSelector == null) {
            throw new IllegalStateException("无法找到四级联动选择器的视图组件。请确保layout_category_selector.xml包含正确的include标签。");
        }

        // Within each included layout, find the TextViews
        categoryLabel = categorySelector.findViewById(R.id.label_text);
        categoryValue = categorySelector.findViewById(R.id.value_text);
        
        subcategoryLabel = subcategorySelector.findViewById(R.id.label_text);
        subcategoryValue = subcategorySelector.findViewById(R.id.value_text);
        
        colorLabel = colorSelector.findViewById(R.id.label_text);
        colorValue = colorSelector.findViewById(R.id.value_text);
        
        occasionLabel = occasionSelector.findViewById(R.id.label_text);
        occasionValue = occasionSelector.findViewById(R.id.value_text);

        // 检查是否找到了所有TextView
        if (categoryLabel == null || categoryValue == null || 
            subcategoryLabel == null || subcategoryValue == null ||
            colorLabel == null || colorValue == null ||
            occasionLabel == null || occasionValue == null) {
            throw new IllegalStateException("无法找到四级联动选择器的文本组件。请确保list_item_selector_row.xml布局正确。");
        }

        // Set labels
        categoryLabel.setText("类型");
        subcategoryLabel.setText("子类型");
        colorLabel.setText("颜色");
        occasionLabel.setText("场合");

        // 创建内嵌选择器容器
        createEmbeddedSelectors(categorySelectorLayout);

        // Set initial values and hide all selectors
        updateUI();
        hideAllSelectors();

        // Set click listeners
        categorySelector.setOnClickListener(this);
        subcategorySelector.setOnClickListener(this);
        colorSelector.setOnClickListener(this);
        occasionSelector.setOnClickListener(this);
    }

    private void createEmbeddedSelectors(View parentLayout) {
        int margin = (int) (8 * activity.getResources().getDisplayMetrics().density);

        // 创建主类别选择器容器
        mainCategoryContainer = new LinearLayout(activity);
        LinearLayout.LayoutParams mainParams = new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        mainParams.topMargin = margin;
        mainCategoryContainer.setLayoutParams(mainParams);
        mainCategoryContainer.setOrientation(LinearLayout.VERTICAL);
        mainCategoryContainer.setVisibility(View.GONE);
        mainCategoryContainer.setBackgroundColor(Color.parseColor("#F5F5F5"));

        mainCategoryRecyclerView = new RecyclerView(activity);
        mainCategoryRecyclerView.setLayoutParams(new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        mainCategoryRecyclerView.setLayoutManager(new LinearLayoutManager(activity));
        mainCategoryRecyclerView.setNestedScrollingEnabled(false); // 解决滚动冲突
        mainCategoryContainer.addView(mainCategoryRecyclerView);

        // 创建子类型选择器容器
        subcategoryContainer = new LinearLayout(activity);
        LinearLayout.LayoutParams subParams = new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        subParams.topMargin = margin;
        subcategoryContainer.setLayoutParams(subParams);
        subcategoryContainer.setOrientation(LinearLayout.VERTICAL);
        subcategoryContainer.setVisibility(View.GONE);
        subcategoryContainer.setBackgroundColor(Color.parseColor("#F5F5F5"));

        subcategoryRecyclerView = new RecyclerView(activity);
        subcategoryRecyclerView.setLayoutParams(new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        subcategoryRecyclerView.setLayoutManager(new LinearLayoutManager(activity));
        subcategoryRecyclerView.setNestedScrollingEnabled(false); // 解决滚动冲突
        subcategoryContainer.addView(subcategoryRecyclerView);

        // 创建颜色选择器容器
        colorContainer = new LinearLayout(activity);
        LinearLayout.LayoutParams colorParams = new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        colorParams.topMargin = margin;
        colorContainer.setLayoutParams(colorParams);
        colorContainer.setOrientation(LinearLayout.VERTICAL);
        colorContainer.setVisibility(View.GONE);
        colorContainer.setBackgroundColor(Color.parseColor("#F0F0F0"));

        colorRecyclerView = new RecyclerView(activity);
        colorRecyclerView.setLayoutParams(new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        colorRecyclerView.setLayoutManager(new LinearLayoutManager(activity));
        colorRecyclerView.setNestedScrollingEnabled(false); // 解决滚动冲突
        colorContainer.addView(colorRecyclerView);

        // 创建场合选择器容器
        occasionContainer = new LinearLayout(activity);
        LinearLayout.LayoutParams occasionParams = new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        occasionParams.topMargin = margin;
        occasionContainer.setLayoutParams(occasionParams);
        occasionContainer.setOrientation(LinearLayout.VERTICAL);
        occasionContainer.setVisibility(View.GONE);
        occasionContainer.setBackgroundColor(Color.parseColor("#EBEBEB"));

        occasionRecyclerView = new RecyclerView(activity);
        occasionRecyclerView.setLayoutParams(new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        occasionRecyclerView.setLayoutManager(new LinearLayoutManager(activity));
        occasionRecyclerView.setNestedScrollingEnabled(false); // 解决滚动冲突
        occasionContainer.addView(occasionRecyclerView);

        // 将容器添加到布局中
        if (parentLayout instanceof LinearLayout) {
            LinearLayout parent = (LinearLayout) parentLayout;
            
            // 在类别选择器后插入主类别容器
            int categoryIndex = parent.indexOfChild(categorySelector);
            parent.addView(mainCategoryContainer, categoryIndex + 1);

            // 在子类型选择器后插入子类型容器
            int subcategoryIndex = parent.indexOfChild(subcategorySelector);
            parent.addView(subcategoryContainer, subcategoryIndex + 1);
            
            // 在颜色选择器后插入颜色容器
            int colorIndex = parent.indexOfChild(colorSelector);
            parent.addView(colorContainer, colorIndex + 1);
            
            // 在场合选择器后插入场合容器
            int occasionIndex = parent.indexOfChild(occasionSelector);
            parent.addView(occasionContainer, occasionIndex + 1);
        }
    }
    
    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.category_selector) {
            toggleMainCategorySelector();
        } else if (id == R.id.subcategory_selector) {
            if (selectedCategory == null) {
                ErrorUtils.showUserError(activity, activity.getString(R.string.category_placeholder));
                return;
            }
            toggleSubcategorySelector();
        } else if (id == R.id.color_selector) {
            if (selectedSubcategory == null) {
                ErrorUtils.showUserError(activity, activity.getString(R.string.category_secondary_placeholder));
                return;
            }
            toggleColorSelector();
        } else if (id == R.id.occasion_selector) {
            if (selectedColorItem == null) {
                ErrorUtils.showUserError(activity, activity.getString(R.string.color_placeholder));
                return;
            }
            toggleOccasionSelector();
        }
    }

    private void hideAllSelectors() {
        mainCategoryContainer.setVisibility(View.GONE);
        subcategoryContainer.setVisibility(View.GONE);
        colorContainer.setVisibility(View.GONE);
        occasionContainer.setVisibility(View.GONE);
    }

    private void toggleMainCategorySelector() {
        boolean isVisible = mainCategoryContainer.getVisibility() == View.VISIBLE;
        hideAllSelectors();
        if (!isVisible) {
            mainCategoryContainer.setVisibility(View.VISIBLE);
            loadMainCategories();
        }
    }

    private void toggleSubcategorySelector() {
        boolean isVisible = subcategoryContainer.getVisibility() == View.VISIBLE;
        hideAllSelectors();
        if (!isVisible) {
            subcategoryContainer.setVisibility(View.VISIBLE);
            loadSubcategories(selectedCategory);
        }
    }

    private void toggleColorSelector() {
        boolean isVisible = colorContainer.getVisibility() == View.VISIBLE;
        hideAllSelectors();
        if (!isVisible) {
            colorContainer.setVisibility(View.VISIBLE);
            loadColors();
        }
    }

    private void toggleOccasionSelector() {
        boolean isVisible = occasionContainer.getVisibility() == View.VISIBLE;
        hideAllSelectors();
        if (!isVisible) {
            occasionContainer.setVisibility(View.VISIBLE);
            loadOccasions();
        }
    }

    private void loadMainCategories() {
        List<CategoryItem> categories = CategoryProvider.getMainCategories(activity);
        CategoryListAdapter adapter = new CategoryListAdapter(activity, categories, category -> {
            boolean selectionChanged = !category.equals(selectedCategory);
            selectedCategory = category.getName();
            categoryValue.setText(selectedCategory);
            if (selectionChanged) {
                selectedSubcategory = null;
                selectedColorItem = null;
                selectedOccasion = null;
            }
            hideAllSelectors();
            updateUI();
            toggleSubcategorySelector();
        });
        mainCategoryRecyclerView.setAdapter(adapter);
    }

    private void loadSubcategories(String mainCategory) {
        List<CategoryItem> subcategories = CategoryProvider.getSubCategories(mainCategory);
        CategoryListAdapter adapter = new CategoryListAdapter(activity, subcategories, subcategory -> {
            boolean selectionChanged = !subcategory.equals(selectedSubcategory);
            selectedSubcategory = subcategory.getName();
            subcategoryValue.setText(selectedSubcategory);
            if (selectionChanged) {
                selectedColorItem = null;
                selectedOccasion = null;
            }
            hideAllSelectors();
            updateUI();
            toggleColorSelector();
        });
        subcategoryRecyclerView.setAdapter(adapter);
    }

    private void loadColors() {
        List<CategoryItem> colors = ColorProvider.getColors(activity);
        ColorListAdapter adapter = new ColorListAdapter(activity, colors, color -> {
            this.selectedColorItem = color;
            updateUI();
            hideAllSelectors();
            toggleOccasionSelector();
        });
        colorRecyclerView.setAdapter(adapter);
    }

    private void openColorPickerDialog() {
        ColorPickerDialogBuilder
                .with(activity, R.style.Theme_WardrobeApp_Dialog)
                .setTitle("选择一个颜色")
                .initialColor(0xFFFFFFFF)
                .wheelType(ColorPickerView.WHEEL_TYPE.FLOWER)
                .density(12)
                .setOnColorSelectedListener(selectedColor -> {
                    // No action on selection, only on confirmation
                })
                .setPositiveButton("确定", (dialog, selectedColor, allColors) -> {
                    String hexColor = String.format("#%06X", (0xFFFFFF & selectedColor));
                    this.selectedColorItem = new CategoryItem(hexColor, hexColor);
                    updateUI();
                    hideAllSelectors();
                    toggleOccasionSelector(); // 自动展开下一个
                })
                .setNegativeButton("取消", (dialog, which) -> {})
                .build()
                .show();
    }

    private void loadOccasions() {
        List<CategoryItem> occasions = OccasionProvider.getOccasions(activity);
        OccasionListAdapter adapter = new OccasionListAdapter(occasions, occasion -> {
            selectedOccasion = occasion.getName();
            occasionValue.setText(occasion.getName());
            hideAllSelectors();
            updateUI();
        });
        occasionRecyclerView.setAdapter(adapter);
    }

    private void updateUI() {
        categoryValue.setText(selectedCategory != null ? selectedCategory : "请选择");
        subcategoryValue.setText(selectedSubcategory != null ? selectedSubcategory : "请选择");
        
        if (selectedColorItem != null) {
            colorValue.setText(selectedColorItem.getName());
        } else {
            colorValue.setText("请选择");
        }
        
        occasionValue.setText(selectedOccasion != null ? selectedOccasion : "请选择");

        // 控制显示逻辑
        subcategorySelector.setVisibility(View.VISIBLE);
        colorSelector.setVisibility(selectedSubcategory != null ? View.VISIBLE : View.GONE);
        occasionSelector.setVisibility(selectedColorItem != null ? View.VISIBLE : View.GONE);

        // 控制可点击状态
        subcategorySelector.setEnabled(selectedCategory != null);
        colorSelector.setEnabled(selectedSubcategory != null);
        occasionSelector.setEnabled(selectedColorItem != null);

        // 根据状态设置不同的透明度
        subcategorySelector.setAlpha(selectedCategory != null ? 1.0f : 0.5f);
        colorSelector.setAlpha(selectedSubcategory != null ? 1.0f : 0.5f);
        occasionSelector.setAlpha(selectedColorItem != null ? 1.0f : 0.5f);

        updateCompletedLevels();
    }

    private void updateCompletedLevels() {
        completedLevels = 0;
        if (selectedCategory != null) completedLevels++;
        if (selectedSubcategory != null) completedLevels++;
        if (selectedColorItem != null) completedLevels++;
        if (selectedOccasion != null) completedLevels++;
        if (listener != null) {
            listener.onSelectionChanged(completedLevels);
        }
    }
    
    // Getter方法
    public String getSelectedCategory() { return selectedCategory; }
    public String getSelectedSubcategory() { return selectedSubcategory; }
    public String getSelectedColor() {
        return selectedColorItem != null ? selectedColorItem.getName() : null;
    }
    public String getSelectedOccasion() { return selectedOccasion; }
    
    // 设置初始选择
    public void setInitialSelection(String category, String subcategory, String colorName, String occasion) {
        this.selectedCategory = category;
        this.selectedSubcategory = subcategory;
        if (colorName != null) {
            this.selectedColorItem = new CategoryItem(colorName, colorName);
        }
        this.selectedOccasion = occasion;
        updateUI();
    }
    
    public int getCompletedLevels() {
        return completedLevels;
    }
}
