<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Package com.wardrobe.app</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Package com.wardrobe.app</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; com.wardrobe.app</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">10</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">3</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.643s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">70%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Classes</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<ul class="linkList">
<li>
<a href="../classes/com.wardrobe.app.ClothingManagerTest.html">ClothingManagerTest</a>.
<a href="../classes/com.wardrobe.app.ClothingManagerTest.html#testAddClothingItem">testAddClothingItem</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.ClothingManagerTest.html">ClothingManagerTest</a>.
<a href="../classes/com.wardrobe.app.ClothingManagerTest.html#testGetClothingList_EmptyList">testGetClothingList_EmptyList</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.PerformanceTest.html">PerformanceTest</a>.
<a href="../classes/com.wardrobe.app.PerformanceTest.html#initializationError">initializationError</a>
</li>
</ul>
</div>
<div id="tab1" class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tr>
<td class="success">
<a href="../classes/com.wardrobe.app.ClothingItemTest.html">ClothingItemTest</a>
</td>
<td>7</td>
<td>0</td>
<td>0</td>
<td>0.012s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="failures">
<a href="../classes/com.wardrobe.app.ClothingManagerTest.html">ClothingManagerTest</a>
</td>
<td>2</td>
<td>2</td>
<td>0</td>
<td>0.630s</td>
<td class="failures">0%</td>
</tr>
<tr>
<td class="failures">
<a href="../classes/com.wardrobe.app.PerformanceTest.html">PerformanceTest</a>
</td>
<td>1</td>
<td>1</td>
<td>0</td>
<td>0.001s</td>
<td class="failures">0%</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.11.1</a> at 2025年6月25日 20:44:37</p>
</div>
</div>
</body>
</html>
