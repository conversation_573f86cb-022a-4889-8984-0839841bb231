<lint-module
    format="1"
    dir="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app"
    name=":app"
    type="APP"
    maven="WardrobeApp:app:unspecified"
    agpVersion="8.10.1"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-35\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\35.0.0\core-lambda-stubs.jar"
    javaSourceLevel="17"
    compileTarget="android-35">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="debug"/>
</lint-module>
