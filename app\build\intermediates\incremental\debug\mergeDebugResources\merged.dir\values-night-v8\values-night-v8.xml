<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="background_primary">#1C1B1F</color>
    <color name="background_secondary">#1C1B1F</color>
    <color name="background_tertiary">#49454F</color>
    <color name="borderColor">#938F99</color>
    <color name="buttonBackground">#D0BCFF</color>
    <color name="buttonTextColor">#381E72</color>
    <color name="cardBackground">#1C1B1F</color>
    <color name="colorBackground">#1C1B1F</color>
    <color name="colorError">#FFB4AB</color>
    <color name="colorOnBackground">#E6E1E5</color>
    <color name="colorOnError">#690005</color>
    <color name="colorOnPrimary">#381E72</color>
    <color name="colorOnSecondary">#332D41</color>
    <color name="colorOnSurface">#E6E1E5</color>
    <color name="colorPrimary">#D0BCFF</color>
    <color name="colorPrimaryVariant">#4F378B</color>
    <color name="colorSecondary">#CCC2DC</color>
    <color name="colorSecondaryVariant">#4A4458</color>
    <color name="colorSurface">#1C1B1F</color>
    <color name="containerBackground">#1C1B1F</color>
    <color name="dividerColor">#49454F</color>
    <color name="navigationBarColor">#1C1B1F</color>
    <color name="rippleColor">#1FD0BCFF</color>
    <color name="selectedItemBackground">#4F378B</color>
    <color name="statusBarColor">#1C1B1F</color>
    <color name="textColorHint">#938F99</color>
    <color name="textColorPrimary">#E6E1E5</color>
    <color name="textColorSecondary">#CAC4D0</color>
    <color name="text_inverse">#1C1B1F</color>
    <color name="text_primary">#E6E1E5</color>
    <color name="text_secondary">#CAC4D0</color>
    <color name="text_tertiary">#938F99</color>
    <style name="AppTheme" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryVariant">@color/colorPrimaryVariant</item>
        <item name="colorOnPrimary">@color/colorOnPrimary</item>
        
        
        <item name="colorSecondary">@color/colorSecondary</item>
        <item name="colorSecondaryVariant">@color/colorSecondaryVariant</item>
        <item name="colorOnSecondary">@color/colorOnSecondary</item>
        
        
        <item name="android:colorBackground">@color/colorBackground</item>
        <item name="colorOnBackground">@color/colorOnBackground</item>
        
        
        <item name="colorSurface">@color/colorSurface</item>
        <item name="colorOnSurface">@color/colorOnSurface</item>
        
        
        <item name="colorError">@color/colorError</item>
        <item name="colorOnError">@color/colorOnError</item>
        
        
        <item name="android:statusBarColor">@color/statusBarColor</item>
        <item name="android:windowLightStatusBar">false</item>
        
        
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:windowLightNavigationBar">false</item>
        
        
        <item name="android:textColorPrimary">@color/textColorPrimary</item>
        <item name="android:textColorSecondary">@color/textColorSecondary</item>
        <item name="android:textColorHint">@color/textColorHint</item>
        
        
        <item name="android:forceDarkAllowed">true</item>
    </style>
    <style name="ButtonStyle" parent="Widget.Material3.Button">
        <item name="android:background">@color/buttonBackground</item>
        <item name="android:textColor">@color/buttonTextColor</item>
        <item name="backgroundTint">@color/buttonBackground</item>
    </style>
    <style name="CardStyle" parent="Widget.Material3.CardView.Elevated">
        <item name="cardBackgroundColor">@color/cardBackground</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style>
    <style name="Theme.AppCompat.DayNight" parent="Theme.AppCompat"/>
    <style name="Theme.AppCompat.DayNight.DarkActionBar" parent="Theme.AppCompat"/>
    <style name="Theme.AppCompat.DayNight.Dialog" parent="Theme.AppCompat.Dialog"/>
    <style name="Theme.AppCompat.DayNight.Dialog.Alert" parent="Theme.AppCompat.Dialog.Alert"/>
    <style name="Theme.AppCompat.DayNight.Dialog.MinWidth" parent="Theme.AppCompat.Dialog.MinWidth"/>
    <style name="Theme.AppCompat.DayNight.DialogWhenLarge" parent="Theme.AppCompat.DialogWhenLarge"/>
    <style name="Theme.AppCompat.DayNight.NoActionBar" parent="Theme.AppCompat.NoActionBar"/>
    <style name="Theme.Material3.DayNight" parent="Theme.Material3.Dark"/>
    <style name="Theme.Material3.DayNight.BottomSheetDialog" parent="Theme.Material3.Dark.BottomSheetDialog"/>
    <style name="Theme.Material3.DayNight.Dialog" parent="Theme.Material3.Dark.Dialog"/>
    <style name="Theme.Material3.DayNight.Dialog.Alert" parent="Theme.Material3.Dark.Dialog.Alert"/>
    <style name="Theme.Material3.DayNight.Dialog.MinWidth" parent="Theme.Material3.Dark.Dialog.MinWidth"/>
    <style name="Theme.Material3.DayNight.DialogWhenLarge" parent="Theme.Material3.Dark.DialogWhenLarge"/>
    <style name="Theme.Material3.DayNight.NoActionBar" parent="Theme.Material3.Dark.NoActionBar"/>
    <style name="Theme.Material3.DayNight.SideSheetDialog" parent="Theme.Material3.Dark.SideSheetDialog"/>
    <style name="Theme.Material3.DynamicColors.DayNight" parent="Theme.Material3.DynamicColors.Dark"/>
    <style name="Theme.Material3.DynamicColors.DayNight.NoActionBar" parent="Theme.Material3.DynamicColors.Dark.NoActionBar"/>
    <style name="Theme.MaterialComponents.DayNight" parent="Theme.MaterialComponents"/>
    <style name="Theme.MaterialComponents.DayNight.BottomSheetDialog" parent="Theme.MaterialComponents.BottomSheetDialog"/>
    <style name="Theme.MaterialComponents.DayNight.Bridge" parent="Theme.MaterialComponents.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.DarkActionBar" parent="Theme.MaterialComponents"/>
    <style name="Theme.MaterialComponents.DayNight.DarkActionBar.Bridge" parent="Theme.MaterialComponents.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog" parent="Theme.MaterialComponents.Dialog"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.Alert" parent="Theme.MaterialComponents.Dialog.Alert"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge" parent="Theme.MaterialComponents.Dialog.Alert.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.Bridge" parent="Theme.MaterialComponents.Dialog.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.FixedSize" parent="Theme.MaterialComponents.Dialog.FixedSize"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge" parent="Theme.MaterialComponents.Dialog.FixedSize.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.MinWidth" parent="Theme.MaterialComponents.Dialog.MinWidth"/>
    <style name="Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge" parent="Theme.MaterialComponents.Dialog.MinWidth.Bridge"/>
    <style name="Theme.MaterialComponents.DayNight.DialogWhenLarge" parent="Theme.MaterialComponents.DialogWhenLarge"/>
    <style name="Theme.MaterialComponents.DayNight.NoActionBar" parent="Theme.MaterialComponents.NoActionBar"/>
    <style name="Theme.MaterialComponents.DayNight.NoActionBar.Bridge" parent="Theme.MaterialComponents.NoActionBar.Bridge"/>
    <style name="ThemeOverlay.AppCompat.DayNight" parent="ThemeOverlay.AppCompat.Dark"/>
    <style name="ThemeOverlay.Material3.DynamicColors.DayNight" parent="ThemeOverlay.Material3.DynamicColors.Dark"/>
    <style name="Widget.MaterialComponents.ActionBar.PrimarySurface" parent="Widget.MaterialComponents.ActionBar.Surface"/>
    <style name="Widget.MaterialComponents.AppBarLayout.PrimarySurface" parent="Widget.MaterialComponents.AppBarLayout.Surface"/>
    <style name="Widget.MaterialComponents.BottomAppBar.PrimarySurface" parent="Widget.MaterialComponents.BottomAppBar"/>
    <style name="Widget.MaterialComponents.BottomNavigationView.PrimarySurface" parent="Widget.MaterialComponents.BottomNavigationView"/>
    <style name="Widget.MaterialComponents.NavigationRailView.PrimarySurface" parent="Widget.MaterialComponents.NavigationRailView"/>
    <style name="Widget.MaterialComponents.TabLayout.PrimarySurface" parent="Widget.MaterialComponents.TabLayout"/>
    <style name="Widget.MaterialComponents.Toolbar.PrimarySurface" parent="Widget.MaterialComponents.Toolbar.Surface"/>
</resources>