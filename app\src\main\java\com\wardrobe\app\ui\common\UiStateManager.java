package com.wardrobe.app.ui.common;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import com.wardrobe.app.utils.Logger;

/**
 * UI状态管理器
 * 统一管理Loading、Empty、Error、Success等UI状态
 * 提供状态切换和状态监听功能
 */
public class UiStateManager {
    
    private static final String TAG = "UiStateManager";
    
    /**
     * UI状态枚举
     */
    public enum UiState {
        IDLE,           // 空闲状态
        LOADING,        // 加载中
        SUCCESS,        // 成功
        EMPTY,          // 空数据
        ERROR,          // 错误
        NETWORK_ERROR,  // 网络错误
        NO_PERMISSION   // 无权限
    }
    
    /**
     * UI状态数据类
     */
    public static class UiStateData {
        private final UiState state;
        private final String message;
        private final Throwable error;
        private final Object data;
        
        public UiStateData(UiState state, String message, Throwable error, Object data) {
            this.state = state;
            this.message = message;
            this.error = error;
            this.data = data;
        }
        
        public UiState getState() {
            return state;
        }
        
        public String getMessage() {
            return message;
        }
        
        public Throwable getError() {
            return error;
        }
        
        public Object getData() {
            return data;
        }
        
        public boolean isLoading() {
            return state == UiState.LOADING;
        }
        
        public boolean isSuccess() {
            return state == UiState.SUCCESS;
        }
        
        public boolean isError() {
            return state == UiState.ERROR || state == UiState.NETWORK_ERROR;
        }
        
        public boolean isEmpty() {
            return state == UiState.EMPTY;
        }
        
        public boolean isIdle() {
            return state == UiState.IDLE;
        }
        
        @Override
        public String toString() {
            return String.format("UiStateData{state=%s, message='%s', hasError=%s, hasData=%s}", 
                    state, message, error != null, data != null);
        }
    }
    
    private final MutableLiveData<UiStateData> uiState = new MutableLiveData<>();
    private final String componentName;
    
    public UiStateManager(String componentName) {
        this.componentName = componentName;
        uiState.setValue(new UiStateData(UiState.IDLE, null, null, null));
        Logger.d(TAG, componentName + " UiStateManager 初始化");
    }
    
    /**
     * 获取UI状态LiveData
     * 
     * @return UI状态LiveData
     */
    public LiveData<UiStateData> getUiState() {
        return uiState;
    }
    
    /**
     * 获取当前UI状态
     * 
     * @return 当前UI状态数据
     */
    public UiStateData getCurrentState() {
        return uiState.getValue();
    }
    
    /**
     * 设置空闲状态
     */
    public void setIdle() {
        setState(UiState.IDLE, null, null, null);
    }
    
    /**
     * 设置加载状态
     * 
     * @param message 加载消息
     */
    public void setLoading(String message) {
        setState(UiState.LOADING, message, null, null);
    }
    
    /**
     * 设置成功状态
     * 
     * @param data 成功数据
     */
    public void setSuccess(Object data) {
        setState(UiState.SUCCESS, null, null, data);
    }
    
    /**
     * 设置成功状态（带消息）
     * 
     * @param message 成功消息
     * @param data 成功数据
     */
    public void setSuccess(String message, Object data) {
        setState(UiState.SUCCESS, message, null, data);
    }
    
    /**
     * 设置空数据状态
     * 
     * @param message 空数据消息
     */
    public void setEmpty(String message) {
        setState(UiState.EMPTY, message, null, null);
    }
    
    /**
     * 设置错误状态
     * 
     * @param message 错误消息
     * @param error 错误对象
     */
    public void setError(String message, Throwable error) {
        setState(UiState.ERROR, message, error, null);
    }
    
    /**
     * 设置网络错误状态
     * 
     * @param message 错误消息
     * @param error 错误对象
     */
    public void setNetworkError(String message, Throwable error) {
        setState(UiState.NETWORK_ERROR, message, error, null);
    }
    
    /**
     * 设置无权限状态
     * 
     * @param message 权限消息
     */
    public void setNoPermission(String message) {
        setState(UiState.NO_PERMISSION, message, null, null);
    }
    
    /**
     * 设置状态
     * 
     * @param state 状态
     * @param message 消息
     * @param error 错误
     * @param data 数据
     */
    private void setState(UiState state, String message, Throwable error, Object data) {
        UiStateData newState = new UiStateData(state, message, error, data);
        uiState.setValue(newState);
        Logger.d(TAG, componentName + " 状态变更: " + newState);
    }
    
    /**
     * 状态监听器接口
     */
    public interface UiStateListener {
        void onStateChanged(UiStateData state);
        
        default void onLoading(String message) {}
        default void onSuccess(Object data) {}
        default void onEmpty(String message) {}
        default void onError(String message, Throwable error) {}
        default void onNetworkError(String message, Throwable error) {}
        default void onNoPermission(String message) {}
    }
    
    /**
     * 添加状态监听器
     * 
     * @param listener 监听器
     */
    public void addStateListener(UiStateListener listener) {
        uiState.observeForever(state -> {
            listener.onStateChanged(state);
            
            switch (state.getState()) {
                case LOADING:
                    listener.onLoading(state.getMessage());
                    break;
                case SUCCESS:
                    listener.onSuccess(state.getData());
                    break;
                case EMPTY:
                    listener.onEmpty(state.getMessage());
                    break;
                case ERROR:
                    listener.onError(state.getMessage(), state.getError());
                    break;
                case NETWORK_ERROR:
                    listener.onNetworkError(state.getMessage(), state.getError());
                    break;
                case NO_PERMISSION:
                    listener.onNoPermission(state.getMessage());
                    break;
            }
        });
    }
    
    /**
     * 重试机制
     */
    public interface RetryAction {
        void retry();
    }
    
    private RetryAction retryAction;
    
    /**
     * 设置重试动作
     * 
     * @param retryAction 重试动作
     */
    public void setRetryAction(RetryAction retryAction) {
        this.retryAction = retryAction;
    }
    
    /**
     * 执行重试
     */
    public void retry() {
        if (retryAction != null) {
            Logger.d(TAG, componentName + " 执行重试");
            retryAction.retry();
        } else {
            Logger.w(TAG, componentName + " 没有设置重试动作");
        }
    }
    
    /**
     * 是否可以重试
     * 
     * @return 是否可以重试
     */
    public boolean canRetry() {
        UiStateData currentState = getCurrentState();
        return retryAction != null && (currentState.isError() || currentState.isEmpty());
    }
    
    /**
     * 获取状态描述
     * 
     * @return 状态描述
     */
    public String getStateDescription() {
        UiStateData currentState = getCurrentState();
        if (currentState == null) {
            return "未知状态";
        }
        
        switch (currentState.getState()) {
            case IDLE:
                return "空闲";
            case LOADING:
                return "加载中" + (currentState.getMessage() != null ? ": " + currentState.getMessage() : "");
            case SUCCESS:
                return "成功" + (currentState.getMessage() != null ? ": " + currentState.getMessage() : "");
            case EMPTY:
                return "暂无数据" + (currentState.getMessage() != null ? ": " + currentState.getMessage() : "");
            case ERROR:
                return "错误" + (currentState.getMessage() != null ? ": " + currentState.getMessage() : "");
            case NETWORK_ERROR:
                return "网络错误" + (currentState.getMessage() != null ? ": " + currentState.getMessage() : "");
            case NO_PERMISSION:
                return "无权限" + (currentState.getMessage() != null ? ": " + currentState.getMessage() : "");
            default:
                return "未知状态";
        }
    }
    
    /**
     * 重置状态
     */
    public void reset() {
        setIdle();
        retryAction = null;
        Logger.d(TAG, componentName + " 状态已重置");
    }
}
