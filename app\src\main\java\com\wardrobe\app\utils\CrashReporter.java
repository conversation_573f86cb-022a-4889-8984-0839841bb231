package com.wardrobe.app.utils;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Environment;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * 崩溃报告系统
 * 收集和保存应用崩溃信息，便于问题排查
 */
public class CrashReporter {
    
    private static final String TAG = "CrashReporter";
    private static final String CRASH_DIR = "crashes";
    private static final String CRASH_FILE_PREFIX = "crash_";
    private static final String CRASH_FILE_SUFFIX = ".txt";
    
    private static volatile CrashReporter instance;
    private static final Object LOCK = new Object();
    
    private final Context context;
    private final SimpleDateFormat dateFormat;
    
    private CrashReporter(Context context) {
        this.context = context.getApplicationContext();
        this.dateFormat = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault());
        
        // 创建崩溃日志目录
        createCrashDirectory();
        
        Logger.d(TAG, "CrashReporter 初始化完成");
    }
    
    /**
     * 获取单例实例
     */
    public static CrashReporter getInstance(Context context) {
        if (instance == null) {
            synchronized (LOCK) {
                if (instance == null) {
                    instance = new CrashReporter(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * 记录崩溃信息
     */
    public void recordCrash(Throwable throwable) {
        recordCrash(throwable, null);
    }
    
    /**
     * 记录崩溃信息（带额外信息）
     */
    public void recordCrash(Throwable throwable, String additionalInfo) {
        try {
            String crashInfo = generateCrashReport(throwable, additionalInfo);
            saveCrashToFile(crashInfo);
            
            Logger.e(TAG, "崩溃信息已记录: " + throwable.getMessage(), throwable);
            
        } catch (Exception e) {
            Logger.e(TAG, "记录崩溃信息失败", e);
        }
    }
    
    /**
     * 生成崩溃报告
     */
    private String generateCrashReport(Throwable throwable, String additionalInfo) {
        StringBuilder report = new StringBuilder();
        
        // 基本信息
        report.append("=== 崩溃报告 ===\n");
        report.append("时间: ").append(new Date().toString()).append("\n");
        report.append("应用版本: ").append(getAppVersion()).append("\n");
        report.append("设备信息: ").append(getDeviceInfo()).append("\n");
        report.append("系统版本: ").append(getSystemInfo()).append("\n");
        report.append("\n");
        
        // 异常信息
        report.append("=== 异常信息 ===\n");
        report.append("异常类型: ").append(throwable.getClass().getName()).append("\n");
        report.append("异常消息: ").append(throwable.getMessage()).append("\n");
        report.append("线程: ").append(Thread.currentThread().getName()).append("\n");
        report.append("\n");
        
        // 堆栈跟踪
        report.append("=== 堆栈跟踪 ===\n");
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        throwable.printStackTrace(pw);
        report.append(sw.toString());
        report.append("\n");
        
        // 额外信息
        if (additionalInfo != null && !additionalInfo.isEmpty()) {
            report.append("=== 额外信息 ===\n");
            report.append(additionalInfo).append("\n");
        }
        
        // 内存信息
        report.append("=== 内存信息 ===\n");
        report.append(getMemoryInfo()).append("\n");
        
        return report.toString();
    }
    
    /**
     * 保存崩溃信息到文件
     */
    private void saveCrashToFile(String crashInfo) {
        try {
            File crashDir = getCrashDirectory();
            if (crashDir == null) {
                Logger.e(TAG, "无法创建崩溃日志目录");
                return;
            }
            
            String fileName = CRASH_FILE_PREFIX + dateFormat.format(new Date()) + CRASH_FILE_SUFFIX;
            File crashFile = new File(crashDir, fileName);
            
            FileWriter writer = new FileWriter(crashFile);
            writer.write(crashInfo);
            writer.close();
            
            Logger.d(TAG, "崩溃信息已保存到: " + crashFile.getAbsolutePath());
            
            // 清理旧的崩溃文件
            cleanupOldCrashFiles(crashDir);
            
        } catch (IOException e) {
            Logger.e(TAG, "保存崩溃信息失败", e);
        }
    }
    
    /**
     * 创建崩溃日志目录
     */
    private void createCrashDirectory() {
        File crashDir = getCrashDirectory();
        if (crashDir != null && !crashDir.exists()) {
            boolean created = crashDir.mkdirs();
            Logger.d(TAG, "崩溃日志目录创建" + (created ? "成功" : "失败"));
        }
    }
    
    /**
     * 获取崩溃日志目录
     */
    private File getCrashDirectory() {
        try {
            // 优先使用外部存储
            if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
                File externalDir = context.getExternalFilesDir(CRASH_DIR);
                if (externalDir != null) {
                    return externalDir;
                }
            }
            
            // 使用内部存储
            return new File(context.getFilesDir(), CRASH_DIR);
            
        } catch (Exception e) {
            Logger.e(TAG, "获取崩溃日志目录失败", e);
            return null;
        }
    }
    
    /**
     * 清理旧的崩溃文件（保留最近10个）
     */
    private void cleanupOldCrashFiles(File crashDir) {
        try {
            File[] crashFiles = crashDir.listFiles((dir, name) -> 
                name.startsWith(CRASH_FILE_PREFIX) && name.endsWith(CRASH_FILE_SUFFIX));
            
            if (crashFiles != null && crashFiles.length > 10) {
                // 按修改时间排序
                java.util.Arrays.sort(crashFiles, (f1, f2) -> 
                    Long.compare(f1.lastModified(), f2.lastModified()));
                
                // 删除最旧的文件
                for (int i = 0; i < crashFiles.length - 10; i++) {
                    boolean deleted = crashFiles[i].delete();
                    Logger.d(TAG, "删除旧崩溃文件: " + crashFiles[i].getName() + 
                            (deleted ? " 成功" : " 失败"));
                }
            }
        } catch (Exception e) {
            Logger.e(TAG, "清理旧崩溃文件失败", e);
        }
    }
    
    /**
     * 获取应用版本信息
     */
    private String getAppVersion() {
        try {
            PackageManager pm = context.getPackageManager();
            PackageInfo pi = pm.getPackageInfo(context.getPackageName(), 0);
            return pi.versionName + " (" + pi.versionCode + ")";
        } catch (PackageManager.NameNotFoundException e) {
            return "Unknown";
        }
    }
    
    /**
     * 获取设备信息
     */
    private String getDeviceInfo() {
        return Build.MANUFACTURER + " " + Build.MODEL + " (" + Build.DEVICE + ")";
    }
    
    /**
     * 获取系统信息
     */
    private String getSystemInfo() {
        return "Android " + Build.VERSION.RELEASE + " (API " + Build.VERSION.SDK_INT + ")";
    }
    
    /**
     * 获取内存信息
     */
    private String getMemoryInfo() {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory() / 1024 / 1024;
        long totalMemory = runtime.totalMemory() / 1024 / 1024;
        long freeMemory = runtime.freeMemory() / 1024 / 1024;
        long usedMemory = totalMemory - freeMemory;
        
        return String.format("最大内存: %dMB, 已分配: %dMB, 已使用: %dMB, 可用: %dMB",
                maxMemory, totalMemory, usedMemory, freeMemory);
    }
    
    /**
     * 获取所有崩溃文件
     */
    public File[] getAllCrashFiles() {
        File crashDir = getCrashDirectory();
        if (crashDir != null && crashDir.exists()) {
            return crashDir.listFiles((dir, name) -> 
                name.startsWith(CRASH_FILE_PREFIX) && name.endsWith(CRASH_FILE_SUFFIX));
        }
        return new File[0];
    }
    
    /**
     * 删除所有崩溃文件
     */
    public void clearAllCrashFiles() {
        File[] crashFiles = getAllCrashFiles();
        for (File file : crashFiles) {
            boolean deleted = file.delete();
            Logger.d(TAG, "删除崩溃文件: " + file.getName() + (deleted ? " 成功" : " 失败"));
        }
    }
    
    /**
     * 获取崩溃文件数量
     */
    public int getCrashFileCount() {
        return getAllCrashFiles().length;
    }
}
