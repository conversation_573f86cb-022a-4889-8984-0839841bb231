package com.wardrobe.app.ui.common;

import android.content.Context;
import android.view.View;
import com.wardrobe.app.utils.Logger;
import com.wardrobe.app.utils.DataException;
import com.wardrobe.app.utils.ValidationException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.io.IOException;

/**
 * 错误处理显示管理器
 * 将各种异常转换为用户友好的错误信息
 * 提供统一的错误显示和重试机制
 */
public class ErrorDisplayManager {
    
    private static final String TAG = "ErrorDisplayManager";
    private static volatile ErrorDisplayManager instance;
    private static final Object LOCK = new Object();
    
    private final UserFeedbackManager feedbackManager;
    
    /**
     * 错误类型枚举
     */
    public enum ErrorType {
        NETWORK_ERROR,      // 网络错误
        DATA_ERROR,         // 数据错误
        VALIDATION_ERROR,   // 验证错误
        PERMISSION_ERROR,   // 权限错误
        FILE_ERROR,         // 文件错误
        UNKNOWN_ERROR       // 未知错误
    }
    
    /**
     * 错误信息类
     */
    public static class ErrorInfo {
        private final ErrorType type;
        private final String title;
        private final String message;
        private final String suggestion;
        private final boolean canRetry;
        private final Throwable cause;
        
        public ErrorInfo(ErrorType type, String title, String message, String suggestion, 
                        boolean canRetry, Throwable cause) {
            this.type = type;
            this.title = title;
            this.message = message;
            this.suggestion = suggestion;
            this.canRetry = canRetry;
            this.cause = cause;
        }
        
        public ErrorType getType() { return type; }
        public String getTitle() { return title; }
        public String getMessage() { return message; }
        public String getSuggestion() { return suggestion; }
        public boolean canRetry() { return canRetry; }
        public Throwable getCause() { return cause; }
        
        public String getFullMessage() {
            StringBuilder sb = new StringBuilder();
            if (title != null) {
                sb.append(title);
            }
            if (message != null) {
                if (sb.length() > 0) sb.append(": ");
                sb.append(message);
            }
            if (suggestion != null) {
                if (sb.length() > 0) sb.append("\n");
                sb.append(suggestion);
            }
            return sb.toString();
        }
    }
    
    private ErrorDisplayManager() {
        this.feedbackManager = UserFeedbackManager.getInstance();
        Logger.d(TAG, "ErrorDisplayManager 初始化");
    }
    
    /**
     * 获取ErrorDisplayManager实例
     * 
     * @return ErrorDisplayManager实例
     */
    public static ErrorDisplayManager getInstance() {
        if (instance == null) {
            synchronized (LOCK) {
                if (instance == null) {
                    instance = new ErrorDisplayManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 分析异常并生成错误信息
     * 
     * @param throwable 异常
     * @return 错误信息
     */
    public ErrorInfo analyzeError(Throwable throwable) {
        if (throwable == null) {
            return new ErrorInfo(ErrorType.UNKNOWN_ERROR, "未知错误", 
                    "发生了未知错误", "请重试或联系技术支持", true, null);
        }
        
        Logger.d(TAG, "分析错误: " + throwable.getClass().getSimpleName() + " - " + throwable.getMessage());
        
        // 网络相关错误
        if (throwable instanceof ConnectException) {
            return new ErrorInfo(ErrorType.NETWORK_ERROR, "连接失败", 
                    "无法连接到服务器", "请检查网络连接后重试", true, throwable);
        }
        
        if (throwable instanceof SocketTimeoutException) {
            return new ErrorInfo(ErrorType.NETWORK_ERROR, "连接超时", 
                    "网络连接超时", "请检查网络状况后重试", true, throwable);
        }
        
        if (throwable instanceof UnknownHostException) {
            return new ErrorInfo(ErrorType.NETWORK_ERROR, "网络错误", 
                    "无法解析服务器地址", "请检查网络设置", true, throwable);
        }
        
        if (throwable instanceof IOException) {
            return new ErrorInfo(ErrorType.FILE_ERROR, "文件操作失败", 
                    "文件读写出现问题", "请检查存储空间和权限", true, throwable);
        }
        
        // 应用自定义错误
        if (throwable instanceof DataException) {
            DataException dataEx = (DataException) throwable;
            return new ErrorInfo(ErrorType.DATA_ERROR, "数据错误", 
                    dataEx.getMessage(), "请检查数据格式或重试", true, throwable);
        }
        
        if (throwable instanceof ValidationException) {
            ValidationException validationEx = (ValidationException) throwable;
            return new ErrorInfo(ErrorType.VALIDATION_ERROR, "输入验证失败", 
                    validationEx.getMessage(), "请检查输入内容", false, throwable);
        }
        
        if (throwable instanceof SecurityException) {
            return new ErrorInfo(ErrorType.PERMISSION_ERROR, "权限不足", 
                    "没有执行此操作的权限", "请检查应用权限设置", false, throwable);
        }
        
        // 其他常见错误
        if (throwable instanceof IllegalArgumentException) {
            return new ErrorInfo(ErrorType.VALIDATION_ERROR, "参数错误", 
                    "输入参数不正确", "请检查输入内容", false, throwable);
        }
        
        if (throwable instanceof IllegalStateException) {
            return new ErrorInfo(ErrorType.DATA_ERROR, "状态错误", 
                    "应用状态异常", "请重启应用或重试", true, throwable);
        }
        
        if (throwable instanceof OutOfMemoryError) {
            return new ErrorInfo(ErrorType.UNKNOWN_ERROR, "内存不足", 
                    "设备内存不足", "请关闭其他应用后重试", true, throwable);
        }
        
        // 默认错误处理
        String message = throwable.getMessage();
        if (message == null || message.trim().isEmpty()) {
            message = "发生了未知错误";
        }
        
        return new ErrorInfo(ErrorType.UNKNOWN_ERROR, "错误", 
                message, "请重试或联系技术支持", true, throwable);
    }
    
    /**
     * 显示错误Toast
     * 
     * @param context 上下文
     * @param throwable 异常
     */
    public void showErrorToast(Context context, Throwable throwable) {
        ErrorInfo errorInfo = analyzeError(throwable);
        feedbackManager.showErrorToast(context, errorInfo.getMessage());
    }
    
    /**
     * 显示错误Toast（自定义消息）
     * 
     * @param context 上下文
     * @param operation 操作名称
     * @param throwable 异常
     */
    public void showErrorToast(Context context, String operation, Throwable throwable) {
        ErrorInfo errorInfo = analyzeError(throwable);
        String message = operation + "失败";
        if (errorInfo.getMessage() != null) {
            message += ": " + errorInfo.getMessage();
        }
        feedbackManager.showErrorToast(context, message);
    }
    
    /**
     * 显示错误Snackbar
     * 
     * @param view 父视图
     * @param throwable 异常
     */
    public void showErrorSnackbar(View view, Throwable throwable) {
        ErrorInfo errorInfo = analyzeError(throwable);
        feedbackManager.showErrorSnackbar(view, errorInfo.getMessage());
    }
    
    /**
     * 显示错误Snackbar（带重试）
     * 
     * @param view 父视图
     * @param throwable 异常
     * @param retryAction 重试动作
     */
    public void showErrorSnackbarWithRetry(View view, Throwable throwable, Runnable retryAction) {
        ErrorInfo errorInfo = analyzeError(throwable);
        if (errorInfo.canRetry() && retryAction != null) {
            feedbackManager.showErrorSnackbarWithRetry(view, errorInfo.getMessage(), retryAction);
        } else {
            feedbackManager.showErrorSnackbar(view, errorInfo.getMessage());
        }
    }
    
    /**
     * 显示操作错误
     * 
     * @param context 上下文
     * @param operation 操作名称
     * @param throwable 异常
     */
    public void showOperationError(Context context, String operation, Throwable throwable) {
        ErrorInfo errorInfo = analyzeError(throwable);
        
        String message;
        switch (errorInfo.getType()) {
            case NETWORK_ERROR:
                message = operation + "失败: 网络连接问题";
                break;
            case VALIDATION_ERROR:
                message = operation + "失败: " + errorInfo.getMessage();
                break;
            case PERMISSION_ERROR:
                message = operation + "失败: 权限不足";
                break;
            case FILE_ERROR:
                message = operation + "失败: 文件操作问题";
                break;
            default:
                message = operation + "失败";
                if (errorInfo.getMessage() != null) {
                    message += ": " + errorInfo.getMessage();
                }
                break;
        }
        
        feedbackManager.showErrorToast(context, message);
    }
    
    /**
     * 显示加载错误（带重试）
     * 
     * @param view 父视图
     * @param throwable 异常
     * @param retryAction 重试动作
     */
    public void showLoadError(View view, Throwable throwable, Runnable retryAction) {
        ErrorInfo errorInfo = analyzeError(throwable);
        
        String message;
        switch (errorInfo.getType()) {
            case NETWORK_ERROR:
                message = "网络连接失败，无法加载数据";
                break;
            case DATA_ERROR:
                message = "数据加载失败";
                break;
            default:
                message = "加载失败";
                break;
        }
        
        if (errorInfo.canRetry() && retryAction != null) {
            feedbackManager.showErrorSnackbarWithRetry(view, message, retryAction);
        } else {
            feedbackManager.showErrorSnackbar(view, message);
        }
    }
    
    /**
     * 显示保存错误
     * 
     * @param context 上下文
     * @param throwable 异常
     */
    public void showSaveError(Context context, Throwable throwable) {
        showOperationError(context, "保存", throwable);
    }
    
    /**
     * 显示删除错误
     * 
     * @param context 上下文
     * @param throwable 异常
     */
    public void showDeleteError(Context context, Throwable throwable) {
        showOperationError(context, "删除", throwable);
    }
    
    /**
     * 显示添加错误
     * 
     * @param context 上下文
     * @param throwable 异常
     */
    public void showAddError(Context context, Throwable throwable) {
        showOperationError(context, "添加", throwable);
    }
    
    /**
     * 显示更新错误
     * 
     * @param context 上下文
     * @param throwable 异常
     */
    public void showUpdateError(Context context, Throwable throwable) {
        showOperationError(context, "更新", throwable);
    }
    
    /**
     * 显示搜索错误
     * 
     * @param view 父视图
     * @param throwable 异常
     * @param retryAction 重试动作
     */
    public void showSearchError(View view, Throwable throwable, Runnable retryAction) {
        ErrorInfo errorInfo = analyzeError(throwable);
        String message = "搜索失败";
        
        if (errorInfo.getType() == ErrorType.NETWORK_ERROR) {
            message = "网络连接失败，无法搜索";
        }
        
        if (errorInfo.canRetry() && retryAction != null) {
            feedbackManager.showErrorSnackbarWithRetry(view, message, retryAction);
        } else {
            feedbackManager.showErrorSnackbar(view, message);
        }
    }
    
    /**
     * 获取错误的用户友好描述
     * 
     * @param throwable 异常
     * @return 用户友好的错误描述
     */
    public String getFriendlyErrorMessage(Throwable throwable) {
        ErrorInfo errorInfo = analyzeError(throwable);
        return errorInfo.getFullMessage();
    }
    
    /**
     * 判断错误是否可以重试
     * 
     * @param throwable 异常
     * @return 是否可以重试
     */
    public boolean canRetry(Throwable throwable) {
        ErrorInfo errorInfo = analyzeError(throwable);
        return errorInfo.canRetry();
    }
    
    /**
     * 记录错误日志
     * 
     * @param operation 操作名称
     * @param throwable 异常
     */
    public void logError(String operation, Throwable throwable) {
        ErrorInfo errorInfo = analyzeError(throwable);
        Logger.e(TAG, String.format("%s失败 [%s]: %s", 
                operation, errorInfo.getType(), errorInfo.getMessage()), throwable);
    }
}
