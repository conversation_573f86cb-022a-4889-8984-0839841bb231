// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemWardrobeGroupBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final RecyclerView rvClothingItems;

  @NonNull
  public final TextView tvCategoryTitle;

  @NonNull
  public final TextView tvItemCount;

  private ItemWardrobeGroupBinding(@NonNull LinearLayout rootView,
      @NonNull RecyclerView rvClothingItems, @NonNull TextView tvCategoryTitle,
      @NonNull TextView tvItemCount) {
    this.rootView = rootView;
    this.rvClothingItems = rvClothingItems;
    this.tvCategoryTitle = tvCategoryTitle;
    this.tvItemCount = tvItemCount;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemWardrobeGroupBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemWardrobeGroupBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_wardrobe_group, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemWardrobeGroupBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.rv_clothing_items;
      RecyclerView rvClothingItems = ViewBindings.findChildViewById(rootView, id);
      if (rvClothingItems == null) {
        break missingId;
      }

      id = R.id.tv_category_title;
      TextView tvCategoryTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvCategoryTitle == null) {
        break missingId;
      }

      id = R.id.tv_item_count;
      TextView tvItemCount = ViewBindings.findChildViewById(rootView, id);
      if (tvItemCount == null) {
        break missingId;
      }

      return new ItemWardrobeGroupBinding((LinearLayout) rootView, rvClothingItems, tvCategoryTitle,
          tvItemCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
