package com.wardrobe.app.ui.activities;

import android.content.Intent;
import android.os.Bundle;
import android.widget.TextView;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.wardrobe.app.R;
import com.wardrobe.app.ui.adapters.CategoryListAdapter;
import com.wardrobe.app.ui.components.CategoryProvider;
import com.wardrobe.app.model.CategoryItem;
import java.util.List;

public class CategorySelectActivity extends AppCompatActivity {

    public static final String EXTRA_SELECTED_CATEGORY = "extra_selected_category";
    public static final String EXTRA_SELECTED_SUBCATEGORY = "extra_selected_subcategory";
    public static final int REQUEST_CODE_SELECT_CATEGORY = 101;

    private RecyclerView recyclerView;
    private TextView tvTitle;
    private CategoryListAdapter adapter;

    private boolean isShowingSubcategories = false;
    private String currentMainCategory;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_category_select);

        tvTitle = findViewById(R.id.tv_title);
        recyclerView = findViewById(R.id.recycler_view);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.addItemDecoration(new DividerItemDecoration(this, DividerItemDecoration.VERTICAL));

        showMainCategories();

        findViewById(R.id.btn_back).setOnClickListener(v -> {
            if (isShowingSubcategories) {
                showMainCategories();
            } else {
                finish();
            }
        });
    }

    private void showMainCategories() {
        isShowingSubcategories = false;
        tvTitle.setText("选择主分类");
        List<CategoryItem> mainCategories = CategoryProvider.getMainCategories(this);
        adapter = new CategoryListAdapter(this, mainCategories, item -> {
            currentMainCategory = item.getName();
            showSubCategories(currentMainCategory);
        });
        recyclerView.setAdapter(adapter);
    }

    private void showSubCategories(String mainCategory) {
        isShowingSubcategories = true;
        tvTitle.setText(mainCategory);
        List<CategoryItem> subCategories = CategoryProvider.getSubCategories(mainCategory);
        if (subCategories == null || subCategories.isEmpty()) {
            // If no subcategories, select the main category and finish
            finishWithResult(mainCategory, null);
            return;
        }

        adapter = new CategoryListAdapter(this, subCategories, item -> {
            finishWithResult(mainCategory, item.getName());
        });
        recyclerView.setAdapter(adapter);
    }

    private void finishWithResult(String mainCategory, String subCategory) {
        Intent resultIntent = new Intent();
        resultIntent.putExtra(EXTRA_SELECTED_CATEGORY, mainCategory);
        if (subCategory != null) {
            resultIntent.putExtra(EXTRA_SELECTED_SUBCATEGORY, subCategory);
        }
        setResult(RESULT_OK, resultIntent);
        finish();
    }

    @Override
    public void onBackPressed() {
        if (isShowingSubcategories) {
            showMainCategories();
        } else {
            super.onBackPressed();
        }
    }
}