package com.wardrobe.app.data;

/**
 * 数据常量定义
 * 统一管理所有数据相关的常量，确保一致性
 */
public final class DataConstants {
    
    // 防止实例化
    private DataConstants() {}
    
    // ==================== SharedPreferences Keys ====================
    
    // 主要数据存储
    public static final String PREFS_NAME = "wardrobe_data";
    public static final String CLOTHING_LIST_KEY = "clothing_list";
    public static final String OUTFIT_RECORDS_KEY = "outfit_records";
    public static final String USER_PREFERENCES_KEY = "user_preferences";
    
    // 应用设置
    public static final String SETTINGS_PREFS_NAME = "app_settings";
    public static final String LANGUAGE_KEY = "language";
    public static final String THEME_KEY = "theme";
    public static final String FIRST_LAUNCH_KEY = "first_launch";
    public static final String APP_VERSION_KEY = "app_version";
    
    // 缓存相关
    public static final String CACHE_PREFS_NAME = "cache_data";
    public static final String LAST_SYNC_TIME_KEY = "last_sync_time";
    public static final String CACHE_VERSION_KEY = "cache_version";
    
    // ==================== 数据限制 ====================
    
    // 衣物相关限制
    public static final int MAX_CLOTHING_ITEMS = 1000;
    public static final int MAX_CLOTHING_NAME_LENGTH = 50;
    public static final int MAX_CLOTHING_NOTES_LENGTH = 500;
    public static final int MAX_CLOTHING_STORY_LENGTH = 1000;
    public static final int MAX_TAGS_PER_ITEM = 10;
    public static final int MAX_TAG_LENGTH = 20;
    
    // 穿搭记录限制
    public static final int MAX_OUTFIT_RECORDS = 365; // 一年的记录
    public static final int MAX_OUTFIT_NOTES_LENGTH = 300;
    public static final int MAX_CLOTHING_ITEMS_PER_OUTFIT = 20;
    public static final int MIN_RATING = 1;
    public static final int MAX_RATING = 5;
    
    // 图片相关限制
    public static final int MAX_IMAGE_SIZE_MB = 10;
    public static final int MAX_IMAGE_WIDTH = 1920;
    public static final int MAX_IMAGE_HEIGHT = 1920;
    public static final int THUMBNAIL_SIZE = 200;
    
    // ==================== 默认值 ====================
    
    // 分类默认值
    public static final String DEFAULT_CATEGORY = "其他";
    public static final String DEFAULT_COLOR = "未知";
    public static final String DEFAULT_OCCASION = "日常";
    
    // 穿搭记录默认值
    public static final String DEFAULT_WEATHER = "晴天";
    public static final String DEFAULT_MOOD = "普通";
    public static final int DEFAULT_RATING = 3;
    
    // ==================== 数据版本 ====================
    
    // 数据格式版本，用于数据迁移
    public static final int CURRENT_DATA_VERSION = 1;
    public static final String DATA_VERSION_KEY = "data_version";
    
    // ==================== 文件路径 ====================
    
    // 图片存储路径
    public static final String IMAGES_DIR = "images";
    public static final String CLOTHING_IMAGES_DIR = "clothing";
    public static final String OUTFIT_IMAGES_DIR = "outfits";
    public static final String THUMBNAILS_DIR = "thumbnails";
    
    // 备份文件路径
    public static final String BACKUP_DIR = "backup";
    public static final String EXPORT_DIR = "export";
    
    // ==================== 数据验证规则 ====================
    
    // 必填字段验证
    public static final String[] REQUIRED_CLOTHING_FIELDS = {
        "id", "name", "category"
    };
    
    public static final String[] REQUIRED_OUTFIT_FIELDS = {
        "id", "date", "clothingItemIds"
    };
    
    // ==================== 业务规则常量 ====================
    
    // 搭配推荐相关
    public static final int MIN_ITEMS_FOR_RECOMMENDATION = 5;
    public static final int MAX_RECOMMENDATIONS = 10;
    public static final double SIMILARITY_THRESHOLD = 0.7;
    
    // 用户行为分析
    public static final int MIN_RECORDS_FOR_ANALYSIS = 10;
    public static final int DAYS_FOR_TREND_ANALYSIS = 30;
    
    // ==================== 错误代码 ====================
    
    // 数据错误代码
    public static final int ERROR_DATA_NOT_FOUND = 1001;
    public static final int ERROR_DATA_INVALID = 1002;
    public static final int ERROR_DATA_CORRUPTED = 1003;
    public static final int ERROR_STORAGE_FULL = 1004;
    public static final int ERROR_PERMISSION_DENIED = 1005;
    
    // 业务错误代码
    public static final int ERROR_ITEM_LIMIT_EXCEEDED = 2001;
    public static final int ERROR_DUPLICATE_ITEM = 2002;
    public static final int ERROR_INVALID_OPERATION = 2003;
    public static final int ERROR_BUSINESS_RULE_VIOLATION = 2004;
    
    // ==================== 性能相关常量 ====================
    
    // 缓存配置
    public static final int IMAGE_CACHE_SIZE_MB = 50;
    public static final int DATA_CACHE_SIZE = 100;
    public static final long CACHE_EXPIRY_TIME_MS = 24 * 60 * 60 * 1000; // 24小时
    
    // 分页配置
    public static final int DEFAULT_PAGE_SIZE = 20;
    public static final int MAX_PAGE_SIZE = 100;
    
    // ==================== 日期格式 ====================
    
    public static final String DATE_FORMAT_DISPLAY = "yyyy年MM月dd日";
    public static final String DATE_FORMAT_STORAGE = "yyyy-MM-dd";
    public static final String DATETIME_FORMAT_STORAGE = "yyyy-MM-dd HH:mm:ss";
    public static final String TIME_FORMAT_DISPLAY = "HH:mm";
    
    // ==================== 正则表达式 ====================
    
    // 数据验证正则
    public static final String REGEX_CLOTHING_NAME = "^[\\u4e00-\\u9fa5a-zA-Z0-9\\s\\-_]{1,50}$";
    public static final String REGEX_TAG = "^[\\u4e00-\\u9fa5a-zA-Z0-9]{1,20}$";
    public static final String REGEX_COLOR = "^[\\u4e00-\\u9fa5a-zA-Z0-9\\s]{1,20}$";
    
    // ==================== 通知相关 ====================
    
    // 通知渠道
    public static final String NOTIFICATION_CHANNEL_ID = "wardrobe_notifications";
    public static final String NOTIFICATION_CHANNEL_NAME = "衣橱管理通知";
    
    // 通知类型
    public static final int NOTIFICATION_TYPE_REMINDER = 1;
    public static final int NOTIFICATION_TYPE_RECOMMENDATION = 2;
    public static final int NOTIFICATION_TYPE_BACKUP = 3;
    
    // ==================== 分析统计 ====================
    
    // 统计类型
    public static final String STAT_TOTAL_ITEMS = "total_items";
    public static final String STAT_ITEMS_BY_CATEGORY = "items_by_category";
    public static final String STAT_ITEMS_BY_COLOR = "items_by_color";
    public static final String STAT_OUTFIT_RECORDS = "outfit_records";
    public static final String STAT_FAVORITE_COMBINATIONS = "favorite_combinations";
    
    // ==================== 导入导出 ====================
    
    // 支持的文件格式
    public static final String EXPORT_FORMAT_JSON = "json";
    public static final String EXPORT_FORMAT_CSV = "csv";
    public static final String EXPORT_FORMAT_ZIP = "zip";
    
    // 文件扩展名
    public static final String FILE_EXT_JSON = ".json";
    public static final String FILE_EXT_CSV = ".csv";
    public static final String FILE_EXT_ZIP = ".zip";
    public static final String FILE_EXT_BACKUP = ".backup";
    
    // ==================== 搜索相关 ====================
    
    // 搜索类型
    public static final String SEARCH_TYPE_NAME = "name";
    public static final String SEARCH_TYPE_CATEGORY = "category";
    public static final String SEARCH_TYPE_COLOR = "color";
    public static final String SEARCH_TYPE_TAG = "tag";
    public static final String SEARCH_TYPE_ALL = "all";
    
    // 搜索配置
    public static final int MIN_SEARCH_LENGTH = 1;
    public static final int MAX_SEARCH_RESULTS = 50;
    
    // ==================== 工具方法 ====================
    
    /**
     * 检查字符串是否为有效的衣物名称
     */
    public static boolean isValidClothingName(String name) {
        return name != null && 
               name.trim().length() > 0 && 
               name.length() <= MAX_CLOTHING_NAME_LENGTH &&
               name.matches(REGEX_CLOTHING_NAME);
    }
    
    /**
     * 检查字符串是否为有效的标签
     */
    public static boolean isValidTag(String tag) {
        return tag != null && 
               tag.trim().length() > 0 && 
               tag.length() <= MAX_TAG_LENGTH &&
               tag.matches(REGEX_TAG);
    }
    
    /**
     * 检查评分是否有效
     */
    public static boolean isValidRating(int rating) {
        return rating >= MIN_RATING && rating <= MAX_RATING;
    }
    
    /**
     * 获取安全的字符串值
     */
    public static String getSafeString(String value, String defaultValue) {
        return value != null && !value.trim().isEmpty() ? value.trim() : defaultValue;
    }
    
    /**
     * 截断字符串到指定长度
     */
    public static String truncateString(String value, int maxLength) {
        if (value == null) return "";
        return value.length() > maxLength ? value.substring(0, maxLength) : value;
    }
}
