package com.wardrobe.app.ui.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.wardrobe.app.R;
import com.wardrobe.app.model.CategoryItem;

import java.util.List;

public class OccasionListAdapter extends RecyclerView.Adapter<OccasionListAdapter.ViewHolder> {
    private final List<CategoryItem> occasionList;
    private final OnOccasionClickListener listener;

    public interface OnOccasionClickListener {
        void onOccasionClick(CategoryItem occasion);
    }

    public OccasionListAdapter(List<CategoryItem> occasionList, OnOccasionClickListener listener) {
        this.occasionList = occasionList;
        this.listener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_occasion_selector, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        CategoryItem occasion = occasionList.get(position);
        int iconResId = occasion.getIconResId(holder.itemView.getContext());
        if (iconResId != 0) {
            holder.occasionIcon.setImageResource(iconResId);
        }
        holder.occasionName.setText(occasion.getDisplayName());
        holder.itemView.setOnClickListener(v -> listener.onOccasionClick(occasion));
    }

    @Override
    public int getItemCount() {
        return occasionList.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView occasionIcon;
        TextView occasionName;

        ViewHolder(View itemView) {
            super(itemView);
            occasionIcon = itemView.findViewById(R.id.occasion_icon);
            occasionName = itemView.findViewById(R.id.occasion_name);
        }
    }
} 