package com.wardrobe.app.utils;

import android.app.ActivityManager;
import android.content.Context;
import android.os.Debug;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.util.Log;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.Map;

/**
 * 性能监控工具
 * 监控应用性能指标，包括启动时间、内存使用、UI响应等
 */
public class PerformanceMonitor {
    
    private static final String TAG = "PerformanceMonitor";
    private static volatile PerformanceMonitor instance;
    private static final Object LOCK = new Object();
    
    private final Context context;
    private final Handler mainHandler;
    private final Map<String, Long> operationStartTimes;
    private final Map<String, PerformanceMetric> metrics;
    
    // 性能阈值配置
    private static final long SLOW_OPERATION_THRESHOLD = 500; // 500ms
    private static final long ANR_THRESHOLD = 5000; // 5秒
    private static final long STARTUP_THRESHOLD = 3000; // 3秒启动时间
    
    // 应用启动时间追踪
    private static final AtomicLong appStartTime = new AtomicLong(0);
    private static final AtomicLong firstActivityTime = new AtomicLong(0);
    
    /**
     * 性能指标数据类
     */
    public static class PerformanceMetric {
        public final String name;
        public final long duration;
        public final long timestamp;
        public final String category;
        public final boolean isSlowOperation;
        
        public PerformanceMetric(String name, long duration, String category) {
            this.name = name;
            this.duration = duration;
            this.timestamp = System.currentTimeMillis();
            this.category = category;
            this.isSlowOperation = duration > SLOW_OPERATION_THRESHOLD;
        }
        
        @Override
        public String toString() {
            return String.format("[%s] %s: %dms %s", 
                category, name, duration, isSlowOperation ? "(SLOW)" : "");
        }
    }
    
    private PerformanceMonitor(Context context) {
        this.context = context.getApplicationContext();
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.operationStartTimes = new ConcurrentHashMap<>();
        this.metrics = new ConcurrentHashMap<>();
        
        Logger.d(TAG, "PerformanceMonitor 初始化完成");
    }
    
    /**
     * 获取单例实例
     */
    public static PerformanceMonitor getInstance(Context context) {
        if (instance == null) {
            synchronized (LOCK) {
                if (instance == null) {
                    instance = new PerformanceMonitor(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * 记录应用启动开始时间
     */
    public static void recordAppStartTime() {
        appStartTime.set(SystemClock.elapsedRealtime());
        Logger.d(TAG, "应用启动时间记录开始");
    }
    
    /**
     * 记录第一个Activity启动时间
     */
    public static void recordFirstActivityTime() {
        firstActivityTime.set(SystemClock.elapsedRealtime());
        long startupTime = firstActivityTime.get() - appStartTime.get();
        
        Logger.i(TAG, "应用启动完成，耗时: " + startupTime + "ms");
        
        if (startupTime > STARTUP_THRESHOLD) {
            Logger.w(TAG, "应用启动时间过长: " + startupTime + "ms");
        }
    }
    
    /**
     * 开始监控操作
     */
    public void startOperation(String operationName) {
        startOperation(operationName, "General");
    }
    
    /**
     * 开始监控操作（带分类）
     */
    public void startOperation(String operationName, String category) {
        String key = category + ":" + operationName;
        operationStartTimes.put(key, SystemClock.elapsedRealtime());
        Logger.d(TAG, "开始监控操作: " + key);
    }
    
    /**
     * 结束监控操作
     */
    public void endOperation(String operationName) {
        endOperation(operationName, "General");
    }
    
    /**
     * 结束监控操作（带分类）
     */
    public void endOperation(String operationName, String category) {
        String key = category + ":" + operationName;
        Long startTime = operationStartTimes.remove(key);
        
        if (startTime != null) {
            long duration = SystemClock.elapsedRealtime() - startTime;
            PerformanceMetric metric = new PerformanceMetric(operationName, duration, category);
            metrics.put(key + "_" + System.currentTimeMillis(), metric);
            
            Logger.i(TAG, "操作完成: " + metric.toString());
            
            // 检查是否为慢操作
            if (metric.isSlowOperation) {
                Logger.w(TAG, "检测到慢操作: " + metric.toString());
                reportSlowOperation(metric);
            }
        }
    }
    
    /**
     * 监控UI线程阻塞
     */
    public void startUIBlockMonitoring() {
        mainHandler.post(new UIBlockMonitor());
    }
    
    /**
     * UI阻塞监控器
     */
    private class UIBlockMonitor implements Runnable {
        private long lastCheckTime = SystemClock.elapsedRealtime();
        
        @Override
        public void run() {
            long currentTime = SystemClock.elapsedRealtime();
            long timeDiff = currentTime - lastCheckTime;
            
            // 如果时间差超过阈值，说明UI线程被阻塞
            if (timeDiff > ANR_THRESHOLD) {
                Logger.w(TAG, "检测到UI线程阻塞: " + timeDiff + "ms");
                reportUIBlock(timeDiff);
            }
            
            lastCheckTime = currentTime;
            
            // 每秒检查一次
            mainHandler.postDelayed(this, 1000);
        }
    }
    
    /**
     * 获取当前内存使用情况
     */
    public MemoryInfo getCurrentMemoryInfo() {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        ActivityManager.MemoryInfo memInfo = new ActivityManager.MemoryInfo();
        activityManager.getMemoryInfo(memInfo);
        
        // 获取应用内存使用
        Debug.MemoryInfo debugMemInfo = new Debug.MemoryInfo();
        Debug.getMemoryInfo(debugMemInfo);
        
        return new MemoryInfo(memInfo, debugMemInfo);
    }
    
    /**
     * 内存信息封装类
     */
    public static class MemoryInfo {
        public final long totalMemory;
        public final long availableMemory;
        public final long usedMemory;
        public final int dalvikPss;
        public final int nativePss;
        public final int otherPss;
        public final boolean isLowMemory;
        
        public MemoryInfo(ActivityManager.MemoryInfo systemMem, Debug.MemoryInfo debugMem) {
            this.totalMemory = systemMem.totalMem;
            this.availableMemory = systemMem.availMem;
            this.usedMemory = totalMemory - availableMemory;
            this.dalvikPss = debugMem.dalvikPss;
            this.nativePss = debugMem.nativePss;
            this.otherPss = debugMem.otherPss;
            this.isLowMemory = systemMem.lowMemory;
        }
        
        @Override
        public String toString() {
            return String.format("Memory: Total=%dMB, Available=%dMB, Used=%dMB, " +
                    "Dalvik=%dKB, Native=%dKB, Other=%dKB, LowMemory=%s",
                    totalMemory / 1024 / 1024, availableMemory / 1024 / 1024, 
                    usedMemory / 1024 / 1024, dalvikPss, nativePss, otherPss, isLowMemory);
        }
    }
    
    /**
     * 报告慢操作
     */
    private void reportSlowOperation(PerformanceMetric metric) {
        // 这里可以集成到崩溃报告系统
        Logger.w(TAG, "慢操作报告: " + metric.toString());
    }
    
    /**
     * 报告UI阻塞
     */
    private void reportUIBlock(long blockTime) {
        // 这里可以集成到崩溃报告系统
        Logger.w(TAG, "UI阻塞报告: " + blockTime + "ms");
    }
    
    /**
     * 获取性能报告
     */
    public String getPerformanceReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== 性能监控报告 ===\n");
        
        // 启动时间
        if (appStartTime.get() > 0 && firstActivityTime.get() > 0) {
            long startupTime = firstActivityTime.get() - appStartTime.get();
            report.append("启动时间: ").append(startupTime).append("ms\n");
        }
        
        // 内存信息
        MemoryInfo memInfo = getCurrentMemoryInfo();
        report.append("内存状态: ").append(memInfo.toString()).append("\n");
        
        // 慢操作统计
        long slowOperationCount = metrics.values().stream()
                .mapToLong(m -> m.isSlowOperation ? 1 : 0)
                .sum();
        report.append("慢操作数量: ").append(slowOperationCount).append("\n");
        
        return report.toString();
    }
    
    /**
     * 清理性能数据
     */
    public void clearMetrics() {
        metrics.clear();
        operationStartTimes.clear();
        Logger.d(TAG, "性能监控数据已清理");
    }
}
