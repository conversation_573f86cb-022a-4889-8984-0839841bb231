package com.wardrobe.app.ui.adapters;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import java.util.List;
import com.wardrobe.app.model.CategoryItem;
import com.wardrobe.app.R;

public class CategorySpinnerAdapter extends ArrayAdapter<String> {

    private final List<String> items;
    private final boolean showIcons;

    public CategorySpinnerAdapter(Context context, List<String> items, boolean showIcons) {
        super(context, android.R.layout.simple_spinner_item, items);
        this.items = items;
        this.showIcons = showIcons;
    }

    @NonNull
    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        return createView(position, convertView, parent, false);
    }

    @Override
    public View getDropDownView(int position, View convertView, ViewGroup parent) {
        return createView(position, convertView, parent, true);
    }

    private View createView(int position, View convertView, ViewGroup parent, boolean isDropDownView) {
        if (convertView == null) {
            convertView = LayoutInflater.from(getContext()).inflate(R.layout.spinner_item_with_icon, parent, false);
        }
        TextView textView = convertView.findViewById(R.id.spinner_text);
        String item = items.get(position);
        textView.setText(item);
        if (showIcons && position > 0) {
            Drawable icon = getIconForCategory(item);
            if (icon != null) {
                textView.setCompoundDrawablesWithIntrinsicBounds(icon, null, null, null);
                textView.setCompoundDrawablePadding(16);
            }
        }
        return convertView;
    }

    private Drawable getIconForCategory(String categoryName) {
        String iconName = "ic_category_" + categoryName.toLowerCase().replace(" ", "_");
        int resourceId = getContext().getResources().getIdentifier(iconName, "drawable", getContext().getPackageName());
        if (resourceId != 0) {
            return ContextCompat.getDrawable(getContext(), resourceId);
        }
        return null;
    }
}
