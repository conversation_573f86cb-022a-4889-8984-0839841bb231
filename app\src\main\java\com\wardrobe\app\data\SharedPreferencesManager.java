package com.wardrobe.app.data;

import android.content.Context;
import android.content.SharedPreferences;

/**
 * SharedPreferences管理器 - 线程安全版本
 * 提供线程安全的数据读写操作
 */
public class SharedPreferencesManager {

    private static final String PREFS_NAME = "wardrobe_prefs";
    private final SharedPreferences sharedPreferences;
    private final Object lock = new Object();

    public SharedPreferencesManager(Context context) {
        sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }

    /**
     * 写入字符串数据（线程安全）
     *
     * @param key 键
     * @param value 值
     */
    public void writeString(String key, String value) {
        synchronized (lock) {
            SharedPreferences.Editor editor = sharedPreferences.edit();
            editor.putString(key, value);
            editor.apply();
        }
    }

    /**
     * 读取字符串数据（线程安全）
     *
     * @param key 键
     * @param defaultValue 默认值
     * @return 读取的值或默认值
     */
    public String readString(String key, String defaultValue) {
        synchronized (lock) {
            return sharedPreferences.getString(key, defaultValue);
        }
    }

    /**
     * 删除指定键的数据（线程安全）
     *
     * @param key 要删除的键
     */
    public void remove(String key) {
        synchronized (lock) {
            SharedPreferences.Editor editor = sharedPreferences.edit();
            editor.remove(key);
            editor.apply();
        }
    }

    /**
     * 清除所有数据（线程安全）
     */
    public void clear() {
        synchronized (lock) {
            SharedPreferences.Editor editor = sharedPreferences.edit();
            editor.clear();
            editor.apply();
        }
    }

    /**
     * 检查是否包含指定键（线程安全）
     *
     * @param key 键
     * @return 是否包含该键
     */
    public boolean contains(String key) {
        synchronized (lock) {
            return sharedPreferences.contains(key);
        }
    }
}