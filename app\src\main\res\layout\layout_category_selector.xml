<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <!-- Category -->
    <include
        android:id="@+id/category_selector"
        layout="@layout/list_item_selector_row"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@drawable/ios_style_group_background_top" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/separator"
        android:layout_marginStart="16dp" />

    <!-- Subcategory -->
    <include
        android:id="@+id/subcategory_selector"
        layout="@layout/list_item_selector_row"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@drawable/ios_style_group_background_middle" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/separator"
        android:layout_marginStart="16dp" />

    <!-- Color -->
    <include
        android:id="@+id/color_selector"
        layout="@layout/list_item_selector_row"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@drawable/ios_style_group_background_middle" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/separator"
        android:layout_marginStart="16dp" />
        
    <!-- Occasion -->
    <include
        android:id="@+id/occasion_selector"
        layout="@layout/list_item_selector_row"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@drawable/ios_style_group_background_bottom" />

</LinearLayout>
