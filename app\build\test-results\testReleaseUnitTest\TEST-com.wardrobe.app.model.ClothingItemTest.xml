<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.wardrobe.app.model.ClothingItemTest" tests="11" skipped="0" failures="0" errors="0" timestamp="2025-06-25T12:57:43" hostname="AMON" time="0.009">
  <properties/>
  <testcase name="testDefaultValues" classname="com.wardrobe.app.model.ClothingItemTest" time="0.001"/>
  <testcase name="testToString" classname="com.wardrobe.app.model.ClothingItemTest" time="0.0"/>
  <testcase name="testSetAndGetName" classname="com.wardrobe.app.model.ClothingItemTest" time="0.001"/>
  <testcase name="testClothingItemCreation" classname="com.wardrobe.app.model.ClothingItemTest" time="0.0"/>
  <testcase name="testSetAndGetColor" classname="com.wardrobe.app.model.ClothingItemTest" time="0.001"/>
  <testcase name="testSetAndGetNotes" classname="com.wardrobe.app.model.ClothingItemTest" time="0.0"/>
  <testcase name="testSetAndGetStory" classname="com.wardrobe.app.model.ClothingItemTest" time="0.0"/>
  <testcase name="testSetAndGetImageUri" classname="com.wardrobe.app.model.ClothingItemTest" time="0.001"/>
  <testcase name="testSetAndGetCategory" classname="com.wardrobe.app.model.ClothingItemTest" time="0.0"/>
  <testcase name="testSetAndGetOccasion" classname="com.wardrobe.app.model.ClothingItemTest" time="0.0"/>
  <testcase name="testSetAndGetSubcategory" classname="com.wardrobe.app.model.ClothingItemTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
