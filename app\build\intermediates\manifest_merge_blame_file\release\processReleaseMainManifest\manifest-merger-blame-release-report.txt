1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.wardrobe.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- 照片相关权限 -->
12    <uses-permission
12-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:6:5-107
13        android:name="android.permission.READ_EXTERNAL_STORAGE"
13-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:6:22-77
14        android:maxSdkVersion="32" />
14-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:6:78-104
15    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
15-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:7:5-76
15-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:7:22-73
16    <uses-permission android:name="android.permission.CAMERA" />
16-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:8:5-65
16-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:8:22-62
17
18    <!-- 如果您的应用需要访问网络，请取消注释以下权限 -->
19    <!-- <uses-permission android:name="android.permission.INTERNET" /> -->
20
21
22    <!-- 相机功能声明 -->
23    <uses-feature
23-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:14:5-16:36
24        android:name="android.hardware.camera"
24-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:15:9-47
25        android:required="false" />
25-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:16:9-33
26
27    <permission
27-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
28        android:name="com.wardrobe.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
28-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
29        android:protectionLevel="signature" />
29-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
30
31    <uses-permission android:name="com.wardrobe.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
31-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
31-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
32
33    <application
33-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:18:5-77:19
34        android:name="com.wardrobe.app.WardrobeApplication"
34-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:19:9-44
35        android:allowBackup="true"
35-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:20:9-35
36        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
36-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\78c90e2c810aa96c87113a3b6d90ee01\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
37        android:dataExtractionRules="@xml/data_extraction_rules"
37-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:21:9-65
38        android:extractNativeLibs="false"
39        android:fullBackupContent="@xml/backup_rules"
39-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:22:9-54
40        android:icon="@mipmap/ic_launcher"
40-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:23:9-43
41        android:label="@string/app_name"
41-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:24:9-41
42        android:roundIcon="@mipmap/ic_launcher_round"
42-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:25:9-54
43        android:supportsRtl="true"
43-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:26:9-35
44        android:theme="@style/Theme.MaterialComponents.DayNight.NoActionBar" >
44-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:27:9-77
45        <activity
45-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:30:9-37:20
46            android:name="com.wardrobe.app.ui.activities.MainActivity"
46-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:31:13-55
47            android:exported="true" >
47-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:32:13-36
48            <intent-filter>
48-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:33:13-36:29
49                <action android:name="android.intent.action.MAIN" />
49-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:34:17-69
49-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:34:25-66
50
51                <category android:name="android.intent.category.LAUNCHER" />
51-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:35:17-77
51-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:35:27-74
52            </intent-filter>
53        </activity>
54        <activity
54-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:39:9-41:40
55            android:name="com.wardrobe.app.ui.activities.AddClothingActivity"
55-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:40:13-62
56            android:exported="false" />
56-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:41:13-37
57        <activity
57-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:43:9-45:40
58            android:name="com.wardrobe.app.ui.activities.CategorySelectActivity"
58-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:44:13-65
59            android:exported="false" />
59-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:45:13-37
60        <activity
60-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:47:9-49:40
61            android:name="com.wardrobe.app.ui.activities.ColorSelectActivity"
61-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:48:13-62
62            android:exported="false" />
62-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:49:13-37
63        <activity
63-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:51:9-53:40
64            android:name="com.wardrobe.app.ui.activities.OccasionSelectActivity"
64-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:52:13-65
65            android:exported="false" />
65-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:53:13-37
66        <activity
66-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:55:9-57:40
67            android:name="com.wardrobe.app.ui.activities.ClothingDetailActivity"
67-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:56:13-65
68            android:exported="false" />
68-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:57:13-37
69
70        <!-- 如果您有 BatchAddClothingActivity，请确保它已声明 -->
71        <activity
71-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:60:9-62:40
72            android:name="com.wardrobe.app.ui.activities.BatchAddClothingActivity"
72-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:61:13-67
73            android:exported="false" />
73-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:62:13-37
74
75        <!-- FileProvider for secure file sharing -->
76        <provider
77            android:name="androidx.core.content.FileProvider"
77-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:66:13-62
78            android:authorities="com.wardrobe.app.fileprovider"
78-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:67:13-64
79            android:exported="false"
79-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:68:13-37
80            android:grantUriPermissions="true" >
80-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:69:13-47
81            <meta-data
81-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:70:13-72:54
82                android:name="android.support.FILE_PROVIDER_PATHS"
82-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:71:17-67
83                android:resource="@xml/file_paths" />
83-->C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\AndroidManifest.xml:72:17-51
84        </provider>
85
86        <!-- 请确保您应用中所有其他的 Activity, Service, Receiver, Provider 都在此处声明 -->
87
88        <provider
88-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
89            android:name="androidx.startup.InitializationProvider"
89-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
90            android:authorities="com.wardrobe.app.androidx-startup"
90-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
91            android:exported="false" >
91-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
92            <meta-data
92-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
93                android:name="androidx.emoji2.text.EmojiCompatInitializer"
93-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
94                android:value="androidx.startup" />
94-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ef084507259944f9a1e135da54f6bb90\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
95            <meta-data
95-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\710e1e3cca06d39ef5f5ec834a7465b3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
96                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
96-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\710e1e3cca06d39ef5f5ec834a7465b3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
97                android:value="androidx.startup" />
97-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\710e1e3cca06d39ef5f5ec834a7465b3\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
98            <meta-data
98-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
99                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
99-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
100                android:value="androidx.startup" />
100-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
101        </provider>
102
103        <receiver
103-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
104            android:name="androidx.profileinstaller.ProfileInstallReceiver"
104-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
105            android:directBootAware="false"
105-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
106            android:enabled="true"
106-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
107            android:exported="true"
107-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
108            android:permission="android.permission.DUMP" >
108-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
109            <intent-filter>
109-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
110                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
110-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
110-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
111            </intent-filter>
112            <intent-filter>
112-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
113                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
113-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
113-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
114            </intent-filter>
115            <intent-filter>
115-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
116                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
116-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
116-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
117            </intent-filter>
118            <intent-filter>
118-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
119                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
119-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
119-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7c374b05dac638e2ee9cc1b991e5e0f\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
120            </intent-filter>
121        </receiver>
122    </application>
123
124</manifest>
