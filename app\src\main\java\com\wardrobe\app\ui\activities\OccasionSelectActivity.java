package com.wardrobe.app.ui.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.wardrobe.app.R;
import com.wardrobe.app.ui.adapters.OccasionListAdapter;
import com.wardrobe.app.ui.components.OccasionProvider;
import com.wardrobe.app.model.CategoryItem;

import java.util.List;

public class OccasionSelectActivity extends AppCompatActivity implements OccasionListAdapter.OnOccasionClickListener {
    public static final String EXTRA_SELECTED_OCCASION = "selected_occasion";

    private RecyclerView recyclerView;
    private TextView tvTitle;
    private OccasionListAdapter adapter;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_occasion_select);

        tvTitle = findViewById(R.id.tv_title);
        recyclerView = findViewById(R.id.recycler_view);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));

        tvTitle.setText("选择场合");
        setupBackButton();
        loadOccasions();
    }

    private void setupBackButton() {
        findViewById(R.id.btn_back).setOnClickListener(v -> finish());
    }

    private void loadOccasions() {
        List<CategoryItem> occasions = OccasionProvider.getOccasions(this);
        adapter = new OccasionListAdapter(occasions, this);
        recyclerView.setAdapter(adapter);
    }

    @Override
    public void onOccasionClick(CategoryItem occasion) {
        Intent result = new Intent();
        result.putExtra(EXTRA_SELECTED_OCCASION, occasion.getName());
        setResult(RESULT_OK, result);
        finish();
    }
} 