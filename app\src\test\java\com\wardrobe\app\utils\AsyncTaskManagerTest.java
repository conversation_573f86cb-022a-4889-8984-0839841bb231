package com.wardrobe.app.utils;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;

/**
 * AsyncTaskManager单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class AsyncTaskManagerTest {
    
    private AsyncTaskManager asyncTaskManager;
    
    @Before
    public void setUp() {
        asyncTaskManager = AsyncTaskManager.getInstance();
    }
    
    @After
    public void tearDown() {
        // 清理资源
    }
    
    @Test
    public void testExecuteBackground_Success() throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(1);
        final String[] result = new String[1];
        final Exception[] error = new Exception[1];
        
        asyncTaskManager.executeBackground(() -> {
            // 模拟后台任务
            Thread.sleep(100);
            return "测试结果";
        }, new AsyncTaskManager.TaskCallback<String>() {
            @Override
            public void onSuccess(String s) {
                result[0] = s;
                latch.countDown();
            }
            
            @Override
            public void onError(Exception e) {
                error[0] = e;
                latch.countDown();
            }
        });
        
        // 等待任务完成
        assertTrue("任务应该在5秒内完成", latch.await(5, TimeUnit.SECONDS));
        assertEquals("测试结果", result[0]);
        assertNull("不应该有错误", error[0]);
    }
    
    @Test
    public void testExecuteBackground_Error() throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(1);
        final String[] result = new String[1];
        final Exception[] error = new Exception[1];
        
        asyncTaskManager.executeBackground(() -> {
            throw new RuntimeException("测试异常");
        }, new AsyncTaskManager.TaskCallback<String>() {
            @Override
            public void onSuccess(String s) {
                result[0] = s;
                latch.countDown();
            }
            
            @Override
            public void onError(Exception e) {
                error[0] = e;
                latch.countDown();
            }
        });
        
        // 等待任务完成
        assertTrue("任务应该在5秒内完成", latch.await(5, TimeUnit.SECONDS));
        assertNull("不应该有结果", result[0]);
        assertNotNull("应该有错误", error[0]);
        assertEquals("测试异常", error[0].getMessage());
    }
    
    @Test
    public void testExecuteVoidBackground_Success() throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(1);
        final boolean[] success = new boolean[1];
        final Exception[] error = new Exception[1];
        
        asyncTaskManager.executeVoidBackground(() -> {
            // 模拟无返回值任务
            Thread.sleep(100);
        }, new AsyncTaskManager.VoidTaskCallback() {
            @Override
            public void onSuccess() {
                success[0] = true;
                latch.countDown();
            }
            
            @Override
            public void onError(Exception e) {
                error[0] = e;
                latch.countDown();
            }
        });
        
        // 等待任务完成
        assertTrue("任务应该在5秒内完成", latch.await(5, TimeUnit.SECONDS));
        assertTrue("任务应该成功", success[0]);
        assertNull("不应该有错误", error[0]);
    }
    
    @Test
    public void testExecuteOnMainThread() throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(1);
        final boolean[] executed = new boolean[1];
        
        asyncTaskManager.executeOnMainThread(() -> {
            executed[0] = true;
            latch.countDown();
        });
        
        // 等待任务完成
        assertTrue("任务应该在5秒内完成", latch.await(5, TimeUnit.SECONDS));
        assertTrue("任务应该被执行", executed[0]);
    }
    
    @Test
    public void testIsMainThread() {
        // 在测试线程中，这应该返回false
        assertFalse("测试线程不是主线程", AsyncTaskManager.isMainThread());
    }
    
    @Test
    public void testGetThreadPoolStatus() {
        String status = asyncTaskManager.getThreadPoolStatus();
        assertNotNull("状态信息不应该为空", status);
        assertTrue("状态信息应该包含线程池信息", status.contains("线程池"));
    }
    
    @Test
    public void testMultipleConcurrentTasks() throws InterruptedException {
        int taskCount = 5;
        CountDownLatch latch = new CountDownLatch(taskCount);
        final int[] completedTasks = new int[1];
        
        for (int i = 0; i < taskCount; i++) {
            final int taskId = i;
            asyncTaskManager.executeBackground(() -> {
                Thread.sleep(50); // 短暂延迟
                return "任务" + taskId + "完成";
            }, new AsyncTaskManager.TaskCallback<String>() {
                @Override
                public void onSuccess(String result) {
                    synchronized (completedTasks) {
                        completedTasks[0]++;
                    }
                    latch.countDown();
                }
                
                @Override
                public void onError(Exception error) {
                    latch.countDown();
                }
            });
        }
        
        // 等待所有任务完成
        assertTrue("所有任务应该在10秒内完成", latch.await(10, TimeUnit.SECONDS));
        assertEquals("应该完成所有任务", taskCount, completedTasks[0]);
    }
    
    @Test
    public void testExecuteDelayedTask() throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(1);
        final long[] executionTime = new long[1];
        long startTime = System.currentTimeMillis();
        
        asyncTaskManager.executeOnMainThreadDelayed(() -> {
            executionTime[0] = System.currentTimeMillis();
            latch.countDown();
        }, 200); // 200ms延迟
        
        // 等待任务完成
        assertTrue("延迟任务应该在5秒内完成", latch.await(5, TimeUnit.SECONDS));
        long actualDelay = executionTime[0] - startTime;
        assertTrue("延迟应该至少200ms", actualDelay >= 200);
        assertTrue("延迟不应该超过1秒", actualDelay < 1000);
    }
    
    @Test
    public void testCancelDelayedTask() throws InterruptedException {
        final boolean[] executed = new boolean[1];
        
        Runnable task = () -> executed[0] = true;
        
        // 安排延迟任务
        asyncTaskManager.executeOnMainThreadDelayed(task, 500);
        
        // 立即取消
        asyncTaskManager.cancelDelayedTask(task);
        
        // 等待足够长的时间
        Thread.sleep(800);
        
        assertFalse("被取消的任务不应该执行", executed[0]);
    }
    
    @Test
    public void testIOTaskExecution() throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(1);
        final String[] result = new String[1];
        
        asyncTaskManager.executeIO(() -> {
            // 模拟IO操作
            Thread.sleep(100);
            return "IO操作完成";
        }, new AsyncTaskManager.TaskCallback<String>() {
            @Override
            public void onSuccess(String s) {
                result[0] = s;
                latch.countDown();
            }
            
            @Override
            public void onError(Exception error) {
                latch.countDown();
            }
        });
        
        // 等待任务完成
        assertTrue("IO任务应该在5秒内完成", latch.await(5, TimeUnit.SECONDS));
        assertEquals("IO操作完成", result[0]);
    }
    
    @Test
    public void testTaskExecutionOrder() throws InterruptedException {
        int taskCount = 3;
        CountDownLatch latch = new CountDownLatch(taskCount);
        final StringBuilder executionOrder = new StringBuilder();
        
        // 提交多个任务
        for (int i = 0; i < taskCount; i++) {
            final int taskId = i;
            asyncTaskManager.executeBackground(() -> {
                synchronized (executionOrder) {
                    executionOrder.append(taskId);
                }
                return null;
            }, new AsyncTaskManager.TaskCallback<Object>() {
                @Override
                public void onSuccess(Object result) {
                    latch.countDown();
                }
                
                @Override
                public void onError(Exception error) {
                    latch.countDown();
                }
            });
        }
        
        // 等待所有任务完成
        assertTrue("所有任务应该在5秒内完成", latch.await(5, TimeUnit.SECONDS));
        assertEquals("应该执行所有任务", taskCount, executionOrder.length());
    }
}
