{"logs": [{"outputFile": "com.wardrobe.app-mergeDebugResources-41:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0a486d55bfd94b1f617db1e4b30fa627\\transformed\\appcompat-1.7.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,414,509,623,709,809,922,999,1074,1165,1258,1352,1446,1546,1639,1734,1832,1923,2014,2092,2195,2293,2389,2493,2592,2693,2846,9280", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "409,504,618,704,804,917,994,1069,1160,1253,1347,1441,1541,1634,1729,1827,1918,2009,2087,2190,2288,2384,2488,2587,2688,2841,2938,9355"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\86e6281540a3cdfac31ba0f2ba29551c\\transformed\\material-1.12.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,338,411,498,586,666,765,884,966,1025,1089,1181,1249,1309,1396,1460,1522,1586,1654,1719,1773,1882,1940,2002,2056,2131,2251,2333,2410,2500,2584,2664,2798,2876,2956,3079,3167,3245,3299,3350,3416,3484,3558,3629,3705,3776,3854,3924,3994,4094,4183,4261,4349,4439,4511,4583,4667,4718,4796,4862,4943,5026,5088,5152,5215,5284,5384,5488,5581,5681,5739,5794,5872,5956,6034", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "256,333,406,493,581,661,760,879,961,1020,1084,1176,1244,1304,1391,1455,1517,1581,1649,1714,1768,1877,1935,1997,2051,2126,2246,2328,2405,2495,2579,2659,2793,2871,2951,3074,3162,3240,3294,3345,3411,3479,3553,3624,3700,3771,3849,3919,3989,4089,4178,4256,4344,4434,4506,4578,4662,4713,4791,4857,4938,5021,5083,5147,5210,5279,5379,5483,5576,5676,5734,5789,5867,5951,6029,6101"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2943,3020,3093,3180,3268,4074,4173,4292,4374,4433,4497,4589,4657,4717,4804,4868,4930,4994,5062,5127,5181,5290,5348,5410,5464,5539,5659,5741,5818,5908,5992,6072,6206,6284,6364,6487,6575,6653,6707,6758,6824,6892,6966,7037,7113,7184,7262,7332,7402,7502,7591,7669,7757,7847,7919,7991,8075,8126,8204,8270,8351,8434,8496,8560,8623,8692,8792,8896,8989,9089,9147,9202,9360,9444,9522", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "306,3015,3088,3175,3263,3343,4168,4287,4369,4428,4492,4584,4652,4712,4799,4863,4925,4989,5057,5122,5176,5285,5343,5405,5459,5534,5654,5736,5813,5903,5987,6067,6201,6279,6359,6482,6570,6648,6702,6753,6819,6887,6961,7032,7108,7179,7257,7327,7397,7497,7586,7664,7752,7842,7914,7986,8070,8121,8199,8265,8346,8429,8491,8555,8618,8687,8787,8891,8984,9084,9142,9197,9275,9439,9517,9589"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e7fea473efc4287bc0512d9da8cbf7e2\\transformed\\core-1.13.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3348,3442,3544,3641,3740,3848,3954,9594", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "3437,3539,3636,3735,3843,3949,4069,9690"}}]}]}