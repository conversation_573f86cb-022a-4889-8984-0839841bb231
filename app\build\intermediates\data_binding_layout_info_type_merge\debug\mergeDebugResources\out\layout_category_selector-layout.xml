<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="layout_category_selector" modulePackage="com.wardrobe.app" filePath="app\src\main\res\layout\layout_category_selector.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/layout_category_selector_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="56" endOffset="14"/></Target><Target id="@+id/category_selector" tag="layout/layout_category_selector_0" include="list_item_selector_row"><Expressions/><location startLine="7" startOffset="4" endLine="12" endOffset="71"/></Target><Target id="@+id/subcategory_selector" tag="layout/layout_category_selector_0" include="list_item_selector_row"><Expressions/><location startLine="21" startOffset="4" endLine="26" endOffset="74"/></Target><Target id="@+id/color_selector" tag="layout/layout_category_selector_0" include="list_item_selector_row"><Expressions/><location startLine="35" startOffset="4" endLine="40" endOffset="74"/></Target><Target id="@+id/occasion_selector" tag="layout/layout_category_selector_0" include="list_item_selector_row"><Expressions/><location startLine="49" startOffset="4" endLine="54" endOffset="74"/></Target></Targets></Layout>