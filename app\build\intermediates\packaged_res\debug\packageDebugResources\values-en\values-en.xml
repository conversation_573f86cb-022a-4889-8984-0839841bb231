<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="add_clothing_image">Add Clothing Image</string>
    <string name="app_name">Wardrobe Manager</string>
    <string name="button_add">Add</string>
    <string name="button_cancel">Cancel</string>
    <string name="button_clear">Clear</string>
    <string name="button_confirm">Confirm</string>
    <string name="button_delete">Delete</string>
    <string name="button_deselect_all">Deselect All</string>
    <string name="button_edit">Edit</string>
    <string name="button_filter">Filter</string>
    <string name="button_save">SAVE</string>
    <string name="button_search">Search</string>
    <string name="button_select_all">Select All</string>
    <string name="category_placeholder">Select Category</string>
    <string name="category_primary_placeholder">Select Main Category</string>
    <string name="category_secondary_placeholder">Select Subcategory</string>
    <string name="clothing_image_description">Clothing Image</string>
    <string name="color_placeholder">Select Color</string>
    <string name="dialog_title_select_color">Select Color</string>
    <string name="dialog_title_select_language">Select Language</string>
    <string name="dialog_title_select_occasion">Select Occasion</string>
    <string name="dialog_title_select_photo">Select Photo</string>
    <string name="hint_clothing_name">Clothing Name</string>
    <string name="label_category">Category</string>
    <string name="label_color">Color</string>
    <string name="label_occasion">Occasion</string>
    <string name="nav_calendar">Calendar</string>
    <string name="nav_outfit">Outfit</string>
    <string name="nav_profile">Profile</string>
    <string name="nav_wardrobe">Wardrobe</string>
    <string name="occasion_placeholder">Select Occasion</string>
    <string name="placeholder_select">Please Select</string>
    <string name="replace_photo">Replace Photo</string>
    <string name="section_optional">OPTIONAL INFORMATION</string>
    <string name="section_required">REQUIRED INFORMATION</string>
    <string name="setting_language">Language</string>
    <string name="title_add_new_clothing">Add New Clothing</string>
    <string name="title_edit_clothing">Edit Clothing</string>
</resources>