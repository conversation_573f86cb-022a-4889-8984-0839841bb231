package com.wardrobe.app.utils;

import android.content.Context;
import android.widget.Toast;

/**
 * 统一错误提示与日志工具类 - 重构版本
 * 现在使用GlobalExceptionHandler和Logger进行统一处理
 *
 * @deprecated 建议使用GlobalExceptionHandler进行异常处理
 */
@Deprecated
public class ErrorUtils {
    private static final String TAG = "ErrorUtils";

    /**
     * 显示用户友好的错误提示
     * @param context 上下文
     * @param userMessage 用户可理解的错误信息
     */
    public static void showUserError(Context context, String userMessage) {
        if (context != null && userMessage != null && !userMessage.isEmpty()) {
            Toast.makeText(context, userMessage, Toast.LENGTH_LONG).show();
        }
    }

    /**
     * 记录详细异常日志
     * @param errorMessage 日志信息
     * @param throwable 异常对象
     */
    public static void logError(String errorMessage, Throwable throwable) {
        Logger.e(TAG, errorMessage, throwable);
    }

    /**
     * 记录详细异常日志（无异常对象）
     * @param errorMessage 日志信息
     */
    public static void logError(String errorMessage) {
        Logger.e(TAG, errorMessage);
    }

    /**
     * 同时提示用户和记录日志
     * @param context 上下文
     * @param userMessage 用户可理解的错误信息
     * @param errorMessage 日志详细信息
     * @param throwable 异常对象
     */
    public static void showAndLog(Context context, String userMessage, String errorMessage, Throwable throwable) {
        showUserError(context, userMessage);
        logError(errorMessage, throwable);
    }

    /**
     * 使用全局异常处理器处理异常（推荐方式）
     * @param context 上下文
     * @param operation 操作描述
     * @param exception 异常对象
     * @param showToUser 是否向用户显示错误
     */
    public static void handleException(Context context, String operation, Exception exception, boolean showToUser) {
        GlobalExceptionHandler.getInstance(context).handleException(operation, exception, showToUser);
    }

    /**
     * 记录用户操作
     * @param action 用户操作
     * @param details 操作详情
     */
    public static void logUserAction(String action, String details) {
        Logger.logUserAction(TAG, action, details);
    }

    /**
     * 记录数据操作
     * @param operation 操作类型
     * @param dataType 数据类型
     * @param count 数据数量
     */
    public static void logDataOperation(String operation, String dataType, int count) {
        Logger.logDataOperation(TAG, operation, dataType, count);
    }
}