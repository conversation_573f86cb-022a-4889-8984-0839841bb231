package com.wardrobe.app.utils;

import android.content.Context;
import android.util.Log;
import android.widget.Toast;

/**
 * 统一错误提示与日志工具类
 */
public class ErrorUtils {
    private static final String TAG = "WardrobeError";

    /**
     * 显示用户友好的错误提示
     * @param context 上下文
     * @param userMessage 用户可理解的错误信息
     */
    public static void showUserError(Context context, String userMessage) {
        if (context != null && userMessage != null && !userMessage.isEmpty()) {
            Toast.makeText(context, userMessage, Toast.LENGTH_LONG).show();
        }
    }

    /**
     * 记录详细异常日志
     * @param errorMessage 日志信息
     * @param throwable 异常对象
     */
    public static void logError(String errorMessage, Throwable throwable) {
        if (throwable != null) {
            Log.e(TAG, errorMessage, throwable);
        } else {
            Log.e(TAG, errorMessage);
        }
    }

    /**
     * 同时提示用户和记录日志
     * @param context 上下文
     * @param userMessage 用户可理解的错误信息
     * @param errorMessage 日志详细信息
     * @param throwable 异常对象
     */
    public static void showAndLog(Context context, String userMessage, String errorMessage, Throwable throwable) {
        showUserError(context, userMessage);
        logError(errorMessage, throwable);
    }
} 