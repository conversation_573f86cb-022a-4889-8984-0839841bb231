// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class SpinnerItemWithIconBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView spinnerIcon;

  @NonNull
  public final TextView spinnerText;

  private SpinnerItemWithIconBinding(@NonNull LinearLayout rootView, @NonNull ImageView spinnerIcon,
      @NonNull TextView spinnerText) {
    this.rootView = rootView;
    this.spinnerIcon = spinnerIcon;
    this.spinnerText = spinnerText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static SpinnerItemWithIconBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static SpinnerItemWithIconBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.spinner_item_with_icon, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static SpinnerItemWithIconBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.spinner_icon;
      ImageView spinnerIcon = ViewBindings.findChildViewById(rootView, id);
      if (spinnerIcon == null) {
        break missingId;
      }

      id = R.id.spinner_text;
      TextView spinnerText = ViewBindings.findChildViewById(rootView, id);
      if (spinnerText == null) {
        break missingId;
      }

      return new SpinnerItemWithIconBinding((LinearLayout) rootView, spinnerIcon, spinnerText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
