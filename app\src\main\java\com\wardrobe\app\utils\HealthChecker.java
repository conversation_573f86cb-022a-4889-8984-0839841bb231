package com.wardrobe.app.utils;

import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Environment;
import android.os.StatFs;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 应用健康检查工具
 * 检查应用运行环境和状态
 */
public class HealthChecker {
    
    private static final String TAG = "HealthChecker";
    private static volatile HealthChecker instance;
    private static final Object LOCK = new Object();
    
    private final Context context;
    
    /**
     * 健康检查结果
     */
    public static class HealthReport {
        public final boolean isHealthy;
        public final List<String> issues;
        public final List<String> warnings;
        public final String summary;
        
        public HealthReport(boolean isHealthy, List<String> issues, List<String> warnings) {
            this.isHealthy = isHealthy;
            this.issues = new ArrayList<>(issues);
            this.warnings = new ArrayList<>(warnings);
            this.summary = generateSummary();
        }
        
        private String generateSummary() {
            StringBuilder sb = new StringBuilder();
            sb.append("健康状态: ").append(isHealthy ? "良好" : "异常").append("\n");
            sb.append("严重问题: ").append(issues.size()).append("个\n");
            sb.append("警告: ").append(warnings.size()).append("个");
            return sb.toString();
        }
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("=== 应用健康报告 ===\n");
            sb.append(summary).append("\n\n");
            
            if (!issues.isEmpty()) {
                sb.append("严重问题:\n");
                for (String issue : issues) {
                    sb.append("- ").append(issue).append("\n");
                }
                sb.append("\n");
            }
            
            if (!warnings.isEmpty()) {
                sb.append("警告:\n");
                for (String warning : warnings) {
                    sb.append("- ").append(warning).append("\n");
                }
            }
            
            return sb.toString();
        }
    }
    
    private HealthChecker(Context context) {
        this.context = context.getApplicationContext();
    }
    
    /**
     * 获取单例实例
     */
    public static HealthChecker getInstance(Context context) {
        if (instance == null) {
            synchronized (LOCK) {
                if (instance == null) {
                    instance = new HealthChecker(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * 执行完整的健康检查
     */
    public HealthReport performHealthCheck() {
        List<String> issues = new ArrayList<>();
        List<String> warnings = new ArrayList<>();
        
        // 检查存储空间
        checkStorageSpace(issues, warnings);
        
        // 检查内存状态
        checkMemoryStatus(issues, warnings);
        
        // 检查权限状态
        checkPermissions(issues, warnings);
        
        // 检查系统兼容性
        checkSystemCompatibility(issues, warnings);
        
        // 检查应用完整性
        checkAppIntegrity(issues, warnings);
        
        // 检查崩溃历史
        checkCrashHistory(issues, warnings);
        
        boolean isHealthy = issues.isEmpty();
        
        HealthReport report = new HealthReport(isHealthy, issues, warnings);
        Logger.i(TAG, "健康检查完成: " + report.summary);
        
        return report;
    }
    
    /**
     * 检查存储空间
     */
    private void checkStorageSpace(List<String> issues, List<String> warnings) {
        try {
            // 检查内部存储
            File internalDir = context.getFilesDir();
            StatFs internalStat = new StatFs(internalDir.getPath());
            long internalAvailable = internalStat.getAvailableBytes();
            long internalTotal = internalStat.getTotalBytes();
            double internalUsagePercent = (double)(internalTotal - internalAvailable) / internalTotal * 100;
            
            if (internalAvailable < 50 * 1024 * 1024) { // 少于50MB
                issues.add("内部存储空间不足: " + (internalAvailable / 1024 / 1024) + "MB");
            } else if (internalUsagePercent > 90) {
                warnings.add("内部存储使用率过高: " + String.format("%.1f%%", internalUsagePercent));
            }
            
            // 检查外部存储
            if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
                File externalDir = Environment.getExternalStorageDirectory();
                StatFs externalStat = new StatFs(externalDir.getPath());
                long externalAvailable = externalStat.getAvailableBytes();
                
                if (externalAvailable < 100 * 1024 * 1024) { // 少于100MB
                    warnings.add("外部存储空间不足: " + (externalAvailable / 1024 / 1024) + "MB");
                }
            } else {
                warnings.add("外部存储不可用");
            }
            
        } catch (Exception e) {
            issues.add("存储空间检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查内存状态
     */
    private void checkMemoryStatus(List<String> issues, List<String> warnings) {
        try {
            MemoryOptimizer memoryOptimizer = MemoryOptimizer.getInstance(context);
            MemoryOptimizer.MemoryInfo memoryInfo = memoryOptimizer.getCurrentMemoryInfo();
            
            if (memoryInfo.isCriticalMemory) {
                issues.add("内存严重不足: " + String.format("%.1f%%", memoryInfo.usagePercentage));
            } else if (memoryInfo.isLowMemory) {
                warnings.add("内存不足: " + String.format("%.1f%%", memoryInfo.usagePercentage));
            }
            
            // 检查内存泄漏风险
            if (memoryOptimizer.checkMemoryLeakRisk()) {
                warnings.add("检测到潜在的内存泄漏风险");
            }
            
        } catch (Exception e) {
            issues.add("内存状态检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查权限状态
     */
    private void checkPermissions(List<String> issues, List<String> warnings) {
        try {
            // 检查相机权限
            if (context.checkSelfPermission(android.Manifest.permission.CAMERA) 
                    != PackageManager.PERMISSION_GRANTED) {
                warnings.add("相机权限未授予");
            }
            
            // 检查存储权限
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                if (context.checkSelfPermission(android.Manifest.permission.READ_MEDIA_IMAGES) 
                        != PackageManager.PERMISSION_GRANTED) {
                    warnings.add("媒体访问权限未授予");
                }
            } else {
                if (context.checkSelfPermission(android.Manifest.permission.READ_EXTERNAL_STORAGE) 
                        != PackageManager.PERMISSION_GRANTED) {
                    warnings.add("存储访问权限未授予");
                }
            }
            
        } catch (Exception e) {
            issues.add("权限检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查系统兼容性
     */
    private void checkSystemCompatibility(List<String> issues, List<String> warnings) {
        try {
            // 检查API级别
            if (Build.VERSION.SDK_INT < 21) { // Android 5.0
                issues.add("系统版本过低: API " + Build.VERSION.SDK_INT);
            } else if (Build.VERSION.SDK_INT < 23) { // Android 6.0
                warnings.add("系统版本较低，建议升级: API " + Build.VERSION.SDK_INT);
            }
            
            // 检查架构兼容性
            String[] supportedAbis = Build.SUPPORTED_ABIS;
            boolean hasArm = false;
            for (String abi : supportedAbis) {
                if (abi.startsWith("arm")) {
                    hasArm = true;
                    break;
                }
            }
            if (!hasArm) {
                warnings.add("设备架构可能不完全兼容: " + String.join(", ", supportedAbis));
            }
            
        } catch (Exception e) {
            issues.add("系统兼容性检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查应用完整性
     */
    private void checkAppIntegrity(List<String> issues, List<String> warnings) {
        try {
            // 检查关键文件是否存在
            File filesDir = context.getFilesDir();
            if (!filesDir.exists() || !filesDir.canWrite()) {
                issues.add("应用数据目录不可访问");
            }
            
            // 检查数据库完整性（如果有的话）
            // 这里可以添加具体的数据库检查逻辑
            
        } catch (Exception e) {
            issues.add("应用完整性检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查崩溃历史
     */
    private void checkCrashHistory(List<String> issues, List<String> warnings) {
        try {
            CrashReporter crashReporter = CrashReporter.getInstance(context);
            int crashCount = crashReporter.getCrashFileCount();
            
            if (crashCount > 10) {
                issues.add("崩溃次数过多: " + crashCount + "次");
            } else if (crashCount > 5) {
                warnings.add("存在多次崩溃记录: " + crashCount + "次");
            }
            
        } catch (Exception e) {
            warnings.add("崩溃历史检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 快速健康检查（仅检查关键项目）
     */
    public boolean quickHealthCheck() {
        try {
            // 检查内存
            MemoryOptimizer memoryOptimizer = MemoryOptimizer.getInstance(context);
            if (memoryOptimizer.getCurrentMemoryInfo().isCriticalMemory) {
                return false;
            }
            
            // 检查存储
            File filesDir = context.getFilesDir();
            if (!filesDir.exists() || !filesDir.canWrite()) {
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            Logger.e(TAG, "快速健康检查失败", e);
            return false;
        }
    }
}
