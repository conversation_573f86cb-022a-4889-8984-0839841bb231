package com.wardrobe.app;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.wardrobe.app.data.SharedPreferencesManager;
import com.wardrobe.app.model.ClothingItem;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

public class ClothingManager {

    private static final String CLOTHING_LIST_KEY = "clothing_list";
    private final SharedPreferencesManager sharedPreferencesManager;
    private final Gson gson;
    private static ClothingManager instance;

    public ClothingManager(SharedPreferencesManager sharedPreferencesManager) {
        this.sharedPreferencesManager = sharedPreferencesManager;
        this.gson = new Gson();
    }

    public List<ClothingItem> getClothingList() {
        String json = sharedPreferencesManager.readString(CLOTHING_LIST_KEY, "[]");
        Type type = new TypeToken<ArrayList<ClothingItem>>() {}.getType();
        List<ClothingItem> items = gson.fromJson(json, type);
        return items != null ? items : new ArrayList<>();
    }

    private void saveClothingList(List<ClothingItem> items) {
        String json = gson.toJson(items);
        sharedPreferencesManager.writeString(CLOTHING_LIST_KEY, json);
    }

    public void addClothingItem(ClothingItem item) {
        List<ClothingItem> items = getClothingList();
        items.add(0, item); // Add to the top of the list
        saveClothingList(items);
    }

    public void updateClothingItem(ClothingItem itemToUpdate) {
        List<ClothingItem> items = getClothingList();
        for (int i = 0; i < items.size(); i++) {
            if (items.get(i).getId().equals(itemToUpdate.getId())) {
                items.set(i, itemToUpdate);
                saveClothingList(items);
                return;
            }
        }
    }

    public ClothingItem getClothingItemById(String id) {
        List<ClothingItem> items = getClothingList();
        for (ClothingItem item : items) {
            if (item.getId().equals(id)) {
                return item;
            }
        }
        return null;
    }

    public static ClothingManager getInstance(android.content.Context context) {
        if (instance == null) {
            instance = new ClothingManager(new com.wardrobe.app.data.SharedPreferencesManager(context));
        }
        return instance;
    }

    public static ClothingManager getInstance(android.app.Application application) {
        return getInstance((android.content.Context) application);
    }

    public void addMultipleItems(List<ClothingItem> itemsToAdd) {
        List<ClothingItem> items = getClothingList();
        items.addAll(0, itemsToAdd);
        saveClothingList(items);
    }

    public List<ClothingItem> getAllClothingItems() {
        return getClothingList();
    }

    public void deleteClothingItem(ClothingItem item) {
        List<ClothingItem> items = getClothingList();
        items.removeIf(i -> i.getId().equals(item.getId()));
        saveClothingList(items);
    }

    public List<ClothingItem> searchClothingItems(String query) {
        List<ClothingItem> items = getClothingList();
        List<ClothingItem> result = new ArrayList<>();
        for (ClothingItem item : items) {
            if (item.getName() != null && item.getName().contains(query)) {
                result.add(item);
            }
        }
        return result;
    }

    public List<ClothingItem> getClothingItemsByCategory(String category) {
        List<ClothingItem> items = getClothingList();
        List<ClothingItem> result = new ArrayList<>();
        for (ClothingItem item : items) {
            if (item.getCategory() != null && item.getCategory().equals(category)) {
                result.add(item);
            }
        }
        return result;
    }

    public List<ClothingItem> getClothingByMainCategory(String mainCategory) {
        return getClothingItemsByCategory(mainCategory);
    }
}