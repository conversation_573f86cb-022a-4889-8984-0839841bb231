package com.wardrobe.app.utils;

import android.content.Context;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import android.util.Patterns;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.regex.Pattern;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

/**
 * 安全工具类
 * 提供数据加密、输入验证和安全检查功能
 */
public class SecurityUtils {
    
    private static final String TAG = "SecurityUtils";
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";
    
    // 允许的图片格式
    private static final String[] ALLOWED_IMAGE_EXTENSIONS = {".jpg", ".jpeg", ".png", ".webp"};
    
    // 输入长度限制
    private static final int MAX_NAME_LENGTH = 100;
    private static final int MAX_DESCRIPTION_LENGTH = 500;
    
    // 正则表达式模式
    private static final Pattern SAFE_FILENAME_PATTERN = Pattern.compile("^[a-zA-Z0-9._-]+$");
    private static final Pattern COLOR_PATTERN = Pattern.compile("^#[0-9A-Fa-f]{6}$");

    /**
     * 验证衣物数据的安全性
     * @param name 衣物名称
     * @param description 描述
     * @param imagePath 图片路径
     * @return 验证结果
     */
    public static ValidationResult validateClothingData(String name, String description, String imagePath) {
        ValidationResult result = new ValidationResult();
        
        // 验证名称
        if (!validateName(name)) {
            result.addError("衣物名称不能为空且长度不能超过" + MAX_NAME_LENGTH + "个字符");
        }
        
        // 验证描述
        if (!validateDescription(description)) {
            result.addError("描述长度不能超过" + MAX_DESCRIPTION_LENGTH + "个字符");
        }
        
        // 验证图片路径
        if (!TextUtils.isEmpty(imagePath) && !validateImagePath(imagePath)) {
            result.addError("图片路径无效或文件格式不支持");
        }
        
        return result;
    }

    /**
     * 验证衣物名称
     * @param name 名称
     * @return 是否有效
     */
    public static boolean validateName(String name) {
        return !TextUtils.isEmpty(name) && name.length() <= MAX_NAME_LENGTH;
    }

    /**
     * 验证描述
     * @param description 描述
     * @return 是否有效
     */
    public static boolean validateDescription(String description) {
        return description == null || description.length() <= MAX_DESCRIPTION_LENGTH;
    }

    /**
     * 验证图片路径
     * @param imagePath 图片路径
     * @return 是否有效
     */
    public static boolean validateImagePath(String imagePath) {
        if (TextUtils.isEmpty(imagePath)) {
            return false;
        }
        
        File file = new File(imagePath);
        if (!file.exists()) {
            return false;
        }
        
        // 🚀 移除文件大小限制，因为所有图片都会被智能压缩
        // 检查文件扩展名
        String fileName = file.getName().toLowerCase();
        for (String extension : ALLOWED_IMAGE_EXTENSIONS) {
            if (fileName.endsWith(extension)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 验证颜色值
     * @param color 颜色值
     * @return 是否有效
     */
    public static boolean validateColor(String color) {
        return TextUtils.isEmpty(color) || COLOR_PATTERN.matcher(color).matches();
    }

    /**
     * 验证文件名安全性
     * @param fileName 文件名
     * @return 是否安全
     */
    public static boolean validateFileName(String fileName) {
        return !TextUtils.isEmpty(fileName) && SAFE_FILENAME_PATTERN.matcher(fileName).matches();
    }

    /**
     * 生成安全的文件名
     * @param originalName 原始文件名
     * @return 安全的文件名
     */
    public static String generateSafeFileName(String originalName) {
        if (TextUtils.isEmpty(originalName)) {
            return generateRandomFileName();
        }
        
        // 移除危险字符
        String safeName = originalName.replaceAll("[^a-zA-Z0-9._-]", "_");
        
        // 确保文件名不为空
        if (TextUtils.isEmpty(safeName)) {
            return generateRandomFileName();
        }
        
        return safeName;
    }

    /**
     * 生成随机文件名
     * @return 随机文件名
     */
    public static String generateRandomFileName() {
        SecureRandom random = new SecureRandom();
        byte[] bytes = new byte[16];
        random.nextBytes(bytes);
        return Base64.encodeToString(bytes, Base64.URL_SAFE | Base64.NO_WRAP) + ".jpg";
    }

    /**
     * 计算文件MD5哈希值
     * @param file 文件
     * @return MD5哈希值
     */
    public static String calculateFileMD5(File file) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            FileInputStream fis = new FileInputStream(file);
            byte[] buffer = new byte[8192];
            int bytesRead;
            
            while ((bytesRead = fis.read(buffer)) != -1) {
                md.update(buffer, 0, bytesRead);
            }
            
            fis.close();
            byte[] digest = md.digest();
            
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            
            return sb.toString();
        } catch (NoSuchAlgorithmException | IOException e) {
            Log.e(TAG, "计算文件MD5失败", e);
            return null;
        }
    }

    /**
     * 加密数据
     * @param data 要加密的数据
     * @param key 加密密钥
     * @return 加密后的数据
     */
    public static String encryptData(String data, String key) {
        if (TextUtils.isEmpty(data) || TextUtils.isEmpty(key)) {
            return null;
        }
        
        try {
            SecretKey secretKey = generateKey(key);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] encryptedBytes = cipher.doFinal(data.getBytes());
            return Base64.encodeToString(encryptedBytes, Base64.DEFAULT);
        } catch (Exception e) {
            Log.e(TAG, "数据加密失败", e);
            return null;
        }
    }

    /**
     * 解密数据
     * @param encryptedData 加密的数据
     * @param key 解密密钥
     * @return 解密后的数据
     */
    public static String decryptData(String encryptedData, String key) {
        if (TextUtils.isEmpty(encryptedData) || TextUtils.isEmpty(key)) {
            return null;
        }
        
        try {
            SecretKey secretKey = generateKey(key);
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decryptedBytes = cipher.doFinal(Base64.decode(encryptedData, Base64.DEFAULT));
            return new String(decryptedBytes);
        } catch (Exception e) {
            Log.e(TAG, "数据解密失败", e);
            return null;
        }
    }

    /**
     * 生成加密密钥
     * @param password 密码
     * @return 密钥
     */
    private static SecretKey generateKey(String password) throws Exception {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hash = digest.digest(password.getBytes("UTF-8"));
        return new SecretKeySpec(hash, ALGORITHM);
    }

    /**
     * 安全删除文件
     * @param file 要删除的文件
     * @return 是否成功
     */
    public static boolean secureDeleteFile(File file) {
        if (!file.exists()) {
            return true;
        }
        
        try {
            // 先覆盖文件内容
            if (file.isFile()) {
                FileOutputStream fos = new FileOutputStream(file);
                byte[] zeros = new byte[8192];
                long fileSize = file.length();
                long written = 0;
                
                while (written < fileSize) {
                    int toWrite = (int) Math.min(zeros.length, fileSize - written);
                    fos.write(zeros, 0, toWrite);
                    written += toWrite;
                }
                
                fos.close();
            }
            
            // 然后删除文件
            return file.delete();
        } catch (IOException e) {
            Log.e(TAG, "安全删除文件失败", e);
            return file.delete(); // 尝试普通删除
        }
    }

    /**
     * 检查应用权限
     * @param context 上下文
     * @param permission 权限
     * @return 是否有权限
     */
    public static boolean hasPermission(Context context, String permission) {
        return context.checkSelfPermission(permission) == android.content.pm.PackageManager.PERMISSION_GRANTED;
    }

    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private final StringBuilder errors = new StringBuilder();
        
        public void addError(String error) {
            if (errors.length() > 0) {
                errors.append("\n");
            }
            errors.append(error);
        }
        
        public boolean isValid() {
            return errors.length() == 0;
        }
        
        public String getErrorMessage() {
            return errors.toString();
        }
        
        @Override
        public String toString() {
            return isValid() ? "验证通过" : "验证失败: " + getErrorMessage();
        }
    }
} 