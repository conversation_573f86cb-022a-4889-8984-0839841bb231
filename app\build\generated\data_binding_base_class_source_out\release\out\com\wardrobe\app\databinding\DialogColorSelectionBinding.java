// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogColorSelectionBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final RecyclerView colorRecyclerView;

  private DialogColorSelectionBinding(@NonNull FrameLayout rootView,
      @NonNull RecyclerView colorRecyclerView) {
    this.rootView = rootView;
    this.colorRecyclerView = colorRecyclerView;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogColorSelectionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogColorSelectionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_color_selection, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogColorSelectionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.color_recycler_view;
      RecyclerView colorRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (colorRecyclerView == null) {
        break missingId;
      }

      return new DialogColorSelectionBinding((FrameLayout) rootView, colorRecyclerView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
