package com.wardrobe.app.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.media.ExifInterface;
import android.net.Uri;
import android.util.LruCache;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 图片优化管理器
 * 提供图片压缩、缓存、旋转等功能
 * 优化内存使用和加载性能
 */
public class ImageOptimizer {
    
    private static final String TAG = "ImageOptimizer";
    private static volatile ImageOptimizer instance;
    private static final Object LOCK = new Object();
    
    // 图片配置
    private static final int MAX_IMAGE_WIDTH = 1024;
    private static final int MAX_IMAGE_HEIGHT = 1024;
    private static final int JPEG_QUALITY = 85;
    private static final Bitmap.CompressFormat COMPRESS_FORMAT = Bitmap.CompressFormat.JPEG;
    
    // LRU缓存配置
    private static final int CACHE_SIZE = (int) (Runtime.getRuntime().maxMemory() / 8); // 使用1/8的可用内存
    
    private final Context context;
    private final LruCache<String, Bitmap> memoryCache;
    private final AsyncTaskManager asyncTaskManager;
    
    /**
     * 图片处理回调接口
     */
    public interface ImageCallback {
        void onSuccess(Bitmap bitmap);
        void onError(Exception error);
    }
    
    /**
     * 图片保存回调接口
     */
    public interface SaveCallback {
        void onSuccess(File savedFile);
        void onError(Exception error);
    }
    
    private ImageOptimizer(Context context) {
        this.context = context.getApplicationContext();
        this.asyncTaskManager = AsyncTaskManager.getInstance();
        
        // 初始化内存缓存
        memoryCache = new LruCache<String, Bitmap>(CACHE_SIZE) {
            @Override
            protected int sizeOf(String key, Bitmap bitmap) {
                return bitmap.getByteCount();
            }
            
            @Override
            protected void entryRemoved(boolean evicted, String key, Bitmap oldValue, Bitmap newValue) {
                if (evicted && oldValue != null && !oldValue.isRecycled()) {
                    Logger.d(TAG, "从缓存中移除图片: " + key);
                }
            }
        };
        
        Logger.d(TAG, "ImageOptimizer 初始化完成，缓存大小: " + (CACHE_SIZE / 1024 / 1024) + "MB");
    }
    
    /**
     * 获取ImageOptimizer实例
     * 
     * @param context 上下文
     * @return ImageOptimizer实例
     */
    public static ImageOptimizer getInstance(Context context) {
        if (instance == null) {
            synchronized (LOCK) {
                if (instance == null) {
                    instance = new ImageOptimizer(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * 异步加载和优化图片
     * 
     * @param imageUri 图片URI
     * @param callback 回调接口
     */
    public void loadOptimizedImage(Uri imageUri, ImageCallback callback) {
        if (imageUri == null) {
            callback.onError(new IllegalArgumentException("图片URI不能为空"));
            return;
        }
        
        String cacheKey = imageUri.toString();
        
        // 先从缓存中查找
        Bitmap cachedBitmap = memoryCache.get(cacheKey);
        if (cachedBitmap != null && !cachedBitmap.isRecycled()) {
            Logger.d(TAG, "从缓存加载图片: " + cacheKey);
            callback.onSuccess(cachedBitmap);
            return;
        }
        
        // 异步加载和处理图片
        asyncTaskManager.executeIO(() -> {
            try {
                Bitmap optimizedBitmap = loadAndOptimizeBitmap(imageUri);
                
                // 添加到缓存
                if (optimizedBitmap != null) {
                    memoryCache.put(cacheKey, optimizedBitmap);
                    Logger.d(TAG, "图片加载并缓存成功: " + cacheKey);
                }
                
                return optimizedBitmap;
                
            } catch (Exception e) {
                Logger.e(TAG, "加载图片失败: " + imageUri, e);
                throw e;
            }
        }, new AsyncTaskManager.TaskCallback<Bitmap>() {
            @Override
            public void onSuccess(Bitmap bitmap) {
                if (bitmap != null) {
                    callback.onSuccess(bitmap);
                } else {
                    callback.onError(new RuntimeException("图片加载失败"));
                }
            }
            
            @Override
            public void onError(Exception error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * 异步压缩并保存图片
     * 
     * @param sourceUri 源图片URI
     * @param targetFile 目标文件
     * @param callback 回调接口
     */
    public void compressAndSaveImage(Uri sourceUri, File targetFile, SaveCallback callback) {
        asyncTaskManager.executeIO(() -> {
            try {
                // 加载并优化图片
                Bitmap optimizedBitmap = loadAndOptimizeBitmap(sourceUri);
                if (optimizedBitmap == null) {
                    throw new RuntimeException("无法加载源图片");
                }
                
                // 保存压缩后的图片
                FileOutputStream fos = new FileOutputStream(targetFile);
                boolean success = optimizedBitmap.compress(COMPRESS_FORMAT, JPEG_QUALITY, fos);
                fos.close();
                
                if (!success) {
                    throw new RuntimeException("图片压缩保存失败");
                }
                
                Logger.d(TAG, "图片压缩保存成功: " + targetFile.getAbsolutePath());
                return targetFile;
                
            } catch (Exception e) {
                Logger.e(TAG, "压缩保存图片失败", e);
                throw e;
            }
        }, new AsyncTaskManager.TaskCallback<File>() {
            @Override
            public void onSuccess(File file) {
                callback.onSuccess(file);
            }
            
            @Override
            public void onError(Exception error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * 加载并优化Bitmap
     * 
     * @param imageUri 图片URI
     * @return 优化后的Bitmap
     * @throws IOException IO异常
     */
    private Bitmap loadAndOptimizeBitmap(Uri imageUri) throws IOException {
        InputStream inputStream = context.getContentResolver().openInputStream(imageUri);
        if (inputStream == null) {
            throw new IOException("无法打开图片流");
        }
        
        try {
            // 第一次解析，获取图片尺寸
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inJustDecodeBounds = true;
            BitmapFactory.decodeStream(inputStream, null, options);
            inputStream.close();
            
            // 计算采样率
            options.inSampleSize = calculateInSampleSize(options, MAX_IMAGE_WIDTH, MAX_IMAGE_HEIGHT);
            options.inJustDecodeBounds = false;
            options.inPreferredConfig = Bitmap.Config.RGB_565; // 使用RGB_565减少内存占用
            
            // 第二次解析，加载压缩后的图片
            inputStream = context.getContentResolver().openInputStream(imageUri);
            Bitmap bitmap = BitmapFactory.decodeStream(inputStream, null, options);
            inputStream.close();
            
            if (bitmap == null) {
                throw new IOException("图片解码失败");
            }
            
            // 处理图片旋转
            bitmap = handleImageRotation(bitmap, imageUri);
            
            Logger.d(TAG, String.format("图片优化完成: %dx%d, 内存占用: %dKB", 
                    bitmap.getWidth(), bitmap.getHeight(), bitmap.getByteCount() / 1024));
            
            return bitmap;
            
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }
    
    /**
     * 计算图片采样率
     * 
     * @param options BitmapFactory.Options
     * @param reqWidth 目标宽度
     * @param reqHeight 目标高度
     * @return 采样率
     */
    private int calculateInSampleSize(BitmapFactory.Options options, int reqWidth, int reqHeight) {
        final int height = options.outHeight;
        final int width = options.outWidth;
        int inSampleSize = 1;
        
        if (height > reqHeight || width > reqWidth) {
            final int halfHeight = height / 2;
            final int halfWidth = width / 2;
            
            while ((halfHeight / inSampleSize) >= reqHeight && (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2;
            }
        }
        
        Logger.d(TAG, String.format("原始尺寸: %dx%d, 采样率: %d", width, height, inSampleSize));
        return inSampleSize;
    }
    
    /**
     * 处理图片旋转
     * 
     * @param bitmap 原始Bitmap
     * @param imageUri 图片URI
     * @return 旋转后的Bitmap
     */
    private Bitmap handleImageRotation(Bitmap bitmap, Uri imageUri) {
        try {
            InputStream inputStream = context.getContentResolver().openInputStream(imageUri);
            if (inputStream == null) {
                return bitmap;
            }
            
            ExifInterface exif = new ExifInterface(inputStream);
            int orientation = exif.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL);
            inputStream.close();
            
            Matrix matrix = new Matrix();
            switch (orientation) {
                case ExifInterface.ORIENTATION_ROTATE_90:
                    matrix.postRotate(90);
                    break;
                case ExifInterface.ORIENTATION_ROTATE_180:
                    matrix.postRotate(180);
                    break;
                case ExifInterface.ORIENTATION_ROTATE_270:
                    matrix.postRotate(270);
                    break;
                default:
                    return bitmap; // 不需要旋转
            }
            
            Bitmap rotatedBitmap = Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
            if (rotatedBitmap != bitmap) {
                bitmap.recycle(); // 回收原始bitmap
            }
            
            Logger.d(TAG, "图片旋转处理完成，角度: " + (orientation * 90));
            return rotatedBitmap;
            
        } catch (Exception e) {
            Logger.e(TAG, "处理图片旋转失败", e);
            return bitmap; // 返回原始bitmap
        }
    }
    
    /**
     * 从缓存中获取图片
     * 
     * @param key 缓存键
     * @return Bitmap或null
     */
    public Bitmap getBitmapFromCache(String key) {
        return memoryCache.get(key);
    }
    
    /**
     * 将图片添加到缓存
     * 
     * @param key 缓存键
     * @param bitmap Bitmap
     */
    public void addBitmapToCache(String key, Bitmap bitmap) {
        if (getBitmapFromCache(key) == null && bitmap != null) {
            memoryCache.put(key, bitmap);
            Logger.d(TAG, "图片添加到缓存: " + key);
        }
    }
    
    /**
     * 清除内存缓存
     */
    public void clearMemoryCache() {
        memoryCache.evictAll();
        Logger.d(TAG, "内存缓存已清除");
    }
    
    /**
     * 获取缓存使用情况
     * 
     * @return 缓存信息
     */
    public String getCacheInfo() {
        return String.format("缓存使用: %dKB/%dKB, 命中率: %.2f%%", 
                memoryCache.size() / 1024, 
                memoryCache.maxSize() / 1024,
                (memoryCache.hitCount() * 100.0f) / (memoryCache.hitCount() + memoryCache.missCount()));
    }
    
    /**
     * 预加载图片
     * 
     * @param imageUris 图片URI列表
     */
    public void preloadImages(java.util.List<Uri> imageUris) {
        for (Uri uri : imageUris) {
            loadOptimizedImage(uri, new ImageCallback() {
                @Override
                public void onSuccess(Bitmap bitmap) {
                    // 图片已加载到缓存中
                }
                
                @Override
                public void onError(Exception error) {
                    Logger.w(TAG, "预加载图片失败: " + uri);
                }
            });
        }
    }
}
