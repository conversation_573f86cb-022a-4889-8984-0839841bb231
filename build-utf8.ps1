# PowerShell script to build with UTF-8 encoding
# Solves Chinese character encoding issues in Windows

Write-Host "Setting UTF-8 encoding environment..." -ForegroundColor Green
$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "Starting project build..." -ForegroundColor Yellow
& .\gradlew clean compileDebugJava

Write-Host "Build completed!" -ForegroundColor Green
