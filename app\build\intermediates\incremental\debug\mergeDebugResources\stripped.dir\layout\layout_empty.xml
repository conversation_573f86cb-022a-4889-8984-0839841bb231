<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="32dp">

    <ImageView
        android:id="@+id/iv_empty"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:alpha="0.6"
        android:src="@drawable/ic_empty_box"
        android:tint="@color/text_tertiary" />

    <TextView
        android:id="@+id/tv_empty_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:text="暂无数据"
        android:textColor="@color/text_primary"
        android:textSize="18sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_empty_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center"
        android:text="还没有任何内容"
        android:textColor="@color/text_secondary"
        android:textSize="14sp" />

</LinearLayout>
