<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="incidents">

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/adapters/CategorySpinnerAdapter.java"
            line="58"
            column="57"
            startOffset="2106"
            endLine="58"
            endColumn="68"
            endOffset="2117"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/CrashReporter.java"
            line="246"
            column="16"
            startOffset="7509"
            endLine="247"
            endColumn="64"
            endOffset="7632"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/FileSecurityManager.java"
            line="158"
            column="78"
            startOffset="5110"
            endLine="158"
            endColumn="89"
            endOffset="5121"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/HealthChecker.java"
            line="140"
            column="46"
            startOffset="4284"
            endLine="140"
            endColumn="91"
            endOffset="4329"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/HealthChecker.java"
            line="170"
            column="41"
            startOffset="5494"
            endLine="170"
            endColumn="92"
            endOffset="5545"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/HealthChecker.java"
            line="172"
            column="41"
            startOffset="5637"
            endLine="172"
            endColumn="92"
            endOffset="5688"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/ImageCompressor.java"
            line="363"
            column="16"
            startOffset="12043"
            endLine="363"
            endColumn="96"
            endOffset="12123"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/ImageCompressor.java"
            line="421"
            column="20"
            startOffset="14127"
            endLine="422"
            endColumn="67"
            endOffset="14250"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/ImageManager.java"
            line="177"
            column="41"
            startOffset="5128"
            endLine="177"
            endColumn="52"
            endOffset="5139"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/ImageManager.java"
            line="193"
            column="16"
            startOffset="5662"
            endLine="193"
            endColumn="96"
            endOffset="5742"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/ImageOptimizer.java"
            line="232"
            column="27"
            startOffset="7374"
            endLine="233"
            endColumn="89"
            endOffset="7506"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/ImageOptimizer.java"
            line="266"
            column="23"
            startOffset="8435"
            endLine="266"
            endColumn="89"
            endOffset="8501"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/ImageOptimizer.java"
            line="354"
            column="16"
            startOffset="10988"
            endLine="357"
            endColumn="104"
            endOffset="11228"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/InputValidator.java"
            line="304"
            column="35"
            startOffset="9241"
            endLine="304"
            endColumn="46"
            endOffset="9252"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/Logger.java"
            line="207"
            column="16"
            startOffset="4614"
            endLine="207"
            endColumn="80"
            endOffset="4678"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/Logger.java"
            line="230"
            column="16"
            startOffset="5200"
            endLine="230"
            endColumn="70"
            endOffset="5254"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/Logger.java"
            line="274"
            column="30"
            startOffset="6390"
            endLine="275"
            endColumn="54"
            endOffset="6483"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/Logger.java"
            line="279"
            column="34"
            startOffset="6575"
            endLine="279"
            endColumn="94"
            endOffset="6635"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/MemoryOptimizer.java"
            line="68"
            column="20"
            startOffset="2139"
            endLine="70"
            endColumn="46"
            endOffset="2328"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/MemoryOptimizer.java"
            line="319"
            column="21"
            startOffset="9520"
            endLine="320"
            endColumn="51"
            endOffset="9622"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/MemoryOptimizer.java"
            line="323"
            column="21"
            startOffset="9697"
            endLine="323"
            endColumn="87"
            endOffset="9763"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/MemoryOptimizer.java"
            line="324"
            column="21"
            startOffset="9786"
            endLine="324"
            endColumn="86"
            endOffset="9851"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/MemoryOptimizer.java"
            line="326"
            column="21"
            startOffset="9960"
            endLine="326"
            endColumn="88"
            endOffset="10027"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/PerformanceMonitor.java"
            line="59"
            column="20"
            startOffset="1870"
            endLine="60"
            endColumn="75"
            endOffset="1979"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/PerformanceMonitor.java"
            line="224"
            column="20"
            startOffset="6936"
            endLine="227"
            endColumn="91"
            endOffset="7245"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/RecyclerViewOptimizer.java"
            line="344"
            column="31"
            startOffset="11692"
            endLine="345"
            endColumn="65"
            endOffset="11811"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/SecurityUtils.java"
            line="107"
            column="42"
            startOffset="2980"
            endLine="107"
            endColumn="53"
            endOffset="2991"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="252"
            column="35"
            startOffset="8439"
            endLine="252"
            endColumn="46"
            endOffset="8450"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="299"
            column="20"
            startOffset="9773"
            endLine="299"
            endColumn="90"
            endOffset="9843"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="377"
            column="58"
            startOffset="12486"
            endLine="377"
            endColumn="69"
            endOffset="12497"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="378"
            column="66"
            startOffset="12590"
            endLine="378"
            endColumn="77"
            endOffset="12601"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="379"
            column="72"
            startOffset="12700"
            endLine="379"
            endColumn="83"
            endOffset="12711"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="380"
            column="60"
            startOffset="12798"
            endLine="380"
            endColumn="71"
            endOffset="12809"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="381"
            column="66"
            startOffset="12902"
            endLine="381"
            endColumn="77"
            endOffset="12913"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="382"
            column="60"
            startOffset="13000"
            endLine="382"
            endColumn="71"
            endOffset="13011"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="383"
            column="60"
            startOffset="13098"
            endLine="383"
            endColumn="71"
            endOffset="13109"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="385"
            column="56"
            startOffset="13258"
            endLine="385"
            endColumn="67"
            endOffset="13269"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/fragments/WardrobeFragment.java"
            line="408"
            column="39"
            startOffset="14232"
            endLine="408"
            endColumn="50"
            endOffset="14243"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/fragments/WardrobeFragment.java"
            line="412"
            column="67"
            startOffset="14532"
            endLine="412"
            endColumn="78"
            endOffset="14543"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/fragments/WardrobeFragment.java"
            line="413"
            column="78"
            startOffset="14645"
            endLine="413"
            endColumn="89"
            endOffset="14656"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/fragments/WardrobeFragment.java"
            line="414"
            column="84"
            startOffset="14764"
            endLine="414"
            endColumn="95"
            endOffset="14775"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/fragments/WardrobeFragment.java"
            line="415"
            column="72"
            startOffset="14871"
            endLine="415"
            endColumn="83"
            endOffset="14882"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/fragments/WardrobeFragment.java"
            line="416"
            column="78"
            startOffset="14984"
            endLine="416"
            endColumn="89"
            endOffset="14995"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="235"
            column="39"
            startOffset="7495"
            endLine="235"
            endColumn="50"
            endOffset="7506"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="261"
            column="54"
            startOffset="8406"
            endLine="261"
            endColumn="65"
            endOffset="8417"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="264"
            column="62"
            startOffset="8535"
            endLine="264"
            endColumn="73"
            endOffset="8546"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="267"
            column="68"
            startOffset="8670"
            endLine="267"
            endColumn="79"
            endOffset="8681"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="270"
            column="56"
            startOffset="8793"
            endLine="270"
            endColumn="67"
            endOffset="8804"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="273"
            column="62"
            startOffset="8922"
            endLine="273"
            endColumn="73"
            endOffset="8933"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="276"
            column="56"
            startOffset="9045"
            endLine="276"
            endColumn="67"
            endOffset="9056"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="281"
            column="40"
            startOffset="9238"
            endLine="281"
            endColumn="51"
            endOffset="9249"/>
    </incident>

    <incident
        id="ExifInterface"
        severity="warning"
        message="Avoid using `android.media.ExifInterface`; use `androidx.exifinterface.media.ExifInterface` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/ImageCompressor.java"
            line="7"
            column="8"
            startOffset="175"
            endLine="7"
            endColumn="35"
            endOffset="202"/>
    </incident>

    <incident
        id="ExifInterface"
        severity="warning"
        message="Avoid using `android.media.ExifInterface`; use `androidx.exifinterface.media.ExifInterface` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/ImageOptimizer.java"
            line="7"
            column="8"
            startOffset="175"
            endLine="7"
            endColumn="35"
            endOffset="202"/>
    </incident>

    <incident
        id="IntentReset"
        severity="warning"
        message="Calling `setType` after setting URI in `Intent` constructor will clear the data: Call `setDataAndType` instead?">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/activities/BatchAddClothingActivity.java"
            line="116"
            column="16"
            startOffset="4715"
            endLine="116"
            endColumn="34"
            endOffset="4733"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/activities/BatchAddClothingActivity.java"
            line="114"
            column="56"
            startOffset="4593"
            endLine="114"
            endColumn="100"
            endOffset="4637"
            message="Originally set here"/>
    </incident>

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 35"
            oldString="34"
            replacement="35"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="13"
            column="9"
            startOffset="272"
            endLine="13"
            endColumn="21"
            endOffset="284"/>
    </incident>

    <incident
        id="WrongConstant"
        severity="error"
        message="Must be one of: Toast.LENGTH_SHORT, Toast.LENGTH_LONG">
        <fix-alternatives>
            <fix-replace
                description="Change to Toast.LENGTH_SHORT"
                replacement="android.widget.Toast.LENGTH_SHORT"
                shortenNames="true"
                priority="0"/>
            <fix-replace
                description="Change to Toast.LENGTH_LONG"
                replacement="android.widget.Toast.LENGTH_LONG"
                shortenNames="true"
                priority="0"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/common/UserFeedbackManager.java"
            line="144"
            column="60"
            startOffset="3723"
            endLine="144"
            endColumn="87"
            endOffset="3750"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_accessories.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_bottoms.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_dresses.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_outerwear.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sets.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_shoes.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_tops.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_bag.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_belt.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_boots.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_bra.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_coat.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_dress.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_formal_pants.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_formal_shoes.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_functional.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_hat.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_hoodie.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jacket.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jeans.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jewelry.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jumpsuit.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_outer_vest.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pajamas.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pants.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_polo.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sandals.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_scarf.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_shirt.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_shorts.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_skirt.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sneakers.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sport_pants.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sport_shoes.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_suit.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sweater.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_tracksuit.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_tshirt.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_vest.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.10.1 is available: 8.11.0">
        <fix-replace
            description="Change to 8.11.0"
            family="Update versions"
            oldString="8.10.1"
            replacement="8.11.0"
            priority="0"/>
        <location
            file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="17"
            endLine="2"
            endColumn="15"
            endOffset="25"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.security:security-crypto than 1.1.0-alpha06 is available: 1.1.0-beta01">
        <fix-replace
            description="Change to 1.1.0-beta01"
            family="Update versions"
            oldString="1.1.0-alpha06"
            replacement="1.1.0-beta01"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="100"
            column="20"
            startOffset="2516"
            endLine="100"
            endColumn="69"
            endOffset="2565"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test:core than 1.5.0 is available: 1.6.1">
        <fix-replace
            description="Change to 1.6.1"
            family="Update versions"
            oldString="1.5.0"
            replacement="1.6.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="111"
            column="24"
            startOffset="2928"
            endLine="111"
            endColumn="50"
            endOffset="2954"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.1.5"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="112"
            column="24"
            startOffset="2978"
            endLine="112"
            endColumn="55"
            endOffset="3009"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test:runner than 1.5.2 is available: 1.6.2">
        <fix-replace
            description="Change to 1.6.2"
            family="Update versions"
            oldString="1.5.2"
            replacement="1.6.2"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="115"
            column="31"
            startOffset="3150"
            endLine="115"
            endColumn="59"
            endOffset="3178"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test:rules than 1.5.0 is available: 1.6.1">
        <fix-replace
            description="Change to 1.6.1"
            family="Update versions"
            oldString="1.5.0"
            replacement="1.6.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="116"
            column="31"
            startOffset="3209"
            endLine="116"
            endColumn="58"
            endOffset="3236"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.16.0">
        <fix-replace
            description="Change to 1.16.0"
            family="Update versions"
            oldString="1.10.1"
            replacement="1.16.0"
            priority="0"/>
        <location
            file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
            line="4"
            column="11"
            startOffset="54"
            endLine="4"
            endColumn="19"
            endOffset="62"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.2.1">
        <fix-replace
            description="Change to 1.2.1"
            family="Update versions"
            oldString="1.1.5"
            replacement="1.2.1"
            priority="0"/>
        <location
            file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
            line="6"
            column="16"
            startOffset="95"
            endLine="6"
            endColumn="23"
            endOffset="102"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.6.1">
        <fix-replace
            description="Change to 3.6.1"
            family="Update versions"
            oldString="3.5.1"
            replacement="3.6.1"
            priority="0"/>
        <location
            file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
            line="7"
            column="16"
            startOffset="118"
            endLine="7"
            endColumn="23"
            endOffset="125"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.activity:activity-compose than 1.8.0 is available: 1.10.1">
        <fix-replace
            description="Change to 1.10.1"
            family="Update versions"
            oldString="1.8.0"
            replacement="1.10.1"
            priority="0"/>
        <location
            file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
            line="9"
            column="19"
            startOffset="174"
            endLine="9"
            endColumn="26"
            endOffset="181"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.compose:compose-bom than 2024.09.00 is available: 2025.06.01">
        <fix-replace
            description="Change to 2025.06.01"
            family="Update versions"
            oldString="2024.09.00"
            replacement="2025.06.01"
            priority="0"/>
        <location
            file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
            line="10"
            column="14"
            startOffset="195"
            endLine="10"
            endColumn="26"
            endOffset="207"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0">
        <fix-replace
            description="Change to 2.11.0"
            family="Update versions"
            oldString="2.10.1"
            replacement="2.11.0"
            priority="0"/>
        <location
            file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
            line="15"
            column="8"
            startOffset="302"
            endLine="15"
            endColumn="16"
            endOffset="310"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-compiler than 2.6.1 is available: 2.7.2">
        <fix-replace
            description="Change to 2.7.2"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.2"
            priority="0"/>
        <location
            file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
            line="17"
            column="8"
            startOffset="335"
            endLine="17"
            endColumn="15"
            endOffset="342"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-ktx than 2.6.1 is available: 2.7.2">
        <fix-replace
            description="Change to 2.7.2"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.2"
            priority="0"/>
        <location
            file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
            line="17"
            column="8"
            startOffset="335"
            endLine="17"
            endColumn="15"
            endOffset="342"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.room:room-runtime than 2.6.1 is available: 2.7.2">
        <fix-replace
            description="Change to 2.7.2"
            family="Update versions"
            oldString="2.6.1"
            replacement="2.7.2"
            priority="0"/>
        <location
            file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
            line="17"
            column="8"
            startOffset="335"
            endLine="17"
            endColumn="15"
            endOffset="342"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-livedata-ktx than 2.7.0 is available: 2.9.1">
        <fix-replace
            description="Change to 2.9.1"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.1"
            priority="0"/>
        <location
            file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
            line="18"
            column="13"
            startOffset="355"
            endLine="18"
            endColumn="20"
            endOffset="362"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.7.0 is available: 2.9.1">
        <fix-replace
            description="Change to 2.9.1"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.1"
            priority="0"/>
        <location
            file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
            line="18"
            column="13"
            startOffset="355"
            endLine="18"
            endColumn="20"
            endOffset="362"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-viewmodel-ktx than 2.7.0 is available: 2.9.1">
        <fix-replace
            description="Change to 2.9.1"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.9.1"
            priority="0"/>
        <location
            file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
            line="18"
            column="13"
            startOffset="355"
            endLine="18"
            endColumn="20"
            endOffset="362"/>
    </incident>

    <incident
        id="SwitchIntDef"
        severity="warning"
        message="Switch statement on an `int` with known associated constant missing case `ComponentCallbacks2.TRIM_MEMORY_RUNNING_CRITICAL`, `ComponentCallbacks2.TRIM_MEMORY_RUNNING_LOW`, `ComponentCallbacks2.TRIM_MEMORY_RUNNING_MODERATE`">
        <fix-data cases="android.content.ComponentCallbacks2.TRIM_MEMORY_RUNNING_CRITICAL, android.content.ComponentCallbacks2.TRIM_MEMORY_RUNNING_LOW, android.content.ComponentCallbacks2.TRIM_MEMORY_RUNNING_MODERATE"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/WardrobeApplication.java"
            line="191"
            column="9"
            startOffset="4927"
            endLine="191"
            endColumn="15"
            endOffset="4933"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`).">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/model/CategoryItem.java"
            line="57"
            column="43"
            startOffset="1388"
            endLine="57"
            endColumn="56"
            endOffset="1401"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`).">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/adapters/CategoryListAdapter.java"
            line="65"
            column="66"
            startOffset="2143"
            endLine="65"
            endColumn="79"
            endOffset="2156"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`).">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/adapters/CategorySpinnerAdapter.java"
            line="59"
            column="54"
            startOffset="2192"
            endLine="59"
            endColumn="67"
            endOffset="2205"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`).">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/adapters/ColorListAdapter.java"
            line="88"
            column="66"
            startOffset="3077"
            endLine="88"
            endColumn="79"
            endOffset="3090"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`).">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/EncodingValidator.java"
            line="108"
            column="42"
            startOffset="3087"
            endLine="108"
            endColumn="55"
            endOffset="3100"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_tertiary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_tertiary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_outfit.xml"
            line="74"
            column="13"
            startOffset="2841"
            endLine="74"
            endColumn="48"
            endOffset="2876"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_tertiary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_tertiary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_wardrobe.xml"
            line="34"
            column="17"
            startOffset="1283"
            endLine="34"
            endColumn="52"
            endOffset="1318"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_tertiary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_tertiary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_wardrobe.xml"
            line="57"
            column="17"
            startOffset="2247"
            endLine="57"
            endColumn="52"
            endOffset="2282"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_tertiary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_tertiary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_wardrobe.xml"
            line="152"
            column="17"
            startOffset="5689"
            endLine="152"
            endColumn="52"
            endOffset="5724"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_tertiary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_tertiary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_category_selector.xml"
            line="38"
            column="9"
            startOffset="1326"
            endLine="38"
            endColumn="44"
            endOffset="1361"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/white&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/white"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_clothing_horizontal.xml"
            line="62"
            column="13"
            startOffset="2265"
            endLine="62"
            endColumn="40"
            endOffset="2292"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_tertiary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_tertiary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_occasion_selector.xml"
            line="36"
            column="9"
            startOffset="1257"
            endLine="36"
            endColumn="44"
            endOffset="1292"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/text_tertiary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/text_tertiary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_empty.xml"
            line="15"
            column="9"
            startOffset="492"
            endLine="15"
            endColumn="44"
            endOffset="527"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/md_theme_light_error&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/md_theme_light_error"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_error.xml"
            line="15"
            column="9"
            startOffset="488"
            endLine="15"
            endColumn="51"
            endOffset="530"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/separator&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/separator"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/list_item_selector_row.xml"
            line="57"
            column="13"
            startOffset="2019"
            endLine="57"
            endColumn="44"
            endOffset="2050"/>
    </incident>

    <incident
        id="UseAppTint"
        severity="error"
        message="Must use `app:tint` instead of `android:tint`">
        <fix-composite
            description="Use `app:tint` instead of `android:tint`">
            <fix-attribute
                description="Set tint=&quot;@color/labelSecondary&quot;"
                namespace="http://schemas.android.com/apk/res-auto"
                attribute="tint"
                value="@color/labelSecondary"/>
            <fix-attribute
                description="Delete tint"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="tint"/>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/list_item_selector_row.xml"
            line="67"
            column="9"
            startOffset="2360"
            endLine="67"
            endColumn="45"
            endOffset="2396"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;dialog_option_camera&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="31"
            column="13"
            startOffset="1176"
            endLine="31"
            endColumn="40"
            endOffset="1203"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;dialog_option_gallery&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="32"
            column="13"
            startOffset="1228"
            endLine="32"
            endColumn="41"
            endOffset="1256"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;dialog_button_cancel&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="33"
            column="13"
            startOffset="1284"
            endLine="33"
            endColumn="40"
            endOffset="1311"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;toast_save_successful&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="36"
            column="13"
            startOffset="1368"
            endLine="36"
            endColumn="41"
            endOffset="1396"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;toast_save_failed&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="37"
            column="13"
            startOffset="1423"
            endLine="37"
            endColumn="37"
            endOffset="1447"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;toast_load_failed&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="38"
            column="13"
            startOffset="1474"
            endLine="38"
            endColumn="37"
            endOffset="1498"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;toast_select_image_first&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="39"
            column="13"
            startOffset="1525"
            endLine="39"
            endColumn="44"
            endOffset="1556"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;toast_image_selection_cancelled&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="40"
            column="13"
            startOffset="1587"
            endLine="40"
            endColumn="51"
            endOffset="1625"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;toast_permissions_denied&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="41"
            column="13"
            startOffset="1655"
            endLine="41"
            endColumn="44"
            endOffset="1686"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;error_load_data&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="44"
            column="13"
            startOffset="1747"
            endLine="44"
            endColumn="35"
            endOffset="1769"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;error_get_storage_info&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="45"
            column="13"
            startOffset="1798"
            endLine="45"
            endColumn="42"
            endOffset="1827"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;success_cleanup_all_images&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="46"
            column="13"
            startOffset="1858"
            endLine="46"
            endColumn="46"
            endOffset="1891"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;error_cleanup_images&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="47"
            column="13"
            startOffset="1933"
            endLine="47"
            endColumn="40"
            endOffset="1960"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;compressing&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="48"
            column="13"
            startOffset="1993"
            endLine="48"
            endColumn="31"
            endOffset="2011"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;compress_all_images&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="49"
            column="13"
            startOffset="2040"
            endLine="49"
            endColumn="39"
            endOffset="2066"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;success_compress_images&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="50"
            column="13"
            startOffset="2095"
            endLine="50"
            endColumn="43"
            endOffset="2125"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;error_compress_images&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="51"
            column="13"
            startOffset="2167"
            endLine="51"
            endColumn="41"
            endOffset="2195"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;error_start_compress&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="52"
            column="13"
            startOffset="2228"
            endLine="52"
            endColumn="40"
            endOffset="2255"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;success_cleanup_unused_images&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="53"
            column="13"
            startOffset="2290"
            endLine="53"
            endColumn="49"
            endOffset="2326"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;error_main_category_missing_batch&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="56"
            column="13"
            startOffset="2401"
            endLine="56"
            endColumn="53"
            endOffset="2441"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;title_batch_add_prefix&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="57"
            column="13"
            startOffset="2476"
            endLine="57"
            endColumn="42"
            endOffset="2505"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;images_selected_count_batch&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="58"
            column="13"
            startOffset="2537"
            endLine="58"
            endColumn="47"
            endOffset="2571"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;error_no_images_selected_batch&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="59"
            column="13"
            startOffset="2604"
            endLine="59"
            endColumn="50"
            endOffset="2641"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;error_incomplete_selection_batch&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="60"
            column="13"
            startOffset="2673"
            endLine="60"
            endColumn="52"
            endOffset="2712"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;success_batch_add_clothing_summary&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="61"
            column="13"
            startOffset="2744"
            endLine="61"
            endColumn="54"
            endOffset="2785"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;success_batch_add_clothing_summary_with_failures&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="62"
            column="13"
            startOffset="2821"
            endLine="62"
            endColumn="68"
            endOffset="2876"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;error_batch_add_all_failed&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="63"
            column="13"
            startOffset="2922"
            endLine="63"
            endColumn="46"
            endOffset="2955"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;info_no_items_to_add_batch&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="64"
            column="13"
            startOffset="2994"
            endLine="64"
            endColumn="46"
            endOffset="3027"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;occasion_sport&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="79"
            column="13"
            startOffset="3592"
            endLine="79"
            endColumn="34"
            endOffset="3613"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;color_custom&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="88"
            column="13"
            startOffset="3958"
            endLine="88"
            endColumn="32"
            endOffset="3977"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;color_beige&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="100"
            column="13"
            startOffset="4474"
            endLine="100"
            endColumn="31"
            endOffset="4492"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;color_khaki&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="101"
            column="13"
            startOffset="4517"
            endLine="101"
            endColumn="31"
            endOffset="4535"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;toast_permission_granted_retry&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="105"
            column="13"
            startOffset="4626"
            endLine="105"
            endColumn="50"
            endOffset="4663"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;toast_permission_denied&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="106"
            column="13"
            startOffset="4697"
            endLine="106"
            endColumn="43"
            endOffset="4727"/>
    </incident>

    <incident
        id="MissingTranslation"
        severity="error"
        message="&quot;toast_create_image_file_failed&quot; is not translated in &quot;en&quot; (English)">
        <fix-attribute
            description="Mark non-translatable"
            attribute="translatable"
            value="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="107"
            column="13"
            startOffset="4762"
            endLine="107"
            endColumn="50"
            endOffset="4799"/>
    </incident>

    <incident
        id="GetInstance"
        severity="warning"
        message="ECB encryption mode should not be used (was &quot;AES/ECB/PKCS5Padding&quot;)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/SecurityUtils.java"
            line="211"
            column="48"
            startOffset="5807"
            endLine="211"
            endColumn="62"
            endOffset="5821"/>
    </incident>

    <incident
        id="GetInstance"
        severity="warning"
        message="ECB encryption mode should not be used (was &quot;AES/ECB/PKCS5Padding&quot;)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/SecurityUtils.java"
            line="234"
            column="48"
            startOffset="6546"
            endLine="234"
            endColumn="62"
            endOffset="6560"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/fragments/CalendarFragment.java"
            line="126"
            column="13"
            startOffset="4013"
            endLine="126"
            endColumn="51"
            endOffset="4051"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/adapters/ClothingListAdapter.java"
            line="60"
            column="9"
            startOffset="1828"
            endLine="60"
            endColumn="31"
            endOffset="1850"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/adapters/ClothingListAdapter.java"
            line="68"
            column="9"
            startOffset="2026"
            endLine="68"
            endColumn="31"
            endOffset="2048"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/adapters/ClothingListAdapter.java"
            line="90"
            column="9"
            startOffset="2674"
            endLine="90"
            endColumn="31"
            endOffset="2696"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/fragments/OutfitFragment.java"
            line="132"
            column="13"
            startOffset="4397"
            endLine="132"
            endColumn="49"
            endOffset="4433"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/RecyclerViewOptimizer.java"
            line="227"
            column="21"
            startOffset="8469"
            endLine="227"
            endColumn="43"
            endOffset="8491"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/adapters/WardrobeGroupAdapter.java"
            line="50"
            column="9"
            startOffset="1568"
            endLine="50"
            endColumn="31"
            endOffset="1590"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is never &lt; 21">
        <fix-data conditional="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/HealthChecker.java"
            line="220"
            column="17"
            startOffset="7210"
            endLine="220"
            endColumn="43"
            endOffset="7236"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/LocalizationManager.java"
            line="301"
            column="13"
            startOffset="8721"
            endLine="301"
            endColumn="72"
            endOffset="8780"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/UIUtils.java"
            line="371"
            column="13"
            startOffset="11037"
            endLine="371"
            endColumn="68"
            endOffset="11092"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is never &lt; 21">
        <fix-data conditional="false"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/UIUtils.java"
            line="384"
            column="29"
            startOffset="11412"
            endLine="384"
            endColumn="81"
            endOffset="11464"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/UIUtils.java"
            line="403"
            column="13"
            startOffset="11991"
            endLine="403"
            endColumn="68"
            endOffset="12046"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/UIUtils.java"
            line="423"
            column="13"
            startOffset="12662"
            endLine="423"
            endColumn="68"
            endOffset="12717"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/list_item_selector_row.xml"
            line="37"
            column="6"
            startOffset="1274"
            endLine="37"
            endColumn="18"
            endOffset="1286"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/spinner_item_with_icon.xml"
            line="2"
            column="2"
            startOffset="41"
            endLine="2"
            endColumn="14"
            endOffset="53"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3399 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_accessories.xml"
            line="10"
            column="25"
            startOffset="401"
            endLine="10"
            endColumn="3424"
            endOffset="3800"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3595 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_accessories.xml"
            line="13"
            column="25"
            startOffset="3873"
            endLine="13"
            endColumn="3620"
            endOffset="7468"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (7757 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_accessories.xml"
            line="16"
            column="25"
            startOffset="7541"
            endLine="16"
            endColumn="7782"
            endOffset="15298"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (8246 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_accessories.xml"
            line="19"
            column="25"
            startOffset="15371"
            endLine="19"
            endColumn="8271"
            endOffset="23617"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (10566 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_accessories.xml"
            line="22"
            column="25"
            startOffset="23690"
            endLine="22"
            endColumn="10591"
            endOffset="34256"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (10561 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_accessories.xml"
            line="25"
            column="25"
            startOffset="34329"
            endLine="25"
            endColumn="10586"
            endOffset="44890"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (10619 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_accessories.xml"
            line="28"
            column="25"
            startOffset="44963"
            endLine="28"
            endColumn="10644"
            endOffset="55582"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2212 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_bottoms.xml"
            line="10"
            column="25"
            startOffset="328"
            endLine="10"
            endColumn="2237"
            endOffset="2540"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1639 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_bottoms.xml"
            line="13"
            column="25"
            startOffset="2613"
            endLine="13"
            endColumn="1664"
            endOffset="4252"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1568 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_bottoms.xml"
            line="16"
            column="25"
            startOffset="4325"
            endLine="16"
            endColumn="1593"
            endOffset="5893"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1258 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_bottoms.xml"
            line="19"
            column="25"
            startOffset="5966"
            endLine="19"
            endColumn="1283"
            endOffset="7224"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (882 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_bottoms.xml"
            line="22"
            column="25"
            startOffset="7297"
            endLine="22"
            endColumn="907"
            endOffset="8179"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3858 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_dresses.xml"
            line="10"
            column="25"
            startOffset="398"
            endLine="10"
            endColumn="3883"
            endOffset="4256"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3920 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_dresses.xml"
            line="13"
            column="25"
            startOffset="4329"
            endLine="13"
            endColumn="3945"
            endOffset="8249"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4134 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_dresses.xml"
            line="16"
            column="25"
            startOffset="8322"
            endLine="16"
            endColumn="4159"
            endOffset="12456"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4171 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_dresses.xml"
            line="19"
            column="25"
            startOffset="12529"
            endLine="19"
            endColumn="4196"
            endOffset="16700"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4418 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_dresses.xml"
            line="22"
            column="25"
            startOffset="16773"
            endLine="22"
            endColumn="4443"
            endOffset="21191"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4379 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_dresses.xml"
            line="25"
            column="25"
            startOffset="21264"
            endLine="25"
            endColumn="4404"
            endOffset="25643"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3700 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_dresses.xml"
            line="28"
            column="25"
            startOffset="25716"
            endLine="28"
            endColumn="3725"
            endOffset="29416"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3143 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_dresses.xml"
            line="31"
            column="25"
            startOffset="29489"
            endLine="31"
            endColumn="3168"
            endOffset="32632"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2664 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_dresses.xml"
            line="34"
            column="25"
            startOffset="32705"
            endLine="34"
            endColumn="2689"
            endOffset="35369"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1624 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_dresses.xml"
            line="37"
            column="25"
            startOffset="35442"
            endLine="37"
            endColumn="1649"
            endOffset="37066"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (837 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_dresses.xml"
            line="40"
            column="25"
            startOffset="37139"
            endLine="40"
            endColumn="862"
            endOffset="37976"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5107 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_outerwear.xml"
            line="10"
            column="25"
            startOffset="398"
            endLine="10"
            endColumn="5132"
            endOffset="5505"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5200 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_outerwear.xml"
            line="13"
            column="25"
            startOffset="5578"
            endLine="13"
            endColumn="5225"
            endOffset="10778"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5520 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_outerwear.xml"
            line="16"
            column="25"
            startOffset="10851"
            endLine="16"
            endColumn="5545"
            endOffset="16371"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4839 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_outerwear.xml"
            line="19"
            column="25"
            startOffset="16444"
            endLine="19"
            endColumn="4864"
            endOffset="21283"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4687 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_outerwear.xml"
            line="22"
            column="25"
            startOffset="21356"
            endLine="22"
            endColumn="4712"
            endOffset="26043"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3171 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_outerwear.xml"
            line="25"
            column="25"
            startOffset="26116"
            endLine="25"
            endColumn="3196"
            endOffset="29287"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (922 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_outerwear.xml"
            line="28"
            column="25"
            startOffset="29360"
            endLine="28"
            endColumn="947"
            endOffset="30282"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1659 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sets.xml"
            line="10"
            column="25"
            startOffset="401"
            endLine="10"
            endColumn="1684"
            endOffset="2060"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2068 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sets.xml"
            line="13"
            column="25"
            startOffset="2133"
            endLine="13"
            endColumn="2093"
            endOffset="4201"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2073 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sets.xml"
            line="16"
            column="25"
            startOffset="4274"
            endLine="16"
            endColumn="2098"
            endOffset="6347"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2190 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sets.xml"
            line="19"
            column="25"
            startOffset="6420"
            endLine="19"
            endColumn="2215"
            endOffset="8610"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2247 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sets.xml"
            line="22"
            column="25"
            startOffset="8683"
            endLine="22"
            endColumn="2272"
            endOffset="10930"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1915 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sets.xml"
            line="25"
            column="25"
            startOffset="11003"
            endLine="25"
            endColumn="1940"
            endOffset="12918"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1808 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sets.xml"
            line="28"
            column="25"
            startOffset="12991"
            endLine="28"
            endColumn="1833"
            endOffset="14799"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1810 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sets.xml"
            line="31"
            column="25"
            startOffset="14872"
            endLine="31"
            endColumn="1835"
            endOffset="16682"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1913 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sets.xml"
            line="34"
            column="25"
            startOffset="16755"
            endLine="34"
            endColumn="1938"
            endOffset="18668"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1907 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sets.xml"
            line="37"
            column="25"
            startOffset="18741"
            endLine="37"
            endColumn="1932"
            endOffset="20648"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1837 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sets.xml"
            line="40"
            column="25"
            startOffset="20721"
            endLine="40"
            endColumn="1862"
            endOffset="22558"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1737 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sets.xml"
            line="43"
            column="25"
            startOffset="22631"
            endLine="43"
            endColumn="1762"
            endOffset="24368"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1633 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sets.xml"
            line="46"
            column="25"
            startOffset="24441"
            endLine="46"
            endColumn="1658"
            endOffset="26074"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1660 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sets.xml"
            line="49"
            column="25"
            startOffset="26147"
            endLine="49"
            endColumn="1685"
            endOffset="27807"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1598 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sets.xml"
            line="52"
            column="25"
            startOffset="27880"
            endLine="52"
            endColumn="1623"
            endOffset="29478"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1655 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sets.xml"
            line="55"
            column="25"
            startOffset="29551"
            endLine="55"
            endColumn="1680"
            endOffset="31206"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1466 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sets.xml"
            line="58"
            column="25"
            startOffset="31279"
            endLine="58"
            endColumn="1491"
            endOffset="32745"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1433 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sets.xml"
            line="61"
            column="25"
            startOffset="32818"
            endLine="61"
            endColumn="1458"
            endOffset="34251"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1429 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sets.xml"
            line="64"
            column="25"
            startOffset="34324"
            endLine="64"
            endColumn="1454"
            endOffset="35753"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2138 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_shoes.xml"
            line="10"
            column="25"
            startOffset="398"
            endLine="10"
            endColumn="2163"
            endOffset="2536"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2104 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_shoes.xml"
            line="13"
            column="25"
            startOffset="2609"
            endLine="13"
            endColumn="2129"
            endOffset="4713"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2279 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_shoes.xml"
            line="16"
            column="25"
            startOffset="4786"
            endLine="16"
            endColumn="2304"
            endOffset="7065"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2342 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_shoes.xml"
            line="19"
            column="25"
            startOffset="7138"
            endLine="19"
            endColumn="2367"
            endOffset="9480"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2110 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_shoes.xml"
            line="22"
            column="25"
            startOffset="9553"
            endLine="22"
            endColumn="2135"
            endOffset="11663"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2131 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_shoes.xml"
            line="25"
            column="25"
            startOffset="11736"
            endLine="25"
            endColumn="2156"
            endOffset="13867"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1409 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_shoes.xml"
            line="28"
            column="25"
            startOffset="13940"
            endLine="28"
            endColumn="1434"
            endOffset="15349"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1101 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_shoes.xml"
            line="31"
            column="25"
            startOffset="15422"
            endLine="31"
            endColumn="1126"
            endOffset="16523"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1498 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_shoes.xml"
            line="34"
            column="25"
            startOffset="16596"
            endLine="34"
            endColumn="1523"
            endOffset="18094"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3581 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_tops.xml"
            line="10"
            column="25"
            startOffset="374"
            endLine="10"
            endColumn="3606"
            endOffset="3955"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3673 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_tops.xml"
            line="13"
            column="25"
            startOffset="4028"
            endLine="13"
            endColumn="3698"
            endOffset="7701"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3126 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_tops.xml"
            line="16"
            column="25"
            startOffset="7774"
            endLine="16"
            endColumn="3151"
            endOffset="10900"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2693 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_tops.xml"
            line="19"
            column="25"
            startOffset="10973"
            endLine="19"
            endColumn="2718"
            endOffset="13666"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1295 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_tops.xml"
            line="22"
            column="25"
            startOffset="13739"
            endLine="22"
            endColumn="1320"
            endOffset="15034"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1564 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_underwear.xml"
            line="10"
            column="25"
            startOffset="403"
            endLine="10"
            endColumn="1589"
            endOffset="1967"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1851 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_underwear.xml"
            line="13"
            column="25"
            startOffset="2040"
            endLine="13"
            endColumn="1876"
            endOffset="3891"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1316 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_underwear.xml"
            line="16"
            column="25"
            startOffset="3964"
            endLine="16"
            endColumn="1341"
            endOffset="5280"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5267 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_workwear.xml"
            line="7"
            column="25"
            startOffset="236"
            endLine="7"
            endColumn="5292"
            endOffset="5503"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5195 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_workwear.xml"
            line="10"
            column="25"
            startOffset="5576"
            endLine="10"
            endColumn="5220"
            endOffset="10771"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5796 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_workwear.xml"
            line="13"
            column="25"
            startOffset="10844"
            endLine="13"
            endColumn="5821"
            endOffset="16640"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5486 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_workwear.xml"
            line="16"
            column="25"
            startOffset="16713"
            endLine="16"
            endColumn="5511"
            endOffset="22199"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5901 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_workwear.xml"
            line="19"
            column="25"
            startOffset="22272"
            endLine="19"
            endColumn="5926"
            endOffset="28173"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1168 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_profile.xml"
            line="3"
            column="70"
            startOffset="269"
            endLine="3"
            endColumn="1238"
            endOffset="1437"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2647 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_bag.xml"
            line="10"
            column="25"
            startOffset="401"
            endLine="10"
            endColumn="2672"
            endOffset="3048"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2674 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_bag.xml"
            line="13"
            column="25"
            startOffset="3121"
            endLine="13"
            endColumn="2699"
            endOffset="5795"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2906 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_bag.xml"
            line="16"
            column="25"
            startOffset="5868"
            endLine="16"
            endColumn="2931"
            endOffset="8774"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2919 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_bag.xml"
            line="19"
            column="25"
            startOffset="8847"
            endLine="19"
            endColumn="2944"
            endOffset="11766"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2723 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_bag.xml"
            line="22"
            column="25"
            startOffset="11839"
            endLine="22"
            endColumn="2748"
            endOffset="14562"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1907 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_bag.xml"
            line="25"
            column="25"
            startOffset="14635"
            endLine="25"
            endColumn="1932"
            endOffset="16542"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2038 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_bag.xml"
            line="28"
            column="25"
            startOffset="16615"
            endLine="28"
            endColumn="2063"
            endOffset="18653"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1922 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_belt.xml"
            line="10"
            column="25"
            startOffset="401"
            endLine="10"
            endColumn="1947"
            endOffset="2323"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1853 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_belt.xml"
            line="13"
            column="25"
            startOffset="2396"
            endLine="13"
            endColumn="1878"
            endOffset="4249"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1916 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_belt.xml"
            line="16"
            column="25"
            startOffset="4322"
            endLine="16"
            endColumn="1941"
            endOffset="6238"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1901 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_belt.xml"
            line="19"
            column="25"
            startOffset="6311"
            endLine="19"
            endColumn="1926"
            endOffset="8212"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1864 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_belt.xml"
            line="22"
            column="25"
            startOffset="8285"
            endLine="22"
            endColumn="1889"
            endOffset="10149"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1911 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_belt.xml"
            line="25"
            column="25"
            startOffset="10222"
            endLine="25"
            endColumn="1936"
            endOffset="12133"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1909 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_belt.xml"
            line="28"
            column="25"
            startOffset="12206"
            endLine="28"
            endColumn="1934"
            endOffset="14115"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3011 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_boots.xml"
            line="10"
            column="25"
            startOffset="398"
            endLine="10"
            endColumn="3036"
            endOffset="3409"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3247 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_boots.xml"
            line="13"
            column="25"
            startOffset="3482"
            endLine="13"
            endColumn="3272"
            endOffset="6729"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3516 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_boots.xml"
            line="16"
            column="25"
            startOffset="6802"
            endLine="16"
            endColumn="3541"
            endOffset="10318"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3790 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_boots.xml"
            line="19"
            column="25"
            startOffset="10391"
            endLine="19"
            endColumn="3815"
            endOffset="14181"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3548 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_boots.xml"
            line="22"
            column="25"
            startOffset="14254"
            endLine="22"
            endColumn="3573"
            endOffset="17802"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2726 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_boots.xml"
            line="25"
            column="25"
            startOffset="17875"
            endLine="25"
            endColumn="2751"
            endOffset="20601"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2258 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_boots.xml"
            line="28"
            column="25"
            startOffset="20674"
            endLine="28"
            endColumn="2283"
            endOffset="22932"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1057 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_boots.xml"
            line="31"
            column="25"
            startOffset="23005"
            endLine="31"
            endColumn="1082"
            endOffset="24062"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3514 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_bra.xml"
            line="10"
            column="25"
            startOffset="401"
            endLine="10"
            endColumn="3539"
            endOffset="3915"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3313 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_bra.xml"
            line="13"
            column="25"
            startOffset="3988"
            endLine="13"
            endColumn="3338"
            endOffset="7301"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3510 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_bra.xml"
            line="16"
            column="25"
            startOffset="7374"
            endLine="16"
            endColumn="3535"
            endOffset="10884"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3964 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_bra.xml"
            line="19"
            column="25"
            startOffset="10957"
            endLine="19"
            endColumn="3989"
            endOffset="14921"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2274 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_bra.xml"
            line="22"
            column="25"
            startOffset="14994"
            endLine="22"
            endColumn="2299"
            endOffset="17268"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2567 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_bra.xml"
            line="25"
            column="25"
            startOffset="17341"
            endLine="25"
            endColumn="2592"
            endOffset="19908"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1489 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_briefs.xml"
            line="7"
            column="25"
            startOffset="220"
            endLine="7"
            endColumn="1514"
            endOffset="1709"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (7943 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_briefs.xml"
            line="10"
            column="25"
            startOffset="1782"
            endLine="10"
            endColumn="7968"
            endOffset="9725"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1161 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_briefs.xml"
            line="13"
            column="25"
            startOffset="9798"
            endLine="13"
            endColumn="1186"
            endOffset="10959"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1036 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_briefs.xml"
            line="16"
            column="25"
            startOffset="11032"
            endLine="16"
            endColumn="1061"
            endOffset="12068"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1189 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_briefs.xml"
            line="19"
            column="25"
            startOffset="12141"
            endLine="19"
            endColumn="1214"
            endOffset="13330"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1008 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_camisole.xml"
            line="10"
            column="25"
            startOffset="368"
            endLine="10"
            endColumn="1033"
            endOffset="1376"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2020 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_camisole.xml"
            line="13"
            column="25"
            startOffset="1449"
            endLine="13"
            endColumn="2045"
            endOffset="3469"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1550 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_camisole_dress.xml"
            line="16"
            column="25"
            startOffset="2599"
            endLine="16"
            endColumn="1575"
            endOffset="4149"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1562 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_camisole_dress.xml"
            line="19"
            column="25"
            startOffset="4222"
            endLine="19"
            endColumn="1587"
            endOffset="5784"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1217 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_camisole_dress.xml"
            line="22"
            column="25"
            startOffset="5857"
            endLine="22"
            endColumn="1242"
            endOffset="7074"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1268 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_camisole_dress.xml"
            line="25"
            column="25"
            startOffset="7147"
            endLine="25"
            endColumn="1293"
            endOffset="8415"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (7986 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_canvas_shoes.xml"
            line="7"
            column="25"
            startOffset="224"
            endLine="7"
            endColumn="8011"
            endOffset="8210"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (10592 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_canvas_shoes.xml"
            line="10"
            column="25"
            startOffset="8283"
            endLine="10"
            endColumn="10617"
            endOffset="18875"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (24103 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_canvas_shoes.xml"
            line="13"
            column="25"
            startOffset="18948"
            endLine="13"
            endColumn="24128"
            endOffset="43051"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (14747 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_canvas_shoes.xml"
            line="16"
            column="25"
            startOffset="43124"
            endLine="16"
            endColumn="14772"
            endOffset="57871"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (12922 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_canvas_shoes.xml"
            line="19"
            column="25"
            startOffset="57944"
            endLine="19"
            endColumn="12947"
            endOffset="70866"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (6048 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_canvas_shoes.xml"
            line="22"
            column="25"
            startOffset="70939"
            endLine="22"
            endColumn="6073"
            endOffset="76987"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3878 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_canvas_shoes.xml"
            line="25"
            column="25"
            startOffset="77060"
            endLine="25"
            endColumn="3903"
            endOffset="80938"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4503 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_coat.xml"
            line="10"
            column="25"
            startOffset="398"
            endLine="10"
            endColumn="4528"
            endOffset="4901"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4987 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_coat.xml"
            line="13"
            column="25"
            startOffset="4974"
            endLine="13"
            endColumn="5012"
            endOffset="9961"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5149 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_coat.xml"
            line="16"
            column="25"
            startOffset="10034"
            endLine="16"
            endColumn="5174"
            endOffset="15183"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4784 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_coat.xml"
            line="19"
            column="25"
            startOffset="15256"
            endLine="19"
            endColumn="4809"
            endOffset="20040"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3778 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_coat.xml"
            line="22"
            column="25"
            startOffset="20113"
            endLine="22"
            endColumn="3803"
            endOffset="23891"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2769 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_coat.xml"
            line="25"
            column="25"
            startOffset="23964"
            endLine="25"
            endColumn="2794"
            endOffset="26733"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1550 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_coat.xml"
            line="28"
            column="25"
            startOffset="26806"
            endLine="28"
            endColumn="1575"
            endOffset="28356"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (6748 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_dress.xml"
            line="10"
            column="25"
            startOffset="374"
            endLine="10"
            endColumn="6773"
            endOffset="7122"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (6737 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_dress.xml"
            line="13"
            column="25"
            startOffset="7195"
            endLine="13"
            endColumn="6762"
            endOffset="13932"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (6360 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_dress.xml"
            line="16"
            column="25"
            startOffset="14005"
            endLine="16"
            endColumn="6385"
            endOffset="20365"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5205 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_dress.xml"
            line="19"
            column="25"
            startOffset="20438"
            endLine="19"
            endColumn="5230"
            endOffset="25643"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2631 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_dress.xml"
            line="22"
            column="25"
            startOffset="25716"
            endLine="22"
            endColumn="2656"
            endOffset="28347"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1954 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_flared_skirt.xml"
            line="7"
            column="25"
            startOffset="224"
            endLine="7"
            endColumn="1979"
            endOffset="2178"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2090 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_flared_skirt.xml"
            line="10"
            column="25"
            startOffset="2251"
            endLine="10"
            endColumn="2115"
            endOffset="4341"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2195 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_flared_skirt.xml"
            line="13"
            column="25"
            startOffset="4414"
            endLine="13"
            endColumn="2220"
            endOffset="6609"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2173 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_flared_skirt.xml"
            line="16"
            column="25"
            startOffset="6682"
            endLine="16"
            endColumn="2198"
            endOffset="8855"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2071 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_flared_skirt.xml"
            line="19"
            column="25"
            startOffset="8928"
            endLine="19"
            endColumn="2096"
            endOffset="10999"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2103 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_flared_skirt.xml"
            line="22"
            column="25"
            startOffset="11072"
            endLine="22"
            endColumn="2128"
            endOffset="13175"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2998 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_formal_pants.xml"
            line="10"
            column="25"
            startOffset="398"
            endLine="10"
            endColumn="3023"
            endOffset="3396"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (8638 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_formal_shoes.xml"
            line="10"
            column="25"
            startOffset="328"
            endLine="10"
            endColumn="8663"
            endOffset="8966"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (8518 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_formal_shoes.xml"
            line="13"
            column="25"
            startOffset="9039"
            endLine="13"
            endColumn="8543"
            endOffset="17557"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (8516 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_formal_shoes.xml"
            line="16"
            column="25"
            startOffset="17630"
            endLine="16"
            endColumn="8541"
            endOffset="26146"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (8158 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_formal_shoes.xml"
            line="19"
            column="25"
            startOffset="26219"
            endLine="19"
            endColumn="8183"
            endOffset="34377"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (6700 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_formal_shoes.xml"
            line="22"
            column="25"
            startOffset="34450"
            endLine="22"
            endColumn="6725"
            endOffset="41150"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (801 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_functional.xml"
            line="10"
            column="25"
            startOffset="398"
            endLine="10"
            endColumn="826"
            endOffset="1199"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2478 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_functional.xml"
            line="13"
            column="25"
            startOffset="1272"
            endLine="13"
            endColumn="2503"
            endOffset="3750"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4209 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_functional.xml"
            line="16"
            column="25"
            startOffset="3823"
            endLine="16"
            endColumn="4234"
            endOffset="8032"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (7767 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_functional.xml"
            line="19"
            column="25"
            startOffset="8105"
            endLine="19"
            endColumn="7792"
            endOffset="15872"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (9400 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_functional.xml"
            line="22"
            column="25"
            startOffset="15945"
            endLine="22"
            endColumn="9425"
            endOffset="25345"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (9577 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_functional.xml"
            line="25"
            column="25"
            startOffset="25418"
            endLine="25"
            endColumn="9602"
            endOffset="34995"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (8662 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_functional.xml"
            line="28"
            column="25"
            startOffset="35068"
            endLine="28"
            endColumn="8687"
            endOffset="43730"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1896 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_hat.xml"
            line="10"
            column="25"
            startOffset="398"
            endLine="10"
            endColumn="1921"
            endOffset="2294"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1902 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_hat.xml"
            line="13"
            column="25"
            startOffset="2367"
            endLine="13"
            endColumn="1927"
            endOffset="4269"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2126 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_hat.xml"
            line="16"
            column="25"
            startOffset="4342"
            endLine="16"
            endColumn="2151"
            endOffset="6468"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1797 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_hat.xml"
            line="19"
            column="25"
            startOffset="6541"
            endLine="19"
            endColumn="1822"
            endOffset="8338"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1943 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_hat.xml"
            line="22"
            column="25"
            startOffset="8411"
            endLine="22"
            endColumn="1968"
            endOffset="10354"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1501 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_hat.xml"
            line="25"
            column="25"
            startOffset="10427"
            endLine="25"
            endColumn="1526"
            endOffset="11928"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1071 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_hat.xml"
            line="28"
            column="25"
            startOffset="12001"
            endLine="28"
            endColumn="1096"
            endOffset="13072"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2950 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_heels.xml"
            line="7"
            column="25"
            startOffset="224"
            endLine="7"
            endColumn="2975"
            endOffset="3174"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3081 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_heels.xml"
            line="10"
            column="25"
            startOffset="3247"
            endLine="10"
            endColumn="3106"
            endOffset="6328"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2895 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_heels.xml"
            line="13"
            column="25"
            startOffset="6401"
            endLine="13"
            endColumn="2920"
            endOffset="9296"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3022 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_heels.xml"
            line="16"
            column="25"
            startOffset="9369"
            endLine="16"
            endColumn="3047"
            endOffset="12391"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4331 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_heels.xml"
            line="19"
            column="25"
            startOffset="12464"
            endLine="19"
            endColumn="4356"
            endOffset="16795"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (913 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_heels.xml"
            line="22"
            column="25"
            startOffset="16868"
            endLine="22"
            endColumn="938"
            endOffset="17781"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (815 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_heels.xml"
            line="25"
            column="25"
            startOffset="17854"
            endLine="25"
            endColumn="840"
            endOffset="18669"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3678 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_hoodie.xml"
            line="10"
            column="25"
            startOffset="398"
            endLine="10"
            endColumn="3703"
            endOffset="4076"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4087 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_hoodie.xml"
            line="13"
            column="25"
            startOffset="4149"
            endLine="13"
            endColumn="4112"
            endOffset="8236"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4048 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_hoodie.xml"
            line="16"
            column="25"
            startOffset="8309"
            endLine="16"
            endColumn="4073"
            endOffset="12357"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3945 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_hoodie.xml"
            line="19"
            column="25"
            startOffset="12430"
            endLine="19"
            endColumn="3970"
            endOffset="16375"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4176 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_hoodie.xml"
            line="22"
            column="25"
            startOffset="16448"
            endLine="22"
            endColumn="4201"
            endOffset="20624"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2966 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_hoodie.xml"
            line="25"
            column="25"
            startOffset="20697"
            endLine="25"
            endColumn="2991"
            endOffset="23663"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2033 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_hoodie.xml"
            line="28"
            column="25"
            startOffset="23736"
            endLine="28"
            endColumn="2058"
            endOffset="25769"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4927 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jacket.xml"
            line="10"
            column="25"
            startOffset="401"
            endLine="10"
            endColumn="4952"
            endOffset="5328"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5310 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jacket.xml"
            line="13"
            column="25"
            startOffset="5401"
            endLine="13"
            endColumn="5335"
            endOffset="10711"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5860 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jacket.xml"
            line="16"
            column="25"
            startOffset="10784"
            endLine="16"
            endColumn="5885"
            endOffset="16644"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4540 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jacket.xml"
            line="19"
            column="25"
            startOffset="16717"
            endLine="19"
            endColumn="4565"
            endOffset="21257"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1819 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jacket.xml"
            line="22"
            column="25"
            startOffset="21330"
            endLine="22"
            endColumn="1844"
            endOffset="23149"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1173 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jacket.xml"
            line="25"
            column="25"
            startOffset="23222"
            endLine="25"
            endColumn="1198"
            endOffset="24395"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3175 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jeans.xml"
            line="10"
            column="25"
            startOffset="398"
            endLine="10"
            endColumn="3200"
            endOffset="3573"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3762 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jeans.xml"
            line="13"
            column="25"
            startOffset="3646"
            endLine="13"
            endColumn="3787"
            endOffset="7408"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4105 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jeans.xml"
            line="16"
            column="25"
            startOffset="7481"
            endLine="16"
            endColumn="4130"
            endOffset="11586"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4956 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jeans.xml"
            line="19"
            column="25"
            startOffset="11659"
            endLine="19"
            endColumn="4981"
            endOffset="16615"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3172 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jeans.xml"
            line="22"
            column="25"
            startOffset="16688"
            endLine="22"
            endColumn="3197"
            endOffset="19860"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2711 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jeans.xml"
            line="25"
            column="25"
            startOffset="19933"
            endLine="25"
            endColumn="2736"
            endOffset="22644"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1267 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jeans.xml"
            line="28"
            column="25"
            startOffset="22717"
            endLine="28"
            endColumn="1292"
            endOffset="23984"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3006 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jewelry.xml"
            line="10"
            column="25"
            startOffset="380"
            endLine="10"
            endColumn="3031"
            endOffset="3386"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3154 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jewelry.xml"
            line="13"
            column="25"
            startOffset="3459"
            endLine="13"
            endColumn="3179"
            endOffset="6613"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3502 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jewelry.xml"
            line="16"
            column="25"
            startOffset="6686"
            endLine="16"
            endColumn="3527"
            endOffset="10188"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3434 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jewelry.xml"
            line="19"
            column="25"
            startOffset="10261"
            endLine="19"
            endColumn="3459"
            endOffset="13695"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3265 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jewelry.xml"
            line="22"
            column="25"
            startOffset="13768"
            endLine="22"
            endColumn="3290"
            endOffset="17033"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2578 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jewelry.xml"
            line="25"
            column="25"
            startOffset="17106"
            endLine="25"
            endColumn="2603"
            endOffset="19684"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1230 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jewelry.xml"
            line="28"
            column="25"
            startOffset="19757"
            endLine="28"
            endColumn="1255"
            endOffset="20987"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3742 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jumpsuit.xml"
            line="10"
            column="25"
            startOffset="377"
            endLine="10"
            endColumn="3767"
            endOffset="4119"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4223 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jumpsuit.xml"
            line="13"
            column="25"
            startOffset="4192"
            endLine="13"
            endColumn="4248"
            endOffset="8415"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4765 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jumpsuit.xml"
            line="16"
            column="25"
            startOffset="8488"
            endLine="16"
            endColumn="4790"
            endOffset="13253"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3951 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jumpsuit.xml"
            line="19"
            column="25"
            startOffset="13326"
            endLine="19"
            endColumn="3976"
            endOffset="17277"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3517 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jumpsuit.xml"
            line="22"
            column="25"
            startOffset="17350"
            endLine="22"
            endColumn="3542"
            endOffset="20867"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2627 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jumpsuit.xml"
            line="25"
            column="25"
            startOffset="20940"
            endLine="25"
            endColumn="2652"
            endOffset="23567"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1239 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jumpsuit.xml"
            line="28"
            column="25"
            startOffset="23640"
            endLine="28"
            endColumn="1264"
            endOffset="24879"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (7478 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_mermaid_skirt.xml"
            line="7"
            column="25"
            startOffset="224"
            endLine="7"
            endColumn="7503"
            endOffset="7702"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (16908 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_mermaid_skirt.xml"
            line="10"
            column="25"
            startOffset="7775"
            endLine="10"
            endColumn="16933"
            endOffset="24683"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5527 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_mermaid_skirt.xml"
            line="13"
            column="25"
            startOffset="24756"
            endLine="13"
            endColumn="5552"
            endOffset="30283"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (11781 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_mermaid_skirt.xml"
            line="16"
            column="25"
            startOffset="30356"
            endLine="16"
            endColumn="11806"
            endOffset="42137"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (6864 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_mermaid_skirt.xml"
            line="19"
            column="25"
            startOffset="42210"
            endLine="19"
            endColumn="6889"
            endOffset="49074"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (6566 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_mermaid_skirt.xml"
            line="22"
            column="25"
            startOffset="49147"
            endLine="22"
            endColumn="6591"
            endOffset="55713"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4424 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_mermaid_skirt.xml"
            line="25"
            column="25"
            startOffset="55786"
            endLine="25"
            endColumn="4449"
            endOffset="60210"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4970 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_other_acc.xml"
            line="10"
            column="25"
            startOffset="410"
            endLine="10"
            endColumn="4995"
            endOffset="5380"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5465 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_other_acc.xml"
            line="13"
            column="25"
            startOffset="5453"
            endLine="13"
            endColumn="5490"
            endOffset="10918"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5816 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_other_acc.xml"
            line="16"
            column="25"
            startOffset="10991"
            endLine="16"
            endColumn="5841"
            endOffset="16807"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (6156 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_other_acc.xml"
            line="19"
            column="25"
            startOffset="16880"
            endLine="19"
            endColumn="6181"
            endOffset="23036"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5851 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_other_acc.xml"
            line="22"
            column="25"
            startOffset="23109"
            endLine="22"
            endColumn="5876"
            endOffset="28960"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3721 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_other_acc.xml"
            line="25"
            column="25"
            startOffset="29033"
            endLine="25"
            endColumn="3746"
            endOffset="32754"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3629 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_other_acc.xml"
            line="28"
            column="25"
            startOffset="32827"
            endLine="28"
            endColumn="3654"
            endOffset="36456"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3324 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_outer_vest.xml"
            line="10"
            column="25"
            startOffset="377"
            endLine="10"
            endColumn="3349"
            endOffset="3701"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3436 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_outer_vest.xml"
            line="13"
            column="25"
            startOffset="3774"
            endLine="13"
            endColumn="3461"
            endOffset="7210"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3249 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_outer_vest.xml"
            line="16"
            column="25"
            startOffset="7283"
            endLine="16"
            endColumn="3274"
            endOffset="10532"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2499 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_outer_vest.xml"
            line="19"
            column="25"
            startOffset="10605"
            endLine="19"
            endColumn="2524"
            endOffset="13104"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1364 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_outer_vest.xml"
            line="22"
            column="25"
            startOffset="13177"
            endLine="22"
            endColumn="1389"
            endOffset="14541"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1201 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_outer_vest.xml"
            line="25"
            column="25"
            startOffset="14614"
            endLine="25"
            endColumn="1226"
            endOffset="15815"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (6626 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pajamas.xml"
            line="10"
            column="25"
            startOffset="398"
            endLine="10"
            endColumn="6651"
            endOffset="7024"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (6465 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pajamas.xml"
            line="13"
            column="25"
            startOffset="7097"
            endLine="13"
            endColumn="6490"
            endOffset="13562"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (6527 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pajamas.xml"
            line="16"
            column="25"
            startOffset="13635"
            endLine="16"
            endColumn="6552"
            endOffset="20162"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (8654 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pajamas.xml"
            line="19"
            column="25"
            startOffset="20235"
            endLine="19"
            endColumn="8679"
            endOffset="28889"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4236 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pajamas.xml"
            line="22"
            column="25"
            startOffset="28962"
            endLine="22"
            endColumn="4261"
            endOffset="33198"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4203 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pajamas.xml"
            line="25"
            column="25"
            startOffset="33271"
            endLine="25"
            endColumn="4228"
            endOffset="37474"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1533 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pajamas.xml"
            line="28"
            column="25"
            startOffset="37547"
            endLine="28"
            endColumn="1558"
            endOffset="39080"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3272 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pants.xml"
            line="10"
            column="25"
            startOffset="398"
            endLine="10"
            endColumn="3297"
            endOffset="3670"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3638 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pants.xml"
            line="13"
            column="25"
            startOffset="3743"
            endLine="13"
            endColumn="3663"
            endOffset="7381"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3725 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pants.xml"
            line="16"
            column="25"
            startOffset="7454"
            endLine="16"
            endColumn="3750"
            endOffset="11179"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3527 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pants.xml"
            line="19"
            column="25"
            startOffset="11252"
            endLine="19"
            endColumn="3552"
            endOffset="14779"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3264 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pants.xml"
            line="22"
            column="25"
            startOffset="14852"
            endLine="22"
            endColumn="3289"
            endOffset="18116"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2144 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pants.xml"
            line="25"
            column="25"
            startOffset="18189"
            endLine="25"
            endColumn="2169"
            endOffset="20333"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1432 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pencil_skirt.xml"
            line="7"
            column="25"
            startOffset="224"
            endLine="7"
            endColumn="1457"
            endOffset="1656"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1429 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pencil_skirt.xml"
            line="10"
            column="25"
            startOffset="1729"
            endLine="10"
            endColumn="1454"
            endOffset="3158"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1551 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pencil_skirt.xml"
            line="13"
            column="25"
            startOffset="3231"
            endLine="13"
            endColumn="1576"
            endOffset="4782"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1460 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pencil_skirt.xml"
            line="16"
            column="25"
            startOffset="4855"
            endLine="16"
            endColumn="1485"
            endOffset="6315"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (982 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pencil_skirt.xml"
            line="22"
            column="25"
            startOffset="7245"
            endLine="22"
            endColumn="1007"
            endOffset="8227"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1398 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pleated_skirt.xml"
            line="10"
            column="25"
            startOffset="1075"
            endLine="10"
            endColumn="1423"
            endOffset="2473"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (7452 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pleated_skirt.xml"
            line="13"
            column="25"
            startOffset="2546"
            endLine="13"
            endColumn="7477"
            endOffset="9998"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5060 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pleated_skirt.xml"
            line="16"
            column="25"
            startOffset="10071"
            endLine="16"
            endColumn="5085"
            endOffset="15131"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3433 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pleated_skirt.xml"
            line="19"
            column="25"
            startOffset="15204"
            endLine="19"
            endColumn="3458"
            endOffset="18637"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (10797 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pleated_skirt.xml"
            line="22"
            column="25"
            startOffset="18710"
            endLine="22"
            endColumn="10822"
            endOffset="29507"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (13913 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pleated_skirt.xml"
            line="25"
            column="25"
            startOffset="29580"
            endLine="25"
            endColumn="13938"
            endOffset="43493"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2042 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pleated_skirt.xml"
            line="28"
            column="25"
            startOffset="43566"
            endLine="28"
            endColumn="2067"
            endOffset="45608"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5372 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_polo.xml"
            line="10"
            column="25"
            startOffset="398"
            endLine="10"
            endColumn="5397"
            endOffset="5770"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5401 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_polo.xml"
            line="13"
            column="25"
            startOffset="5843"
            endLine="13"
            endColumn="5426"
            endOffset="11244"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5476 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_polo.xml"
            line="16"
            column="25"
            startOffset="11317"
            endLine="16"
            endColumn="5501"
            endOffset="16793"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3354 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_polo.xml"
            line="19"
            column="25"
            startOffset="16866"
            endLine="19"
            endColumn="3379"
            endOffset="20220"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1188 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_polo.xml"
            line="22"
            column="25"
            startOffset="20293"
            endLine="22"
            endColumn="1213"
            endOffset="21481"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1494 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_polo.xml"
            line="25"
            column="25"
            startOffset="21554"
            endLine="25"
            endColumn="1519"
            endOffset="23048"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3003 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_rain_boots.xml"
            line="7"
            column="25"
            startOffset="224"
            endLine="7"
            endColumn="3028"
            endOffset="3227"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5968 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_rain_boots.xml"
            line="10"
            column="25"
            startOffset="3300"
            endLine="10"
            endColumn="5993"
            endOffset="9268"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (6803 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_rain_boots.xml"
            line="13"
            column="25"
            startOffset="9341"
            endLine="13"
            endColumn="6828"
            endOffset="16144"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (7158 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_rain_boots.xml"
            line="16"
            column="25"
            startOffset="16217"
            endLine="16"
            endColumn="7183"
            endOffset="23375"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (8426 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_rain_boots.xml"
            line="19"
            column="25"
            startOffset="23448"
            endLine="19"
            endColumn="8451"
            endOffset="31874"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (8117 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_rain_boots.xml"
            line="22"
            column="25"
            startOffset="31947"
            endLine="22"
            endColumn="8142"
            endOffset="40064"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (8025 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_rain_boots.xml"
            line="25"
            column="25"
            startOffset="40137"
            endLine="25"
            endColumn="8050"
            endOffset="48162"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3247 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sandals.xml"
            line="10"
            column="25"
            startOffset="401"
            endLine="10"
            endColumn="3272"
            endOffset="3648"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2628 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sandals.xml"
            line="13"
            column="25"
            startOffset="3721"
            endLine="13"
            endColumn="2653"
            endOffset="6349"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3108 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sandals.xml"
            line="16"
            column="25"
            startOffset="6422"
            endLine="16"
            endColumn="3133"
            endOffset="9530"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2523 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sandals.xml"
            line="19"
            column="25"
            startOffset="9603"
            endLine="19"
            endColumn="2548"
            endOffset="12126"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (853 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sandals.xml"
            line="22"
            column="25"
            startOffset="12199"
            endLine="22"
            endColumn="878"
            endOffset="13052"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3939 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_scarf.xml"
            line="10"
            column="25"
            startOffset="401"
            endLine="10"
            endColumn="3964"
            endOffset="4340"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (6027 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_scarf.xml"
            line="13"
            column="25"
            startOffset="4413"
            endLine="13"
            endColumn="6052"
            endOffset="10440"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2357 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_scarf.xml"
            line="16"
            column="25"
            startOffset="10513"
            endLine="16"
            endColumn="2382"
            endOffset="12870"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2707 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_scarf.xml"
            line="19"
            column="25"
            startOffset="12943"
            endLine="19"
            endColumn="2732"
            endOffset="15650"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1394 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_scarf.xml"
            line="22"
            column="25"
            startOffset="15723"
            endLine="22"
            endColumn="1419"
            endOffset="17117"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3578 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_shirt.xml"
            line="10"
            column="25"
            startOffset="401"
            endLine="10"
            endColumn="3603"
            endOffset="3979"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4298 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_shirt.xml"
            line="13"
            column="25"
            startOffset="4052"
            endLine="13"
            endColumn="4323"
            endOffset="8350"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4411 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_shirt.xml"
            line="16"
            column="25"
            startOffset="8423"
            endLine="16"
            endColumn="4436"
            endOffset="12834"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3348 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_shirt.xml"
            line="19"
            column="25"
            startOffset="12907"
            endLine="19"
            endColumn="3373"
            endOffset="16255"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3774 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_shirt.xml"
            line="22"
            column="25"
            startOffset="16328"
            endLine="22"
            endColumn="3799"
            endOffset="20102"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1649 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_shirt.xml"
            line="25"
            column="25"
            startOffset="20175"
            endLine="25"
            endColumn="1674"
            endOffset="21824"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (852 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_shirt.xml"
            line="28"
            column="25"
            startOffset="21897"
            endLine="28"
            endColumn="877"
            endOffset="22749"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3040 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_shorts.xml"
            line="10"
            column="25"
            startOffset="398"
            endLine="10"
            endColumn="3065"
            endOffset="3438"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3175 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_shorts.xml"
            line="13"
            column="25"
            startOffset="3511"
            endLine="13"
            endColumn="3200"
            endOffset="6686"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3581 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_shorts.xml"
            line="16"
            column="25"
            startOffset="6759"
            endLine="16"
            endColumn="3606"
            endOffset="10340"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2818 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_shorts.xml"
            line="19"
            column="25"
            startOffset="10413"
            endLine="19"
            endColumn="2843"
            endOffset="13231"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1362 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_shorts.xml"
            line="22"
            column="25"
            startOffset="13304"
            endLine="22"
            endColumn="1387"
            endOffset="14666"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3934 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_skirt.xml"
            line="10"
            column="25"
            startOffset="398"
            endLine="10"
            endColumn="3959"
            endOffset="4332"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4292 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_skirt.xml"
            line="13"
            column="25"
            startOffset="4405"
            endLine="13"
            endColumn="4317"
            endOffset="8697"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4052 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_skirt.xml"
            line="16"
            column="25"
            startOffset="8770"
            endLine="16"
            endColumn="4077"
            endOffset="12822"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4258 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_skirt.xml"
            line="19"
            column="25"
            startOffset="12895"
            endLine="19"
            endColumn="4283"
            endOffset="17153"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5491 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_skirt.xml"
            line="22"
            column="25"
            startOffset="17226"
            endLine="22"
            endColumn="5516"
            endOffset="22717"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (860 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_skirt.xml"
            line="25"
            column="25"
            startOffset="22790"
            endLine="25"
            endColumn="885"
            endOffset="23650"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4106 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_skirt_bottom.xml"
            line="7"
            column="25"
            startOffset="224"
            endLine="7"
            endColumn="4131"
            endOffset="4330"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3902 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_skirt_bottom.xml"
            line="10"
            column="25"
            startOffset="4403"
            endLine="10"
            endColumn="3927"
            endOffset="8305"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (7007 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_skirt_bottom.xml"
            line="13"
            column="25"
            startOffset="8378"
            endLine="13"
            endColumn="7032"
            endOffset="15385"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2759 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_skirt_bottom.xml"
            line="16"
            column="25"
            startOffset="15458"
            endLine="16"
            endColumn="2784"
            endOffset="18217"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3283 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_skirt_bottom.xml"
            line="19"
            column="25"
            startOffset="18290"
            endLine="19"
            endColumn="3308"
            endOffset="21573"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1752 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_skirt_bottom.xml"
            line="22"
            column="25"
            startOffset="21646"
            endLine="22"
            endColumn="1777"
            endOffset="23398"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1461 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_slit_skirt.xml"
            line="7"
            column="25"
            startOffset="224"
            endLine="7"
            endColumn="1486"
            endOffset="1685"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1962 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_slit_skirt.xml"
            line="10"
            column="25"
            startOffset="1758"
            endLine="10"
            endColumn="1987"
            endOffset="3720"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2486 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_slit_skirt.xml"
            line="13"
            column="25"
            startOffset="3793"
            endLine="13"
            endColumn="2511"
            endOffset="6279"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2715 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_slit_skirt.xml"
            line="16"
            column="25"
            startOffset="6352"
            endLine="16"
            endColumn="2740"
            endOffset="9067"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3739 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_slit_skirt.xml"
            line="19"
            column="25"
            startOffset="9140"
            endLine="19"
            endColumn="3764"
            endOffset="12879"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2707 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_slit_skirt.xml"
            line="22"
            column="25"
            startOffset="12952"
            endLine="22"
            endColumn="2732"
            endOffset="15659"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2056 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_slit_skirt.xml"
            line="25"
            column="25"
            startOffset="15732"
            endLine="25"
            endColumn="2081"
            endOffset="17788"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2348 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sneakers.xml"
            line="10"
            column="25"
            startOffset="398"
            endLine="10"
            endColumn="2373"
            endOffset="2746"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3764 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sneakers.xml"
            line="13"
            column="25"
            startOffset="2819"
            endLine="13"
            endColumn="3789"
            endOffset="6583"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (844 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sneakers.xml"
            line="16"
            column="25"
            startOffset="6656"
            endLine="16"
            endColumn="869"
            endOffset="7500"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4965 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_socks.xml"
            line="10"
            column="25"
            startOffset="422"
            endLine="10"
            endColumn="4990"
            endOffset="5387"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5203 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_socks.xml"
            line="13"
            column="25"
            startOffset="5460"
            endLine="13"
            endColumn="5228"
            endOffset="10663"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5811 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_socks.xml"
            line="16"
            column="25"
            startOffset="10736"
            endLine="16"
            endColumn="5836"
            endOffset="16547"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3032 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_socks.xml"
            line="19"
            column="25"
            startOffset="16620"
            endLine="19"
            endColumn="3057"
            endOffset="19652"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2550 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_socks.xml"
            line="22"
            column="25"
            startOffset="19725"
            endLine="22"
            endColumn="2575"
            endOffset="22275"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3330 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sport_pants.xml"
            line="10"
            column="25"
            startOffset="374"
            endLine="10"
            endColumn="3355"
            endOffset="3704"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3250 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sport_pants.xml"
            line="13"
            column="25"
            startOffset="3777"
            endLine="13"
            endColumn="3275"
            endOffset="7027"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3217 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sport_pants.xml"
            line="16"
            column="25"
            startOffset="7100"
            endLine="16"
            endColumn="3242"
            endOffset="10317"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3389 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sport_pants.xml"
            line="19"
            column="25"
            startOffset="10390"
            endLine="19"
            endColumn="3414"
            endOffset="13779"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3858 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sport_pants.xml"
            line="22"
            column="25"
            startOffset="13852"
            endLine="22"
            endColumn="3883"
            endOffset="17710"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3039 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sport_pants.xml"
            line="25"
            column="25"
            startOffset="17783"
            endLine="25"
            endColumn="3064"
            endOffset="20822"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1787 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sport_pants.xml"
            line="28"
            column="25"
            startOffset="20895"
            endLine="28"
            endColumn="1812"
            endOffset="22682"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (8123 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sport_shoes.xml"
            line="10"
            column="25"
            startOffset="401"
            endLine="10"
            endColumn="8148"
            endOffset="8524"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (8183 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sport_shoes.xml"
            line="13"
            column="25"
            startOffset="8597"
            endLine="13"
            endColumn="8208"
            endOffset="16780"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (8086 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sport_shoes.xml"
            line="16"
            column="25"
            startOffset="16853"
            endLine="16"
            endColumn="8111"
            endOffset="24939"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (7105 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sport_shoes.xml"
            line="19"
            column="25"
            startOffset="25012"
            endLine="19"
            endColumn="7130"
            endOffset="32117"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1379 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sport_shoes.xml"
            line="22"
            column="25"
            startOffset="32190"
            endLine="22"
            endColumn="1404"
            endOffset="33569"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2070 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_straight_skirt.xml"
            line="7"
            column="25"
            startOffset="224"
            endLine="7"
            endColumn="2095"
            endOffset="2294"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2134 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_straight_skirt.xml"
            line="10"
            column="25"
            startOffset="2367"
            endLine="10"
            endColumn="2159"
            endOffset="4501"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1895 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_straight_skirt.xml"
            line="13"
            column="25"
            startOffset="4574"
            endLine="13"
            endColumn="1920"
            endOffset="6469"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1932 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_straight_skirt.xml"
            line="16"
            column="25"
            startOffset="6542"
            endLine="16"
            endColumn="1957"
            endOffset="8474"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1826 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_straight_skirt.xml"
            line="19"
            column="25"
            startOffset="8547"
            endLine="19"
            endColumn="1851"
            endOffset="10373"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1859 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_straight_skirt.xml"
            line="22"
            column="25"
            startOffset="10446"
            endLine="22"
            endColumn="1884"
            endOffset="12305"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1921 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_straight_skirt.xml"
            line="25"
            column="25"
            startOffset="12378"
            endLine="25"
            endColumn="1946"
            endOffset="14299"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (8315 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_suit.xml"
            line="10"
            column="25"
            startOffset="398"
            endLine="10"
            endColumn="8340"
            endOffset="8713"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (8248 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_suit.xml"
            line="13"
            column="25"
            startOffset="8786"
            endLine="13"
            endColumn="8273"
            endOffset="17034"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (10557 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_suit.xml"
            line="16"
            column="25"
            startOffset="17107"
            endLine="16"
            endColumn="10582"
            endOffset="27664"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (7341 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_suit.xml"
            line="19"
            column="25"
            startOffset="27737"
            endLine="19"
            endColumn="7366"
            endOffset="35078"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3615 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_suit.xml"
            line="22"
            column="25"
            startOffset="35151"
            endLine="22"
            endColumn="3640"
            endOffset="38766"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1832 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_suit.xml"
            line="25"
            column="25"
            startOffset="38839"
            endLine="25"
            endColumn="1857"
            endOffset="40671"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1482 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_suit.xml"
            line="28"
            column="25"
            startOffset="40744"
            endLine="28"
            endColumn="1507"
            endOffset="42226"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (18776 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sweater.xml"
            line="10"
            column="25"
            startOffset="398"
            endLine="10"
            endColumn="18801"
            endOffset="19174"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (16581 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sweater.xml"
            line="13"
            column="25"
            startOffset="19247"
            endLine="13"
            endColumn="16606"
            endOffset="35828"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (13888 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sweater.xml"
            line="16"
            column="25"
            startOffset="35901"
            endLine="16"
            endColumn="13913"
            endOffset="49789"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (7673 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sweater.xml"
            line="19"
            column="25"
            startOffset="49862"
            endLine="19"
            endColumn="7698"
            endOffset="57535"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3018 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sweater.xml"
            line="22"
            column="25"
            startOffset="57608"
            endLine="22"
            endColumn="3043"
            endOffset="60626"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3150 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_thermal.xml"
            line="10"
            column="25"
            startOffset="400"
            endLine="10"
            endColumn="3175"
            endOffset="3550"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2516 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_thermal.xml"
            line="13"
            column="25"
            startOffset="3623"
            endLine="13"
            endColumn="2541"
            endOffset="6139"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2229 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_thermal.xml"
            line="16"
            column="25"
            startOffset="6212"
            endLine="16"
            endColumn="2254"
            endOffset="8441"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1129 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_thermal.xml"
            line="19"
            column="25"
            startOffset="8514"
            endLine="19"
            endColumn="1154"
            endOffset="9643"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5490 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_tracksuit.xml"
            line="10"
            column="25"
            startOffset="398"
            endLine="10"
            endColumn="5515"
            endOffset="5888"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5764 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_tracksuit.xml"
            line="13"
            column="25"
            startOffset="5961"
            endLine="13"
            endColumn="5789"
            endOffset="11725"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (6081 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_tracksuit.xml"
            line="16"
            column="25"
            startOffset="11798"
            endLine="16"
            endColumn="6106"
            endOffset="17879"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5298 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_tracksuit.xml"
            line="19"
            column="25"
            startOffset="17952"
            endLine="19"
            endColumn="5323"
            endOffset="23250"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3534 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_tracksuit.xml"
            line="22"
            column="25"
            startOffset="23323"
            endLine="22"
            endColumn="3559"
            endOffset="26857"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1336 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_traditional.xml"
            line="7"
            column="25"
            startOffset="224"
            endLine="7"
            endColumn="1361"
            endOffset="1560"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2338 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_traditional.xml"
            line="10"
            column="25"
            startOffset="1633"
            endLine="10"
            endColumn="2363"
            endOffset="3971"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2413 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_traditional.xml"
            line="13"
            column="25"
            startOffset="4044"
            endLine="13"
            endColumn="2438"
            endOffset="6457"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4385 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_traditional.xml"
            line="16"
            column="25"
            startOffset="6530"
            endLine="16"
            endColumn="4410"
            endOffset="10915"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4887 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_traditional.xml"
            line="19"
            column="25"
            startOffset="10988"
            endLine="19"
            endColumn="4912"
            endOffset="15875"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4727 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_traditional.xml"
            line="22"
            column="25"
            startOffset="15948"
            endLine="22"
            endColumn="4752"
            endOffset="20675"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (4412 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_traditional.xml"
            line="25"
            column="25"
            startOffset="20748"
            endLine="25"
            endColumn="4437"
            endOffset="25160"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2486 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_tshirt.xml"
            line="10"
            column="25"
            startOffset="398"
            endLine="10"
            endColumn="2511"
            endOffset="2884"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2706 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_tshirt.xml"
            line="13"
            column="25"
            startOffset="2957"
            endLine="13"
            endColumn="2731"
            endOffset="5663"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2256 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_tshirt.xml"
            line="16"
            column="25"
            startOffset="5736"
            endLine="16"
            endColumn="2281"
            endOffset="7992"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1614 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_tshirt.xml"
            line="19"
            column="25"
            startOffset="8065"
            endLine="19"
            endColumn="1639"
            endOffset="9679"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1260 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_tshirt.xml"
            line="22"
            column="25"
            startOffset="9752"
            endLine="22"
            endColumn="1285"
            endOffset="11012"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1760 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_vest.xml"
            line="10"
            column="25"
            startOffset="374"
            endLine="10"
            endColumn="1785"
            endOffset="2134"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1947 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_vest.xml"
            line="13"
            column="25"
            startOffset="2207"
            endLine="13"
            endColumn="1972"
            endOffset="4154"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1442 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_vest.xml"
            line="16"
            column="25"
            startOffset="4227"
            endLine="16"
            endColumn="1467"
            endOffset="5669"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1249 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_vest.xml"
            line="19"
            column="25"
            startOffset="5742"
            endLine="19"
            endColumn="1274"
            endOffset="6991"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2987 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_wedding.xml"
            line="7"
            column="25"
            startOffset="224"
            endLine="7"
            endColumn="3012"
            endOffset="3211"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (5166 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_wedding.xml"
            line="10"
            column="25"
            startOffset="3284"
            endLine="10"
            endColumn="5191"
            endOffset="8450"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (3727 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_wedding.xml"
            line="13"
            column="25"
            startOffset="8523"
            endLine="13"
            endColumn="3752"
            endOffset="12250"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2355 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_wedding.xml"
            line="16"
            column="25"
            startOffset="12323"
            endLine="16"
            endColumn="2380"
            endOffset="14678"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2952 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_wedding.xml"
            line="19"
            column="25"
            startOffset="14751"
            endLine="19"
            endColumn="2977"
            endOffset="17703"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (2579 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_wedding.xml"
            line="22"
            column="25"
            startOffset="17776"
            endLine="22"
            endColumn="2604"
            endOffset="20355"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1545 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_wedding.xml"
            line="25"
            column="25"
            startOffset="20428"
            endLine="25"
            endColumn="1570"
            endOffset="21973"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/systemGray6` with a theme that also paints a background (inferred theme is `@style/Theme.MaterialComponents.DayNight.NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_clothing.xml"
            line="8"
            column="5"
            startOffset="339"
            endLine="8"
            endColumn="44"
            endOffset="378"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_primary` with a theme that also paints a background (inferred theme is `@style/Theme.MaterialComponents.DayNight.NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_category_select.xml"
            line="7"
            column="5"
            startOffset="347"
            endLine="7"
            endColumn="51"
            endOffset="393"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#F5F5F5` with a theme that also paints a background (inferred theme is `@style/Theme.MaterialComponents.DayNight.NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_clothing_detail.xml"
            line="6"
            column="5"
            startOffset="232"
            endLine="6"
            endColumn="33"
            endOffset="260"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_primary` with a theme that also paints a background (inferred theme is `@style/Theme.MaterialComponents.DayNight.NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_color_select.xml"
            line="7"
            column="5"
            startOffset="347"
            endLine="7"
            endColumn="51"
            endOffset="393"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/systemBackground` with a theme that also paints a background (inferred theme is `@style/Theme.MaterialComponents.DayNight.NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="7"
            column="5"
            startOffset="300"
            endLine="7"
            endColumn="49"
            endOffset="344"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_primary` with a theme that also paints a background (inferred theme is `@style/Theme.MaterialComponents.DayNight.NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_occasion_select.xml"
            line="7"
            column="5"
            startOffset="347"
            endLine="7"
            endColumn="51"
            endOffset="393"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/systemGroupedBackground` with a theme that also paints a background (inferred theme is `@style/Theme.MaterialComponents.DayNight.NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_category_selection.xml"
            line="6"
            column="5"
            startOffset="284"
            endLine="6"
            endColumn="56"
            endOffset="335"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/systemGroupedBackground` with a theme that also paints a background (inferred theme is `@style/Theme.MaterialComponents.DayNight.NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_color_selection.xml"
            line="6"
            column="5"
            startOffset="284"
            endLine="6"
            endColumn="56"
            endOffset="335"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@android:color/black` with a theme that also paints a background (inferred theme is `@style/Theme.MaterialComponents.DayNight.NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_image_fullscreen.xml"
            line="5"
            column="5"
            startOffset="200"
            endLine="5"
            endColumn="46"
            endOffset="241"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/systemGroupedBackground` with a theme that also paints a background (inferred theme is `@style/Theme.MaterialComponents.DayNight.NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_occasion_selection.xml"
            line="6"
            column="5"
            startOffset="284"
            endLine="6"
            endColumn="56"
            endOffset="335"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_primary` with a theme that also paints a background (inferred theme is `@style/Theme.MaterialComponents.DayNight.NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_calendar.xml"
            line="7"
            column="5"
            startOffset="341"
            endLine="7"
            endColumn="51"
            endOffset="387"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_primary` with a theme that also paints a background (inferred theme is `@style/Theme.MaterialComponents.DayNight.NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_outfit.xml"
            line="7"
            column="5"
            startOffset="341"
            endLine="7"
            endColumn="51"
            endOffset="387"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_primary` with a theme that also paints a background (inferred theme is `@style/Theme.MaterialComponents.DayNight.NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_profile.xml"
            line="6"
            column="5"
            startOffset="251"
            endLine="6"
            endColumn="51"
            endOffset="297"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_primary` with a theme that also paints a background (inferred theme is `@style/Theme.MaterialComponents.DayNight.NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_wardrobe.xml"
            line="7"
            column="5"
            startOffset="349"
            endLine="7"
            endColumn="51"
            endOffset="395"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_primary` with a theme that also paints a background (inferred theme is `@style/Theme.MaterialComponents.DayNight.NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_category_selector.xml"
            line="7"
            column="5"
            startOffset="270"
            endLine="7"
            endColumn="51"
            endOffset="316"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/secondarySystemGroupedBackground` with a theme that also paints a background (inferred theme is `@style/Theme.MaterialComponents.DayNight.NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_color_spinner.xml"
            line="8"
            column="5"
            startOffset="306"
            endLine="8"
            endColumn="65"
            endOffset="366"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_primary` with a theme that also paints a background (inferred theme is `@style/Theme.MaterialComponents.DayNight.NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_occasion_selector.xml"
            line="7"
            column="5"
            startOffset="270"
            endLine="7"
            endColumn="51"
            endOffset="316"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/surface_primary` with a theme that also paints a background (inferred theme is `@style/Theme.MaterialComponents.DayNight.NoActionBar`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/multi_select_toolbar.xml"
            line="6"
            column="5"
            startOffset="237"
            endLine="6"
            endColumn="48"
            endOffset="280"/>
    </incident>

    <incident
        id="TypographyEllipsis"
        severity="warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?">
        <fix-replace
            description="Replace with …"
            oldString="..."
            replacement="…"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="48"
            column="32"
            startOffset="2012"
            endLine="48"
            endColumn="38"
            endOffset="2018"/>
    </incident>

    <incident
        id="TypographyEllipsis"
        severity="warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?">
        <fix-replace
            description="Replace with …"
            oldString="..."
            replacement="…"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-en/strings.xml"
            line="98"
            column="36"
            startOffset="4374"
            endLine="98"
            endColumn="46"
            endOffset="4384"/>
    </incident>

    <incident
        id="TypographyEllipsis"
        severity="warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?">
        <fix-replace
            description="Replace with …"
            oldString="..."
            replacement="…"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-en/strings.xml"
            line="113"
            column="32"
            startOffset="5072"
            endLine="113"
            endColumn="50"
            endOffset="5090"/>
    </incident>

    <incident
        id="TypographyEllipsis"
        severity="warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?">
        <fix-replace
            description="Replace with …"
            oldString="..."
            replacement="…"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="142"
            column="36"
            startOffset="6185"
            endLine="142"
            endColumn="42"
            endOffset="6191"/>
    </incident>

    <incident
        id="TypographyEllipsis"
        severity="warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?">
        <fix-replace
            description="Replace with …"
            oldString="..."
            replacement="…"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="157"
            column="32"
            startOffset="6743"
            endLine="157"
            endColumn="39"
            endOffset="6750"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/ic_category_suits.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_suits.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/ic_subcategory_evening_dress.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_evening_dress.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/ic_subcategory_spaghetti_dress.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_spaghetti_dress.png"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_clothing.xml"
            line="90"
            column="22"
            startOffset="3972"
            endLine="90"
            endColumn="30"
            endOffset="3980"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_clothing.xml"
            line="90"
            column="22"
            startOffset="3972"
            endLine="90"
            endColumn="30"
            endOffset="3980"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_wardrobe.xml"
            line="37"
            column="14"
            startOffset="1389"
            endLine="37"
            endColumn="22"
            endOffset="1397"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for security-crypto"
            robot="true">
            <fix-replace
                description="Replace with securityCryptoVersion = &quot;1.1.0-alpha06&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="securityCryptoVersion = &quot;1.1.0-alpha06&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
                    startOffset="381"
                    endOffset="381"/>
            </fix-replace>
            <fix-replace
                description="Replace with security-crypto = { module = &quot;androidx.security:security-crypto&quot;, version.ref = &quot;securityCryptoVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="security-crypto = { module = &quot;androidx.security:security-crypto&quot;, version.ref = &quot;securityCryptoVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
                    startOffset="2966"
                    endOffset="2966"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.security.crypto"
                robot="true"
                replacement="libs.security.crypto"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2516"
                    endOffset="2565"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="100"
            column="20"
            startOffset="2516"
            endLine="100"
            endColumn="69"
            endOffset="2565"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for core-testing"
            robot="true">
            <fix-replace
                description="Replace with coreTestingVersion = &quot;2.2.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="coreTestingVersion = &quot;2.2.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with core-testing = { module = &quot;androidx.arch.core:core-testing&quot;, version.ref = &quot;coreTestingVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="core-testing = { module = &quot;androidx.arch.core:core-testing&quot;, version.ref = &quot;coreTestingVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
                    startOffset="486"
                    endOffset="486"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.core.testing"
                robot="true"
                replacement="libs.core.testing"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2865"
                    endOffset="2904"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="110"
            column="24"
            startOffset="2865"
            endLine="110"
            endColumn="63"
            endOffset="2904"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for core"
            robot="true">
            <fix-replace
                description="Replace with coreVersion = &quot;1.5.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="coreVersion = &quot;1.5.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with core = { module = &quot;androidx.test:core&quot;, version.ref = &quot;coreVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="core = { module = &quot;androidx.test:core&quot;, version.ref = &quot;coreVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
                    startOffset="486"
                    endOffset="486"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.core"
                robot="true"
                replacement="libs.core"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2928"
                    endOffset="2954"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="111"
            column="24"
            startOffset="2928"
            endLine="111"
            endColumn="50"
            endOffset="2954"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use the existing version catalog reference (`libs.androidx.junit`) instead">
        <fix-replace
            description="Replace with existing version catalog reference `androidx-junit`"
            robot="true"
            independent="true"
            replacement="libs.androidx.junit"
            priority="0">
            <range
                file="${:app*projectDir}/build.gradle"
                startOffset="2978"
                endOffset="3009"/>
        </fix-replace>
        <location
            file="${:app*projectDir}/build.gradle"
            line="112"
            column="24"
            startOffset="2978"
            endLine="112"
            endColumn="55"
            endOffset="3009"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for runner"
            robot="true">
            <fix-replace
                description="Replace with runnerVersion = &quot;1.5.2&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="runnerVersion = &quot;1.5.2&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
                    startOffset="381"
                    endOffset="381"/>
            </fix-replace>
            <fix-replace
                description="Replace with runner = { module = &quot;androidx.test:runner&quot;, version.ref = &quot;runnerVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="runner = { module = &quot;androidx.test:runner&quot;, version.ref = &quot;runnerVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
                    startOffset="2966"
                    endOffset="2966"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.runner"
                robot="true"
                replacement="libs.runner"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="3150"
                    endOffset="3178"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="115"
            column="31"
            startOffset="3150"
            endLine="115"
            endColumn="59"
            endOffset="3178"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for rules"
            robot="true">
            <fix-replace
                description="Replace with rulesVersion = &quot;1.5.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="rulesVersion = &quot;1.5.0&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
                    startOffset="381"
                    endOffset="381"/>
            </fix-replace>
            <fix-replace
                description="Replace with rules = { module = &quot;androidx.test:rules&quot;, version.ref = &quot;rulesVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="rules = { module = &quot;androidx.test:rules&quot;, version.ref = &quot;rulesVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
                    startOffset="2966"
                    endOffset="2966"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.rules"
                robot="true"
                replacement="libs.rules"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="3209"
                    endOffset="3236"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="116"
            column="31"
            startOffset="3209"
            endLine="116"
            endColumn="58"
            endOffset="3236"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for quadflask-colorpicker"
            robot="true">
            <fix-replace
                description="Replace with colorpickerVersion = &quot;0.0.13&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="colorpickerVersion = &quot;0.0.13&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with quadflask-colorpicker = { module = &quot;com.github.QuadFlask:colorpicker&quot;, version.ref = &quot;colorpickerVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="quadflask-colorpicker = { module = &quot;com.github.QuadFlask:colorpicker&quot;, version.ref = &quot;colorpickerVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
                    startOffset="2526"
                    endOffset="2526"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.quadflask.colorpicker"
                robot="true"
                replacement="libs.quadflask.colorpicker"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="3285"
                    endOffset="3326"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="119"
            column="20"
            startOffset="3285"
            endLine="119"
            endColumn="61"
            endOffset="3326"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for squareup-leakcanary-android"
            robot="true">
            <fix-replace
                description="Replace with leakcanaryAndroidVersion = &quot;2.12&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="leakcanaryAndroidVersion = &quot;2.12&quot;&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
                    startOffset="126"
                    endOffset="126"/>
            </fix-replace>
            <fix-replace
                description="Replace with squareup-leakcanary-android = { module = &quot;com.squareup.leakcanary:leakcanary-android&quot;, version.ref = &quot;leakcanaryAndroidVersion&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="squareup-leakcanary-android = { module = &quot;com.squareup.leakcanary:leakcanary-android&quot;, version.ref = &quot;leakcanaryAndroidVersion&quot; }&#xA;"
                priority="0">
                <range
                    file="$HOME/AndroidStudioProjects/WardrobeApp/gradle/libs.versions.toml"
                    startOffset="2966"
                    endOffset="2966"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.squareup.leakcanary.android"
                robot="true"
                replacement="libs.squareup.leakcanary.android"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="3352"
                    endOffset="3401"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="121"
            column="25"
            startOffset="3352"
            endLine="121"
            endColumn="74"
            endOffset="3401"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_clothing.xml"
            line="70"
            column="26"
            startOffset="2950"
            endLine="70"
            endColumn="35"
            endOffset="2959"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_add_clothing.xml"
            line="77"
            column="26"
            startOffset="3297"
            endLine="77"
            endColumn="35"
            endOffset="3306"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_image_fullscreen.xml"
            line="7"
            column="6"
            startOffset="251"
            endLine="7"
            endColumn="15"
            endOffset="260"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_image_fullscreen.xml"
            line="15"
            column="6"
            startOffset="519"
            endLine="15"
            endColumn="15"
            endOffset="528"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_calendar.xml"
            line="68"
            column="10"
            startOffset="2625"
            endLine="68"
            endColumn="19"
            endOffset="2634"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_outfit.xml"
            line="69"
            column="10"
            startOffset="2658"
            endLine="69"
            endColumn="19"
            endOffset="2667"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_wardrobe.xml"
            line="147"
            column="14"
            startOffset="5466"
            endLine="147"
            endColumn="23"
            endOffset="5475"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_wardrobe.xml"
            line="176"
            column="6"
            startOffset="6502"
            endLine="176"
            endColumn="75"
            endOffset="6571"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_clothing_horizontal.xml"
            line="55"
            column="10"
            startOffset="1971"
            endLine="55"
            endColumn="19"
            endOffset="1980"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_color_selector.xml"
            line="13"
            column="6"
            startOffset="495"
            endLine="13"
            endColumn="15"
            endOffset="504"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_color_spinner.xml"
            line="10"
            column="6"
            startOffset="376"
            endLine="10"
            endColumn="15"
            endOffset="385"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_empty.xml"
            line="9"
            column="6"
            startOffset="291"
            endLine="9"
            endColumn="15"
            endOffset="300"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_error.xml"
            line="9"
            column="6"
            startOffset="291"
            endLine="9"
            endColumn="15"
            endOffset="300"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/list_item_selector_row.xml"
            line="53"
            column="10"
            startOffset="1859"
            endLine="53"
            endColumn="19"
            endOffset="1868"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/list_item_selector_row.xml"
            line="60"
            column="6"
            startOffset="2083"
            endLine="60"
            endColumn="15"
            endOffset="2092"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/spinner_item_with_icon.xml"
            line="10"
            column="6"
            startOffset="362"
            endLine="10"
            endColumn="15"
            endOffset="371"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/activities/ClothingDetailActivity.java"
            line="131"
            column="39"
            startOffset="4725"
            endLine="131"
            endColumn="74"
            endOffset="4760"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/fragments/OutfitFragment.java"
            line="136"
            column="34"
            startOffset="4533"
            endLine="136"
            endColumn="70"
            endOffset="4569"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/fragments/ProfileFragment.java"
            line="150"
            column="35"
            startOffset="4755"
            endLine="150"
            endColumn="75"
            endOffset="4795"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/fragments/ProfileFragment.java"
            line="151"
            column="34"
            startOffset="4831"
            endLine="151"
            endColumn="78"
            endOffset="4875"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/fragments/ProfileFragment.java"
            line="152"
            column="37"
            startOffset="4914"
            endLine="152"
            endColumn="68"
            endOffset="4945"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/adapters/WardrobeGroupAdapter.java"
            line="97"
            column="33"
            startOffset="3294"
            endLine="97"
            endColumn="52"
            endOffset="3313"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;删除&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/action_mode_menu.xml"
            line="8"
            column="9"
            startOffset="292"
            endLine="8"
            endColumn="27"
            endOffset="310"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;批量添加衣物&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_batch_add_clothing.xml"
            line="14"
            column="9"
            startOffset="521"
            endLine="14"
            endColumn="30"
            endOffset="542"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;备注 (可选，应用于所有批量衣物)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_batch_add_clothing.xml"
            line="34"
            column="13"
            startOffset="1495"
            endLine="34"
            endColumn="45"
            endOffset="1527"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;衣物故事 (可选，应用于所有批量衣物)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_batch_add_clothing.xml"
            line="49"
            column="13"
            startOffset="2191"
            endLine="49"
            endColumn="47"
            endOffset="2225"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;标签 (可选，逗号分隔，应用于所有批量衣物)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_batch_add_clothing.xml"
            line="64"
            column="13"
            startOffset="2888"
            endLine="64"
            endColumn="50"
            endOffset="2925"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;选择多张照片&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_batch_add_clothing.xml"
            line="73"
            column="9"
            startOffset="3215"
            endLine="73"
            endColumn="30"
            endOffset="3236"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;保存批量衣物&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_batch_add_clothing.xml"
            line="95"
            column="9"
            startOffset="3990"
            endLine="95"
            endColumn="30"
            endOffset="4011"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;返回&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_category_select.xml"
            line="28"
            column="13"
            startOffset="1155"
            endLine="28"
            endColumn="44"
            endOffset="1186"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;选择分类&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_category_select.xml"
            line="36"
            column="13"
            startOffset="1437"
            endLine="36"
            endColumn="32"
            endOffset="1456"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;上装列表&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_clothing_detail.xml"
            line="21"
            column="13"
            startOffset="704"
            endLine="21"
            endColumn="32"
            endOffset="723"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;共0件衣物&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_clothing_detail.xml"
            line="32"
            column="13"
            startOffset="1092"
            endLine="32"
            endColumn="33"
            endOffset="1112"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;批量添加该分类衣物&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_clothing_detail.xml"
            line="43"
            column="13"
            startOffset="1473"
            endLine="43"
            endColumn="37"
            endOffset="1497"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;该分类暂无衣物\n点击上方按钮批量添加，或返回主页单件添加&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_clothing_detail.xml"
            line="66"
            column="9"
            startOffset="2187"
            endLine="66"
            endColumn="53"
            endOffset="2231"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;返回&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_color_select.xml"
            line="28"
            column="13"
            startOffset="1155"
            endLine="28"
            endColumn="44"
            endOffset="1186"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;选择颜色&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_color_select.xml"
            line="36"
            column="13"
            startOffset="1437"
            endLine="36"
            endColumn="32"
            endOffset="1456"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;添加新颜色&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_color_select.xml"
            line="48"
            column="13"
            startOffset="1896"
            endLine="48"
            endColumn="47"
            endOffset="1930"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;返回&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_occasion_select.xml"
            line="28"
            column="13"
            startOffset="1155"
            endLine="28"
            endColumn="44"
            endOffset="1186"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;选择场合&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_occasion_select.xml"
            line="36"
            column="13"
            startOffset="1437"
            endLine="36"
            endColumn="32"
            endOffset="1456"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;衣橱&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/bottom_navigation_menu.xml"
            line="7"
            column="9"
            startOffset="214"
            endLine="7"
            endColumn="27"
            endOffset="232"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;搭配&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/bottom_navigation_menu.xml"
            line="12"
            column="9"
            startOffset="340"
            endLine="12"
            endColumn="27"
            endOffset="358"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;日历&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/bottom_navigation_menu.xml"
            line="17"
            column="9"
            startOffset="470"
            endLine="17"
            endColumn="27"
            endOffset="488"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;我的&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/bottom_navigation_menu.xml"
            line="22"
            column="9"
            startOffset="598"
            endLine="22"
            endColumn="27"
            endOffset="616"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;选择照片方式&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_photo_selection.xml"
            line="13"
            column="9"
            startOffset="457"
            endLine="13"
            endColumn="30"
            endOffset="478"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;📷&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_photo_selection.xml"
            line="46"
            column="13"
            startOffset="1562"
            endLine="46"
            endColumn="30"
            endOffset="1579"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;拍摄照片&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_photo_selection.xml"
            line="55"
            column="13"
            startOffset="1866"
            endLine="55"
            endColumn="32"
            endOffset="1885"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;🖼️&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_photo_selection.xml"
            line="87"
            column="13"
            startOffset="2895"
            endLine="87"
            endColumn="31"
            endOffset="2913"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;从相册选择&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_photo_selection.xml"
            line="96"
            column="13"
            startOffset="3200"
            endLine="96"
            endColumn="33"
            endOffset="3220"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;取消&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_photo_selection.xml"
            line="117"
            column="9"
            startOffset="3823"
            endLine="117"
            endColumn="26"
            endOffset="3840"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;更换图片&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_photo_selection_ios.xml"
            line="13"
            column="9"
            startOffset="478"
            endLine="13"
            endColumn="28"
            endOffset="497"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;拍照&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_photo_selection_ios.xml"
            line="31"
            column="13"
            startOffset="1132"
            endLine="31"
            endColumn="30"
            endOffset="1149"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;从相册选择&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_photo_selection_ios.xml"
            line="47"
            column="13"
            startOffset="1706"
            endLine="47"
            endColumn="33"
            endOffset="1726"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;取消&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_photo_selection_ios.xml"
            line="60"
            column="9"
            startOffset="2146"
            endLine="60"
            endColumn="26"
            endOffset="2163"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;穿搭日历&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_calendar.xml"
            line="16"
            column="9"
            startOffset="635"
            endLine="16"
            endColumn="28"
            endOffset="654"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;2024年12月19日 星期四&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_calendar.xml"
            line="31"
            column="9"
            startOffset="1159"
            endLine="31"
            endColumn="39"
            endOffset="1189"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;暂无穿搭记录&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_calendar.xml"
            line="79"
            column="13"
            startOffset="3032"
            endLine="79"
            endColumn="34"
            endOffset="3053"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;开始记录您的每日穿搭\n查看穿搭历史&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_calendar.xml"
            line="87"
            column="13"
            startOffset="3310"
            endLine="87"
            endColumn="46"
            endOffset="3343"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;今日搭配&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_outfit.xml"
            line="16"
            column="9"
            startOffset="635"
            endLine="16"
            endColumn="28"
            endOffset="654"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;为您推荐今日搭配&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_outfit.xml"
            line="31"
            column="9"
            startOffset="1160"
            endLine="31"
            endColumn="32"
            endOffset="1183"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;暂无搭配推荐&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_outfit.xml"
            line="81"
            column="13"
            startOffset="3097"
            endLine="81"
            endColumn="34"
            endOffset="3118"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;添加一些衣物后\n我们会为您推荐搭配&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_outfit.xml"
            line="91"
            column="13"
            startOffset="3455"
            endLine="91"
            endColumn="46"
            endOffset="3488"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;个人资料&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_profile.xml"
            line="17"
            column="13"
            startOffset="606"
            endLine="17"
            endColumn="32"
            endOffset="625"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;存储信息&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_profile.xml"
            line="39"
            column="21"
            startOffset="1461"
            endLine="39"
            endColumn="40"
            endOffset="1480"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;存储空间: 计算中...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_profile.xml"
            line="49"
            column="21"
            startOffset="1902"
            endLine="49"
            endColumn="48"
            endOffset="1929"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;图片文件: 0 个&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_profile.xml"
            line="58"
            column="21"
            startOffset="2306"
            endLine="58"
            endColumn="45"
            endOffset="2330"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;衣物数量: 0 件&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_profile.xml"
            line="67"
            column="21"
            startOffset="2710"
            endLine="67"
            endColumn="45"
            endOffset="2734"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;存储管理&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_profile.xml"
            line="91"
            column="21"
            startOffset="3595"
            endLine="91"
            endColumn="40"
            endOffset="3614"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;清理图片缓存&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_profile.xml"
            line="102"
            column="21"
            startOffset="4111"
            endLine="102"
            endColumn="42"
            endOffset="4132"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;压缩图片&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_profile.xml"
            line="110"
            column="21"
            startOffset="4480"
            endLine="110"
            endColumn="40"
            endOffset="4499"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;清理未使用文件&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_profile.xml"
            line="118"
            column="21"
            startOffset="4846"
            endLine="118"
            endColumn="43"
            endOffset="4868"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;应用设置&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_profile.xml"
            line="139"
            column="21"
            startOffset="5576"
            endLine="139"
            endColumn="40"
            endOffset="5595"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;深色模式&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_profile.xml"
            line="156"
            column="25"
            startOffset="6327"
            endLine="156"
            endColumn="44"
            endOffset="6346"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;通知&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_profile.xml"
            line="178"
            column="25"
            startOffset="7282"
            endLine="178"
            endColumn="42"
            endOffset="7299"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;自动备份&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_profile.xml"
            line="199"
            column="25"
            startOffset="8184"
            endLine="199"
            endColumn="44"
            endOffset="8203"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;搜索图标&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_wardrobe.xml"
            line="35"
            column="17"
            startOffset="1336"
            endLine="35"
            endColumn="50"
            endOffset="1369"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;搜索衣物...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_wardrobe.xml"
            line="44"
            column="17"
            startOffset="1695"
            endLine="44"
            endColumn="39"
            endOffset="1717"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;清除搜索&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_wardrobe.xml"
            line="59"
            column="17"
            startOffset="2343"
            endLine="59"
            endColumn="50"
            endOffset="2376"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;我的衣橱&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_wardrobe.xml"
            line="79"
            column="13"
            startOffset="2985"
            endLine="79"
            endColumn="32"
            endOffset="3004"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;正在加载...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_wardrobe.xml"
            line="131"
            column="17"
            startOffset="4938"
            endLine="131"
            endColumn="39"
            endOffset="4960"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;衣橱空空如也&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_wardrobe.xml"
            line="158"
            column="17"
            startOffset="5928"
            endLine="158"
            endColumn="38"
            endOffset="5949"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;点击右下角按钮添加第一件衣物&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_wardrobe.xml"
            line="166"
            column="17"
            startOffset="6242"
            endLine="166"
            endColumn="46"
            endOffset="6271"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;12月19日&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_calendar.xml"
            line="28"
            column="17"
            startOffset="1047"
            endLine="28"
            endColumn="38"
            endOffset="1068"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;周四&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_calendar.xml"
            line="38"
            column="17"
            startOffset="1459"
            endLine="38"
            endColumn="34"
            endOffset="1476"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;白色T恤 + 牛仔裤 + 运动鞋&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_calendar.xml"
            line="51"
            column="13"
            startOffset="1893"
            endLine="51"
            endColumn="44"
            endOffset="1924"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;查看&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_calendar.xml"
            line="62"
            column="13"
            startOffset="2311"
            endLine="62"
            endColumn="30"
            endOffset="2328"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;类别图标&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_category_selector.xml"
            line="18"
            column="9"
            startOffset="670"
            endLine="18"
            endColumn="42"
            endOffset="703"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;分类名称&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_category_selector.xml"
            line="27"
            column="9"
            startOffset="960"
            endLine="27"
            endColumn="28"
            endOffset="979"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;进入&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_category_selector.xml"
            line="39"
            column="9"
            startOffset="1371"
            endLine="39"
            endColumn="40"
            endOffset="1402"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;衣物图片&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_clothing_horizontal.xml"
            line="28"
            column="17"
            startOffset="1050"
            endLine="28"
            endColumn="50"
            endOffset="1083"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;衣物名称&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_clothing_horizontal.xml"
            line="36"
            column="17"
            startOffset="1345"
            endLine="36"
            endColumn="36"
            endOffset="1364"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Clothing Item Image&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_clothing_pro.xml"
            line="23"
            column="13"
            startOffset="830"
            endLine="23"
            endColumn="61"
            endOffset="878"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Item Name&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_clothing_pro.xml"
            line="34"
            column="13"
            startOffset="1311"
            endLine="34"
            endColumn="37"
            endOffset="1335"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Category&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_clothing_pro.xml"
            line="44"
            column="13"
            startOffset="1735"
            endLine="44"
            endColumn="36"
            endOffset="1758"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;颜色名称&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_color_selector.xml"
            line="30"
            column="9"
            startOffset="1081"
            endLine="30"
            endColumn="28"
            endOffset="1100"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Color Name&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_color_spinner.xml"
            line="24"
            column="9"
            startOffset="894"
            endLine="24"
            endColumn="34"
            endOffset="919"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;场合图标&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_occasion_selector.xml"
            line="18"
            column="9"
            startOffset="670"
            endLine="18"
            endColumn="42"
            endOffset="703"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;场合名称&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_occasion_selector.xml"
            line="25"
            column="9"
            startOffset="891"
            endLine="25"
            endColumn="28"
            endOffset="910"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;进入&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_occasion_selector.xml"
            line="37"
            column="9"
            startOffset="1302"
            endLine="37"
            endColumn="40"
            endOffset="1333"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;搭配建议 1&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_outfit.xml"
            line="23"
            column="13"
            startOffset="865"
            endLine="23"
            endColumn="34"
            endOffset="886"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;白色T恤 + 牛仔裤 + 运动鞋&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_outfit.xml"
            line="33"
            column="13"
            startOffset="1227"
            endLine="33"
            endColumn="44"
            endOffset="1258"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;保存搭配&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_outfit.xml"
            line="51"
            column="17"
            startOffset="1921"
            endLine="51"
            endColumn="36"
            endOffset="1940"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;分享&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_outfit.xml"
            line="61"
            column="17"
            startOffset="2315"
            endLine="61"
            endColumn="34"
            endOffset="2332"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;上衣&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_wardrobe_group.xml"
            line="22"
            column="13"
            startOffset="792"
            endLine="22"
            endColumn="30"
            endOffset="809"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;5 件&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_wardrobe_group.xml"
            line="31"
            column="13"
            startOffset="1119"
            endLine="31"
            endColumn="31"
            endOffset="1137"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;暂无数据&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_empty.xml"
            line="22"
            column="9"
            startOffset="724"
            endLine="22"
            endColumn="28"
            endOffset="743"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;还没有任何内容&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_empty.xml"
            line="33"
            column="9"
            startOffset="1087"
            endLine="33"
            endColumn="31"
            endOffset="1109"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;出错了&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_error.xml"
            line="22"
            column="9"
            startOffset="727"
            endLine="22"
            endColumn="27"
            endOffset="745"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;请稍后重试&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_error.xml"
            line="33"
            column="9"
            startOffset="1089"
            endLine="33"
            endColumn="29"
            endOffset="1109"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;重试&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_error.xml"
            line="43"
            column="9"
            startOffset="1439"
            endLine="43"
            endColumn="26"
            endOffset="1456"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;加载中...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/layout_loading.xml"
            line="19"
            column="9"
            startOffset="621"
            endLine="19"
            endColumn="30"
            endOffset="642"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;图标&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/list_item_selector_row.xml"
            line="14"
            column="9"
            startOffset="503"
            endLine="14"
            endColumn="40"
            endOffset="534"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;名称&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/list_item_selector_row.xml"
            line="23"
            column="9"
            startOffset="821"
            endLine="23"
            endColumn="26"
            endOffset="838"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Label&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/list_item_selector_row.xml"
            line="33"
            column="9"
            startOffset="1160"
            endLine="33"
            endColumn="29"
            endOffset="1180"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;请选择&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/list_item_selector_row.xml"
            line="49"
            column="13"
            startOffset="1733"
            endLine="49"
            endColumn="31"
            endOffset="1751"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;取消&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/multi_select_toolbar.xml"
            line="18"
            column="9"
            startOffset="631"
            endLine="18"
            endColumn="26"
            endOffset="648"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;已选择 0 项&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/multi_select_toolbar.xml"
            line="30"
            column="9"
            startOffset="1043"
            endLine="30"
            endColumn="31"
            endOffset="1065"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;全选&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/multi_select_toolbar.xml"
            line="40"
            column="9"
            startOffset="1363"
            endLine="40"
            endColumn="26"
            endOffset="1380"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;删除&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/multi_select_toolbar.xml"
            line="52"
            column="9"
            startOffset="1783"
            endLine="52"
            endColumn="26"
            endOffset="1800"/>
    </incident>

</incidents>
