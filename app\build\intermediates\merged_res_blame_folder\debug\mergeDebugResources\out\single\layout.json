[{"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/item_clothing_pro.xml", "source": "com.wardrobe.app-main-45:/layout/item_clothing_pro.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/item_color_spinner.xml", "source": "com.wardrobe.app-main-45:/layout/item_color_spinner.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/item_category_selector.xml", "source": "com.wardrobe.app-main-45:/layout/item_category_selector.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/layout_empty.xml", "source": "com.wardrobe.app-main-45:/layout/layout_empty.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/item_calendar.xml", "source": "com.wardrobe.app-main-45:/layout/item_calendar.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/item_outfit.xml", "source": "com.wardrobe.app-main-45:/layout/item_outfit.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/fragment_profile.xml", "source": "com.wardrobe.app-main-45:/layout/fragment_profile.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/item_clothing_horizontal.xml", "source": "com.wardrobe.app-main-45:/layout/item_clothing_horizontal.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/layout_error.xml", "source": "com.wardrobe.app-main-45:/layout/layout_error.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/dialog_color_selection.xml", "source": "com.wardrobe.app-main-45:/layout/dialog_color_selection.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/activity_batch_add_clothing.xml", "source": "com.wardrobe.app-main-45:/layout/activity_batch_add_clothing.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/dialog_image_fullscreen.xml", "source": "com.wardrobe.app-main-45:/layout/dialog_image_fullscreen.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/activity_category_select.xml", "source": "com.wardrobe.app-main-45:/layout/activity_category_select.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/spinner_item_with_icon.xml", "source": "com.wardrobe.app-main-45:/layout/spinner_item_with_icon.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/layout_category_selector.xml", "source": "com.wardrobe.app-main-45:/layout/layout_category_selector.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/multi_select_toolbar.xml", "source": "com.wardrobe.app-main-45:/layout/multi_select_toolbar.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/activity_clothing_detail.xml", "source": "com.wardrobe.app-main-45:/layout/activity_clothing_detail.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/activity_add_clothing.xml", "source": "com.wardrobe.app-main-45:/layout/activity_add_clothing.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/dialog_occasion_selection.xml", "source": "com.wardrobe.app-main-45:/layout/dialog_occasion_selection.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/layout_loading.xml", "source": "com.wardrobe.app-main-45:/layout/layout_loading.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/dialog_photo_selection.xml", "source": "com.wardrobe.app-main-45:/layout/dialog_photo_selection.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/fragment_calendar.xml", "source": "com.wardrobe.app-main-45:/layout/fragment_calendar.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/activity_occasion_select.xml", "source": "com.wardrobe.app-main-45:/layout/activity_occasion_select.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/dialog_recyclerview.xml", "source": "com.wardrobe.app-main-45:/layout/dialog_recyclerview.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/fragment_wardrobe.xml", "source": "com.wardrobe.app-main-45:/layout/fragment_wardrobe.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/item_wardrobe_group.xml", "source": "com.wardrobe.app-main-45:/layout/item_wardrobe_group.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/item_color_selector.xml", "source": "com.wardrobe.app-main-45:/layout/item_color_selector.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/dialog_photo_selection_ios.xml", "source": "com.wardrobe.app-main-45:/layout/dialog_photo_selection_ios.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/list_item_selector_row.xml", "source": "com.wardrobe.app-main-45:/layout/list_item_selector_row.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/item_occasion_selector.xml", "source": "com.wardrobe.app-main-45:/layout/item_occasion_selector.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/activity_main.xml", "source": "com.wardrobe.app-main-45:/layout/activity_main.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/dialog_category_selection.xml", "source": "com.wardrobe.app-main-45:/layout/dialog_category_selection.xml"}, {"merged": "com.wardrobe.app-mergeDebugResources-42:/layout/activity_color_select.xml", "source": "com.wardrobe.app-main-45:/layout/activity_color_select.xml"}]