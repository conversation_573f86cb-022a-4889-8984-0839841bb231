<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res"><file name="fade_in" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\anim\fade_in.xml" qualifiers="" type="anim"/><file name="fade_out" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\anim\fade_out.xml" qualifiers="" type="anim"/><file name="scale_in" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\anim\scale_in.xml" qualifiers="" type="anim"/><file name="slide_in_right" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\anim\slide_in_right.xml" qualifiers="" type="anim"/><file name="slide_out_left" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\anim\slide_out_left.xml" qualifiers="" type="anim"/><file name="bottom_nav_color_selector" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\color\bottom_nav_color_selector.xml" qualifiers="" type="color"/><file name="button_background" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\button_background.xml" qualifiers="" type="drawable"/><file name="button_background_destructive" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\button_background_destructive.xml" qualifiers="" type="drawable"/><file name="button_background_prominent" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\button_background_prominent.xml" qualifiers="" type="drawable"/><file name="color_circle_background" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\color_circle_background.xml" qualifiers="" type="drawable"/><file name="dialog_background" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\dialog_background.xml" qualifiers="" type="drawable"/><file name="dialog_rounded_background" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\dialog_rounded_background.xml" qualifiers="" type="drawable"/><file name="dotted_border_background" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\dotted_border_background.xml" qualifiers="" type="drawable"/><file name="edit_text_background_ios" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\edit_text_background_ios.xml" qualifiers="" type="drawable"/><file name="ic_add" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_calendar" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_calendar.xml" qualifiers="" type="drawable"/><file name="ic_category_accessories" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_category_accessories.xml" qualifiers="" type="drawable"/><file name="ic_category_bottoms" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_category_bottoms.xml" qualifiers="" type="drawable"/><file name="ic_category_dresses" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_category_dresses.xml" qualifiers="" type="drawable"/><file name="ic_category_outerwear" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_category_outerwear.xml" qualifiers="" type="drawable"/><file name="ic_category_sets" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_category_sets.xml" qualifiers="" type="drawable"/><file name="ic_category_shoes" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_category_shoes.xml" qualifiers="" type="drawable"/><file name="ic_category_suits" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_category_suits.png" qualifiers="" type="drawable"/><file name="ic_category_tops" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_category_tops.xml" qualifiers="" type="drawable"/><file name="ic_category_tuxedo" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_category_tuxedo.xml" qualifiers="" type="drawable"/><file name="ic_category_underwear" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_category_underwear.xml" qualifiers="" type="drawable"/><file name="ic_category_workwear" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_category_workwear.xml" qualifiers="" type="drawable"/><file name="ic_check_circle" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_check_circle.xml" qualifiers="" type="drawable"/><file name="ic_chevron_right" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_chevron_right.xml" qualifiers="" type="drawable"/><file name="ic_clear" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_clear.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_occasion_casual" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_occasion_casual.xml" qualifiers="" type="drawable"/><file name="ic_occasion_daily" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_occasion_daily.xml" qualifiers="" type="drawable"/><file name="ic_occasion_date" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_occasion_date.xml" qualifiers="" type="drawable"/><file name="ic_occasion_formal" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_occasion_formal.xml" qualifiers="" type="drawable"/><file name="ic_occasion_home" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_occasion_home.xml" qualifiers="" type="drawable"/><file name="ic_occasion_outdoor" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_occasion_outdoor.xml" qualifiers="" type="drawable"/><file name="ic_occasion_party" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_occasion_party.xml" qualifiers="" type="drawable"/><file name="ic_occasion_sports" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_occasion_sports.xml" qualifiers="" type="drawable"/><file name="ic_occasion_travel" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_occasion_travel.xml" qualifiers="" type="drawable"/><file name="ic_occasion_work" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_occasion_work.xml" qualifiers="" type="drawable"/><file name="ic_outfit" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_outfit.xml" qualifiers="" type="drawable"/><file name="ic_profile" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_profile.xml" qualifiers="" type="drawable"/><file name="ic_replace_photo" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_replace_photo.xml" qualifiers="" type="drawable"/><file name="ic_search" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_search.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_bag" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_bag.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_belt" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_belt.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_boots" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_boots.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_bra" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_bra.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_briefs" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_briefs.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_camisole" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_camisole.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_camisole_dress" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_camisole_dress.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_canvas_shoes" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_canvas_shoes.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_coat" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_coat.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_dress" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_dress.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_evening_dress" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_evening_dress.png" qualifiers="" type="drawable"/><file name="ic_subcategory_flared_skirt" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_flared_skirt.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_formal_dress" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_formal_dress.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_formal_pants" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_formal_pants.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_formal_shoes" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_formal_shoes.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_functional" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_functional.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_hat" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_hat.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_heels" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_heels.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_hoodie" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_hoodie.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_jacket" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_jacket.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_jeans" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_jeans.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_jewelry" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_jewelry.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_jumpsuit" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_jumpsuit.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_mermaid_skirt" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_mermaid_skirt.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_other_acc" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_other_acc.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_outer_vest" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_outer_vest.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_pajamas" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_pajamas.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_pants" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_pants.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_pencil_skirt" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_pencil_skirt.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_pleated_skirt" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_pleated_skirt.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_polo" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_polo.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_rain_boots" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_rain_boots.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_sandals" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_sandals.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_scarf" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_scarf.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_shirt" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_shirt.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_shorts" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_shorts.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_skirt" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_skirt.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_skirt_bottom" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_skirt_bottom.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_slit_skirt" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_slit_skirt.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_sneakers" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_sneakers.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_socks" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_socks.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_spaghetti_dress" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_spaghetti_dress.png" qualifiers="" type="drawable"/><file name="ic_subcategory_sport_pants" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_sport_pants.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_sport_shoes" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_sport_shoes.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_straight_skirt" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_straight_skirt.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_suit" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_suit.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_sweater" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_sweater.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_thermal" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_thermal.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_tracksuit" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_tracksuit.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_traditional" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_traditional.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_tshirt" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_tshirt.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_vest" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_vest.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_wedding" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_wedding.xml" qualifiers="" type="drawable"/><file name="ic_wardrobe" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_wardrobe.xml" qualifiers="" type="drawable"/><file name="ios_style_group_background_bottom" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ios_style_group_background_bottom.xml" qualifiers="" type="drawable"/><file name="ios_style_group_background_middle" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ios_style_group_background_middle.xml" qualifiers="" type="drawable"/><file name="ios_style_group_background_single" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ios_style_group_background_single.xml" qualifiers="" type="drawable"/><file name="ios_style_group_background_top" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ios_style_group_background_top.xml" qualifiers="" type="drawable"/><file name="option_background" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\option_background.xml" qualifiers="" type="drawable"/><file name="replace_icon_background" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\replace_icon_background.xml" qualifiers="" type="drawable"/><file name="search_background" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\search_background.xml" qualifiers="" type="drawable"/><file name="secondary_system_grouped_background_ripple" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\secondary_system_grouped_background_ripple.xml" qualifiers="" type="drawable"/><file name="selected_item_background" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\selected_item_background.xml" qualifiers="" type="drawable"/><file name="spinner_background" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\spinner_background.xml" qualifiers="" type="drawable"/><file name="activity_add_clothing" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\activity_add_clothing.xml" qualifiers="" type="layout"/><file name="activity_batch_add_clothing" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\activity_batch_add_clothing.xml" qualifiers="" type="layout"/><file name="activity_category_select" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\activity_category_select.xml" qualifiers="" type="layout"/><file name="activity_clothing_detail" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\activity_clothing_detail.xml" qualifiers="" type="layout"/><file name="activity_color_select" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\activity_color_select.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_occasion_select" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\activity_occasion_select.xml" qualifiers="" type="layout"/><file name="dialog_category_selection" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\dialog_category_selection.xml" qualifiers="" type="layout"/><file name="dialog_color_selection" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\dialog_color_selection.xml" qualifiers="" type="layout"/><file name="dialog_image_fullscreen" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\dialog_image_fullscreen.xml" qualifiers="" type="layout"/><file name="dialog_occasion_selection" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\dialog_occasion_selection.xml" qualifiers="" type="layout"/><file name="dialog_photo_selection" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\dialog_photo_selection.xml" qualifiers="" type="layout"/><file name="dialog_photo_selection_ios" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\dialog_photo_selection_ios.xml" qualifiers="" type="layout"/><file name="dialog_recyclerview" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\dialog_recyclerview.xml" qualifiers="" type="layout"/><file name="fragment_calendar" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\fragment_calendar.xml" qualifiers="" type="layout"/><file name="fragment_outfit" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\fragment_outfit.xml" qualifiers="" type="layout"/><file name="fragment_profile" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\fragment_profile.xml" qualifiers="" type="layout"/><file name="fragment_wardrobe" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\fragment_wardrobe.xml" qualifiers="" type="layout"/><file name="item_calendar" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\item_calendar.xml" qualifiers="" type="layout"/><file name="item_category_selector" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\item_category_selector.xml" qualifiers="" type="layout"/><file name="item_clothing_horizontal" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\item_clothing_horizontal.xml" qualifiers="" type="layout"/><file name="item_clothing_pro" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\item_clothing_pro.xml" qualifiers="" type="layout"/><file name="item_color_selector" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\item_color_selector.xml" qualifiers="" type="layout"/><file name="item_color_spinner" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\item_color_spinner.xml" qualifiers="" type="layout"/><file name="item_occasion_selector" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\item_occasion_selector.xml" qualifiers="" type="layout"/><file name="item_outfit" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\item_outfit.xml" qualifiers="" type="layout"/><file name="item_wardrobe_group" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\item_wardrobe_group.xml" qualifiers="" type="layout"/><file name="layout_category_selector" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\layout_category_selector.xml" qualifiers="" type="layout"/><file name="list_item_selector_row" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\list_item_selector_row.xml" qualifiers="" type="layout"/><file name="multi_select_toolbar" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\multi_select_toolbar.xml" qualifiers="" type="layout"/><file name="spinner_item_with_icon" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\spinner_item_with_icon.xml" qualifiers="" type="layout"/><file name="action_mode_menu" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\menu\action_mode_menu.xml" qualifiers="" type="menu"/><file name="bottom_navigation_menu" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\menu\bottom_navigation_menu.xml" qualifiers="" type="menu"/><file name="menu" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\menu\menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="systemBackground">#FFFFFF</color><color name="systemGroupedBackground">#F2F2F7</color><color name="text_secondary">#8E8E93</color><color name="text_tertiary">#C7C7CC</color><color name="surface_primary">#FFFFFF</color><color name="systemBlue">#007AFF</color><color name="systemGray">#8E8E93</color><color name="systemGray2">#AEAEB2</color><color name="systemGray3">#C7C7CC</color><color name="systemGray4">#D1D1D6</color><color name="systemGray5">#E5E5EA</color><color name="systemGray6">#F2F2F7</color><color name="separator">#3C3C43</color><color name="opaqueSeparator">#C6C6C8</color><color name="secondarySystemGroupedBackground">#F2F2F7</color><color name="placeholderText">#8E8E93</color><color name="destructive">#FF3B30</color><color name="labelPrimary">#000000</color><color name="labelSecondary">#3C3C43</color><color name="labelTertiary">#3C3C43</color><color name="labelQuaternary">#3C3C43</color><color name="fillTertiary">#D1D1D6</color><color name="systemRed">#FF3B30</color><color name="quaternarySystemGroupedBackground">#FFFFFF</color><color name="background_secondary">#F2F2F7</color><color name="border_primary">#D1D1D6</color><color name="accent_primary">#007AFF</color><color name="background_primary">#FFFFFF</color><color name="text_primary">#000000</color><color name="destructiveRed">#FF3B30</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">WardrobeApp</string><string name="title_add_new_clothing">添加新衣物</string><string name="title_edit_clothing">编辑衣物</string><string name="button_save">保存</string><string name="hint_clothing_name">衣物名称</string><string name="section_required">必填信息</string><string name="section_optional">可选信息</string><string name="label_category">分类</string><string name="label_color">颜色</string><string name="label_occasion">场合</string><string name="category_placeholder">请选择分类</string><string name="color_placeholder">请选择颜色</string><string name="occasion_placeholder">请选择场合</string><string name="category_primary_placeholder">请选择主分类</string><string name="category_secondary_placeholder">请选择子分类</string><string name="placeholder_select">请选择</string><string name="dialog_title_select_color">选择颜色</string><string name="dialog_title_select_occasion">选择场合</string><string name="dialog_title_select_photo">选择图片</string><string name="dialog_option_camera">拍照</string><string name="dialog_option_gallery">从相册选择</string><string name="dialog_button_cancel">取消</string><string name="toast_save_successful">保存成功</string><string name="toast_save_failed">保存失败</string><string name="toast_load_failed">加载失败</string><string name="toast_select_image_first">请先选择一张图片</string><string name="toast_image_selection_cancelled">已取消选择图片</string><string name="toast_permissions_denied">权限被拒绝，无法选择图片</string><string name="error_load_data">加载数据失败</string><string name="error_get_storage_info">获取存储信息失败</string><string name="success_cleanup_all_images">清理完成，删除了 %1$d 个图片文件</string><string name="error_cleanup_images">清理失败: %1$s</string><string name="compressing">压缩中...</string><string name="compress_all_images">压缩所有图片</string><string name="success_compress_images">压缩完成，处理了 %1$d 个图片文件</string><string name="error_compress_images">压缩失败: %1$s</string><string name="error_start_compress">启动压缩失败: %1$s</string><string name="success_cleanup_unused_images">清理完成，删除了 %1$d 个未使用的图片文件</string><string name="error_main_category_missing_batch">主分类缺失，无法批量添加</string><string name="title_batch_add_prefix">批量添加：%1$s</string><string name="images_selected_count_batch">已选择%1$d张图片</string><string name="error_no_images_selected_batch">请至少选择一张图片</string><string name="error_incomplete_selection_batch">请先选择完整的分类</string><string name="success_batch_add_clothing_summary">成功添加%1$d件%2$s</string><string name="success_batch_add_clothing_summary_with_failures">成功添加%1$d件%2$s，%3$d张图片失败</string><string name="error_batch_add_all_failed">全部失败，共%1$d张图片未添加</string><string name="info_no_items_to_add_batch">没有可添加的衣物</string><string name="category_tops">上衣</string><string name="category_bottoms">下装</string><string name="category_dresses">连衣裙</string><string name="category_outerwear">外套</string><string name="category_shoes">鞋子</string><string name="category_accessories">配饰</string><string name="occasion_work">工作</string><string name="occasion_casual">休闲</string><string name="occasion_formal">正式</string><string name="occasion_sports">运动</string><string name="occasion_sport">运动</string><string name="occasion_party">派对</string><string name="occasion_travel">旅行</string><string name="occasion_home">居家</string><string name="occasion_date">约会</string><string name="occasion_daily">日常</string><string name="occasion_outdoor">户外</string><string name="color_custom">自定义</string><string name="color_black">黑色</string><string name="color_white">白色</string><string name="color_gray">灰色</string><string name="color_red">红色</string><string name="color_green">绿色</string><string name="color_blue">蓝色</string><string name="color_yellow">黄色</string><string name="color_pink">粉色</string><string name="color_purple">紫色</string><string name="color_orange">橙色</string><string name="color_brown">棕色</string><string name="color_beige">米色</string><string name="color_khaki">卡其色</string><string name="color_navy">藏青色</string><string name="toast_permission_granted_retry">权限已授予，请重试操作</string><string name="toast_permission_denied">权限被拒绝，无法继续操作</string><string name="toast_create_image_file_failed">无法创建图片文件</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\values\styles.xml" qualifiers=""><style name="iOSGroupedBackground">
        <item name="android:background">@color/white</item>
        <item name="android:orientation">vertical</item>
    </style><style name="iOSGroupedBackground.Top" parent="iOSGroupedBackground">
        <item name="android:background">@drawable/ios_style_group_background_top</item>
    </style><style name="iOSGroupedBackground.Middle" parent="iOSGroupedBackground">
        <item name="android:background">@drawable/ios_style_group_background_middle</item>
    </style><style name="iOSGroupedBackground.Bottom" parent="iOSGroupedBackground">
        <item name="android:background">@drawable/ios_style_group_background_bottom</item>
    </style><style name="iOSGroupedBackground.Single" parent="iOSGroupedBackground">
        <item name="android:background">@drawable/ios_style_group_background_single</item>
    </style><style name="iOSListItem">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:minHeight">44dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">16dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
        <item name="android:background">@drawable/secondary_system_grouped_background_ripple</item>
    </style><style name="iOSLabel">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_alignParentStart">true</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:textColor">@color/labelPrimary</item>
        <item name="android:textSize">17sp</item>
    </style><style name="iOSValue">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:textColor">@color/systemGray</item>
        <item name="android:textSize">17sp</item>
        <item name="android:layout_marginEnd">8dp</item>
    </style><style name="iOSChevron">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_alignParentEnd">true</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:src">@drawable/ic_chevron_right</item>
        
    </style><style name="Separator">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0.5dp</item>
        <item name="android:background">@color/separator</item>
    </style><style name="SectionHeader">
        <item name="android:textSize">13sp</item>
        <item name="android:textColor">@color/systemGray</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingBottom">8dp</item>
    </style><style name="ProminentButton" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background_prominent</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">17sp</item>
        <item name="android:textStyle">bold</item>
    </style><style name="DestructiveButton" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background_destructive</item>
        <item name="android:textColor">@color/destructiveRed</item>
        <item name="android:textSize">17sp</item>
    </style><style name="Base.Theme.WardrobeApp" parent="Theme.Material3.DayNight.NoActionBar">
        
    </style><style name="Theme.WardrobeApp" parent="Base.Theme.WardrobeApp"/><style name="TextAppearance.App.Base" parent="">
        <item name="android:textColor">@color/labelPrimary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style><style name="TextAppearance.App.LargeTitle" parent="TextAppearance.App.Base">
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:textSize">34sp</item>
    </style><style name="TextAppearance.App.Title1" parent="TextAppearance.App.Base">
        <item name="android:textSize">28sp</item>
    </style><style name="TextAppearance.App.Title2" parent="TextAppearance.App.Base">
        <item name="android:textSize">22sp</item>
    </style><style name="TextAppearance.App.Title3" parent="TextAppearance.App.Base">
        <item name="android:textSize">20sp</item>
    </style><style name="TextAppearance.App.Headline" parent="TextAppearance.App.Base">
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textSize">17sp</item>
    </style><style name="TextAppearance.App.Body" parent="TextAppearance.App.Base">
        <item name="android:textSize">17sp</item>
    </style><style name="TextAppearance.App.Callout" parent="TextAppearance.App.Base">
        <item name="android:textSize">16sp</item>
    </style><style name="TextAppearance.App.Subheadline" parent="TextAppearance.App.Base">
        <item name="android:textSize">15sp</item>
        <item name="android:textColor">@color/labelSecondary</item>
    </style><style name="TextAppearance.App.Footnote" parent="TextAppearance.App.Base">
        <item name="android:textSize">13sp</item>
        <item name="android:textColor">@color/labelSecondary</item>
    </style><style name="TextAppearance.App.Caption1" parent="TextAppearance.App.Base">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/labelSecondary</item>
    </style><style name="TextAppearance.App.Caption2" parent="TextAppearance.App.Base">
        <item name="android:textSize">11sp</item>
        <item name="android:textColor">@color/labelTertiary</item>
    </style><style name="ButtonPrimary" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background_prominent</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:minHeight">44dp</item>
        <item name="android:textAllCaps">false</item>
    </style><style name="ButtonSecondary" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background</item>
        <item name="android:textColor">@color/systemBlue</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:minHeight">44dp</item>
        <item name="android:textAllCaps">false</item>
    </style><style name="ButtonTertiary" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background</item>
        <item name="android:textColor">@color/labelSecondary</item>
        <item name="android:textSize">14sp</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:minHeight">32dp</item>
        <item name="android:textAllCaps">false</item>
    </style><style name="ButtonDestructive" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background_destructive</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:minHeight">44dp</item>
        <item name="android:textAllCaps">false</item>
    </style><style name="BottomSheetDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomSheetStyle</item>
    </style><style name="BottomSheetStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
    </style><style name="Theme.WardrobeApp.Dialog" parent="Theme.Material3.DayNight.Dialog.Alert">
        <item name="colorPrimary">@color/systemBlue</item>
        <item name="android:background">@color/secondarySystemGroupedBackground</item>
        <item name="android:windowBackground">@drawable/dialog_rounded_background</item>
    </style><style name="Theme.WardrobeApp.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style><style name="Theme.WardrobeApp.BottomSheetDialog" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/AppModalStyle</item>
    </style><style name="AppModalStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@drawable/dialog_rounded_background</item>
    </style><style name="Theme.WardrobeApp.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/><style name="Theme.WardrobeApp.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/><style name="Theme.WardrobeApp.EditTextStyle" parent="Widget.AppCompat.EditText">
        <item name="android:textColor">@color/labelPrimary</item>
        <item name="android:textColorHint">@color/placeholderText</item>
        <item name="android:textSize">16sp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
    </style><style name="Theme.WardrobeApp.Button.Prominent" parent="ButtonPrimary"/><style name="Theme.WardrobeApp.Button.Destructive" parent="ButtonDestructive"/></file><file name="backup_rules" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>