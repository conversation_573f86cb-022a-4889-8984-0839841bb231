<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res"><file name="fade_in" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\anim\fade_in.xml" qualifiers="" type="anim"/><file name="fade_out" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\anim\fade_out.xml" qualifiers="" type="anim"/><file name="scale_in" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\anim\scale_in.xml" qualifiers="" type="anim"/><file name="slide_in_left" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\anim\slide_in_left.xml" qualifiers="" type="anim"/><file name="slide_in_right" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\anim\slide_in_right.xml" qualifiers="" type="anim"/><file name="slide_out_left" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\anim\slide_out_left.xml" qualifiers="" type="anim"/><file name="slide_out_right" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\anim\slide_out_right.xml" qualifiers="" type="anim"/><file name="slide_up" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\anim\slide_up.xml" qualifiers="" type="anim"/><file name="bottom_nav_color_selector" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\color\bottom_nav_color_selector.xml" qualifiers="" type="color"/><file name="button_background" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\button_background.xml" qualifiers="" type="drawable"/><file name="button_background_destructive" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\button_background_destructive.xml" qualifiers="" type="drawable"/><file name="button_background_prominent" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\button_background_prominent.xml" qualifiers="" type="drawable"/><file name="color_circle_background" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\color_circle_background.xml" qualifiers="" type="drawable"/><file name="dialog_background" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\dialog_background.xml" qualifiers="" type="drawable"/><file name="dialog_rounded_background" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\dialog_rounded_background.xml" qualifiers="" type="drawable"/><file name="dotted_border_background" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\dotted_border_background.xml" qualifiers="" type="drawable"/><file name="edit_text_background_ios" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\edit_text_background_ios.xml" qualifiers="" type="drawable"/><file name="ic_add" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_calendar" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_calendar.xml" qualifiers="" type="drawable"/><file name="ic_category_accessories" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_category_accessories.xml" qualifiers="" type="drawable"/><file name="ic_category_bottoms" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_category_bottoms.xml" qualifiers="" type="drawable"/><file name="ic_category_dresses" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_category_dresses.xml" qualifiers="" type="drawable"/><file name="ic_category_outerwear" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_category_outerwear.xml" qualifiers="" type="drawable"/><file name="ic_category_sets" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_category_sets.xml" qualifiers="" type="drawable"/><file name="ic_category_shoes" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_category_shoes.xml" qualifiers="" type="drawable"/><file name="ic_category_suits" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_category_suits.png" qualifiers="" type="drawable"/><file name="ic_category_tops" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_category_tops.xml" qualifiers="" type="drawable"/><file name="ic_category_tuxedo" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_category_tuxedo.xml" qualifiers="" type="drawable"/><file name="ic_category_underwear" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_category_underwear.xml" qualifiers="" type="drawable"/><file name="ic_category_workwear" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_category_workwear.xml" qualifiers="" type="drawable"/><file name="ic_check_circle" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_check_circle.xml" qualifiers="" type="drawable"/><file name="ic_chevron_right" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_chevron_right.xml" qualifiers="" type="drawable"/><file name="ic_clear" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_clear.xml" qualifiers="" type="drawable"/><file name="ic_clothing_placeholder" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_clothing_placeholder.xml" qualifiers="" type="drawable"/><file name="ic_empty_box" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_empty_box.xml" qualifiers="" type="drawable"/><file name="ic_error" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_error.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_occasion_casual" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_occasion_casual.xml" qualifiers="" type="drawable"/><file name="ic_occasion_daily" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_occasion_daily.xml" qualifiers="" type="drawable"/><file name="ic_occasion_date" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_occasion_date.xml" qualifiers="" type="drawable"/><file name="ic_occasion_formal" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_occasion_formal.xml" qualifiers="" type="drawable"/><file name="ic_occasion_home" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_occasion_home.xml" qualifiers="" type="drawable"/><file name="ic_occasion_outdoor" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_occasion_outdoor.xml" qualifiers="" type="drawable"/><file name="ic_occasion_party" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_occasion_party.xml" qualifiers="" type="drawable"/><file name="ic_occasion_sports" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_occasion_sports.xml" qualifiers="" type="drawable"/><file name="ic_occasion_travel" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_occasion_travel.xml" qualifiers="" type="drawable"/><file name="ic_occasion_work" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_occasion_work.xml" qualifiers="" type="drawable"/><file name="ic_outfit" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_outfit.xml" qualifiers="" type="drawable"/><file name="ic_profile" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_profile.xml" qualifiers="" type="drawable"/><file name="ic_replace_photo" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_replace_photo.xml" qualifiers="" type="drawable"/><file name="ic_search" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_search.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_bag" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_bag.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_belt" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_belt.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_boots" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_boots.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_bra" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_bra.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_briefs" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_briefs.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_camisole" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_camisole.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_camisole_dress" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_camisole_dress.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_canvas_shoes" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_canvas_shoes.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_coat" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_coat.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_dress" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_dress.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_evening_dress" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_evening_dress.png" qualifiers="" type="drawable"/><file name="ic_subcategory_flared_skirt" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_flared_skirt.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_formal_dress" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_formal_dress.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_formal_pants" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_formal_pants.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_formal_shoes" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_formal_shoes.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_functional" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_functional.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_hat" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_hat.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_heels" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_heels.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_hoodie" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_hoodie.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_jacket" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_jacket.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_jeans" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_jeans.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_jewelry" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_jewelry.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_jumpsuit" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_jumpsuit.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_mermaid_skirt" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_mermaid_skirt.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_other_acc" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_other_acc.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_outer_vest" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_outer_vest.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_pajamas" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_pajamas.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_pants" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_pants.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_pencil_skirt" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_pencil_skirt.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_pleated_skirt" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_pleated_skirt.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_polo" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_polo.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_rain_boots" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_rain_boots.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_sandals" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_sandals.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_scarf" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_scarf.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_shirt" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_shirt.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_shorts" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_shorts.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_skirt" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_skirt.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_skirt_bottom" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_skirt_bottom.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_slit_skirt" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_slit_skirt.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_sneakers" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_sneakers.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_socks" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_socks.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_spaghetti_dress" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_spaghetti_dress.png" qualifiers="" type="drawable"/><file name="ic_subcategory_sport_pants" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_sport_pants.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_sport_shoes" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_sport_shoes.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_straight_skirt" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_straight_skirt.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_suit" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_suit.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_sweater" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_sweater.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_thermal" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_thermal.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_tracksuit" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_tracksuit.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_traditional" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_traditional.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_tshirt" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_tshirt.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_vest" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_vest.xml" qualifiers="" type="drawable"/><file name="ic_subcategory_wedding" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_subcategory_wedding.xml" qualifiers="" type="drawable"/><file name="ic_wardrobe" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ic_wardrobe.xml" qualifiers="" type="drawable"/><file name="ios_style_group_background_bottom" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ios_style_group_background_bottom.xml" qualifiers="" type="drawable"/><file name="ios_style_group_background_middle" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ios_style_group_background_middle.xml" qualifiers="" type="drawable"/><file name="ios_style_group_background_single" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ios_style_group_background_single.xml" qualifiers="" type="drawable"/><file name="ios_style_group_background_top" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\ios_style_group_background_top.xml" qualifiers="" type="drawable"/><file name="option_background" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\option_background.xml" qualifiers="" type="drawable"/><file name="replace_icon_background" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\replace_icon_background.xml" qualifiers="" type="drawable"/><file name="search_background" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\search_background.xml" qualifiers="" type="drawable"/><file name="secondary_system_grouped_background_ripple" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\secondary_system_grouped_background_ripple.xml" qualifiers="" type="drawable"/><file name="selected_item_background" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\selected_item_background.xml" qualifiers="" type="drawable"/><file name="spinner_background" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\drawable\spinner_background.xml" qualifiers="" type="drawable"/><file name="activity_add_clothing" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\activity_add_clothing.xml" qualifiers="" type="layout"/><file name="activity_batch_add_clothing" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\activity_batch_add_clothing.xml" qualifiers="" type="layout"/><file name="activity_category_select" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\activity_category_select.xml" qualifiers="" type="layout"/><file name="activity_clothing_detail" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\activity_clothing_detail.xml" qualifiers="" type="layout"/><file name="activity_color_select" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\activity_color_select.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_occasion_select" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\activity_occasion_select.xml" qualifiers="" type="layout"/><file name="dialog_category_selection" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\dialog_category_selection.xml" qualifiers="" type="layout"/><file name="dialog_color_selection" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\dialog_color_selection.xml" qualifiers="" type="layout"/><file name="dialog_image_fullscreen" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\dialog_image_fullscreen.xml" qualifiers="" type="layout"/><file name="dialog_occasion_selection" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\dialog_occasion_selection.xml" qualifiers="" type="layout"/><file name="dialog_photo_selection" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\dialog_photo_selection.xml" qualifiers="" type="layout"/><file name="dialog_photo_selection_ios" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\dialog_photo_selection_ios.xml" qualifiers="" type="layout"/><file name="dialog_recyclerview" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\dialog_recyclerview.xml" qualifiers="" type="layout"/><file name="fragment_calendar" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\fragment_calendar.xml" qualifiers="" type="layout"/><file name="fragment_profile" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\fragment_profile.xml" qualifiers="" type="layout"/><file name="fragment_wardrobe" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\fragment_wardrobe.xml" qualifiers="" type="layout"/><file name="item_calendar" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\item_calendar.xml" qualifiers="" type="layout"/><file name="item_category_selector" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\item_category_selector.xml" qualifiers="" type="layout"/><file name="item_clothing_horizontal" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\item_clothing_horizontal.xml" qualifiers="" type="layout"/><file name="item_clothing_pro" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\item_clothing_pro.xml" qualifiers="" type="layout"/><file name="item_color_selector" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\item_color_selector.xml" qualifiers="" type="layout"/><file name="item_color_spinner" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\item_color_spinner.xml" qualifiers="" type="layout"/><file name="item_occasion_selector" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\item_occasion_selector.xml" qualifiers="" type="layout"/><file name="item_outfit" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\item_outfit.xml" qualifiers="" type="layout"/><file name="item_wardrobe_group" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\item_wardrobe_group.xml" qualifiers="" type="layout"/><file name="layout_category_selector" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\layout_category_selector.xml" qualifiers="" type="layout"/><file name="layout_empty" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\layout_empty.xml" qualifiers="" type="layout"/><file name="layout_error" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\layout_error.xml" qualifiers="" type="layout"/><file name="layout_loading" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\layout_loading.xml" qualifiers="" type="layout"/><file name="list_item_selector_row" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\list_item_selector_row.xml" qualifiers="" type="layout"/><file name="multi_select_toolbar" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\multi_select_toolbar.xml" qualifiers="" type="layout"/><file name="spinner_item_with_icon" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\spinner_item_with_icon.xml" qualifiers="" type="layout"/><file name="action_mode_menu" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\menu\action_mode_menu.xml" qualifiers="" type="menu"/><file name="bottom_navigation_menu" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\menu\bottom_navigation_menu.xml" qualifiers="" type="menu"/><file name="menu" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\menu\menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\values\colors.xml" qualifiers=""><color name="md_theme_light_primary">#6750A4</color><color name="md_theme_light_onPrimary">#FFFFFF</color><color name="md_theme_light_primaryContainer">#EADDFF</color><color name="md_theme_light_onPrimaryContainer">#21005D</color><color name="md_theme_light_secondary">#625B71</color><color name="md_theme_light_onSecondary">#FFFFFF</color><color name="md_theme_light_secondaryContainer">#E8DEF8</color><color name="md_theme_light_onSecondaryContainer">#1D192B</color><color name="md_theme_light_tertiary">#7D5260</color><color name="md_theme_light_onTertiary">#FFFFFF</color><color name="md_theme_light_tertiaryContainer">#FFD8E4</color><color name="md_theme_light_onTertiaryContainer">#31111D</color><color name="md_theme_light_error">#BA1A1A</color><color name="md_theme_light_onError">#FFFFFF</color><color name="md_theme_light_errorContainer">#FFDAD6</color><color name="md_theme_light_onErrorContainer">#410002</color><color name="md_theme_light_background">#FFFBFE</color><color name="md_theme_light_onBackground">#1C1B1F</color><color name="md_theme_light_surface">#FFFBFE</color><color name="md_theme_light_onSurface">#1C1B1F</color><color name="md_theme_light_surfaceVariant">#E7E0EC</color><color name="md_theme_light_onSurfaceVariant">#49454F</color><color name="md_theme_light_outline">#79747E</color><color name="md_theme_light_outlineVariant">#CAC4D0</color><color name="md_theme_dark_primary">#D0BCFF</color><color name="md_theme_dark_onPrimary">#381E72</color><color name="md_theme_dark_primaryContainer">#4F378B</color><color name="md_theme_dark_onPrimaryContainer">#EADDFF</color><color name="md_theme_dark_secondary">#CCC2DC</color><color name="md_theme_dark_onSecondary">#332D41</color><color name="md_theme_dark_secondaryContainer">#4A4458</color><color name="md_theme_dark_onSecondaryContainer">#E8DEF8</color><color name="md_theme_dark_tertiary">#EFB8C8</color><color name="md_theme_dark_onTertiary">#492532</color><color name="md_theme_dark_tertiaryContainer">#633B48</color><color name="md_theme_dark_onTertiaryContainer">#FFD8E4</color><color name="md_theme_dark_error">#FFB4AB</color><color name="md_theme_dark_onError">#690005</color><color name="md_theme_dark_errorContainer">#93000A</color><color name="md_theme_dark_onErrorContainer">#FFDAD6</color><color name="md_theme_dark_background">#1C1B1F</color><color name="md_theme_dark_onBackground">#E6E1E5</color><color name="md_theme_dark_surface">#1C1B1F</color><color name="md_theme_dark_onSurface">#E6E1E5</color><color name="md_theme_dark_surfaceVariant">#49454F</color><color name="md_theme_dark_onSurfaceVariant">#CAC4D0</color><color name="md_theme_dark_outline">#938F99</color><color name="md_theme_dark_outlineVariant">#49454F</color><color name="primary">#6750A4</color><color name="primary_dark">#4F378B</color><color name="accent">#D0BCFF</color><color name="success">#4CAF50</color><color name="warning">#FF9800</color><color name="info">#2196F3</color><color name="background_primary">#FFFBFE</color><color name="background_secondary">#F7F2FA</color><color name="background_tertiary">#E7E0EC</color><color name="text_primary">#1C1B1F</color><color name="text_secondary">#49454F</color><color name="text_tertiary">#79747E</color><color name="text_inverse">#FFFFFF</color><color name="divider">#CAC4D0</color><color name="border">#79747E</color><color name="selected">#EADDFF</color><color name="pressed">#E8DEF8</color><color name="disabled">#F7F2FA</color><color name="black_alpha_12">#1F000000</color><color name="black_alpha_24">#3D000000</color><color name="black_alpha_54">#8A000000</color><color name="white_alpha_12">#1FFFFFFF</color><color name="white_alpha_24">#3DFFFFFF</color><color name="white_alpha_54">#8AFFFFFF</color><color name="shimmer_base">#F0F0F0</color><color name="shimmer_highlight">#FFFFFF</color><color name="overlay">#80000000</color><color name="category_tops">#E3F2FD</color><color name="category_bottoms">#F3E5F5</color><color name="category_outerwear">#E8F5E8</color><color name="category_shoes">#FFF3E0</color><color name="category_accessories">#FCE4EC</color><color name="category_other">#F5F5F5</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="systemBackground">#FFFFFF</color><color name="systemGroupedBackground">#F2F2F7</color><color name="text_secondary_ios">#8E8E93</color><color name="text_tertiary_ios">#C7C7CC</color><color name="surface_primary">#FFFFFF</color><color name="systemBlue">#007AFF</color><color name="systemGray">#8E8E93</color><color name="systemGray2">#AEAEB2</color><color name="systemGray3">#C7C7CC</color><color name="systemGray4">#D1D1D6</color><color name="systemGray5">#E5E5EA</color><color name="systemGray6">#F2F2F7</color><color name="separator">#3C3C43</color><color name="opaqueSeparator">#C6C6C8</color><color name="secondarySystemGroupedBackground">#F2F2F7</color><color name="placeholderText">#8E8E93</color><color name="destructive">#FF3B30</color><color name="labelPrimary">#000000</color><color name="labelSecondary">#3C3C43</color><color name="labelTertiary">#3C3C43</color><color name="labelQuaternary">#3C3C43</color><color name="fillTertiary">#D1D1D6</color><color name="systemRed">#FF3B30</color><color name="quaternarySystemGroupedBackground">#FFFFFF</color><color name="background_secondary_ios">#F2F2F7</color><color name="border_primary">#D1D1D6</color><color name="accent_primary">#007AFF</color><color name="background_primary_ios">#FFFFFF</color><color name="text_primary_ios">#000000</color><color name="destructiveRed">#FF3B30</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">WardrobeApp</string><string name="title_add_new_clothing">添加新衣物</string><string name="title_edit_clothing">编辑衣物</string><string name="button_save">保存</string><string name="hint_clothing_name">衣物名称</string><string name="section_required">必填信息</string><string name="section_optional">可选信息</string><string name="label_category">分类</string><string name="label_color">颜色</string><string name="label_occasion">场合</string><string name="category_placeholder">请选择分类</string><string name="color_placeholder">请选择颜色</string><string name="occasion_placeholder">请选择场合</string><string name="category_primary_placeholder">请选择主分类</string><string name="category_secondary_placeholder">请选择子分类</string><string name="placeholder_select">请选择</string><string name="dialog_title_select_color">选择颜色</string><string name="dialog_title_select_occasion">选择场合</string><string name="dialog_title_select_photo">选择图片</string><string name="dialog_option_camera">拍照</string><string name="dialog_option_gallery">从相册选择</string><string name="dialog_button_cancel">取消</string><string name="toast_save_successful">保存成功</string><string name="toast_save_failed">保存失败</string><string name="toast_load_failed">加载失败</string><string name="toast_select_image_first">请先选择一张图片</string><string name="toast_image_selection_cancelled">已取消选择图片</string><string name="toast_permissions_denied">权限被拒绝，无法选择图片</string><string name="error_load_data">加载数据失败</string><string name="error_get_storage_info">获取存储信息失败</string><string name="success_cleanup_all_images">清理完成，删除了 %1$d 个图片文件</string><string name="error_cleanup_images">清理失败: %1$s</string><string name="compressing">压缩中...</string><string name="compress_all_images">压缩所有图片</string><string name="success_compress_images">压缩完成，处理了 %1$d 个图片文件</string><string name="error_compress_images">压缩失败: %1$s</string><string name="error_start_compress">启动压缩失败: %1$s</string><string name="success_cleanup_unused_images">清理完成，删除了 %1$d 个未使用的图片文件</string><string name="error_main_category_missing_batch">主分类缺失，无法批量添加</string><string name="title_batch_add_prefix">批量添加：%1$s</string><string name="images_selected_count_batch">已选择%1$d张图片</string><string name="error_no_images_selected_batch">请至少选择一张图片</string><string name="error_incomplete_selection_batch">请先选择完整的分类</string><string name="success_batch_add_clothing_summary">成功添加%1$d件%2$s</string><string name="success_batch_add_clothing_summary_with_failures">成功添加%1$d件%2$s，%3$d张图片失败</string><string name="error_batch_add_all_failed">全部失败，共%1$d张图片未添加</string><string name="info_no_items_to_add_batch">没有可添加的衣物</string><string name="category_tops">上衣</string><string name="category_bottoms">下装</string><string name="category_dresses">连衣裙</string><string name="category_outerwear">外套</string><string name="category_shoes">鞋子</string><string name="category_accessories">配饰</string><string name="occasion_work">工作</string><string name="occasion_casual">休闲</string><string name="occasion_formal">正式</string><string name="occasion_sports">运动</string><string name="occasion_sport">运动</string><string name="occasion_party">派对</string><string name="occasion_travel">旅行</string><string name="occasion_home">居家</string><string name="occasion_date">约会</string><string name="occasion_daily">日常</string><string name="occasion_outdoor">户外</string><string name="color_custom">自定义</string><string name="color_black">黑色</string><string name="color_white">白色</string><string name="color_gray">灰色</string><string name="color_red">红色</string><string name="color_green">绿色</string><string name="color_blue">蓝色</string><string name="color_yellow">黄色</string><string name="color_pink">粉色</string><string name="color_purple">紫色</string><string name="color_orange">橙色</string><string name="color_brown">棕色</string><string name="color_beige">米色</string><string name="color_khaki">卡其色</string><string name="color_navy">藏青色</string><string name="toast_permission_granted_retry">权限已授予，请重试操作</string><string name="toast_permission_denied">权限被拒绝，无法继续操作</string><string name="toast_create_image_file_failed">无法创建图片文件</string><string name="nav_wardrobe">衣橱</string><string name="nav_outfit">搭配</string><string name="nav_calendar">日历</string><string name="nav_profile">个人</string><string name="button_add">添加</string><string name="button_cancel">取消</string><string name="button_confirm">确认</string><string name="button_delete">删除</string><string name="button_edit">编辑</string><string name="button_search">搜索</string><string name="button_filter">筛选</string><string name="button_clear">清除</string><string name="button_select_all">全选</string><string name="button_deselect_all">取消全选</string><string name="category_sets">套装</string><string name="category_suits">西装</string><string name="category_underwear">内衣</string><string name="category_workwear">工作服</string><string name="message_add_success">添加成功</string><string name="message_delete_success">删除成功</string><string name="message_save_success">保存成功</string><string name="message_update_success">更新成功</string><string name="message_operation_failed">操作失败</string><string name="message_network_error">网络连接失败</string><string name="message_permission_denied">权限被拒绝</string><string name="message_no_data">暂无数据</string><string name="message_loading">加载中...</string><string name="message_search_no_results">未找到搜索结果</string><string name="validation_name_required">衣物名称不能为空</string><string name="validation_category_required">分类不能为空</string><string name="validation_name_too_long">名称过长</string><string name="validation_invalid_input">输入无效</string><string name="photo_take_photo">拍照</string><string name="photo_choose_gallery">从相册选择</string><string name="photo_remove">移除照片</string><string name="search_hint">搜索衣物...</string><string name="filter_by_category">按分类筛选</string><string name="filter_by_color">按颜色筛选</string><string name="filter_by_occasion">按场合筛选</string><string name="filter_clear_all">清除所有筛选</string><string name="settings_language">语言</string><string name="settings_theme">主题</string><string name="settings_notifications">通知</string><string name="settings_privacy">隐私</string><string name="settings_about">关于</string><string name="error_network">网络连接失败</string><string name="error_permission">权限被拒绝</string><string name="error_file_not_found">文件未找到</string><string name="error_invalid_data">数据无效</string><string name="error_generic">发生错误</string><string name="accessibility_add_button">添加新衣物</string><string name="accessibility_search_button">搜索衣物</string><string name="accessibility_filter_button">筛选衣物</string><string name="accessibility_clothing_image">衣物图片</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\values\styles.xml" qualifiers=""><style name="iOSGroupedBackground">
        <item name="android:background">@color/white</item>
        <item name="android:orientation">vertical</item>
    </style><style name="iOSGroupedBackground.Top" parent="iOSGroupedBackground">
        <item name="android:background">@drawable/ios_style_group_background_top</item>
    </style><style name="iOSGroupedBackground.Middle" parent="iOSGroupedBackground">
        <item name="android:background">@drawable/ios_style_group_background_middle</item>
    </style><style name="iOSGroupedBackground.Bottom" parent="iOSGroupedBackground">
        <item name="android:background">@drawable/ios_style_group_background_bottom</item>
    </style><style name="iOSGroupedBackground.Single" parent="iOSGroupedBackground">
        <item name="android:background">@drawable/ios_style_group_background_single</item>
    </style><style name="iOSListItem">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:minHeight">44dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingLeft">16dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
        <item name="android:background">@drawable/secondary_system_grouped_background_ripple</item>
    </style><style name="iOSLabel">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_alignParentStart">true</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:textColor">@color/labelPrimary</item>
        <item name="android:textSize">17sp</item>
    </style><style name="iOSValue">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:textColor">@color/systemGray</item>
        <item name="android:textSize">17sp</item>
        <item name="android:layout_marginEnd">8dp</item>
    </style><style name="iOSChevron">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_alignParentEnd">true</item>
        <item name="android:layout_centerVertical">true</item>
        <item name="android:src">@drawable/ic_chevron_right</item>
        
    </style><style name="Separator">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">0.5dp</item>
        <item name="android:background">@color/separator</item>
    </style><style name="SectionHeader">
        <item name="android:textSize">13sp</item>
        <item name="android:textColor">@color/systemGray</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingBottom">8dp</item>
    </style><style name="ProminentButton" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background_prominent</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">17sp</item>
        <item name="android:textStyle">bold</item>
    </style><style name="DestructiveButton" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background_destructive</item>
        <item name="android:textColor">@color/destructiveRed</item>
        <item name="android:textSize">17sp</item>
    </style><style name="Base.Theme.WardrobeApp" parent="Theme.WardrobeApp.Material3">
        
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style><style name="Theme.WardrobeApp" parent="Base.Theme.WardrobeApp"/><style name="TextAppearance.App.Base" parent="">
        <item name="android:textColor">@color/labelPrimary</item>
        <item name="android:fontFamily">sans-serif</item>
    </style><style name="TextAppearance.App.LargeTitle" parent="TextAppearance.App.Base">
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:textSize">34sp</item>
    </style><style name="TextAppearance.App.Title1" parent="TextAppearance.App.Base">
        <item name="android:textSize">28sp</item>
    </style><style name="TextAppearance.App.Title2" parent="TextAppearance.App.Base">
        <item name="android:textSize">22sp</item>
    </style><style name="TextAppearance.App.Title3" parent="TextAppearance.App.Base">
        <item name="android:textSize">20sp</item>
    </style><style name="TextAppearance.App.Headline" parent="TextAppearance.App.Base">
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textSize">17sp</item>
    </style><style name="TextAppearance.App.Body" parent="TextAppearance.App.Base">
        <item name="android:textSize">17sp</item>
    </style><style name="TextAppearance.App.Callout" parent="TextAppearance.App.Base">
        <item name="android:textSize">16sp</item>
    </style><style name="TextAppearance.App.Subheadline" parent="TextAppearance.App.Base">
        <item name="android:textSize">15sp</item>
        <item name="android:textColor">@color/labelSecondary</item>
    </style><style name="TextAppearance.App.Footnote" parent="TextAppearance.App.Base">
        <item name="android:textSize">13sp</item>
        <item name="android:textColor">@color/labelSecondary</item>
    </style><style name="TextAppearance.App.Caption1" parent="TextAppearance.App.Base">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/labelSecondary</item>
    </style><style name="TextAppearance.App.Caption2" parent="TextAppearance.App.Base">
        <item name="android:textSize">11sp</item>
        <item name="android:textColor">@color/labelTertiary</item>
    </style><style name="ButtonPrimary" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background_prominent</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:minHeight">44dp</item>
        <item name="android:textAllCaps">false</item>
    </style><style name="ButtonSecondary" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background</item>
        <item name="android:textColor">@color/systemBlue</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:minHeight">44dp</item>
        <item name="android:textAllCaps">false</item>
    </style><style name="ButtonTertiary" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background</item>
        <item name="android:textColor">@color/labelSecondary</item>
        <item name="android:textSize">14sp</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:minHeight">32dp</item>
        <item name="android:textAllCaps">false</item>
    </style><style name="ButtonDestructive" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/button_background_destructive</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:minHeight">44dp</item>
        <item name="android:textAllCaps">false</item>
    </style><style name="BottomSheetDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/BottomSheetStyle</item>
    </style><style name="BottomSheetStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@android:color/transparent</item>
    </style><style name="Theme.WardrobeApp.Dialog" parent="Theme.Material3.DayNight.Dialog.Alert">
        <item name="colorPrimary">@color/systemBlue</item>
        <item name="android:background">@color/secondarySystemGroupedBackground</item>
        <item name="android:windowBackground">@drawable/dialog_rounded_background</item>
    </style><style name="Theme.WardrobeApp.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style><style name="Theme.WardrobeApp.BottomSheetDialog" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/AppModalStyle</item>
    </style><style name="AppModalStyle" parent="Widget.Design.BottomSheet.Modal">
        <item name="android:background">@drawable/dialog_rounded_background</item>
    </style><style name="Theme.WardrobeApp.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/><style name="Theme.WardrobeApp.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/><style name="Theme.WardrobeApp.EditTextStyle" parent="Widget.AppCompat.EditText">
        <item name="android:textColor">@color/labelPrimary</item>
        <item name="android:textColorHint">@color/placeholderText</item>
        <item name="android:textSize">16sp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
    </style><style name="Theme.WardrobeApp.Button.Prominent" parent="ButtonPrimary"/><style name="Theme.WardrobeApp.Button.Destructive" parent="ButtonDestructive"/></file><file path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\values\themes_material3.xml" qualifiers=""><style name="Theme.WardrobeApp.Material3" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>
        
        
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_light_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_light_onSecondaryContainer</item>
        
        
        <item name="colorTertiary">@color/md_theme_light_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_light_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_light_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_light_onTertiaryContainer</item>
        
        
        <item name="colorError">@color/md_theme_light_error</item>
        <item name="colorOnError">@color/md_theme_light_onError</item>
        <item name="colorErrorContainer">@color/md_theme_light_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_light_onErrorContainer</item>
        
        
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_light_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_onSurfaceVariant</item>
        
        
        <item name="colorOutline">@color/md_theme_light_outline</item>
        <item name="colorOutlineVariant">@color/md_theme_light_outlineVariant</item>
        
        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar">true</item>
        
        
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        
        
        <item name="textAppearanceHeadlineLarge">@style/TextAppearance.WardrobeApp.HeadlineLarge</item>
        <item name="textAppearanceHeadlineMedium">@style/TextAppearance.WardrobeApp.HeadlineMedium</item>
        <item name="textAppearanceHeadlineSmall">@style/TextAppearance.WardrobeApp.HeadlineSmall</item>
        <item name="textAppearanceTitleLarge">@style/TextAppearance.WardrobeApp.TitleLarge</item>
        <item name="textAppearanceTitleMedium">@style/TextAppearance.WardrobeApp.TitleMedium</item>
        <item name="textAppearanceTitleSmall">@style/TextAppearance.WardrobeApp.TitleSmall</item>
        <item name="textAppearanceBodyLarge">@style/TextAppearance.WardrobeApp.BodyLarge</item>
        <item name="textAppearanceBodyMedium">@style/TextAppearance.WardrobeApp.BodyMedium</item>
        <item name="textAppearanceBodySmall">@style/TextAppearance.WardrobeApp.BodySmall</item>
        <item name="textAppearanceLabelLarge">@style/TextAppearance.WardrobeApp.LabelLarge</item>
        <item name="textAppearanceLabelMedium">@style/TextAppearance.WardrobeApp.LabelMedium</item>
        <item name="textAppearanceLabelSmall">@style/TextAppearance.WardrobeApp.LabelSmall</item>
        
        
        <item name="materialButtonStyle">@style/Widget.WardrobeApp.Button</item>
        <item name="materialCardViewStyle">@style/Widget.WardrobeApp.CardView</item>
        <item name="chipStyle">@style/Widget.WardrobeApp.Chip</item>
        <item name="textInputStyle">@style/Widget.WardrobeApp.TextInputLayout</item>
        
        
        <item name="android:windowAnimationStyle">@style/Animation.WardrobeApp.Activity</item>
        <item name="android:windowEnterAnimation">@anim/slide_in_right</item>
        <item name="android:windowExitAnimation">@anim/slide_out_left</item>
    </style><style name="Theme.WardrobeApp.Material3.Dark" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/md_theme_dark_primary</item>
        <item name="colorOnPrimary">@color/md_theme_dark_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_dark_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_dark_onPrimaryContainer</item>
        
        
        <item name="colorSecondary">@color/md_theme_dark_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_dark_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_dark_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_dark_onSecondaryContainer</item>
        
        
        <item name="colorTertiary">@color/md_theme_dark_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_dark_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_dark_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_dark_onTertiaryContainer</item>
        
        
        <item name="colorError">@color/md_theme_dark_error</item>
        <item name="colorOnError">@color/md_theme_dark_onError</item>
        <item name="colorErrorContainer">@color/md_theme_dark_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_dark_onErrorContainer</item>
        
        
        <item name="android:colorBackground">@color/md_theme_dark_background</item>
        <item name="colorOnBackground">@color/md_theme_dark_onBackground</item>
        <item name="colorSurface">@color/md_theme_dark_surface</item>
        <item name="colorOnSurface">@color/md_theme_dark_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_dark_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_dark_onSurfaceVariant</item>
        
        
        <item name="colorOutline">@color/md_theme_dark_outline</item>
        <item name="colorOutlineVariant">@color/md_theme_dark_outlineVariant</item>
        
        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
        
        
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
    </style><style name="TextAppearance.WardrobeApp.HeadlineLarge" parent="TextAppearance.Material3.HeadlineLarge">
        <item name="android:fontFamily">sans-serif-medium</item>
    </style><style name="TextAppearance.WardrobeApp.HeadlineMedium" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:fontFamily">sans-serif-medium</item>
    </style><style name="TextAppearance.WardrobeApp.HeadlineSmall" parent="TextAppearance.Material3.HeadlineSmall">
        <item name="android:fontFamily">sans-serif-medium</item>
    </style><style name="TextAppearance.WardrobeApp.TitleLarge" parent="TextAppearance.Material3.TitleLarge">
        <item name="android:fontFamily">sans-serif-medium</item>
    </style><style name="TextAppearance.WardrobeApp.TitleMedium" parent="TextAppearance.Material3.TitleMedium">
        <item name="android:fontFamily">sans-serif-medium</item>
    </style><style name="TextAppearance.WardrobeApp.TitleSmall" parent="TextAppearance.Material3.TitleSmall">
        <item name="android:fontFamily">sans-serif-medium</item>
    </style><style name="TextAppearance.WardrobeApp.BodyLarge" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:fontFamily">sans-serif</item>
    </style><style name="TextAppearance.WardrobeApp.BodyMedium" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:fontFamily">sans-serif</item>
    </style><style name="TextAppearance.WardrobeApp.BodySmall" parent="TextAppearance.Material3.BodySmall">
        <item name="android:fontFamily">sans-serif</item>
    </style><style name="TextAppearance.WardrobeApp.LabelLarge" parent="TextAppearance.Material3.LabelLarge">
        <item name="android:fontFamily">sans-serif-medium</item>
    </style><style name="TextAppearance.WardrobeApp.LabelMedium" parent="TextAppearance.Material3.LabelMedium">
        <item name="android:fontFamily">sans-serif-medium</item>
    </style><style name="TextAppearance.WardrobeApp.LabelSmall" parent="TextAppearance.Material3.LabelSmall">
        <item name="android:fontFamily">sans-serif-medium</item>
    </style><style name="Widget.WardrobeApp.Button" parent="Widget.Material3.Button">
        <item name="android:textAppearance">@style/TextAppearance.WardrobeApp.LabelLarge</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
    </style><style name="Widget.WardrobeApp.Button.Outlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textAppearance">@style/TextAppearance.WardrobeApp.LabelLarge</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
    </style><style name="Widget.WardrobeApp.Button.Text" parent="Widget.Material3.Button.TextButton">
        <item name="android:textAppearance">@style/TextAppearance.WardrobeApp.LabelLarge</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
    </style><style name="Widget.WardrobeApp.CardView" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">16dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:layout_margin">8dp</item>
    </style><style name="Widget.WardrobeApp.Chip" parent="Widget.Material3.Chip.Filter">
        <item name="chipCornerRadius">20dp</item>
        <item name="chipMinHeight">40dp</item>
        <item name="android:textAppearance">@style/TextAppearance.WardrobeApp.LabelMedium</item>
    </style><style name="Widget.WardrobeApp.TextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxCornerRadiusTopStart">12dp</item>
        <item name="boxCornerRadiusTopEnd">12dp</item>
        <item name="boxCornerRadiusBottomStart">12dp</item>
        <item name="boxCornerRadiusBottomEnd">12dp</item>
        <item name="android:layout_marginBottom">16dp</item>
    </style><style name="Animation.WardrobeApp.Activity" parent="@android:style/Animation.Activity">
        <item name="android:activityOpenEnterAnimation">@anim/slide_in_right</item>
        <item name="android:activityOpenExitAnimation">@anim/slide_out_left</item>
        <item name="android:activityCloseEnterAnimation">@anim/slide_in_left</item>
        <item name="android:activityCloseExitAnimation">@anim/slide_out_right</item>
    </style></file><file path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\values-en\strings.xml" qualifiers="en"><string name="app_name">Wardrobe Manager</string><string name="title_add_new_clothing">Add New Clothing</string><string name="title_edit_clothing">Edit Clothing</string><string name="button_save">Save</string><string name="hint_clothing_name">Clothing Name</string><string name="section_required">Required Information</string><string name="section_optional">Optional Information</string><string name="label_category">Category</string><string name="label_color">Color</string><string name="label_occasion">Occasion</string><string name="category_placeholder">Select Category</string><string name="color_placeholder">Select Color</string><string name="occasion_placeholder">Select Occasion</string><string name="category_primary_placeholder">Select Main Category</string><string name="category_secondary_placeholder">Select Subcategory</string><string name="placeholder_select">Please Select</string><string name="dialog_title_select_color">Select Color</string><string name="dialog_title_select_occasion">Select Occasion</string><string name="dialog_title_select_photo">Select Photo</string><string name="button_cancel">Cancel</string><string name="button_confirm">Confirm</string><string name="button_delete">Delete</string><string name="button_edit">Edit</string><string name="button_add">Add</string><string name="button_search">Search</string><string name="button_filter">Filter</string><string name="button_clear">Clear</string><string name="button_select_all">Select All</string><string name="button_deselect_all">Deselect All</string><string name="nav_wardrobe">Wardrobe</string><string name="nav_outfit">Outfit</string><string name="nav_calendar">Calendar</string><string name="nav_profile">Profile</string><string name="category_tops">Tops</string><string name="category_bottoms">Bottoms</string><string name="category_dresses">Dresses</string><string name="category_outerwear">Outerwear</string><string name="category_shoes">Shoes</string><string name="category_accessories">Accessories</string><string name="category_underwear">Underwear</string><string name="category_suits">Suits</string><string name="category_sets">Sets</string><string name="category_workwear">Workwear</string><string name="color_red">Red</string><string name="color_blue">Blue</string><string name="color_green">Green</string><string name="color_yellow">Yellow</string><string name="color_black">Black</string><string name="color_white">White</string><string name="color_gray">Gray</string><string name="color_brown">Brown</string><string name="color_pink">Pink</string><string name="color_purple">Purple</string><string name="color_orange">Orange</string><string name="color_navy">Navy</string><string name="occasion_casual">Casual</string><string name="occasion_formal">Formal</string><string name="occasion_work">Work</string><string name="occasion_party">Party</string><string name="occasion_sports">Sports</string><string name="occasion_travel">Travel</string><string name="occasion_date">Date</string><string name="occasion_home">Home</string><string name="occasion_outdoor">Outdoor</string><string name="occasion_daily">Daily</string><string name="message_save_success">Saved successfully</string><string name="message_delete_success">Deleted successfully</string><string name="message_add_success">Added successfully</string><string name="message_update_success">Updated successfully</string><string name="message_operation_failed">Operation failed</string><string name="message_network_error">Network connection failed</string><string name="message_permission_denied">Permission denied</string><string name="message_no_data">No data available</string><string name="message_loading">Loading...</string><string name="message_search_no_results">No search results found</string><string name="validation_name_required">Clothing name is required</string><string name="validation_category_required">Category is required</string><string name="validation_name_too_long">Name is too long</string><string name="validation_invalid_input">Invalid input</string><string name="photo_take_photo">Take Photo</string><string name="photo_choose_gallery">Choose from Gallery</string><string name="photo_remove">Remove Photo</string><string name="search_hint">Search clothing...</string><string name="filter_by_category">Filter by Category</string><string name="filter_by_color">Filter by Color</string><string name="filter_by_occasion">Filter by Occasion</string><string name="filter_clear_all">Clear All Filters</string><string name="settings_language">Language</string><string name="settings_theme">Theme</string><string name="settings_notifications">Notifications</string><string name="settings_privacy">Privacy</string><string name="settings_about">About</string><string name="error_generic">An error occurred</string><string name="error_network">Network error</string><string name="error_permission">Permission required</string><string name="error_file_not_found">File not found</string><string name="error_invalid_data">Invalid data</string><string name="accessibility_add_button">Add new clothing item</string><string name="accessibility_search_button">Search clothing</string><string name="accessibility_filter_button">Filter clothing</string><string name="accessibility_clothing_image">Clothing item image</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\values-night\colors.xml" qualifiers="night-v8"><color name="colorPrimary">#BB86FC</color><color name="colorPrimaryVariant">#3700B3</color><color name="colorOnPrimary">#000000</color><color name="colorSecondary">#03DAC6</color><color name="colorSecondaryVariant">#018786</color><color name="colorOnSecondary">#000000</color><color name="colorBackground">#121212</color><color name="colorOnBackground">#FFFFFF</color><color name="colorSurface">#1E1E1E</color><color name="colorOnSurface">#FFFFFF</color><color name="colorError">#CF6679</color><color name="colorOnError">#000000</color><color name="textColorPrimary">#FFFFFF</color><color name="textColorSecondary">#B3FFFFFF</color><color name="textColorHint">#66FFFFFF</color><color name="dividerColor">#33FFFFFF</color><color name="borderColor">#33FFFFFF</color><color name="cardBackground">#2C2C2C</color><color name="containerBackground">#1E1E1E</color><color name="buttonBackground">#BB86FC</color><color name="buttonTextColor">#000000</color><color name="statusBarColor">#000000</color><color name="navigationBarColor">#121212</color><color name="selectedItemBackground">#33BB86FC</color><color name="rippleColor">#33FFFFFF</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\values-night\styles.xml" qualifiers="night-v8"><style name="AppTheme" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryVariant">@color/colorPrimaryVariant</item>
        <item name="colorOnPrimary">@color/colorOnPrimary</item>
        
        
        <item name="colorSecondary">@color/colorSecondary</item>
        <item name="colorSecondaryVariant">@color/colorSecondaryVariant</item>
        <item name="colorOnSecondary">@color/colorOnSecondary</item>
        
        
        <item name="android:colorBackground">@color/colorBackground</item>
        <item name="colorOnBackground">@color/colorOnBackground</item>
        
        
        <item name="colorSurface">@color/colorSurface</item>
        <item name="colorOnSurface">@color/colorOnSurface</item>
        
        
        <item name="colorError">@color/colorError</item>
        <item name="colorOnError">@color/colorOnError</item>
        
        
        <item name="android:statusBarColor">@color/statusBarColor</item>
        <item name="android:windowLightStatusBar">false</item>
        
        
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:windowLightNavigationBar">false</item>
        
        
        <item name="android:textColorPrimary">@color/textColorPrimary</item>
        <item name="android:textColorSecondary">@color/textColorSecondary</item>
        <item name="android:textColorHint">@color/textColorHint</item>
        
        
        <item name="android:forceDarkAllowed">true</item>
    </style><style name="ButtonStyle" parent="Widget.Material3.Button">
        <item name="android:background">@color/buttonBackground</item>
        <item name="android:textColor">@color/buttonTextColor</item>
        <item name="backgroundTint">@color/buttonBackground</item>
    </style><style name="CardStyle" parent="Widget.Material3.CardView.Elevated">
        <item name="cardBackgroundColor">@color/cardBackground</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style></file><file name="backup_rules" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="fragment_outfit" path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\main\res\layout\fragment_outfit.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\WardrobeApp\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>