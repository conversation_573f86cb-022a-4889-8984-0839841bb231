<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Package com.wardrobe.app.utils</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Package com.wardrobe.app.utils</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; com.wardrobe.app.utils</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">68</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">41</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">1</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.305s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">38%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Ignored tests</a>
</li>
<li>
<a href="#tab2">Classes</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<ul class="linkList">
<li>
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html">AsyncTaskManagerTest</a>.
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html#testCancelDelayedTask">testCancelDelayedTask</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html">AsyncTaskManagerTest</a>.
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html#testExecuteBackground_Error">testExecuteBackground_Error</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html">AsyncTaskManagerTest</a>.
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html#testExecuteBackground_Success">testExecuteBackground_Success</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html">AsyncTaskManagerTest</a>.
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html#testExecuteDelayedTask">testExecuteDelayedTask</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html">AsyncTaskManagerTest</a>.
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html#testExecuteOnMainThread">testExecuteOnMainThread</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html">AsyncTaskManagerTest</a>.
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html#testExecuteVoidBackground_Success">testExecuteVoidBackground_Success</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html">AsyncTaskManagerTest</a>.
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html#testGetThreadPoolStatus">testGetThreadPoolStatus</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html">AsyncTaskManagerTest</a>.
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html#testIOTaskExecution">testIOTaskExecution</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html">AsyncTaskManagerTest</a>.
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html#testIsMainThread">testIsMainThread</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html">AsyncTaskManagerTest</a>.
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html#testMultipleConcurrentTasks">testMultipleConcurrentTasks</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html">AsyncTaskManagerTest</a>.
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html#testTaskExecutionOrder">testTaskExecutionOrder</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testIsFilePathSafe_AbsolutePath">testIsFilePathSafe_AbsolutePath</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testIsFilePathSafe_EmptyPath">testIsFilePathSafe_EmptyPath</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testIsFilePathSafe_PathTraversal">testIsFilePathSafe_PathTraversal</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testIsFilePathSafe_SafePath">testIsFilePathSafe_SafePath</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testSQLInjectionDetection">testSQLInjectionDetection</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testScriptInjectionDetection">testScriptInjectionDetection</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidateBrandName_EmptyInput">testValidateBrandName_EmptyInput</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidateBrandName_ValidInput">testValidateBrandName_ValidInput</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidateCategory_EmptyInput">testValidateCategory_EmptyInput</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidateCategory_ValidInput">testValidateCategory_ValidInput</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidateClothingName_EmptyInput">testValidateClothingName_EmptyInput</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidateClothingName_InvalidCharacters">testValidateClothingName_InvalidCharacters</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidateClothingName_NullInput">testValidateClothingName_NullInput</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidateClothingName_TooLong">testValidateClothingName_TooLong</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidateClothingName_ValidInput">testValidateClothingName_ValidInput</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidateColor_EmptyInput">testValidateColor_EmptyInput</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidateColor_ValidInput">testValidateColor_ValidInput</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidateDescription_TooLong">testValidateDescription_TooLong</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidateDescription_ValidInput">testValidateDescription_ValidInput</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidateId_EmptyId">testValidateId_EmptyId</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidateId_InvalidFormat">testValidateId_InvalidFormat</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidateId_ValidNumericId">testValidateId_ValidNumericId</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidateId_ValidUUID">testValidateId_ValidUUID</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidatePrice_EmptyInput">testValidatePrice_EmptyInput</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidatePrice_InvalidFormat">testValidatePrice_InvalidFormat</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidatePrice_NegativeValue">testValidatePrice_NegativeValue</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidatePrice_TooLarge">testValidatePrice_TooLarge</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidatePrice_ValidInput">testValidatePrice_ValidInput</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidateSize_EmptyInput">testValidateSize_EmptyInput</a>
</li>
<li>
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>.
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html#testValidateSize_ValidInput">testValidateSize_ValidInput</a>
</li>
</ul>
</div>
<div id="tab1" class="tab">
<h2>Ignored tests</h2>
<ul class="linkList">
<li>
<a href="../classes/com.wardrobe.app.utils.SecurityUtilsTest.html">SecurityUtilsTest</a>.
<a href="../classes/com.wardrobe.app.utils.SecurityUtilsTest.html#testEncryptAndDecryptData">testEncryptAndDecryptData</a>
</li>
</ul>
</div>
<div id="tab2" class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tr>
<td class="failures">
<a href="../classes/com.wardrobe.app.utils.AsyncTaskManagerTest.html">AsyncTaskManagerTest</a>
</td>
<td>11</td>
<td>11</td>
<td>0</td>
<td>0.017s</td>
<td class="failures">0%</td>
</tr>
<tr>
<td class="failures">
<a href="../classes/com.wardrobe.app.utils.InputValidatorTest.html">InputValidatorTest</a>
</td>
<td>33</td>
<td>30</td>
<td>0</td>
<td>0.041s</td>
<td class="failures">9%</td>
</tr>
<tr>
<td class="skipped">
<a href="../classes/com.wardrobe.app.utils.SecurityUtilsTest.html">SecurityUtilsTest</a>
</td>
<td>24</td>
<td>0</td>
<td>1</td>
<td>0.247s</td>
<td class="skipped">100%</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.11.1</a> at 2025年6月25日 20:44:37</p>
</div>
</div>
</body>
</html>
