<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary">

    <!-- 顶部导航栏 -->
    <LinearLayout
        android:id="@+id/nav_bar"
        android:layout_width="0dp"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="@color/background_primary"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@android:color/transparent"
            android:src="@drawable/ic_chevron_right"
            android:rotation="180"
            android:contentDescription="返回"
            app:tint="@color/text_primary" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="选择分类"
            android:textColor="@color/text_primary"
            android:textSize="20sp"
            android:textStyle="bold"
            android:gravity="center" />

        <View
            android:layout_width="48dp"
            android:layout_height="48dp" />
    </LinearLayout>

    <!-- 分类列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:clipToPadding="false"
        app:layout_constraintTop_toBottomOf="@id/nav_bar"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:paddingStart="0dp"
        android:paddingEnd="0dp"
        tools:listitem="@layout/item_category_selector" />

</androidx.constraintlayout.widget.ConstraintLayout> 