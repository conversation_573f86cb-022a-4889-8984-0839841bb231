package com.wardrobe.app.utils;

import android.os.Handler;
import android.os.Looper;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 异步任务管理器
 * 提供统一的后台任务执行和主线程回调机制
 * 管理线程池，避免创建过多线程
 */
public class AsyncTaskManager {
    
    private static final String TAG = "AsyncTaskManager";
    private static volatile AsyncTaskManager instance;
    private static final Object LOCK = new Object();
    
    // 线程池配置
    private static final int CORE_POOL_SIZE = 2;
    private static final int MAXIMUM_POOL_SIZE = 4;
    private static final long KEEP_ALIVE_TIME = 30L;
    
    private final ExecutorService backgroundExecutor;
    private final ExecutorService ioExecutor;
    private final Handler mainHandler;
    
    /**
     * 任务回调接口
     */
    public interface TaskCallback<T> {
        void onSuccess(T result);
        void onError(Exception error);
    }
    
    /**
     * 简单任务接口
     */
    public interface SimpleTask<T> {
        T execute() throws Exception;
    }
    
    /**
     * 无返回值任务接口
     */
    public interface VoidTask {
        void execute() throws Exception;
    }
    
    /**
     * 无返回值任务回调接口
     */
    public interface VoidTaskCallback {
        void onSuccess();
        void onError(Exception error);
    }
    
    private AsyncTaskManager() {
        // 创建后台任务线程池
        backgroundExecutor = Executors.newFixedThreadPool(CORE_POOL_SIZE);
        
        // 创建IO任务线程池
        ioExecutor = Executors.newFixedThreadPool(MAXIMUM_POOL_SIZE);
        
        // 主线程Handler
        mainHandler = new Handler(Looper.getMainLooper());
        
        Logger.d(TAG, "AsyncTaskManager 初始化完成");
    }
    
    /**
     * 获取AsyncTaskManager实例
     * 
     * @return AsyncTaskManager实例
     */
    public static AsyncTaskManager getInstance() {
        if (instance == null) {
            synchronized (LOCK) {
                if (instance == null) {
                    instance = new AsyncTaskManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 执行后台任务（CPU密集型）
     * 
     * @param task 任务
     * @param callback 回调
     * @param <T> 返回类型
     * @return Future对象
     */
    public <T> Future<?> executeBackground(SimpleTask<T> task, TaskCallback<T> callback) {
        return backgroundExecutor.submit(() -> {
            try {
                long startTime = System.currentTimeMillis();
                T result = task.execute();
                long executionTime = System.currentTimeMillis() - startTime;
                
                Logger.d(TAG, "后台任务执行完成，耗时: " + executionTime + "ms");
                
                // 切换到主线程执行回调
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onSuccess(result);
                    }
                });
                
            } catch (Exception e) {
                Logger.e(TAG, "后台任务执行失败", e);
                
                // 切换到主线程执行错误回调
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError(e);
                    }
                });
            }
        });
    }
    
    /**
     * 执行IO任务（IO密集型）
     * 
     * @param task 任务
     * @param callback 回调
     * @param <T> 返回类型
     * @return Future对象
     */
    public <T> Future<?> executeIO(SimpleTask<T> task, TaskCallback<T> callback) {
        return ioExecutor.submit(() -> {
            try {
                long startTime = System.currentTimeMillis();
                T result = task.execute();
                long executionTime = System.currentTimeMillis() - startTime;
                
                Logger.d(TAG, "IO任务执行完成，耗时: " + executionTime + "ms");
                
                // 切换到主线程执行回调
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onSuccess(result);
                    }
                });
                
            } catch (Exception e) {
                Logger.e(TAG, "IO任务执行失败", e);
                
                // 切换到主线程执行错误回调
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError(e);
                    }
                });
            }
        });
    }
    
    /**
     * 执行无返回值的后台任务
     * 
     * @param task 任务
     * @param callback 回调
     * @return Future对象
     */
    public Future<?> executeVoidBackground(VoidTask task, VoidTaskCallback callback) {
        return backgroundExecutor.submit(() -> {
            try {
                long startTime = System.currentTimeMillis();
                task.execute();
                long executionTime = System.currentTimeMillis() - startTime;
                
                Logger.d(TAG, "无返回值后台任务执行完成，耗时: " + executionTime + "ms");
                
                // 切换到主线程执行回调
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onSuccess();
                    }
                });
                
            } catch (Exception e) {
                Logger.e(TAG, "无返回值后台任务执行失败", e);
                
                // 切换到主线程执行错误回调
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError(e);
                    }
                });
            }
        });
    }
    
    /**
     * 执行无返回值的IO任务
     * 
     * @param task 任务
     * @param callback 回调
     * @return Future对象
     */
    public Future<?> executeVoidIO(VoidTask task, VoidTaskCallback callback) {
        return ioExecutor.submit(() -> {
            try {
                long startTime = System.currentTimeMillis();
                task.execute();
                long executionTime = System.currentTimeMillis() - startTime;
                
                Logger.d(TAG, "无返回值IO任务执行完成，耗时: " + executionTime + "ms");
                
                // 切换到主线程执行回调
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onSuccess();
                    }
                });
                
            } catch (Exception e) {
                Logger.e(TAG, "无返回值IO任务执行失败", e);
                
                // 切换到主线程执行错误回调
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError(e);
                    }
                });
            }
        });
    }
    
    /**
     * 在主线程执行任务
     * 
     * @param task 任务
     */
    public void executeOnMainThread(Runnable task) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            // 已经在主线程，直接执行
            task.run();
        } else {
            // 切换到主线程执行
            mainHandler.post(task);
        }
    }
    
    /**
     * 延迟在主线程执行任务
     * 
     * @param task 任务
     * @param delayMillis 延迟时间（毫秒）
     */
    public void executeOnMainThreadDelayed(Runnable task, long delayMillis) {
        mainHandler.postDelayed(task, delayMillis);
    }
    
    /**
     * 取消延迟任务
     * 
     * @param task 任务
     */
    public void cancelDelayedTask(Runnable task) {
        mainHandler.removeCallbacks(task);
    }
    
    /**
     * 获取线程池状态信息
     * 
     * @return 状态信息
     */
    public String getThreadPoolStatus() {
        StringBuilder status = new StringBuilder();
        
        if (backgroundExecutor instanceof ThreadPoolExecutor) {
            ThreadPoolExecutor tpe = (ThreadPoolExecutor) backgroundExecutor;
            status.append("后台线程池: ")
                  .append("活跃线程=").append(tpe.getActiveCount())
                  .append(", 队列大小=").append(tpe.getQueue().size())
                  .append(", 已完成任务=").append(tpe.getCompletedTaskCount());
        }
        
        if (ioExecutor instanceof ThreadPoolExecutor) {
            ThreadPoolExecutor tpe = (ThreadPoolExecutor) ioExecutor;
            status.append(" | IO线程池: ")
                  .append("活跃线程=").append(tpe.getActiveCount())
                  .append(", 队列大小=").append(tpe.getQueue().size())
                  .append(", 已完成任务=").append(tpe.getCompletedTaskCount());
        }
        
        return status.toString();
    }
    
    /**
     * 关闭线程池
     */
    public void shutdown() {
        Logger.d(TAG, "关闭线程池");
        
        backgroundExecutor.shutdown();
        ioExecutor.shutdown();
        
        try {
            if (!backgroundExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                backgroundExecutor.shutdownNow();
            }
            if (!ioExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                ioExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            Logger.e(TAG, "关闭线程池时被中断", e);
            backgroundExecutor.shutdownNow();
            ioExecutor.shutdownNow();
        }
    }
    
    /**
     * 检查是否在主线程
     * 
     * @return 是否在主线程
     */
    public static boolean isMainThread() {
        return Looper.myLooper() == Looper.getMainLooper();
    }
    
    /**
     * 确保在主线程执行
     * 
     * @param task 任务
     */
    public static void ensureMainThread(Runnable task) {
        if (isMainThread()) {
            task.run();
        } else {
            new Handler(Looper.getMainLooper()).post(task);
        }
    }
}
