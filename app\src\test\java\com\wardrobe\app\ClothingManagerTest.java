package com.wardrobe.app;

import android.content.Context;
import android.content.SharedPreferences;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.doNothing;

import com.wardrobe.app.model.ClothingItem;
import com.wardrobe.app.data.ClothingManager;

@RunWith(MockitoJUnitRunner.class)
public class ClothingManagerTest {

    @Mock
    private Context mockContext;
    
    @Mock
    private SharedPreferences mockSharedPreferences;
    
    @Mock
    private SharedPreferences.Editor mockEditor;

    private ClothingManager clothingManager;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(mockContext.getSharedPreferences(anyString(), eq(Context.MODE_PRIVATE)))
                .thenReturn(mockSharedPreferences);
        when(mockContext.getApplicationContext()).thenReturn(mockContext);
        when(mockSharedPreferences.edit()).thenReturn(mockEditor);
        when(mockEditor.putString(anyString(), anyString())).thenReturn(mockEditor);
        doNothing().when(mockEditor).apply();
        clothingManager = ClothingManager.getInstance(mockContext);
    }

    @Test
    public void testGetClothingList_EmptyList() {
        when(mockSharedPreferences.getString(eq("clothing_list"), eq("[]")))
                .thenReturn("[]");

        List<ClothingItem> result = clothingManager.getClothingList();

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testAddClothingItem() {
        when(mockSharedPreferences.getString(eq("clothing_list"), eq("[]")))
                .thenReturn("[]");

        ClothingItem item = new ClothingItem();
        item.setName("测试衣物");
        item.setCategory("上衣");

        clothingManager.addClothingItem(item);

        // 验证保存操作被调用
        // 注意：由于我们使用了mock，实际的保存逻辑不会执行
        // 这里主要测试方法调用不会抛出异常
        assertNotNull(item.getId());
        assertEquals("测试衣物", item.getName());
    }
}