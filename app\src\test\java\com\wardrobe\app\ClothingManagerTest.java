package com.wardrobe.app;

import android.content.Context;
import android.content.SharedPreferences;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.wardrobe.app.model.ClothingItem;
import com.wardrobe.app.data.ClothingManager;

@RunWith(MockitoJUnitRunner.class)
public class ClothingManagerTest {

    @Mock
    private Context mockContext;
    
    @Mock
    private SharedPreferences mockSharedPreferences;
    
    @Mock
    private SharedPreferences.Editor mockEditor;

    private ClothingManager clothingManager;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        when(mockContext.getSharedPreferences(anyString(), eq(Context.MODE_PRIVATE)))
                .thenReturn(mockSharedPreferences);
        when(mockContext.getApplicationContext()).thenReturn(mockContext);
        clothingManager = ClothingManager.getInstance(mockContext);
    }

    @Test
    public void testGetClothingList_EmptyList() {
        when(mockSharedPreferences.getString(eq("clothing_list"), eq("")))
                .thenReturn("");
        
        List<ClothingItem> result = clothingManager.getClothingList();
        
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
} 