<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:paddingHorizontal="16dp"
    android:paddingVertical="12dp"
    android:background="@drawable/secondary_system_grouped_background_ripple"
    android:clickable="true"
    android:focusable="true">

    <ImageView
        android:id="@+id/color_circle"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:src="@drawable/color_circle_background" />

    <View
        android:id="@+id/color_preview_circle"
        android:layout_width="28dp"
        android:layout_height="28dp"
        android:background="@drawable/color_circle_background" />

    <TextView
        android:id="@+id/color_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:text="颜色名称"
        android:textAppearance="?attr/textAppearanceBody1"
        android:textColor="@color/labelPrimary" />

</LinearLayout> 