package com.wardrobe.app.ui.components;

import android.content.Context;
import com.wardrobe.app.model.CategoryItem;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CategoryProvider {

    private static final Map<String, List<CategoryItem>> subCategoryMap = new HashMap<>();

    static {
        // Initialize subcategories
        List<CategoryItem> topsSub = new ArrayList<>();
        topsSub.add(new CategoryItem("T恤", "ic_subcategory_tshirt"));
        topsSub.add(new CategoryItem("Polo衫", "ic_subcategory_polo"));
        topsSub.add(new CategoryItem("衬衫", "ic_subcategory_shirt"));
        topsSub.add(new CategoryItem("卫衣", "ic_subcategory_hoodie"));
        topsSub.add(new CategoryItem("毛衣", "ic_subcategory_sweater"));
        topsSub.add(new CategoryItem("背心", "ic_subcategory_vest"));
        topsSub.add(new CategoryItem("吊带", "ic_subcategory_camisole"));
        subCategoryMap.put("上装", topsSub);

        List<CategoryItem> bottomsSub = new ArrayList<>();
        bottomsSub.add(new CategoryItem("牛仔裤", "ic_subcategory_jeans"));
        bottomsSub.add(new CategoryItem("休闲裤", "ic_subcategory_pants"));
        bottomsSub.add(new CategoryItem("正装裤", "ic_subcategory_formal_pants"));
        bottomsSub.add(new CategoryItem("运动裤", "ic_subcategory_sport_pants"));
        bottomsSub.add(new CategoryItem("短裤", "ic_subcategory_shorts"));
        bottomsSub.add(new CategoryItem("半身裙", "ic_subcategory_skirt"));
        subCategoryMap.put("下装", bottomsSub);
        
        List<CategoryItem> dressesSub = new ArrayList<>();
        dressesSub.add(new CategoryItem("连衣裙", "ic_subcategory_dress"));
        dressesSub.add(new CategoryItem("吊带裙", "ic_subcategory_spaghetti_dress"));
        dressesSub.add(new CategoryItem("正装裙", "ic_subcategory_formal_dress"));
        dressesSub.add(new CategoryItem("晚礼服", "ic_subcategory_evening_dress"));
        dressesSub.add(new CategoryItem("婚纱", "ic_subcategory_wedding"));
        subCategoryMap.put("连衣裙", dressesSub);

        List<CategoryItem> outerwearSub = new ArrayList<>();
        outerwearSub.add(new CategoryItem("夹克", "ic_subcategory_jacket"));
        outerwearSub.add(new CategoryItem("大衣", "ic_subcategory_coat"));
        outerwearSub.add(new CategoryItem("西装", "ic_subcategory_suit"));
        outerwearSub.add(new CategoryItem("马甲", "ic_subcategory_outer_vest"));
        subCategoryMap.put("外套", outerwearSub);

        List<CategoryItem> shoesSub = new ArrayList<>();
        shoesSub.add(new CategoryItem("运动鞋", "ic_subcategory_sneakers"));
        shoesSub.add(new CategoryItem("正装鞋", "ic_subcategory_formal_shoes"));
        shoesSub.add(new CategoryItem("靴子", "ic_subcategory_boots"));
        shoesSub.add(new CategoryItem("凉鞋", "ic_subcategory_sandals"));
        shoesSub.add(new CategoryItem("高跟鞋", "ic_subcategory_heels"));
        subCategoryMap.put("鞋类", shoesSub);

        List<CategoryItem> accessoriesSub = new ArrayList<>();
        accessoriesSub.add(new CategoryItem("包", "ic_subcategory_bag"));
        accessoriesSub.add(new CategoryItem("帽子", "ic_subcategory_hat"));
        accessoriesSub.add(new CategoryItem("腰带", "ic_subcategory_belt"));
        accessoriesSub.add(new CategoryItem("围巾", "ic_subcategory_scarf"));
        accessoriesSub.add(new CategoryItem("首饰", "ic_subcategory_jewelry"));
        subCategoryMap.put("配饰", accessoriesSub);
    }

    public static List<CategoryItem> getMainCategories(Context context) {
        List<CategoryItem> list = new ArrayList<>();
        list.add(new CategoryItem("上装", "ic_category_tops"));
        list.add(new CategoryItem("下装", "ic_category_bottoms"));
        list.add(new CategoryItem("连衣裙", "ic_category_dresses"));
        list.add(new CategoryItem("外套", "ic_category_outerwear"));
        list.add(new CategoryItem("鞋类", "ic_category_shoes"));
        list.add(new CategoryItem("配饰", "ic_category_accessories"));
        list.add(new CategoryItem("套装", "ic_category_sets"));
        return list;
    }

    public static List<CategoryItem> getSubCategories(String mainCategory) {
        return subCategoryMap.get(mainCategory);
    }
}