<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F5F5F5">

    <!-- 🍎 标题区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="#FFFFFF"
        android:elevation="2dp">

        <TextView
            android:id="@+id/category_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="上装列表"
            android:textSize="24sp"
            android:textColor="#333333"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/count_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="共0件衣物"
            android:textSize="16sp"
            android:textColor="#666666"
            android:gravity="center"
            android:layout_marginBottom="10dp" />

        <!-- 🍎 批量添加按钮 -->
        <Button
            android:id="@+id/btn_batch_add"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="批量添加该分类衣物"
            android:layout_marginTop="8dp"
            android:backgroundTint="#007AFF"
            android:textColor="#FFFFFF"
            android:visibility="gone"/>

    </LinearLayout>

    <!-- 🍎 RecyclerView区域 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="8dp"
        android:clipToPadding="false" />

    <!-- 🍎 空状态提示 -->
    <TextView
        android:id="@+id/empty_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:text="该分类暂无衣物\n点击上方按钮批量添加，或返回主页单件添加"
        android:textSize="16sp"
        android:textColor="#999999"
        android:gravity="center"
        android:padding="40dp"
        android:visibility="gone" />

</LinearLayout>
