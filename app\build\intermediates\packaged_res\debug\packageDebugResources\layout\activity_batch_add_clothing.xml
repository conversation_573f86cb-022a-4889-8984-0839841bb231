<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".BatchAddClothingActivity">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="批量添加衣物"
        android:textSize="24sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp"/>

    <!-- 🍎 Category Selector (full 4-level linkage) -->
    <!-- This replaces the individual subcategory, color, occasion inputs/spinners -->
    <include layout="@layout/layout_category_selector" android:id="@+id/batch_category_selector_view" />

    <!-- General batch attributes for notes, tags, story -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edit_notes_batch"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="备注 (可选，应用于所有批量衣物)"
            android:inputType="textMultiLine"
            android:lines="3"
            android:maxLines="5"/>
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edit_story_batch"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="衣物故事 (可选，应用于所有批量衣物)"
            android:inputType="textMultiLine"
            android:lines="3"
            android:maxLines="5"/>
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/edit_tags_batch"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="标签 (可选，逗号分隔，应用于所有批量衣物)"
            android:inputType="text"/>
    </com.google.android.material.textfield.TextInputLayout>

    <!-- Image Selection -->
    <Button
        android:id="@+id/btn_select_images_batch"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="选择多张照片"
        android:backgroundTint="@color/material_dynamic_primary0"
        android:textColor="@android:color/white"/>

    <GridView
        android:id="@+id/grid_view_selected_images"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:numColumns="auto_fit"
        android:columnWidth="90dp"
        android:verticalSpacing="8dp"
        android:horizontalSpacing="8dp"
        android:stretchMode="columnWidth"
        android:gravity="center"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"/>

    <!-- Save Button -->
    <Button
        android:id="@+id/btn_save_batch"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="保存批量衣物"
        android:backgroundTint="@color/material_dynamic_primary0"
        android:textColor="@android:color/white"/>

</LinearLayout>
