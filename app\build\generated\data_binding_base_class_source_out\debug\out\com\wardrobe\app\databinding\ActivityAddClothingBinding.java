// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAddClothingBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView buttonReplacePhoto;

  @NonNull
  public final Button buttonSave;

  @NonNull
  public final ImageView clothingImageView;

  @NonNull
  public final EditText editTextName;

  @NonNull
  public final FrameLayout imageContainer;

  @NonNull
  public final FrameLayout imagePlaceholder;

  @NonNull
  public final RelativeLayout layoutCategory;

  @NonNull
  public final RelativeLayout layoutColor;

  @NonNull
  public final RelativeLayout layoutOccasion;

  @NonNull
  public final TextView textViewCategoryValue;

  @NonNull
  public final TextView textViewColorValue;

  @NonNull
  public final TextView textViewOccasionValue;

  private ActivityAddClothingBinding(@NonNull LinearLayout rootView,
      @NonNull ImageView buttonReplacePhoto, @NonNull Button buttonSave,
      @NonNull ImageView clothingImageView, @NonNull EditText editTextName,
      @NonNull FrameLayout imageContainer, @NonNull FrameLayout imagePlaceholder,
      @NonNull RelativeLayout layoutCategory, @NonNull RelativeLayout layoutColor,
      @NonNull RelativeLayout layoutOccasion, @NonNull TextView textViewCategoryValue,
      @NonNull TextView textViewColorValue, @NonNull TextView textViewOccasionValue) {
    this.rootView = rootView;
    this.buttonReplacePhoto = buttonReplacePhoto;
    this.buttonSave = buttonSave;
    this.clothingImageView = clothingImageView;
    this.editTextName = editTextName;
    this.imageContainer = imageContainer;
    this.imagePlaceholder = imagePlaceholder;
    this.layoutCategory = layoutCategory;
    this.layoutColor = layoutColor;
    this.layoutOccasion = layoutOccasion;
    this.textViewCategoryValue = textViewCategoryValue;
    this.textViewColorValue = textViewColorValue;
    this.textViewOccasionValue = textViewOccasionValue;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAddClothingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAddClothingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_add_clothing, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAddClothingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_replace_photo;
      ImageView buttonReplacePhoto = ViewBindings.findChildViewById(rootView, id);
      if (buttonReplacePhoto == null) {
        break missingId;
      }

      id = R.id.button_save;
      Button buttonSave = ViewBindings.findChildViewById(rootView, id);
      if (buttonSave == null) {
        break missingId;
      }

      id = R.id.clothing_image_view;
      ImageView clothingImageView = ViewBindings.findChildViewById(rootView, id);
      if (clothingImageView == null) {
        break missingId;
      }

      id = R.id.edit_text_name;
      EditText editTextName = ViewBindings.findChildViewById(rootView, id);
      if (editTextName == null) {
        break missingId;
      }

      id = R.id.image_container;
      FrameLayout imageContainer = ViewBindings.findChildViewById(rootView, id);
      if (imageContainer == null) {
        break missingId;
      }

      id = R.id.image_placeholder;
      FrameLayout imagePlaceholder = ViewBindings.findChildViewById(rootView, id);
      if (imagePlaceholder == null) {
        break missingId;
      }

      id = R.id.layout_category;
      RelativeLayout layoutCategory = ViewBindings.findChildViewById(rootView, id);
      if (layoutCategory == null) {
        break missingId;
      }

      id = R.id.layout_color;
      RelativeLayout layoutColor = ViewBindings.findChildViewById(rootView, id);
      if (layoutColor == null) {
        break missingId;
      }

      id = R.id.layout_occasion;
      RelativeLayout layoutOccasion = ViewBindings.findChildViewById(rootView, id);
      if (layoutOccasion == null) {
        break missingId;
      }

      id = R.id.text_view_category_value;
      TextView textViewCategoryValue = ViewBindings.findChildViewById(rootView, id);
      if (textViewCategoryValue == null) {
        break missingId;
      }

      id = R.id.text_view_color_value;
      TextView textViewColorValue = ViewBindings.findChildViewById(rootView, id);
      if (textViewColorValue == null) {
        break missingId;
      }

      id = R.id.text_view_occasion_value;
      TextView textViewOccasionValue = ViewBindings.findChildViewById(rootView, id);
      if (textViewOccasionValue == null) {
        break missingId;
      }

      return new ActivityAddClothingBinding((LinearLayout) rootView, buttonReplacePhoto, buttonSave,
          clothingImageView, editTextName, imageContainer, imagePlaceholder, layoutCategory,
          layoutColor, layoutOccasion, textViewCategoryValue, textViewColorValue,
          textViewOccasionValue);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
