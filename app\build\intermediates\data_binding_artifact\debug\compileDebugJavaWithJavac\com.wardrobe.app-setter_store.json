{"useAndroidX": true, "version": 5, "adapterMethods": {"activated": [[{"viewType": "android.view.View", "valueType": "boolean"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setActivated", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "backgroundColor": [[{"viewType": "android.view.View", "valueType": "int"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setBackgroundColor", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "circleImageUrl": [[{"viewType": "android.widget.ImageView", "valueType": "java.lang.String"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "loadCircleImage", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "countText": [[{"viewType": "android.widget.TextView", "valueType": "int"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setCountText", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "dateText": [[{"viewType": "android.widget.TextView", "valueType": "java.util.Date"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setDateText", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "dateTimeText": [[{"viewType": "android.widget.TextView", "valueType": "java.util.Date"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setDateTimeText", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "emptyText": [[{"viewType": "android.widget.TextView", "valueType": "boolean"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setEmptyText", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "enabledIf": [[{"viewType": "android.view.View", "valueType": "boolean"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setEnabledIf", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "fadeIn": [[{"viewType": "android.view.View", "valueType": "boolean"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setFadeIn", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "fileSizeText": [[{"viewType": "android.widget.TextView", "valueType": "long"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setFileSizeText", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "goneIf": [[{"viewType": "android.view.View", "valueType": "boolean"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setGoneIf", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "imageUrl": [[{"viewType": "android.widget.ImageView", "valueType": "java.lang.String"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "loadImage", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "invisibleIf": [[{"viewType": "android.view.View", "valueType": "boolean"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setInvisibleIf", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "items": [[{"viewType": "androidx.recyclerview.widget.RecyclerView", "valueType": "java.util.List"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setRecyclerViewItems", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "loading": [[{"viewType": "android.view.View", "valueType": "boolean"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setLoading", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "percentageText": [[{"viewType": "android.widget.TextView", "valueType": "double"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setPercentageText", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "ratingText": [[{"viewType": "android.widget.TextView", "valueType": "int"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setRatingText", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "roundedImageUrl": [[{"viewType": "android.widget.ImageView", "valueType": "java.lang.String"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "loadRoundedImage", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "scaleIn": [[{"viewType": "android.view.View", "valueType": "boolean"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setScaleIn", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "selected": [[{"viewType": "android.view.View", "valueType": "boolean"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setSelected", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "slideIn": [[{"viewType": "android.view.View", "valueType": "boolean"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setSlideIn", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "srcCompat": [[{"viewType": "android.widget.ImageView", "valueType": "int"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setImageResource", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "srcDrawable": [[{"viewType": "android.widget.ImageView", "valueType": "android.graphics.drawable.Drawable"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setImageDrawable", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "tagList": [[{"viewType": "android.widget.TextView", "valueType": "java.util.List<java.lang.String>"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setTagList", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "textColor": [[{"viewType": "android.widget.TextView", "valueType": "int"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setTextColor", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "visibleIf": [[{"viewType": "android.view.View", "valueType": "boolean"}, {"type": "com.wardrobe.app.ui.binding.BindingAdapters", "method": "setVisibleIf", "requiresOldValue": false, "isStatic": true, "componentClass": null}]]}, "renamedMethods": {}, "conversionMethods": {}, "untaggableTypes": {}, "multiValueAdapters": {}, "inverseAdapters": {}, "inverseMethods": {}, "twoWayMethods": {}}