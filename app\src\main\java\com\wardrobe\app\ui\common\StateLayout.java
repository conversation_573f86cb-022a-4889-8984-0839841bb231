package com.wardrobe.app.ui.common;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Button;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.wardrobe.app.R;

/**
 * 统一的状态布局组件
 * 用于显示加载、空状态、错误等不同的UI状态
 * 提供一致的用户体验
 */
public class StateLayout extends FrameLayout {
    
    private View contentView;
    private View loadingView;
    private View emptyView;
    private View errorView;
    
    private ProgressBar progressBar;
    private ImageView ivEmpty;
    private TextView tvEmptyTitle;
    private TextView tvEmptyMessage;
    private ImageView ivError;
    private TextView tvErrorTitle;
    private TextView tvErrorMessage;
    private Button btnRetry;
    
    private UiState currentState = UiState.INITIAL;
    private OnRetryClickListener retryClickListener;
    
    public interface OnRetryClickListener {
        void onRetryClick();
    }
    
    public StateLayout(@NonNull Context context) {
        super(context);
        init(context);
    }
    
    public StateLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }
    
    public StateLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }
    
    private void init(Context context) {
        LayoutInflater inflater = LayoutInflater.from(context);
        
        // 创建加载视图
        loadingView = inflater.inflate(R.layout.layout_loading, this, false);
        progressBar = loadingView.findViewById(R.id.progress_bar);
        addView(loadingView);
        
        // 创建空状态视图
        emptyView = inflater.inflate(R.layout.layout_empty, this, false);
        ivEmpty = emptyView.findViewById(R.id.iv_empty);
        tvEmptyTitle = emptyView.findViewById(R.id.tv_empty_title);
        tvEmptyMessage = emptyView.findViewById(R.id.tv_empty_message);
        addView(emptyView);
        
        // 创建错误视图
        errorView = inflater.inflate(R.layout.layout_error, this, false);
        ivError = errorView.findViewById(R.id.iv_error);
        tvErrorTitle = errorView.findViewById(R.id.tv_error_title);
        tvErrorMessage = errorView.findViewById(R.id.tv_error_message);
        btnRetry = errorView.findViewById(R.id.btn_retry);
        addView(errorView);
        
        // 设置重试按钮点击事件
        btnRetry.setOnClickListener(v -> {
            if (retryClickListener != null) {
                retryClickListener.onRetryClick();
            }
        });
        
        // 初始状态隐藏所有视图
        hideAllViews();
    }
    
    /**
     * 设置内容视图
     */
    public void setContentView(View view) {
        if (contentView != null) {
            removeView(contentView);
        }
        contentView = view;
        if (contentView != null) {
            addView(contentView, 0); // 添加到最底层
        }
    }
    
    /**
     * 设置UI状态
     */
    public void setState(UiState state) {
        if (currentState == state) {
            return;
        }
        
        currentState = state;
        hideAllViews();
        
        switch (state) {
            case LOADING:
            case REFRESHING:
                showLoading();
                break;
            case CONTENT:
            case SUCCESS:
                showContent();
                break;
            case EMPTY:
                showEmpty("暂无数据", "还没有任何内容");
                break;
            case NO_RESULTS:
                showEmpty("无搜索结果", "尝试调整搜索条件");
                break;
            case ERROR:
                showError("出错了", "请稍后重试");
                break;
            case NETWORK_ERROR:
                showError("网络连接失败", "请检查网络连接后重试");
                break;
            case OFFLINE:
                showError("离线模式", "显示的是缓存数据");
                break;
            default:
                showContent();
                break;
        }
    }
    
    /**
     * 显示加载状态
     */
    private void showLoading() {
        loadingView.setVisibility(VISIBLE);
        loadingView.animate()
                .alpha(1f)
                .setDuration(200)
                .start();
    }
    
    /**
     * 显示内容
     */
    private void showContent() {
        if (contentView != null) {
            contentView.setVisibility(VISIBLE);
            contentView.animate()
                    .alpha(1f)
                    .setDuration(200)
                    .start();
        }
    }
    
    /**
     * 显示空状态
     */
    private void showEmpty(String title, String message) {
        tvEmptyTitle.setText(title);
        tvEmptyMessage.setText(message);
        emptyView.setVisibility(VISIBLE);
        emptyView.animate()
                .alpha(1f)
                .setDuration(200)
                .start();
    }
    
    /**
     * 显示错误状态
     */
    private void showError(String title, String message) {
        tvErrorTitle.setText(title);
        tvErrorMessage.setText(message);
        errorView.setVisibility(VISIBLE);
        errorView.animate()
                .alpha(1f)
                .setDuration(200)
                .start();
    }
    
    /**
     * 隐藏所有视图
     */
    private void hideAllViews() {
        loadingView.setVisibility(GONE);
        emptyView.setVisibility(GONE);
        errorView.setVisibility(GONE);
        if (contentView != null) {
            contentView.setVisibility(GONE);
        }
    }
    
    /**
     * 设置重试点击监听器
     */
    public void setOnRetryClickListener(OnRetryClickListener listener) {
        this.retryClickListener = listener;
    }
    
    /**
     * 设置空状态图标
     */
    public void setEmptyIcon(int resId) {
        ivEmpty.setImageResource(resId);
    }
    
    /**
     * 设置错误状态图标
     */
    public void setErrorIcon(int resId) {
        ivError.setImageResource(resId);
    }
    
    /**
     * 获取当前状态
     */
    public UiState getCurrentState() {
        return currentState;
    }
    
    /**
     * 是否正在加载
     */
    public boolean isLoading() {
        return currentState == UiState.LOADING || currentState == UiState.REFRESHING;
    }
    
    /**
     * 是否显示内容
     */
    public boolean isShowingContent() {
        return currentState == UiState.CONTENT || currentState == UiState.SUCCESS;
    }
    
    /**
     * 是否为空状态
     */
    public boolean isEmpty() {
        return currentState == UiState.EMPTY || currentState == UiState.NO_RESULTS;
    }
    
    /**
     * 是否为错误状态
     */
    public boolean isError() {
        return currentState == UiState.ERROR || 
               currentState == UiState.NETWORK_ERROR || 
               currentState == UiState.OFFLINE;
    }
}
