// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentOutfitBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final LinearLayout layoutEmptyState;

  @NonNull
  public final RecyclerView recyclerViewOutfits;

  @NonNull
  public final TextView tvEmptyState;

  @NonNull
  public final TextView tvSuggestion;

  @NonNull
  public final TextView tvTitle;

  private FragmentOutfitBinding(@NonNull ConstraintLayout rootView,
      @NonNull LinearLayout layoutEmptyState, @NonNull RecyclerView recyclerViewOutfits,
      @NonNull TextView tvEmptyState, @NonNull TextView tvSuggestion, @NonNull TextView tvTitle) {
    this.rootView = rootView;
    this.layoutEmptyState = layoutEmptyState;
    this.recyclerViewOutfits = recyclerViewOutfits;
    this.tvEmptyState = tvEmptyState;
    this.tvSuggestion = tvSuggestion;
    this.tvTitle = tvTitle;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentOutfitBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentOutfitBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_outfit, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentOutfitBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.layout_empty_state;
      LinearLayout layoutEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (layoutEmptyState == null) {
        break missingId;
      }

      id = R.id.recycler_view_outfits;
      RecyclerView recyclerViewOutfits = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewOutfits == null) {
        break missingId;
      }

      id = R.id.tv_empty_state;
      TextView tvEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (tvEmptyState == null) {
        break missingId;
      }

      id = R.id.tv_suggestion;
      TextView tvSuggestion = ViewBindings.findChildViewById(rootView, id);
      if (tvSuggestion == null) {
        break missingId;
      }

      id = R.id.tv_title;
      TextView tvTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvTitle == null) {
        break missingId;
      }

      return new FragmentOutfitBinding((ConstraintLayout) rootView, layoutEmptyState,
          recyclerViewOutfits, tvEmptyState, tvSuggestion, tvTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
