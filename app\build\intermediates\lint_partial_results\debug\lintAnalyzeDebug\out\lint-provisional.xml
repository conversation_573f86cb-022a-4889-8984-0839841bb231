<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="conditional_incidents">

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="7"
            column="36"
            startOffset="256"
            endLine="7"
            endColumn="76"
            endOffset="296"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="32"/>
            <entry
                name="read"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/activities/BatchAddClothingActivity.java"
            line="167"
            column="26"
            startOffset="6960"
            endLine="167"
            endColumn="34"
            endOffset="6968"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Predicate;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#removeIf`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="removeIf"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/CalendarViewModel.java"
            line="298"
            column="26"
            startOffset="9856"
            endLine="298"
            endColumn="34"
            endOffset="9864"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Predicate;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#removeIf`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="removeIf"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/ClothingManager.java"
            line="199"
            column="15"
            startOffset="5743"
            endLine="199"
            endColumn="23"
            endOffset="5751"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Predicate;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#removeIf`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="removeIf"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="23-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/HealthChecker.java"
            line="191"
            column="25"
            startOffset="6150"
            endLine="191"
            endColumn="44"
            endOffset="6169"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.String;)"/>
            <entry
                name="message"
                string="Call requires API level 23 (current min is %1$s): `android.content.Context#checkSelfPermission`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="checkSelfPermission"/>
            <entry
                name="owner"
                string="android.content.Context"/>
            <api-levels id="requiresApi"
                value="23-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-33" requiresApi="23-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/HealthChecker.java"
            line="203"
            column="29"
            startOffset="6704"
            endLine="203"
            endColumn="48"
            endOffset="6723"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.String;)"/>
            <entry
                name="message"
                string="Call requires API level 23 (current min is %1$s): `android.content.Context#checkSelfPermission`"/>
            <api-levels id="minSdk"
                value="21-33"/>
            <entry
                name="name"
                string="checkSelfPermission"/>
            <entry
                name="owner"
                string="android.content.Context"/>
            <api-levels id="requiresApi"
                value="23-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/ImageOptimizer.java"
            line="284"
            column="34"
            startOffset="8975"
            endLine="284"
            endColumn="51"
            endOffset="8992"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.io.InputStream;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `new android.media.ExifInterface`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="&lt;init>"/>
            <entry
                name="owner"
                string="android.media.ExifInterface"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/LocalizationManager.java"
            line="281"
            column="32"
            startOffset="8351"
            endLine="281"
            endColumn="44"
            endOffset="8363"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.Object;Ljava.lang.Object;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Map#getOrDefault`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="getOrDefault"/>
            <entry
                name="owner"
                string="java.util.Map"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/repository/OutfitRepository.java"
            line="85"
            column="42"
            startOffset="2490"
            endLine="85"
            endColumn="48"
            endOffset="2496"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#stream`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/repository/OutfitRepository.java"
            line="86"
            column="22"
            startOffset="2520"
            endLine="86"
            endColumn="28"
            endOffset="2526"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Predicate;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#filter`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="filter"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/repository/OutfitRepository.java"
            line="87"
            column="22"
            startOffset="2585"
            endLine="87"
            endColumn="31"
            endOffset="2594"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#findFirst`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="findFirst"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/repository/OutfitRepository.java"
            line="88"
            column="22"
            startOffset="2618"
            endLine="88"
            endColumn="28"
            endOffset="2624"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.Object;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Optional#orElse`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="orElse"/>
            <entry
                name="owner"
                string="java.util.Optional"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/repository/OutfitRepository.java"
            line="169"
            column="39"
            startOffset="4981"
            endLine="169"
            endColumn="47"
            endOffset="4989"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Predicate;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#removeIf`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="removeIf"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/repository/OutfitRepository.java"
            line="208"
            column="31"
            startOffset="6118"
            endLine="208"
            endColumn="37"
            endOffset="6124"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#stream`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/repository/OutfitRepository.java"
            line="209"
            column="22"
            startOffset="6148"
            endLine="209"
            endColumn="28"
            endOffset="6154"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Predicate;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#filter`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="filter"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/repository/OutfitRepository.java"
            line="214"
            column="22"
            startOffset="6411"
            endLine="214"
            endColumn="29"
            endOffset="6418"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.stream.Collector;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#collect`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="collect"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/repository/OutfitRepository.java"
            line="214"
            column="41"
            startOffset="6430"
            endLine="214"
            endColumn="47"
            endOffset="6436"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#toList`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="toList"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="204"
            column="50"
            startOffset="6858"
            endLine="204"
            endColumn="62"
            endOffset="6870"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.Object;Ljava.lang.Object;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Map#getOrDefault`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="getOrDefault"/>
            <entry
                name="owner"
                string="java.util.Map"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="205"
            column="53"
            startOffset="6949"
            endLine="205"
            endColumn="65"
            endOffset="6961"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.Object;Ljava.lang.Object;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Map#getOrDefault`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="getOrDefault"/>
            <entry
                name="owner"
                string="java.util.Map"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="206"
            column="51"
            startOffset="7038"
            endLine="206"
            endColumn="63"
            endOffset="7050"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.Object;Ljava.lang.Object;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Map#getOrDefault`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="getOrDefault"/>
            <entry
                name="owner"
                string="java.util.Map"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="239"
            column="55"
            startOffset="8493"
            endLine="239"
            endColumn="67"
            endOffset="8505"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.Object;Ljava.lang.Object;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Map#getOrDefault`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="getOrDefault"/>
            <entry
                name="owner"
                string="java.util.Map"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="318"
            column="70"
            startOffset="11697"
            endLine="318"
            endColumn="76"
            endOffset="11703"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#stream`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="319"
            column="18"
            startOffset="11723"
            endLine="319"
            endColumn="21"
            endOffset="11726"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Function;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#map`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="map"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="320"
            column="18"
            startOffset="11765"
            endLine="320"
            endColumn="25"
            endOffset="11772"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.stream.Collector;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#collect`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="collect"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="320"
            column="37"
            startOffset="11784"
            endLine="320"
            endColumn="43"
            endOffset="11790"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#toList`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="toList"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="360"
            column="24"
            startOffset="13035"
            endLine="360"
            endColumn="39"
            endOffset="13050"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.Object;Ljava.util.function.Function;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Map#computeIfAbsent`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="computeIfAbsent"/>
            <entry
                name="owner"
                string="java.util.Map"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="393"
            column="37"
            startOffset="13866"
            endLine="393"
            endColumn="43"
            endOffset="13872"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#stream`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="394"
            column="18"
            startOffset="13892"
            endLine="394"
            endColumn="21"
            endOffset="13895"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Function;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#map`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="map"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="395"
            column="18"
            startOffset="13937"
            endLine="395"
            endColumn="24"
            endOffset="13943"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Predicate;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#filter`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="filter"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="396"
            column="18"
            startOffset="14005"
            endLine="396"
            endColumn="25"
            endOffset="14012"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.stream.Collector;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#collect`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="collect"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="396"
            column="37"
            startOffset="14024"
            endLine="396"
            endColumn="43"
            endOffset="14030"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#toList`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="toList"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="408"
            column="40"
            startOffset="14359"
            endLine="408"
            endColumn="46"
            endOffset="14365"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#stream`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="409"
            column="18"
            startOffset="14385"
            endLine="409"
            endColumn="21"
            endOffset="14388"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Function;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#map`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="map"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="410"
            column="18"
            startOffset="14433"
            endLine="410"
            endColumn="24"
            endOffset="14439"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Predicate;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#filter`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="filter"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="411"
            column="18"
            startOffset="14510"
            endLine="411"
            endColumn="26"
            endOffset="14518"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#distinct`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="distinct"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="412"
            column="18"
            startOffset="14538"
            endLine="412"
            endColumn="25"
            endOffset="14545"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.stream.Collector;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#collect`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="collect"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="412"
            column="37"
            startOffset="14557"
            endLine="412"
            endColumn="43"
            endOffset="14563"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#toList`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="toList"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="442"
            column="33"
            startOffset="15518"
            endLine="442"
            endColumn="39"
            endOffset="15524"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#stream`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="443"
            column="18"
            startOffset="15544"
            endLine="443"
            endColumn="24"
            endOffset="15550"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Predicate;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#filter`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="filter"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="444"
            column="18"
            startOffset="15613"
            endLine="444"
            endColumn="25"
            endOffset="15620"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.stream.Collector;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#collect`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="collect"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="444"
            column="37"
            startOffset="15632"
            endLine="444"
            endColumn="43"
            endOffset="15638"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#toList`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="toList"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="458"
            column="58"
            startOffset="16118"
            endLine="458"
            endColumn="64"
            endOffset="16124"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#stream`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="459"
            column="18"
            startOffset="16144"
            endLine="459"
            endColumn="25"
            endOffset="16151"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.stream.Collector;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#collect`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="collect"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="459"
            column="37"
            startOffset="16163"
            endLine="459"
            endColumn="47"
            endOffset="16173"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Function;Ljava.util.stream.Collector;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#groupingBy`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="groupingBy"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="461"
            column="32"
            startOffset="16296"
            endLine="461"
            endColumn="40"
            endOffset="16304"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#counting`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="counting"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="464"
            column="63"
            startOffset="16398"
            endLine="464"
            endColumn="69"
            endOffset="16404"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#stream`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="465"
            column="18"
            startOffset="16424"
            endLine="465"
            endColumn="21"
            endOffset="16427"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.Comparator;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#max`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="max"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="465"
            column="32"
            startOffset="16438"
            endLine="465"
            endColumn="48"
            endOffset="16454"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Map.Entry#comparingByValue`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="comparingByValue"/>
            <entry
                name="owner"
                string="java.util.Map.Entry"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="466"
            column="18"
            startOffset="16475"
            endLine="466"
            endColumn="21"
            endOffset="16478"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Function;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Optional#map`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="map"/>
            <entry
                name="owner"
                string="java.util.Optional"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="467"
            column="18"
            startOffset="16515"
            endLine="467"
            endColumn="24"
            endOffset="16521"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.Object;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Optional#orElse`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="orElse"/>
            <entry
                name="owner"
                string="java.util.Optional"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/OutfitViewModel.java"
            line="469"
            column="64"
            startOffset="16601"
            endLine="469"
            endColumn="76"
            endOffset="16613"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.Object;Ljava.lang.Object;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Map#getOrDefault`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="getOrDefault"/>
            <entry
                name="owner"
                string="java.util.Map"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/PerformanceMonitor.java"
            line="265"
            column="52"
            startOffset="8270"
            endLine="265"
            endColumn="58"
            endOffset="8276"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#stream`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/PerformanceMonitor.java"
            line="266"
            column="18"
            startOffset="8296"
            endLine="266"
            endColumn="27"
            endOffset="8305"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.ToLongFunction;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#mapToLong`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="mapToLong"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/PerformanceMonitor.java"
            line="267"
            column="18"
            startOffset="8355"
            endLine="267"
            endColumn="21"
            endOffset="8358"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.LongStream#sum`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="sum"/>
            <entry
                name="owner"
                string="java.util.stream.LongStream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/fragments/ProfileFragment.java"
            line="224"
            column="56"
            startOffset="8154"
            endLine="224"
            endColumn="62"
            endOffset="8160"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#stream`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/fragments/ProfileFragment.java"
            line="225"
            column="22"
            startOffset="8184"
            endLine="225"
            endColumn="25"
            endOffset="8187"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Function;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#map`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="map"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/fragments/ProfileFragment.java"
            line="226"
            column="22"
            startOffset="8236"
            endLine="226"
            endColumn="28"
            endOffset="8242"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Predicate;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#filter`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="filter"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/fragments/ProfileFragment.java"
            line="227"
            column="22"
            startOffset="8305"
            endLine="227"
            endColumn="29"
            endOffset="8312"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.stream.Collector;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#collect`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="collect"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/fragments/ProfileFragment.java"
            line="227"
            column="41"
            startOffset="8324"
            endLine="227"
            endColumn="47"
            endOffset="8330"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#toList`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="toList"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/ProfileViewModel.java"
            line="174"
            column="60"
            startOffset="5833"
            endLine="174"
            endColumn="66"
            endOffset="5839"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#stream`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/ProfileViewModel.java"
            line="175"
            column="18"
            startOffset="5859"
            endLine="175"
            endColumn="25"
            endOffset="5866"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.stream.Collector;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#collect`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="collect"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/ProfileViewModel.java"
            line="175"
            column="37"
            startOffset="5878"
            endLine="175"
            endColumn="47"
            endOffset="5888"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Function;Ljava.util.stream.Collector;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#groupingBy`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="groupingBy"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/ProfileViewModel.java"
            line="177"
            column="32"
            startOffset="6006"
            endLine="177"
            endColumn="49"
            endOffset="6023"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.stream.Collector;Ljava.util.function.Function;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#collectingAndThen`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="collectingAndThen"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/ProfileViewModel.java"
            line="177"
            column="61"
            startOffset="6035"
            endLine="177"
            endColumn="69"
            endOffset="6043"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#counting`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="counting"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/ProfileViewModel.java"
            line="182"
            column="57"
            startOffset="6214"
            endLine="182"
            endColumn="63"
            endOffset="6220"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#stream`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/ProfileViewModel.java"
            line="183"
            column="18"
            startOffset="6240"
            endLine="183"
            endColumn="25"
            endOffset="6247"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.stream.Collector;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#collect`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="collect"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/ProfileViewModel.java"
            line="183"
            column="37"
            startOffset="6259"
            endLine="183"
            endColumn="47"
            endOffset="6269"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Function;Ljava.util.stream.Collector;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#groupingBy`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="groupingBy"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/ProfileViewModel.java"
            line="185"
            column="32"
            startOffset="6380"
            endLine="185"
            endColumn="49"
            endOffset="6397"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.stream.Collector;Ljava.util.function.Function;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#collectingAndThen`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="collectingAndThen"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/ProfileViewModel.java"
            line="185"
            column="61"
            startOffset="6409"
            endLine="185"
            endColumn="69"
            endOffset="6417"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#counting`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="counting"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/ProfileViewModel.java"
            line="194"
            column="64"
            startOffset="6724"
            endLine="194"
            endColumn="70"
            endOffset="6730"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#stream`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/ProfileViewModel.java"
            line="195"
            column="22"
            startOffset="6754"
            endLine="195"
            endColumn="29"
            endOffset="6761"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.stream.Collector;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#collect`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="collect"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/ProfileViewModel.java"
            line="195"
            column="41"
            startOffset="6773"
            endLine="195"
            endColumn="51"
            endOffset="6783"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Function;Ljava.util.stream.Collector;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#groupingBy`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="groupingBy"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/ProfileViewModel.java"
            line="197"
            column="36"
            startOffset="6914"
            endLine="197"
            endColumn="53"
            endOffset="6931"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.stream.Collector;Ljava.util.function.Function;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#collectingAndThen`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="collectingAndThen"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/ProfileViewModel.java"
            line="197"
            column="65"
            startOffset="6943"
            endLine="197"
            endColumn="73"
            endOffset="6951"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#counting`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="counting"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/ProfileViewModel.java"
            line="202"
            column="50"
            startOffset="7132"
            endLine="202"
            endColumn="56"
            endOffset="7138"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#stream`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/ProfileViewModel.java"
            line="203"
            column="22"
            startOffset="7162"
            endLine="203"
            endColumn="30"
            endOffset="7170"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.ToIntFunction;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#mapToInt`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="mapToInt"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/ProfileViewModel.java"
            line="204"
            column="22"
            startOffset="7217"
            endLine="204"
            endColumn="29"
            endOffset="7224"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.IntStream#average`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="average"/>
            <entry
                name="owner"
                string="java.util.stream.IntStream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/ProfileViewModel.java"
            line="205"
            column="22"
            startOffset="7248"
            endLine="205"
            endColumn="28"
            endOffset="7254"/>
        <map>
            <entry
                name="desc"
                string="(D)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.OptionalDouble#orElse`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="orElse"/>
            <entry
                name="owner"
                string="java.util.OptionalDouble"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="23-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/SecurityUtils.java"
            line="297"
            column="24"
            startOffset="8413"
            endLine="297"
            endColumn="43"
            endOffset="8432"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.String;)"/>
            <entry
                name="message"
                string="Call requires API level 23 (current min is %1$s): `android.content.Context#checkSelfPermission`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="checkSelfPermission"/>
            <entry
                name="owner"
                string="android.content.Context"/>
            <api-levels id="requiresApi"
                value="23-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="65"
            column="42"
            startOffset="2329"
            endLine="65"
            endColumn="48"
            endOffset="2335"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#stream`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="66"
            column="22"
            startOffset="2359"
            endLine="66"
            endColumn="28"
            endOffset="2365"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Predicate;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#filter`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="filter"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="67"
            column="22"
            startOffset="2420"
            endLine="67"
            endColumn="31"
            endOffset="2429"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#findFirst`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="findFirst"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="68"
            column="22"
            startOffset="2453"
            endLine="68"
            endColumn="28"
            endOffset="2459"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.Object;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Optional#orElse`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="orElse"/>
            <entry
                name="owner"
                string="java.util.Optional"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="171"
            column="30"
            startOffset="5809"
            endLine="171"
            endColumn="38"
            endOffset="5817"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Predicate;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#removeIf`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="removeIf"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="207"
            column="25"
            startOffset="6961"
            endLine="207"
            endColumn="33"
            endOffset="6969"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Predicate;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#removeIf`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="removeIf"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="237"
            column="42"
            startOffset="7902"
            endLine="237"
            endColumn="48"
            endOffset="7908"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#stream`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="238"
            column="22"
            startOffset="7932"
            endLine="238"
            endColumn="28"
            endOffset="7938"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Predicate;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#filter`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="filter"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="239"
            column="22"
            startOffset="8035"
            endLine="239"
            endColumn="29"
            endOffset="8042"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.stream.Collector;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#collect`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="collect"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="239"
            column="41"
            startOffset="8054"
            endLine="239"
            endColumn="47"
            endOffset="8060"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#toList`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="toList"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="254"
            column="42"
            startOffset="8516"
            endLine="254"
            endColumn="48"
            endOffset="8522"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#stream`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="255"
            column="22"
            startOffset="8546"
            endLine="255"
            endColumn="28"
            endOffset="8552"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Predicate;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#filter`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="filter"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="256"
            column="22"
            startOffset="8620"
            endLine="256"
            endColumn="29"
            endOffset="8627"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.stream.Collector;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#collect`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="collect"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="256"
            column="41"
            startOffset="8639"
            endLine="256"
            endColumn="47"
            endOffset="8645"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#toList`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="toList"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="384"
            column="58"
            startOffset="13194"
            endLine="384"
            endColumn="64"
            endOffset="13200"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#stream`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/data/SharedPreferencesClothingDataSource.java"
            line="385"
            column="21"
            startOffset="13223"
            endLine="385"
            endColumn="29"
            endOffset="13231"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Predicate;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#anyMatch`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="anyMatch"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="159"
            column="20"
            startOffset="5281"
            endLine="159"
            endColumn="35"
            endOffset="5296"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.lang.Object;Ljava.util.function.Function;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Map#computeIfAbsent`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="computeIfAbsent"/>
            <entry
                name="owner"
                string="java.util.Map"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="227"
            column="43"
            startOffset="7186"
            endLine="227"
            endColumn="49"
            endOffset="7192"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#stream`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="228"
            column="22"
            startOffset="7216"
            endLine="228"
            endColumn="28"
            endOffset="7222"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Predicate;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#filter`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="filter"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="229"
            column="22"
            startOffset="7287"
            endLine="229"
            endColumn="29"
            endOffset="7294"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.stream.Collector;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#collect`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="collect"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="229"
            column="41"
            startOffset="7306"
            endLine="229"
            endColumn="47"
            endOffset="7312"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#toList`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="toList"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="236"
            column="43"
            startOffset="7559"
            endLine="236"
            endColumn="49"
            endOffset="7565"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#stream`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="237"
            column="22"
            startOffset="7589"
            endLine="237"
            endColumn="28"
            endOffset="7595"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Predicate;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#filter`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="filter"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="238"
            column="22"
            startOffset="7663"
            endLine="238"
            endColumn="29"
            endOffset="7670"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.stream.Collector;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#collect`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="collect"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="238"
            column="41"
            startOffset="7682"
            endLine="238"
            endColumn="47"
            endOffset="7688"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#toList`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="toList"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="379"
            column="38"
            startOffset="12230"
            endLine="379"
            endColumn="44"
            endOffset="12236"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#stream`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="380"
            column="18"
            startOffset="12256"
            endLine="380"
            endColumn="21"
            endOffset="12259"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Function;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#map`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="map"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="381"
            column="18"
            startOffset="12304"
            endLine="381"
            endColumn="24"
            endOffset="12310"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Predicate;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#filter`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="filter"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="382"
            column="18"
            startOffset="12388"
            endLine="382"
            endColumn="26"
            endOffset="12396"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#distinct`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="distinct"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="383"
            column="18"
            startOffset="12416"
            endLine="383"
            endColumn="24"
            endOffset="12422"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#sorted`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="sorted"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="384"
            column="18"
            startOffset="12442"
            endLine="384"
            endColumn="25"
            endOffset="12449"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.stream.Collector;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#collect`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="collect"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="384"
            column="37"
            startOffset="12461"
            endLine="384"
            endColumn="43"
            endOffset="12467"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#toList`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="toList"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="391"
            column="38"
            startOffset="12600"
            endLine="391"
            endColumn="44"
            endOffset="12606"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.Collection#stream`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="stream"/>
            <entry
                name="owner"
                string="java.util.Collection"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="392"
            column="18"
            startOffset="12626"
            endLine="392"
            endColumn="21"
            endOffset="12629"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Function;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#map`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="map"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="393"
            column="18"
            startOffset="12671"
            endLine="393"
            endColumn="24"
            endOffset="12677"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.function.Predicate;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#filter`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="filter"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="394"
            column="18"
            startOffset="12746"
            endLine="394"
            endColumn="26"
            endOffset="12754"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#distinct`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="distinct"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="395"
            column="18"
            startOffset="12774"
            endLine="395"
            endColumn="24"
            endOffset="12780"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#sorted`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="sorted"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="396"
            column="18"
            startOffset="12800"
            endLine="396"
            endColumn="25"
            endOffset="12807"/>
        <map>
            <entry
                name="desc"
                string="(Ljava.util.stream.Collector;)"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Stream#collect`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="collect"/>
            <entry
                name="owner"
                string="java.util.stream.Stream"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="24-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/viewmodel/WardrobeViewModel.java"
            line="396"
            column="37"
            startOffset="12819"
            endLine="396"
            endColumn="43"
            endOffset="12825"/>
        <map>
            <entry
                name="desc"
                string="()"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `java.util.stream.Collectors#toList`"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <entry
                name="name"
                string="toList"/>
            <entry
                name="owner"
                string="java.util.stream.Collectors"/>
            <api-levels id="requiresApi"
                value="24-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="23-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/styles.xml"
            line="29"
            column="15"
            startOffset="1196"
            endLine="29"
            endColumn="50"
            endOffset="1231"/>
        <map>
            <entry
                name="message"
                string="`android:windowLightStatusBar` requires API level 23 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <api-levels id="requiresApi"
                value="23-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="27-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/styles.xml"
            line="33"
            column="15"
            startOffset="1370"
            endLine="33"
            endColumn="54"
            endOffset="1409"/>
        <map>
            <entry
                name="message"
                string="`android:windowLightNavigationBar` requires API level 27 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <api-levels id="requiresApi"
                value="27-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="29-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/styles.xml"
            line="41"
            column="15"
            startOffset="1730"
            endLine="41"
            endColumn="46"
            endOffset="1761"/>
        <map>
            <entry
                name="message"
                string="`android:forceDarkAllowed` requires API level 29 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <api-levels id="requiresApi"
                value="29-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="23-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="46"
            column="15"
            startOffset="2536"
            endLine="46"
            endColumn="50"
            endOffset="2571"/>
        <map>
            <entry
                name="message"
                string="`android:windowLightStatusBar` requires API level 23 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <api-levels id="requiresApi"
                value="23-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="27-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="47"
            column="15"
            startOffset="2598"
            endLine="47"
            endColumn="54"
            endOffset="2637"/>
        <map>
            <entry
                name="message"
                string="`android:windowLightNavigationBar` requires API level 27 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <api-levels id="requiresApi"
                value="27-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="23-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="121"
            column="15"
            startOffset="7190"
            endLine="121"
            endColumn="50"
            endOffset="7225"/>
        <map>
            <entry
                name="message"
                string="`android:windowLightStatusBar` requires API level 23 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <api-levels id="requiresApi"
                value="23-∞"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="21-∞" requiresApi="27-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="122"
            column="15"
            startOffset="7253"
            endLine="122"
            endColumn="54"
            endOffset="7292"/>
        <map>
            <entry
                name="message"
                string="`android:windowLightNavigationBar` requires API level 27 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <api-levels id="requiresApi"
                value="27-∞"/>
        </map>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="21-∞" requiresApi="23-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_category_selector.xml"
            line="10"
            column="5"
            startOffset="382"
            endLine="10"
            endColumn="64"
            endOffset="441"/>
        <map>
            <entry
                name="message"
                string="Attribute `android:foreground` has no effect on API levels lower than 23 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <api-levels id="requiresApi"
                value="23-∞"/>
        </map>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="21-∞" requiresApi="23-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_occasion_selector.xml"
            line="10"
            column="5"
            startOffset="382"
            endLine="10"
            endColumn="64"
            endOffset="441"/>
        <map>
            <entry
                name="message"
                string="Attribute `android:foreground` has no effect on API levels lower than 23 (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="21-∞"/>
            <api-levels id="requiresApi"
                value="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 24 (BatchAddClothingActivity)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/activities/BatchAddClothingActivity.java"
            line="103"
            column="15"
            startOffset="4186"
            endLine="103"
            endColumn="18"
            endOffset="4189"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="LongLogTag"
        severity="error"
        message="The logging tag can be at most 23 characters, was 24 (BatchAddClothingActivity)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/ui/activities/BatchAddClothingActivity.java"
            line="127"
            column="15"
            startOffset="5091"
            endLine="127"
            endColumn="18"
            endOffset="5094"/>
        <map>
            <condition minLT="23-∞"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_add.xml"
            line="6"
            column="19"
            startOffset="208"
            endLine="6"
            endColumn="39"
            endOffset="228"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_add.xml"
            line="8"
            column="26"
            startOffset="266"
            endLine="8"
            endColumn="46"
            endOffset="286"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_calendar.xml"
            line="3"
            column="30"
            startOffset="229"
            endLine="3"
            endColumn="50"
            endOffset="249"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_chevron_right.xml"
            line="7"
            column="28"
            startOffset="229"
            endLine="7"
            endColumn="59"
            endOffset="260"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_clothing_placeholder.xml"
            line="9"
            column="28"
            startOffset="263"
            endLine="9"
            endColumn="54"
            endOffset="289"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_empty_box.xml"
            line="9"
            column="28"
            startOffset="263"
            endLine="9"
            endColumn="48"
            endOffset="283"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_error.xml"
            line="9"
            column="28"
            startOffset="263"
            endLine="9"
            endColumn="48"
            endOffset="283"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_outfit.xml"
            line="3"
            column="30"
            startOffset="229"
            endLine="3"
            endColumn="50"
            endOffset="249"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_profile.xml"
            line="3"
            column="30"
            startOffset="229"
            endLine="3"
            endColumn="50"
            endOffset="249"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_replace_photo.xml"
            line="6"
            column="19"
            startOffset="204"
            endLine="6"
            endColumn="43"
            endOffset="228"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_replace_photo.xml"
            line="8"
            column="26"
            startOffset="266"
            endLine="8"
            endColumn="46"
            endOffset="286"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_wardrobe.xml"
            line="3"
            column="30"
            startOffset="227"
            endLine="3"
            endColumn="50"
            endOffset="247"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 31">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
                startOffset="1262"
                endOffset="1282"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="31"
            column="9"
            startOffset="1262"
            endLine="31"
            endColumn="29"
            endOffset="1282"/>
        <map>
            <condition minGE="31-∞"/>
        </map>
    </incident>

</incidents>
