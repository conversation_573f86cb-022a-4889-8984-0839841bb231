package com.wardrobe.app.viewmodel;

import android.app.Application;
import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.wardrobe.app.model.ClothingItem;
import com.wardrobe.app.repository.ClothingRepository;
import com.wardrobe.app.di.ServiceLocator;
import com.wardrobe.app.utils.Logger;
import com.wardrobe.app.utils.GlobalExceptionHandler;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 衣橱页面ViewModel
 * 管理衣物数据的展示、分组、筛选和搜索
 * 符合Android MVVM架构标准
 */
public class WardrobeViewModel extends AndroidViewModel {
    
    private static final String TAG = "WardrobeViewModel";
    
    // Repository
    private final ClothingRepository clothingRepository;
    private final GlobalExceptionHandler exceptionHandler;
    
    // LiveData
    private final MutableLiveData<List<ClothingItem>> allClothingItems = new MutableLiveData<>();
    private final MutableLiveData<Map<String, List<ClothingItem>>> groupedClothing = new MutableLiveData<>();
    private final MutableLiveData<List<String>> selectedTags = new MutableLiveData<>();
    private final MutableLiveData<String> searchQuery = new MutableLiveData<>();
    private final MutableLiveData<Boolean> isLoading = new MutableLiveData<>();
    private final MutableLiveData<String> errorMessage = new MutableLiveData<>();
    private final MutableLiveData<String> successMessage = new MutableLiveData<>();
    private final MutableLiveData<ViewState> viewState = new MutableLiveData<>();
    
    // 内部数据
    private List<ClothingItem> originalClothingItems = new ArrayList<>();
    
    public WardrobeViewModel(@NonNull Application application) {
        super(application);
        
        // 使用ServiceLocator获取Repository
        ServiceLocator serviceLocator = ServiceLocator.getInstance(application);
        this.clothingRepository = serviceLocator.getService(ClothingRepository.class);
        this.exceptionHandler = GlobalExceptionHandler.getInstance();
        
        // 初始化默认值
        isLoading.setValue(false);
        selectedTags.setValue(new ArrayList<>());
        searchQuery.setValue("");
        viewState.setValue(ViewState.LOADING);
        
        Logger.d(TAG, "WardrobeViewModel 初始化完成");
    }
    
    // ==================== LiveData Getters ====================
    
    public LiveData<List<ClothingItem>> getAllClothingItems() {
        return allClothingItems;
    }
    
    public LiveData<Map<String, List<ClothingItem>>> getGroupedClothing() {
        return groupedClothing;
    }
    
    public LiveData<List<String>> getSelectedTags() {
        return selectedTags;
    }
    
    public LiveData<String> getSearchQuery() {
        return searchQuery;
    }
    
    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }
    
    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }
    
    public LiveData<String> getSuccessMessage() {
        return successMessage;
    }
    
    public LiveData<ViewState> getViewState() {
        return viewState;
    }
    
    // ==================== 数据加载方法 ====================
    
    /**
     * 加载所有衣物数据
     */
    public void loadClothingItems() {
        Logger.enter(TAG, "loadClothingItems");
        isLoading.setValue(true);
        viewState.setValue(ViewState.LOADING);
        
        clothingRepository.getAllClothingItemsAsync(new ClothingRepository.DataCallback<List<ClothingItem>>() {
            @Override
            public void onSuccess(List<ClothingItem> items) {
                originalClothingItems = new ArrayList<>(items);
                allClothingItems.setValue(items);
                
                if (items.isEmpty()) {
                    viewState.setValue(ViewState.EMPTY);
                } else {
                    groupClothingByCategory(items);
                    applyFiltersAndSearch();
                    viewState.setValue(ViewState.CONTENT);
                }
                
                isLoading.setValue(false);
                Logger.d(TAG, "加载了 " + items.size() + " 件衣物");
            }
            
            @Override
            public void onError(String error) {
                exceptionHandler.handleException("加载衣物数据", new Exception(error), false);
                errorMessage.setValue("加载衣物数据失败: " + error);
                viewState.setValue(ViewState.ERROR);
                isLoading.setValue(false);
            }
        });
    }
    
    /**
     * 刷新数据
     */
    public void refreshData() {
        Logger.d(TAG, "刷新衣物数据");
        loadClothingItems();
    }
    
    // ==================== 分组和筛选方法 ====================
    
    /**
     * 按分类分组衣物
     */
    private void groupClothingByCategory(List<ClothingItem> items) {
        Map<String, List<ClothingItem>> groups = new HashMap<>();
        
        for (ClothingItem item : items) {
            String category = item.getCategory();
            if (category == null || category.trim().isEmpty()) {
                category = "其他";
            }
            
            groups.computeIfAbsent(category, k -> new ArrayList<>()).add(item);
        }
        
        groupedClothing.setValue(groups);
        Logger.d(TAG, "衣物已按 " + groups.size() + " 个分类分组");
    }
    
    /**
     * 设置标签筛选
     */
    public void setSelectedTags(List<String> tags) {
        selectedTags.setValue(new ArrayList<>(tags));
        applyFiltersAndSearch();
        Logger.d(TAG, "设置筛选标签: " + tags);
    }
    
    /**
     * 添加筛选标签
     */
    public void addSelectedTag(String tag) {
        List<String> currentTags = selectedTags.getValue();
        if (currentTags != null && !currentTags.contains(tag)) {
            List<String> newTags = new ArrayList<>(currentTags);
            newTags.add(tag);
            setSelectedTags(newTags);
        }
    }
    
    /**
     * 移除筛选标签
     */
    public void removeSelectedTag(String tag) {
        List<String> currentTags = selectedTags.getValue();
        if (currentTags != null && currentTags.contains(tag)) {
            List<String> newTags = new ArrayList<>(currentTags);
            newTags.remove(tag);
            setSelectedTags(newTags);
        }
    }
    
    /**
     * 清除所有筛选
     */
    public void clearFilters() {
        setSelectedTags(new ArrayList<>());
        setSearchQuery("");
    }
    
    // ==================== 搜索方法 ====================
    
    /**
     * 设置搜索查询
     */
    public void setSearchQuery(String query) {
        searchQuery.setValue(query != null ? query : "");
        applyFiltersAndSearch();
        Logger.d(TAG, "设置搜索查询: " + query);
    }
    
    /**
     * 应用筛选和搜索
     */
    private void applyFiltersAndSearch() {
        List<ClothingItem> filteredItems = new ArrayList<>(originalClothingItems);
        
        // 应用标签筛选
        List<String> tags = selectedTags.getValue();
        if (tags != null && !tags.isEmpty() && !tags.contains("全部")) {
            filteredItems = filteredItems.stream()
                    .filter(item -> tags.contains(item.getCategory()))
                    .collect(Collectors.toList());
        }
        
        // 应用搜索筛选
        String query = searchQuery.getValue();
        if (query != null && !query.trim().isEmpty()) {
            String lowerQuery = query.toLowerCase().trim();
            filteredItems = filteredItems.stream()
                    .filter(item -> matchesSearchQuery(item, lowerQuery))
                    .collect(Collectors.toList());
        }
        
        // 更新显示的数据
        allClothingItems.setValue(filteredItems);
        groupClothingByCategory(filteredItems);
        
        // 更新视图状态
        if (filteredItems.isEmpty() && !originalClothingItems.isEmpty()) {
            viewState.setValue(ViewState.NO_RESULTS);
        } else if (filteredItems.isEmpty()) {
            viewState.setValue(ViewState.EMPTY);
        } else {
            viewState.setValue(ViewState.CONTENT);
        }
        
        Logger.d(TAG, "筛选后显示 " + filteredItems.size() + " 件衣物");
    }
    
    /**
     * 检查衣物是否匹配搜索查询
     */
    private boolean matchesSearchQuery(ClothingItem item, String query) {
        if (item.getName() != null && item.getName().toLowerCase().contains(query)) {
            return true;
        }
        if (item.getCategory() != null && item.getCategory().toLowerCase().contains(query)) {
            return true;
        }
        if (item.getSubcategory() != null && item.getSubcategory().toLowerCase().contains(query)) {
            return true;
        }
        if (item.getColor() != null && item.getColor().toLowerCase().contains(query)) {
            return true;
        }
        if (item.getOccasion() != null && item.getOccasion().toLowerCase().contains(query)) {
            return true;
        }
        if (item.getNotes() != null && item.getNotes().toLowerCase().contains(query)) {
            return true;
        }
        if (item.getTags() != null) {
            for (String tag : item.getTags()) {
                if (tag != null && tag.toLowerCase().contains(query)) {
                    return true;
                }
            }
        }
        return false;
    }
    
    // ==================== 衣物操作方法 ====================
    
    /**
     * 添加新衣物
     */
    public void addClothingItem(ClothingItem item) {
        clothingRepository.addClothingItemAsync(item, new ClothingRepository.BooleanCallback() {
            @Override
            public void onSuccess(boolean result) {
                successMessage.setValue("衣物添加成功");
                refreshData(); // 重新加载数据
                Logger.d(TAG, "衣物添加成功: " + item.getName());
            }
            
            @Override
            public void onError(String error) {
                errorMessage.setValue("添加衣物失败: " + error);
                Logger.e(TAG, "添加衣物失败: " + error);
            }
        });
    }
    
    /**
     * 更新衣物
     */
    public void updateClothingItem(ClothingItem item) {
        clothingRepository.updateClothingItemAsync(item, new ClothingRepository.BooleanCallback() {
            @Override
            public void onSuccess(boolean result) {
                successMessage.setValue("衣物更新成功");
                refreshData(); // 重新加载数据
                Logger.d(TAG, "衣物更新成功: " + item.getName());
            }
            
            @Override
            public void onError(String error) {
                errorMessage.setValue("更新衣物失败: " + error);
                Logger.e(TAG, "更新衣物失败: " + error);
            }
        });
    }
    
    /**
     * 删除衣物
     */
    public void deleteClothingItem(String itemId) {
        clothingRepository.deleteClothingItemAsync(itemId, new ClothingRepository.BooleanCallback() {
            @Override
            public void onSuccess(boolean result) {
                successMessage.setValue("衣物删除成功");
                refreshData(); // 重新加载数据
                Logger.d(TAG, "衣物删除成功: " + itemId);
            }
            
            @Override
            public void onError(String error) {
                errorMessage.setValue("删除衣物失败: " + error);
                Logger.e(TAG, "删除衣物失败: " + error);
            }
        });
    }
    
    // ==================== 统计方法 ====================
    
    /**
     * 获取分类统计
     */
    public Map<String, Integer> getCategoryStatistics() {
        Map<String, List<ClothingItem>> groups = groupedClothing.getValue();
        if (groups == null) return new HashMap<>();
        
        Map<String, Integer> stats = new HashMap<>();
        for (Map.Entry<String, List<ClothingItem>> entry : groups.entrySet()) {
            stats.put(entry.getKey(), entry.getValue().size());
        }
        return stats;
    }
    
    /**
     * 获取总衣物数量
     */
    public int getTotalClothingCount() {
        List<ClothingItem> items = allClothingItems.getValue();
        return items != null ? items.size() : 0;
    }
    
    /**
     * 获取可用的分类列表
     */
    public List<String> getAvailableCategories() {
        return originalClothingItems.stream()
                .map(ClothingItem::getCategory)
                .filter(category -> category != null && !category.trim().isEmpty())
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }
    
    /**
     * 获取可用的颜色列表
     */
    public List<String> getAvailableColors() {
        return originalClothingItems.stream()
                .map(ClothingItem::getColor)
                .filter(color -> color != null && !color.trim().isEmpty())
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }
    
    @Override
    protected void onCleared() {
        super.onCleared();
        // Repository由ServiceLocator管理，不需要手动关闭
        Logger.d(TAG, "WardrobeViewModel 已清理");
    }
    
    // ==================== 枚举类 ====================
    
    /**
     * 视图状态枚举
     */
    public enum ViewState {
        LOADING,    // 加载中
        CONTENT,    // 有内容
        EMPTY,      // 空状态（没有衣物）
        NO_RESULTS, // 无搜索结果
        ERROR       // 错误状态
    }
}
