[{"merged": "com.wardrobe.app-release-39:/drawable_ic_occasion_daily.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_occasion_daily.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_slit_skirt.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_slit_skirt.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_launcher_foreground.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_activity_main.xml.flat", "source": "com.wardrobe.app-main-40:/layout/activity_main.xml"}, {"merged": "com.wardrobe.app-release-39:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.wardrobe.app-main-40:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.wardrobe.app-release-39:/layout_item_wardrobe_group.xml.flat", "source": "com.wardrobe.app-main-40:/layout/item_wardrobe_group.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_fragment_outfit.xml.flat", "source": "com.wardrobe.app-main-40:/layout/fragment_outfit.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_rain_boots.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_rain_boots.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_search.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_search.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_camisole_dress.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_camisole_dress.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_pajamas.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_pajamas.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_layout_category_selector.xml.flat", "source": "com.wardrobe.app-main-40:/layout/layout_category_selector.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_jewelry.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_jewelry.xml"}, {"merged": "com.wardrobe.app-release-39:/xml_data_extraction_rules.xml.flat", "source": "com.wardrobe.app-main-40:/xml/data_extraction_rules.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_wedding.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_wedding.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_bag.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_bag.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_activity_category_select.xml.flat", "source": "com.wardrobe.app-main-40:/layout/activity_category_select.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_dialog_recyclerview.xml.flat", "source": "com.wardrobe.app-main-40:/layout/dialog_recyclerview.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_item_calendar.xml.flat", "source": "com.wardrobe.app-main-40:/layout/item_calendar.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_functional.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_functional.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_other_acc.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_other_acc.xml"}, {"merged": "com.wardrobe.app-release-39:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.wardrobe.app-main-40:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_sneakers.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_sneakers.xml"}, {"merged": "com.wardrobe.app-release-39:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.wardrobe.app-main-40:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_pleated_skirt.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_pleated_skirt.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_selected_item_background.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/selected_item_background.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_category_outerwear.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_category_outerwear.xml"}, {"merged": "com.wardrobe.app-release-39:/xml_file_paths.xml.flat", "source": "com.wardrobe.app-main-40:/xml/file_paths.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_traditional.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_traditional.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_occasion_date.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_occasion_date.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_shirt.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_shirt.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_calendar.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_calendar.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_socks.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_socks.xml"}, {"merged": "com.wardrobe.app-release-39:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.wardrobe.app-main-40:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.wardrobe.app-release-39:/drawable_ios_style_group_background_middle.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ios_style_group_background_middle.xml"}, {"merged": "com.wardrobe.app-release-39:/anim_slide_in_right.xml.flat", "source": "com.wardrobe.app-main-40:/anim/slide_in_right.xml"}, {"merged": "com.wardrobe.app-release-39:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.wardrobe.app-main-40:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.wardrobe.app-release-39:/drawable_option_background.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/option_background.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_category_dresses.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_category_dresses.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_occasion_party.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_occasion_party.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_replace_photo.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_replace_photo.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_vest.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_vest.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_outfit.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_outfit.xml"}, {"merged": "com.wardrobe.app-release-39:/anim_fade_in.xml.flat", "source": "com.wardrobe.app-main-40:/anim/fade_in.xml"}, {"merged": "com.wardrobe.app-release-39:/color_bottom_nav_color_selector.xml.flat", "source": "com.wardrobe.app-main-40:/color/bottom_nav_color_selector.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_activity_add_clothing.xml.flat", "source": "com.wardrobe.app-main-40:/layout/activity_add_clothing.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_spaghetti_dress.png.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_spaghetti_dress.png"}, {"merged": "com.wardrobe.app-release-39:/menu_action_mode_menu.xml.flat", "source": "com.wardrobe.app-main-40:/menu/action_mode_menu.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_straight_skirt.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_straight_skirt.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_button_background.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/button_background.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_dialog_category_selection.xml.flat", "source": "com.wardrobe.app-main-40:/layout/dialog_category_selection.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_category_underwear.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_category_underwear.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_boots.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_boots.xml"}, {"merged": "com.wardrobe.app-release-39:/menu_menu.xml.flat", "source": "com.wardrobe.app-main-40:/menu/menu.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_thermal.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_thermal.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_wardrobe.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_wardrobe.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_occasion_travel.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_occasion_travel.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_category_bottoms.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_category_bottoms.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_coat.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_coat.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_activity_occasion_select.xml.flat", "source": "com.wardrobe.app-main-40:/layout/activity_occasion_select.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_dialog_color_selection.xml.flat", "source": "com.wardrobe.app-main-40:/layout/dialog_color_selection.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_add.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_add.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ios_style_group_background_bottom.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ios_style_group_background_bottom.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_hat.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_hat.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_jacket.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_jacket.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_fragment_wardrobe.xml.flat", "source": "com.wardrobe.app-main-40:/layout/fragment_wardrobe.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_item_clothing_horizontal.xml.flat", "source": "com.wardrobe.app-main-40:/layout/item_clothing_horizontal.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_clear.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_clear.xml"}, {"merged": "com.wardrobe.app-release-39:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.wardrobe.app-main-40:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_occasion_casual.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_occasion_casual.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_jeans.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_jeans.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_item_occasion_selector.xml.flat", "source": "com.wardrobe.app-main-40:/layout/item_occasion_selector.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_scarf.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_scarf.xml"}, {"merged": "com.wardrobe.app-release-39:/menu_bottom_navigation_menu.xml.flat", "source": "com.wardrobe.app-main-40:/menu/bottom_navigation_menu.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_dialog_rounded_background.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/dialog_rounded_background.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_category_tuxedo.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_category_tuxedo.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_skirt.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_skirt.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_dialog_background.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/dialog_background.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_category_workwear.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_category_workwear.xml"}, {"merged": "com.wardrobe.app-release-39:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.wardrobe.app-main-40:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.wardrobe.app-release-39:/anim_fade_out.xml.flat", "source": "com.wardrobe.app-main-40:/anim/fade_out.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_jumpsuit.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_jumpsuit.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_formal_dress.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_formal_dress.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_belt.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_belt.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ios_style_group_background_top.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ios_style_group_background_top.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_list_item_selector_row.xml.flat", "source": "com.wardrobe.app-main-40:/layout/list_item_selector_row.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_fragment_calendar.xml.flat", "source": "com.wardrobe.app-main-40:/layout/fragment_calendar.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_flared_skirt.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_flared_skirt.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_edit_text_background_ios.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/edit_text_background_ios.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_formal_shoes.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_formal_shoes.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_occasion_work.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_occasion_work.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_spinner_item_with_icon.xml.flat", "source": "com.wardrobe.app-main-40:/layout/spinner_item_with_icon.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_replace_icon_background.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/replace_icon_background.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_button_background_destructive.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/button_background_destructive.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_dialog_photo_selection_ios.xml.flat", "source": "com.wardrobe.app-main-40:/layout/dialog_photo_selection_ios.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_category_suits.png.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_category_suits.png"}, {"merged": "com.wardrobe.app-release-39:/layout_dialog_image_fullscreen.xml.flat", "source": "com.wardrobe.app-main-40:/layout/dialog_image_fullscreen.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_activity_color_select.xml.flat", "source": "com.wardrobe.app-main-40:/layout/activity_color_select.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_item_color_spinner.xml.flat", "source": "com.wardrobe.app-main-40:/layout/item_color_spinner.xml"}, {"merged": "com.wardrobe.app-release-39:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.wardrobe.app-main-40:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_shorts.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_shorts.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_item_category_selector.xml.flat", "source": "com.wardrobe.app-main-40:/layout/item_category_selector.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_fragment_profile.xml.flat", "source": "com.wardrobe.app-main-40:/layout/fragment_profile.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_mermaid_skirt.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_mermaid_skirt.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_occasion_formal.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_occasion_formal.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_check_circle.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_check_circle.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_evening_dress.png.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_evening_dress.png"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_occasion_sports.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_occasion_sports.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_hoodie.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_hoodie.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_category_tops.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_category_tops.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_launcher_background.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_launcher_background.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_color_circle_background.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/color_circle_background.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_button_background_prominent.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/button_background_prominent.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_secondary_system_grouped_background_ripple.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/secondary_system_grouped_background_ripple.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_sandals.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_sandals.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_heels.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_heels.xml"}, {"merged": "com.wardrobe.app-release-39:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.wardrobe.app-main-40:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_item_clothing_pro.xml.flat", "source": "com.wardrobe.app-main-40:/layout/item_clothing_pro.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_formal_pants.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_formal_pants.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_sweater.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_sweater.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_search_background.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/search_background.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_activity_clothing_detail.xml.flat", "source": "com.wardrobe.app-main-40:/layout/activity_clothing_detail.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_item_color_selector.xml.flat", "source": "com.wardrobe.app-main-40:/layout/item_color_selector.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_spinner_background.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/spinner_background.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_multi_select_toolbar.xml.flat", "source": "com.wardrobe.app-main-40:/layout/multi_select_toolbar.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_dress.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_dress.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_profile.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_profile.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_item_outfit.xml.flat", "source": "com.wardrobe.app-main-40:/layout/item_outfit.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_tracksuit.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_tracksuit.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_category_sets.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_category_sets.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_polo.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_polo.xml"}, {"merged": "com.wardrobe.app-release-39:/anim_scale_in.xml.flat", "source": "com.wardrobe.app-main-40:/anim/scale_in.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_sport_shoes.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_sport_shoes.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_suit.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_suit.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_dialog_photo_selection.xml.flat", "source": "com.wardrobe.app-main-40:/layout/dialog_photo_selection.xml"}, {"merged": "com.wardrobe.app-release-39:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.wardrobe.app-main-40:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.wardrobe.app-release-39:/layout_dialog_occasion_selection.xml.flat", "source": "com.wardrobe.app-main-40:/layout/dialog_occasion_selection.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_camisole.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_camisole.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ios_style_group_background_single.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ios_style_group_background_single.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_category_shoes.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_category_shoes.xml"}, {"merged": "com.wardrobe.app-release-39:/layout_activity_batch_add_clothing.xml.flat", "source": "com.wardrobe.app-main-40:/layout/activity_batch_add_clothing.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_occasion_outdoor.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_occasion_outdoor.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_pencil_skirt.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_pencil_skirt.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_dotted_border_background.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/dotted_border_background.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_chevron_right.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_chevron_right.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_pants.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_pants.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_skirt_bottom.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_skirt_bottom.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_bra.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_bra.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_tshirt.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_tshirt.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_canvas_shoes.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_canvas_shoes.xml"}, {"merged": "com.wardrobe.app-release-39:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.wardrobe.app-main-40:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.wardrobe.app-release-39:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.wardrobe.app-main-40:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.wardrobe.app-release-39:/anim_slide_out_left.xml.flat", "source": "com.wardrobe.app-main-40:/anim/slide_out_left.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_occasion_home.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_occasion_home.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_category_accessories.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_category_accessories.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_outer_vest.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_outer_vest.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_sport_pants.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_sport_pants.xml"}, {"merged": "com.wardrobe.app-release-39:/drawable_ic_subcategory_briefs.xml.flat", "source": "com.wardrobe.app-main-40:/drawable/ic_subcategory_briefs.xml"}, {"merged": "com.wardrobe.app-release-39:/xml_backup_rules.xml.flat", "source": "com.wardrobe.app-main-40:/xml/backup_rules.xml"}]