<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/systemGray6"
    tools:context=".ui.activities.AddClothingActivity">

    <!-- Header / Toolbar can be added here if needed -->

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Required Information Section -->
            <TextView
                style="@style/SectionHeader"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/section_required" />

            <LinearLayout
                style="@style/iOSGroupedBackground.Single"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingTop="12dp"
                android:paddingBottom="12dp">


                <!-- Image and Name Row -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp">

                    <FrameLayout
                        android:id="@+id/image_container"
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:layout_marginEnd="16dp">

                        <FrameLayout
                            android:id="@+id/image_placeholder"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/dotted_border_background"
                            android:clickable="true"
                            android:focusable="true">
                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:layout_gravity="center"
                                android:src="@drawable/ic_add"
                                app:tint="@color/systemGray"/>
                        </FrameLayout>

                        <ImageView
                            android:id="@+id/clothing_image_view"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:scaleType="centerCrop"
                            android:visibility="gone" />

                        <ImageView
                            android:id="@+id/button_replace_photo"
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:padding="6dp"
                            android:layout_gravity="bottom|end"
                            android:src="@drawable/ic_replace_photo"
                            android:background="@drawable/replace_icon_background"
                            android:visibility="gone"
                            android:clickable="true"
                            android:focusable="true"/>
                    </FrameLayout>

                    <EditText
                        android:id="@+id/edit_text_name"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@android:color/transparent"
                        android:hint="@string/hint_clothing_name"
                        android:textSize="17sp" />

                </LinearLayout>

                <View style="@style/Separator" android:layout_marginStart="16dp" android:layout_marginTop="12dp" android:layout_marginBottom="12dp" />

                <!-- Category Row -->
                <RelativeLayout
                    android:id="@+id/layout_category"
                    style="@style/iOSListItem">
                    <TextView
                        style="@style/iOSLabel"
                        android:text="@string/label_category" />
                    <TextView
                        android:id="@+id/text_view_category_value"
                        style="@style/iOSValue"
                        android:text="@string/category_placeholder" />
                    <androidx.appcompat.widget.AppCompatImageView style="@style/iOSChevron" />
                </RelativeLayout>

            </LinearLayout>


            <!-- Optional Information Section -->
            <TextView
                style="@style/SectionHeader"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:text="@string/section_optional" />

            <LinearLayout
                style="@style/iOSGroupedBackground.Single"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Color Row -->
                <RelativeLayout
                    android:id="@+id/layout_color"
                    style="@style/iOSListItem">
                    <TextView
                        style="@style/iOSLabel"
                        android:text="@string/label_color" />
                    <TextView
                        android:id="@+id/text_view_color_value"
                        style="@style/iOSValue"
                        android:text="@string/color_placeholder" />
                    <androidx.appcompat.widget.AppCompatImageView style="@style/iOSChevron" />
                </RelativeLayout>

                <View style="@style/Separator" android:layout_marginStart="16dp" />

                <!-- Occasion Row -->
                <RelativeLayout
                    android:id="@+id/layout_occasion"
                    style="@style/iOSListItem">
                    <TextView
                        style="@style/iOSLabel"
                        android:text="@string/label_occasion" />
                    <TextView
                        android:id="@+id/text_view_occasion_value"
                        style="@style/iOSValue"
                        android:text="@string/occasion_placeholder" />
                    <androidx.appcompat.widget.AppCompatImageView style="@style/iOSChevron" />
                </RelativeLayout>

            </LinearLayout>

        </LinearLayout>
    </ScrollView>

    <Button
        android:id="@+id/button_save"
        style="@style/ProminentButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:text="@string/button_save" />

</LinearLayout>