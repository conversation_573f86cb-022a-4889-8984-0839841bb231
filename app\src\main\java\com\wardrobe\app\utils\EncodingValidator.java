package com.wardrobe.app.utils;

import android.content.Context;
import android.content.res.Resources;
import android.util.Log;

import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.util.Locale;

/**
 * 编码验证工具
 * 用于检查和验证应用的字符编码是否正确
 */
public class EncodingValidator {
    
    private static final String TAG = "EncodingValidator";
    
    /**
     * 验证系统编码设置
     */
    public static void validateSystemEncoding() {
        Log.i(TAG, "=== 系统编码验证 ===");
        
        // 检查默认字符集
        Charset defaultCharset = Charset.defaultCharset();
        Log.i(TAG, "默认字符集: " + defaultCharset.name());
        
        // 检查文件编码
        String fileEncoding = System.getProperty("file.encoding");
        Log.i(TAG, "文件编码: " + fileEncoding);
        
        // 检查JNU编码
        String jnuEncoding = System.getProperty("sun.jnu.encoding");
        Log.i(TAG, "JNU编码: " + jnuEncoding);
        
        // 检查语言设置
        Locale defaultLocale = Locale.getDefault();
        Log.i(TAG, "默认语言: " + defaultLocale.toString());
        
        // 验证UTF-8支持
        boolean utf8Supported = Charset.isSupported("UTF-8");
        Log.i(TAG, "UTF-8支持: " + utf8Supported);
        
        // 编码建议
        if (!"UTF-8".equals(fileEncoding)) {
            Log.w(TAG, "⚠️ 建议设置文件编码为UTF-8");
        }
        
        if (!utf8Supported) {
            Log.e(TAG, "❌ 系统不支持UTF-8编码");
        } else {
            Log.i(TAG, "✅ 系统编码配置正常");
        }
    }
    
    /**
     * 验证字符串编码
     */
    public static void validateStringEncoding(Context context) {
        Log.i(TAG, "=== 字符串编码验证 ===");
        
        // 测试中文字符
        String testChinese = "测试中文字符：衣橱管理应用";
        Log.i(TAG, "中文测试: " + testChinese);
        
        // 测试字符串字节长度
        try {
            byte[] utf8Bytes = testChinese.getBytes("UTF-8");
            byte[] gbkBytes = testChinese.getBytes("GBK");
            
            Log.i(TAG, "UTF-8字节长度: " + utf8Bytes.length);
            Log.i(TAG, "GBK字节长度: " + gbkBytes.length);
            
            // 验证编码转换
            String utf8String = new String(utf8Bytes, "UTF-8");
            String gbkString = new String(gbkBytes, "GBK");
            
            Log.i(TAG, "UTF-8转换: " + utf8String);
            Log.i(TAG, "GBK转换: " + gbkString);
            
            if (testChinese.equals(utf8String)) {
                Log.i(TAG, "✅ UTF-8编码正常");
            } else {
                Log.e(TAG, "❌ UTF-8编码异常");
            }
            
        } catch (UnsupportedEncodingException e) {
            Log.e(TAG, "编码转换失败", e);
        }
    }
    
    /**
     * 验证资源文件编码
     */
    public static void validateResourceEncoding(Context context) {
        Log.i(TAG, "=== 资源文件编码验证 ===");
        
        try {
            Resources resources = context.getResources();
            
            // 测试字符串资源
            String appName = resources.getString(context.getApplicationInfo().labelRes);
            Log.i(TAG, "应用名称: " + appName);
            
            // 测试中文字符串资源（如果存在）
            try {
                int stringId = resources.getIdentifier("title_add_new_clothing", "string", context.getPackageName());
                if (stringId != 0) {
                    String chineseString = resources.getString(stringId);
                    Log.i(TAG, "中文资源: " + chineseString);
                    
                    // 检查是否包含乱码字符
                    if (chineseString.contains("?") || chineseString.contains("锟")) {
                        Log.w(TAG, "⚠️ 检测到可能的乱码字符");
                    } else {
                        Log.i(TAG, "✅ 中文资源显示正常");
                    }
                }
            } catch (Exception e) {
                Log.w(TAG, "中文资源检查失败: " + e.getMessage());
            }
            
        } catch (Exception e) {
            Log.e(TAG, "资源编码验证失败", e);
        }
    }
    
    /**
     * 完整的编码验证
     */
    public static void performFullValidation(Context context) {
        Log.i(TAG, "开始完整编码验证...");
        
        validateSystemEncoding();
        validateStringEncoding(context);
        validateResourceEncoding(context);
        
        Log.i(TAG, "编码验证完成");
    }
    
    /**
     * 获取编码诊断报告
     */
    public static String getEncodingDiagnosticReport() {
        StringBuilder report = new StringBuilder();
        
        report.append("=== 编码诊断报告 ===\n");
        report.append("默认字符集: ").append(Charset.defaultCharset().name()).append("\n");
        report.append("文件编码: ").append(System.getProperty("file.encoding")).append("\n");
        report.append("JNU编码: ").append(System.getProperty("sun.jnu.encoding")).append("\n");
        report.append("默认语言: ").append(Locale.getDefault().toString()).append("\n");
        report.append("UTF-8支持: ").append(Charset.isSupported("UTF-8")).append("\n");
        
        // 测试中文字符
        String testString = "中文测试";
        try {
            byte[] bytes = testString.getBytes("UTF-8");
            String restored = new String(bytes, "UTF-8");
            report.append("中文编码测试: ").append(testString.equals(restored) ? "通过" : "失败").append("\n");
        } catch (Exception e) {
            report.append("中文编码测试: 异常 - ").append(e.getMessage()).append("\n");
        }
        
        return report.toString();
    }
    
    /**
     * 修复常见编码问题
     */
    public static void fixCommonEncodingIssues() {
        Log.i(TAG, "尝试修复常见编码问题...");
        
        try {
            // 设置系统属性
            System.setProperty("file.encoding", "UTF-8");
            System.setProperty("sun.jnu.encoding", "UTF-8");
            
            // 设置默认语言（如果需要）
            // Locale.setDefault(Locale.SIMPLIFIED_CHINESE);
            
            Log.i(TAG, "编码修复完成");
            
        } catch (Exception e) {
            Log.e(TAG, "编码修复失败", e);
        }
    }
}
