package com.wardrobe.app;

import android.app.Application;
import com.wardrobe.app.di.ServiceLocator;
import com.wardrobe.app.utils.Logger;
import com.wardrobe.app.utils.PerformanceMonitor;
import com.wardrobe.app.utils.CrashReporter;

/**
 * 应用程序主类
 * 负责初始化全局服务和配置
 */
public class WardrobeApplication extends Application {
    
    private static final String TAG = "WardrobeApplication";
    private ServiceLocator serviceLocator;
    
    @Override
    public void onCreate() {
        super.onCreate();

        // 记录应用启动时间
        PerformanceMonitor.recordAppStartTime();

        Logger.i(TAG, "WardrobeApplication 启动");

        // 初始化崩溃报告系统
        initializeCrashReporter();

        // 初始化性能监控
        initializePerformanceMonitor();

        // 初始化服务定位器
        initializeServiceLocator();

        // 预加载核心服务
        preloadServices();

        // 初始化其他全局配置
        initializeGlobalConfigurations();

        Logger.i(TAG, "WardrobeApplication 初始化完成");
    }
    
    /**
     * 初始化崩溃报告系统
     */
    private void initializeCrashReporter() {
        CrashReporter.getInstance(this);
        Logger.d(TAG, "崩溃报告系统初始化完成");
    }

    /**
     * 初始化性能监控
     */
    private void initializePerformanceMonitor() {
        PerformanceMonitor performanceMonitor = PerformanceMonitor.getInstance(this);
        performanceMonitor.startUIBlockMonitoring();
        Logger.d(TAG, "性能监控系统初始化完成");
    }

    /**
     * 初始化服务定位器
     */
    private void initializeServiceLocator() {
        serviceLocator = ServiceLocator.getInstance(this);
        Logger.d(TAG, "服务定位器初始化完成");
    }
    
    /**
     * 预加载核心服务（异步）
     */
    private void preloadServices() {
        // 异步预加载，避免阻塞应用启动
        new Thread(() -> {
            try {
                long startTime = System.currentTimeMillis();
                serviceLocator.preloadCoreServices();
                long loadTime = System.currentTimeMillis() - startTime;
                Logger.d(TAG, "核心服务预加载完成，耗时: " + loadTime + "ms");
            } catch (Exception e) {
                Logger.e(TAG, "核心服务预加载失败", e);
            }
        }, "ServicePreloader").start();
    }
    
    /**
     * 初始化全局配置
     */
    private void initializeGlobalConfigurations() {
        // 设置全局异常处理器
        Thread.setDefaultUncaughtExceptionHandler(new GlobalUncaughtExceptionHandler());
        
        // 其他全局配置...
        Logger.d(TAG, "全局配置初始化完成");
    }
    
    /**
     * 获取服务定位器
     * 
     * @return 服务定位器实例
     */
    public ServiceLocator getServiceLocator() {
        return serviceLocator;
    }
    
    /**
     * 全局未捕获异常处理器
     */
    private class GlobalUncaughtExceptionHandler implements Thread.UncaughtExceptionHandler {
        
        private final Thread.UncaughtExceptionHandler defaultHandler;
        
        public GlobalUncaughtExceptionHandler() {
            this.defaultHandler = Thread.getDefaultUncaughtExceptionHandler();
        }
        
        @Override
        public void uncaughtException(Thread thread, Throwable throwable) {
            try {
                Logger.e(TAG, "未捕获的异常", throwable);
                
                // 记录崩溃信息
                recordCrashInfo(throwable);
                
            } catch (Exception e) {
                Logger.e(TAG, "处理未捕获异常时发生错误", e);
            } finally {
                // 调用默认处理器
                if (defaultHandler != null) {
                    defaultHandler.uncaughtException(thread, throwable);
                }
            }
        }
        
        /**
         * 记录崩溃信息
         *
         * @param throwable 异常对象
         */
        private void recordCrashInfo(Throwable throwable) {
            try {
                // 使用崩溃报告系统记录详细信息
                CrashReporter crashReporter = CrashReporter.getInstance(WardrobeApplication.this);
                crashReporter.recordCrash(throwable, "应用主线程崩溃");

                Logger.e(TAG, "应用崩溃: " + throwable.getMessage(), throwable);

            } catch (Exception e) {
                Logger.e(TAG, "记录崩溃信息失败", e);
            }
        }
    }
    
    @Override
    public void onTerminate() {
        Logger.i(TAG, "WardrobeApplication 终止");
        
        // 清理资源
        if (serviceLocator != null) {
            serviceLocator.clearServices();
        }
        
        super.onTerminate();
    }
    
    @Override
    public void onLowMemory() {
        super.onLowMemory();
        Logger.w(TAG, "系统内存不足");
        
        // 可以在这里添加内存清理逻辑
        // 例如清理缓存、释放非必要资源等
    }
    
    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        Logger.w(TAG, "系统要求释放内存，级别: " + level);
        
        // 根据不同级别释放内存
        switch (level) {
            case TRIM_MEMORY_UI_HIDDEN:
                // UI不可见时释放UI相关资源
                Logger.d(TAG, "释放UI相关资源");
                break;
            case TRIM_MEMORY_BACKGROUND:
                // 应用在后台时释放一些资源
                Logger.d(TAG, "释放后台资源");
                break;
            case TRIM_MEMORY_MODERATE:
            case TRIM_MEMORY_COMPLETE:
                // 系统内存紧张时释放更多资源
                Logger.d(TAG, "释放更多资源");
                break;
        }
    }
}
