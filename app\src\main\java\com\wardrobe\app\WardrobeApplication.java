package com.wardrobe.app;

import android.app.Application;
import com.wardrobe.app.di.ServiceLocator;
import com.wardrobe.app.utils.Logger;

/**
 * 应用程序主类
 * 负责初始化全局服务和配置
 */
public class WardrobeApplication extends Application {
    
    private static final String TAG = "WardrobeApplication";
    private ServiceLocator serviceLocator;
    
    @Override
    public void onCreate() {
        super.onCreate();
        
        Logger.i(TAG, "WardrobeApplication 启动");
        
        // 初始化服务定位器
        initializeServiceLocator();
        
        // 预加载核心服务
        preloadServices();
        
        // 初始化其他全局配置
        initializeGlobalConfigurations();
        
        Logger.i(TAG, "WardrobeApplication 初始化完成");
    }
    
    /**
     * 初始化服务定位器
     */
    private void initializeServiceLocator() {
        serviceLocator = ServiceLocator.getInstance(this);
        Logger.d(TAG, "服务定位器初始化完成");
    }
    
    /**
     * 预加载核心服务
     */
    private void preloadServices() {
        try {
            serviceLocator.preloadCoreServices();
            Logger.d(TAG, "核心服务预加载完成");
        } catch (Exception e) {
            Logger.e(TAG, "核心服务预加载失败", e);
        }
    }
    
    /**
     * 初始化全局配置
     */
    private void initializeGlobalConfigurations() {
        // 设置全局异常处理器
        Thread.setDefaultUncaughtExceptionHandler(new GlobalUncaughtExceptionHandler());
        
        // 其他全局配置...
        Logger.d(TAG, "全局配置初始化完成");
    }
    
    /**
     * 获取服务定位器
     * 
     * @return 服务定位器实例
     */
    public ServiceLocator getServiceLocator() {
        return serviceLocator;
    }
    
    /**
     * 全局未捕获异常处理器
     */
    private class GlobalUncaughtExceptionHandler implements Thread.UncaughtExceptionHandler {
        
        private final Thread.UncaughtExceptionHandler defaultHandler;
        
        public GlobalUncaughtExceptionHandler() {
            this.defaultHandler = Thread.getDefaultUncaughtExceptionHandler();
        }
        
        @Override
        public void uncaughtException(Thread thread, Throwable throwable) {
            try {
                Logger.e(TAG, "未捕获的异常", throwable);
                
                // 记录崩溃信息
                recordCrashInfo(throwable);
                
            } catch (Exception e) {
                Logger.e(TAG, "处理未捕获异常时发生错误", e);
            } finally {
                // 调用默认处理器
                if (defaultHandler != null) {
                    defaultHandler.uncaughtException(thread, throwable);
                }
            }
        }
        
        /**
         * 记录崩溃信息
         * 
         * @param throwable 异常对象
         */
        private void recordCrashInfo(Throwable throwable) {
            try {
                // 这里可以添加崩溃报告逻辑
                // 例如保存到本地文件或发送到服务器
                Logger.e(TAG, "应用崩溃: " + throwable.getMessage(), throwable);
                
                // 保存基本的崩溃信息
                String crashInfo = String.format(
                    "崩溃时间: %s\n异常类型: %s\n异常信息: %s\n线程: %s",
                    new java.util.Date().toString(),
                    throwable.getClass().getSimpleName(),
                    throwable.getMessage(),
                    Thread.currentThread().getName()
                );
                
                Logger.e(TAG, "崩溃详情: " + crashInfo);
                
            } catch (Exception e) {
                Logger.e(TAG, "记录崩溃信息失败", e);
            }
        }
    }
    
    @Override
    public void onTerminate() {
        Logger.i(TAG, "WardrobeApplication 终止");
        
        // 清理资源
        if (serviceLocator != null) {
            serviceLocator.clearServices();
        }
        
        super.onTerminate();
    }
    
    @Override
    public void onLowMemory() {
        super.onLowMemory();
        Logger.w(TAG, "系统内存不足");
        
        // 可以在这里添加内存清理逻辑
        // 例如清理缓存、释放非必要资源等
    }
    
    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        Logger.w(TAG, "系统要求释放内存，级别: " + level);
        
        // 根据不同级别释放内存
        switch (level) {
            case TRIM_MEMORY_UI_HIDDEN:
                // UI不可见时释放UI相关资源
                Logger.d(TAG, "释放UI相关资源");
                break;
            case TRIM_MEMORY_BACKGROUND:
                // 应用在后台时释放一些资源
                Logger.d(TAG, "释放后台资源");
                break;
            case TRIM_MEMORY_MODERATE:
            case TRIM_MEMORY_COMPLETE:
                // 系统内存紧张时释放更多资源
                Logger.d(TAG, "释放更多资源");
                break;
        }
    }
}
