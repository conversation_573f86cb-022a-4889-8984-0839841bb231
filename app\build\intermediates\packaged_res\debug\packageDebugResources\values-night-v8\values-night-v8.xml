<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="borderColor">#33FFFFFF</color>
    <color name="buttonBackground">#BB86FC</color>
    <color name="buttonTextColor">#000000</color>
    <color name="cardBackground">#2C2C2C</color>
    <color name="colorBackground">#121212</color>
    <color name="colorError">#CF6679</color>
    <color name="colorOnBackground">#FFFFFF</color>
    <color name="colorOnError">#000000</color>
    <color name="colorOnPrimary">#000000</color>
    <color name="colorOnSecondary">#000000</color>
    <color name="colorOnSurface">#FFFFFF</color>
    <color name="colorPrimary">#BB86FC</color>
    <color name="colorPrimaryVariant">#3700B3</color>
    <color name="colorSecondary">#03DAC6</color>
    <color name="colorSecondaryVariant">#018786</color>
    <color name="colorSurface">#1E1E1E</color>
    <color name="containerBackground">#1E1E1E</color>
    <color name="dividerColor">#33FFFFFF</color>
    <color name="navigationBarColor">#121212</color>
    <color name="rippleColor">#33FFFFFF</color>
    <color name="selectedItemBackground">#33BB86FC</color>
    <color name="statusBarColor">#000000</color>
    <color name="textColorHint">#66FFFFFF</color>
    <color name="textColorPrimary">#FFFFFF</color>
    <color name="textColorSecondary">#B3FFFFFF</color>
    <style name="AppTheme" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryVariant">@color/colorPrimaryVariant</item>
        <item name="colorOnPrimary">@color/colorOnPrimary</item>
        
        
        <item name="colorSecondary">@color/colorSecondary</item>
        <item name="colorSecondaryVariant">@color/colorSecondaryVariant</item>
        <item name="colorOnSecondary">@color/colorOnSecondary</item>
        
        
        <item name="android:colorBackground">@color/colorBackground</item>
        <item name="colorOnBackground">@color/colorOnBackground</item>
        
        
        <item name="colorSurface">@color/colorSurface</item>
        <item name="colorOnSurface">@color/colorOnSurface</item>
        
        
        <item name="colorError">@color/colorError</item>
        <item name="colorOnError">@color/colorOnError</item>
        
        
        <item name="android:statusBarColor">@color/statusBarColor</item>
        <item name="android:windowLightStatusBar">false</item>
        
        
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:windowLightNavigationBar">false</item>
        
        
        <item name="android:textColorPrimary">@color/textColorPrimary</item>
        <item name="android:textColorSecondary">@color/textColorSecondary</item>
        <item name="android:textColorHint">@color/textColorHint</item>
        
        
        <item name="android:forceDarkAllowed">true</item>
    </style>
    <style name="ButtonStyle" parent="Widget.Material3.Button">
        <item name="android:background">@color/buttonBackground</item>
        <item name="android:textColor">@color/buttonTextColor</item>
        <item name="backgroundTint">@color/buttonBackground</item>
    </style>
    <style name="CardStyle" parent="Widget.Material3.CardView.Elevated">
        <item name="cardBackgroundColor">@color/cardBackground</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style>
</resources>