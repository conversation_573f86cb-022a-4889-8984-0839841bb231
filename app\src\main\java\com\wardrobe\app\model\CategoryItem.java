package com.wardrobe.app.model;

import android.content.Context;

// 注意：这个类不需要实现Serializable，因为它只用于在UI层临时展示
public class CategoryItem {
    private final String name;
    private final int iconResId; // 用于存储图标的资源ID, 例如 R.drawable.ic_category_tops
    private final String icon; // 新增：用于存储图标名称
    private final String displayName; // 显示名称

    public CategoryItem(String name, int iconResId) {
        this.name = name;
        this.iconResId = iconResId;
        this.icon = null;
        this.displayName = name;
    }

    public CategoryItem(String name, String icon) {
        this.name = name;
        this.iconResId = 0;
        this.icon = icon;
        this.displayName = name;
    }

    public CategoryItem(String name, String displayName, String icon) {
        this.name = name;
        this.iconResId = 0;
        this.icon = icon;
        this.displayName = displayName;
    }

    public String getName() {
        return name;
    }

    public String getDisplayName() {
        return displayName;
    }

    public int getIconResId() {
        return iconResId;
    }

    public String getIcon() {
        return icon;
    }

    /**
     * 根据图标名称获取资源ID
     */
    public int getIconResId(Context context) {
        if (iconResId != 0) {
            return iconResId;
        }
        if (icon != null && !icon.isEmpty()) {
            return context.getResources().getIdentifier(icon, "drawable", context.getPackageName());
        }
        return 0;
    }

    // 重写toString()方法是很好的实践，
    // 这样即使在简单的ArrayAdapter中也能正确显示分类名称
    @Override
    public String toString() {
        return displayName != null ? displayName : name;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CategoryItem that = (CategoryItem) o;
        return name != null ? name.equals(that.name) : that.name == null;
    }

    @Override
    public int hashCode() {
        return name != null ? name.hashCode() : 0;
    }

    // 兼容旧代码
    public String getIconName() {
        return getIcon();
    }
    // 判断是否为自定义类型/颜色
    public boolean isCustom() {
        return "自定义".equals(displayName) || "自定义颜色".equals(displayName) || "custom".equalsIgnoreCase(name);
    }
}
