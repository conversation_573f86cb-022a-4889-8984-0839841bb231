package com.wardrobe.app.ui.components;

import android.content.Context;
import com.wardrobe.app.R;
import com.wardrobe.app.model.CategoryItem;
import java.util.ArrayList;
import java.util.List;

public class OccasionProvider {

    public static List<CategoryItem> getOccasions(Context context) {
        List<CategoryItem> occasions = new ArrayList<>();
        occasions.add(new CategoryItem(context.getString(R.string.occasion_work), "ic_occasion_work"));
        occasions.add(new CategoryItem(context.getString(R.string.occasion_casual), "ic_occasion_casual"));
        occasions.add(new CategoryItem(context.getString(R.string.occasion_formal), "ic_occasion_formal"));
        occasions.add(new CategoryItem(context.getString(R.string.occasion_sport), "ic_occasion_sports"));
        occasions.add(new CategoryItem(context.getString(R.string.occasion_date), "ic_occasion_date"));
        occasions.add(new CategoryItem(context.getString(R.string.occasion_home), "ic_occasion_home"));
        occasions.add(new CategoryItem(context.getString(R.string.occasion_party), "ic_occasion_party"));
        occasions.add(new CategoryItem(context.getString(R.string.occasion_travel), "ic_occasion_travel"));
        return occasions;
    }
}