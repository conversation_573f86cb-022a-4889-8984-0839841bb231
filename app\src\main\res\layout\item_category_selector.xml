<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="56dp"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:background="@color/background_primary"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <ImageView
        android:id="@+id/category_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="12dp"
        android:contentDescription="类别图标" />

    <TextView
        android:id="@+id/tv_category"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:paddingStart="0dp"
        android:paddingEnd="12dp"
        android:text="分类名称"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:singleLine="true"
        android:ellipsize="end" />

    <ImageView
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="20dp"
        android:src="@drawable/ic_chevron_right"
        android:tint="@color/text_tertiary"
        android:contentDescription="进入" />

</LinearLayout> 