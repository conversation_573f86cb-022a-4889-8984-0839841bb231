package com.wardrobe.app.di;

import android.content.Context;
import com.wardrobe.app.data.ClothingManager;
import com.wardrobe.app.data.SharedPreferencesManager;
import com.wardrobe.app.repository.ClothingRepository;
import com.wardrobe.app.utils.GlobalExceptionHandler;
import com.wardrobe.app.utils.Logger;
import com.wardrobe.app.config.AppConfig;
import com.wardrobe.app.data.SecurePreferencesManager;
import com.wardrobe.app.utils.FileSecurityManager;
import com.wardrobe.app.utils.PermissionManager;
import com.wardrobe.app.utils.AsyncTaskManager;
import com.wardrobe.app.utils.ImageOptimizer;
import com.wardrobe.app.utils.MemoryOptimizer;
import com.wardrobe.app.utils.LocalizationManager;
import com.wardrobe.app.ui.common.UiStateManager;
import com.wardrobe.app.ui.common.UserFeedbackManager;
import com.wardrobe.app.ui.common.ErrorDisplayManager;

import java.util.HashMap;
import java.util.Map;

/**
 * 简单的服务定位器模式实现
 * 提供依赖注入功能，管理应用中的单例对象
 * 避免了复杂的依赖注入框架，适合中小型项目
 */
public class ServiceLocator {
    
    private static final String TAG = "ServiceLocator";
    private static volatile ServiceLocator instance;
    private static final Object LOCK = new Object();
    
    private final Context applicationContext;
    private final Map<Class<?>, Object> services = new HashMap<>();
    
    private ServiceLocator(Context context) {
        this.applicationContext = context.getApplicationContext();
        Logger.d(TAG, "ServiceLocator 初始化完成");
    }
    
    /**
     * 获取ServiceLocator实例
     * 
     * @param context 上下文
     * @return ServiceLocator实例
     */
    public static ServiceLocator getInstance(Context context) {
        if (instance == null) {
            synchronized (LOCK) {
                if (instance == null) {
                    instance = new ServiceLocator(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * 获取服务实例
     * 
     * @param serviceClass 服务类型
     * @param <T> 服务类型泛型
     * @return 服务实例
     */
    @SuppressWarnings("unchecked")
    public <T> T getService(Class<T> serviceClass) {
        synchronized (services) {
            T service = (T) services.get(serviceClass);
            if (service == null) {
                service = createService(serviceClass);
                if (service != null) {
                    services.put(serviceClass, service);
                    Logger.d(TAG, "创建服务: " + serviceClass.getSimpleName());
                }
            }
            return service;
        }
    }
    
    /**
     * 注册服务实例
     * 
     * @param serviceClass 服务类型
     * @param service 服务实例
     * @param <T> 服务类型泛型
     */
    public <T> void registerService(Class<T> serviceClass, T service) {
        synchronized (services) {
            services.put(serviceClass, service);
            Logger.d(TAG, "注册服务: " + serviceClass.getSimpleName());
        }
    }
    
    /**
     * 移除服务实例
     * 
     * @param serviceClass 服务类型
     */
    public void removeService(Class<?> serviceClass) {
        synchronized (services) {
            services.remove(serviceClass);
            Logger.d(TAG, "移除服务: " + serviceClass.getSimpleName());
        }
    }
    
    /**
     * 清除所有服务
     */
    public void clearServices() {
        synchronized (services) {
            services.clear();
            Logger.d(TAG, "清除所有服务");
        }
    }
    
    /**
     * 创建服务实例
     * 
     * @param serviceClass 服务类型
     * @param <T> 服务类型泛型
     * @return 服务实例
     */
    @SuppressWarnings("unchecked")
    private <T> T createService(Class<T> serviceClass) {
        try {
            if (serviceClass == SharedPreferencesManager.class) {
                return (T) new SharedPreferencesManager(applicationContext);
            } else if (serviceClass == ClothingManager.class) {
                return (T) ClothingManager.getInstance(applicationContext);
            } else if (serviceClass == ClothingRepository.class) {
                // 如果有ClothingRepository的话
                return (T) new ClothingRepository(applicationContext);
            } else if (serviceClass == GlobalExceptionHandler.class) {
                return (T) GlobalExceptionHandler.getInstance(applicationContext);
            } else if (serviceClass == AppConfig.class) {
                return (T) AppConfig.getInstance(applicationContext);
            } else if (serviceClass == SecurePreferencesManager.class) {
                try {
                    return (T) SecurePreferencesManager.getInstance(applicationContext);
                } catch (SecurityException e) {
                    Logger.e(TAG, "创建SecurePreferencesManager失败", e);
                    return null;
                }
            } else if (serviceClass == FileSecurityManager.class) {
                return (T) new FileSecurityManager(applicationContext);
            } else if (serviceClass == PermissionManager.class) {
                return (T) new PermissionManager(applicationContext);
            } else if (serviceClass == AsyncTaskManager.class) {
                return (T) AsyncTaskManager.getInstance();
            } else if (serviceClass == ImageOptimizer.class) {
                return (T) ImageOptimizer.getInstance(applicationContext);
            } else if (serviceClass == MemoryOptimizer.class) {
                return (T) MemoryOptimizer.getInstance(applicationContext);
            } else if (serviceClass == LocalizationManager.class) {
                return (T) LocalizationManager.getInstance(applicationContext);
            } else if (serviceClass == UserFeedbackManager.class) {
                return (T) UserFeedbackManager.getInstance();
            } else if (serviceClass == ErrorDisplayManager.class) {
                return (T) ErrorDisplayManager.getInstance();
            }
            
            // 尝试使用反射创建实例（适用于有无参构造函数的类）
            return serviceClass.getDeclaredConstructor().newInstance();
            
        } catch (Exception e) {
            Logger.e(TAG, "创建服务失败: " + serviceClass.getSimpleName(), e);
            return null;
        }
    }
    
    /**
     * 检查服务是否已注册
     * 
     * @param serviceClass 服务类型
     * @return 是否已注册
     */
    public boolean isServiceRegistered(Class<?> serviceClass) {
        synchronized (services) {
            return services.containsKey(serviceClass);
        }
    }
    
    /**
     * 获取已注册服务的数量
     * 
     * @return 服务数量
     */
    public int getServiceCount() {
        synchronized (services) {
            return services.size();
        }
    }
    
    /**
     * 获取所有已注册的服务类型
     * 
     * @return 服务类型集合
     */
    public java.util.Set<Class<?>> getRegisteredServiceTypes() {
        synchronized (services) {
            return new java.util.HashSet<>(services.keySet());
        }
    }
    
    /**
     * 预加载核心服务
     * 在应用启动时调用，提前创建常用服务
     */
    public void preloadCoreServices() {
        Logger.d(TAG, "开始预加载核心服务");

        // 预加载核心服务
        getService(SharedPreferencesManager.class);
        getService(GlobalExceptionHandler.class);
        getService(AppConfig.class);
        getService(AsyncTaskManager.class);
        getService(MemoryOptimizer.class);
        getService(ImageOptimizer.class);
        getService(LocalizationManager.class);
        getService(UserFeedbackManager.class);
        getService(ErrorDisplayManager.class);
        getService(ClothingManager.class);

        // 启动内存监控
        MemoryOptimizer memoryOptimizer = getService(MemoryOptimizer.class);
        memoryOptimizer.startMemoryMonitoring();

        Logger.d(TAG, "核心服务预加载完成，共加载 " + getServiceCount() + " 个服务");
    }
    
    /**
     * 获取应用上下文
     * 
     * @return 应用上下文
     */
    public Context getApplicationContext() {
        return applicationContext;
    }
}
