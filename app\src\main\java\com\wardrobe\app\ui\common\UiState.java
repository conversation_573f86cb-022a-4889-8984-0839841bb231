package com.wardrobe.app.ui.common;

/**
 * UI状态枚举
 * 定义应用中所有可能的UI状态，确保一致的用户体验
 */
public enum UiState {
    
    /**
     * 初始状态 - 组件刚创建，还未开始加载
     */
    INITIAL,
    
    /**
     * 加载中状态 - 正在获取数据或执行操作
     */
    LOADING,
    
    /**
     * 内容状态 - 有数据且正常显示
     */
    CONTENT,
    
    /**
     * 空状态 - 没有数据可显示
     */
    EMPTY,
    
    /**
     * 无搜索结果状态 - 搜索或筛选后没有匹配的结果
     */
    NO_RESULTS,
    
    /**
     * 错误状态 - 发生错误，无法正常显示内容
     */
    ERROR,
    
    /**
     * 网络错误状态 - 网络连接问题
     */
    NETWORK_ERROR,
    
    /**
     * 刷新状态 - 正在刷新数据（通常显示下拉刷新指示器）
     */
    REFRESHING,
    
    /**
     * 加载更多状态 - 正在加载更多数据（分页加载）
     */
    LOADING_MORE,
    
    /**
     * 成功状态 - 操作成功完成
     */
    SUCCESS,
    
    /**
     * 离线状态 - 设备离线，显示缓存数据
     */
    OFFLINE
}
