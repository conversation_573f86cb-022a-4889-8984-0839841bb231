package com.wardrobe.app.utils;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Log;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;

import com.wardrobe.app.R;
import com.wardrobe.app.utils.ErrorUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class ImagePickerHelper {

    private final AppCompatActivity activity;
    private final OnImagePickedListener onImagePickedListener;
    private final ActivityResultLauncher<Intent> galleryLauncher;
    private final ActivityResultLauncher<Uri> cameraLauncher;
    private final ActivityResultLauncher<String> requestPermissionLauncher;
    private Uri cameraImageUri;

    public interface OnImagePickedListener {
        void onImagePicked(Uri imageUri);
    }

    public ImagePickerHelper(AppCompatActivity activity, OnImagePickedListener onImagePickedListener) {
        this.activity = activity;
        this.onImagePickedListener = onImagePickedListener;

        galleryLauncher = activity.registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == Activity.RESULT_OK && result.getData() != null) {
                        Uri imageUri = result.getData().getData();
                        if (imageUri != null) {
                            Uri finalUri = copyToInternalStorage(imageUri);
                            onImagePickedListener.onImagePicked(finalUri);
                        }
                    }
                });

        cameraLauncher = activity.registerForActivityResult(
                new ActivityResultContracts.TakePicture(),
                success -> {
                    if (success) {
                        Uri finalUri = copyToInternalStorage(cameraImageUri);
                        onImagePickedListener.onImagePicked(finalUri);
                    }
                });
        
        requestPermissionLauncher = activity.registerForActivityResult(
            new ActivityResultContracts.RequestPermission(),
            isGranted -> {
                if (isGranted) {
                    // This is a generic handler. The specific action (camera/gallery)
                    // needs to be re-triggered by the user. A toast can guide them.
                    Toast.makeText(activity, activity.getString(R.string.toast_permission_granted_retry), Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(activity, activity.getString(R.string.toast_permission_denied), Toast.LENGTH_SHORT).show();
                }
            });
    }

    public void showPhotoSelectionDialog() {
        new AlertDialog.Builder(activity)
                .setTitle("Select Photo")
                .setItems(new CharSequence[]{"Take Photo", "Choose from Gallery"}, (dialog, which) -> {
                    if (which == 0) {
                        checkPermissionAndLaunchCamera();
                    } else {
                        checkPermissionAndLaunchGallery();
                    }
                })
                .show();
    }
    
    private void checkPermissionAndLaunchCamera() {
        if (ContextCompat.checkSelfPermission(activity, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED) {
            launchCamera();
        } else {
            requestPermissionLauncher.launch(Manifest.permission.CAMERA);
        }
    }
    
    private void checkPermissionAndLaunchGallery() {
        String permission = Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU ?
                Manifest.permission.READ_MEDIA_IMAGES : Manifest.permission.READ_EXTERNAL_STORAGE;
        
        if (ContextCompat.checkSelfPermission(activity, permission) == PackageManager.PERMISSION_GRANTED) {
            launchGallery();
        } else {
            requestPermissionLauncher.launch(permission);
        }
    }

    private void launchCamera() {
        try {
            cameraImageUri = createImageFileUri();
            cameraLauncher.launch(cameraImageUri);
        } catch (IOException e) {
            Toast.makeText(activity, activity.getString(R.string.toast_create_image_file_failed), Toast.LENGTH_SHORT).show();
        }
    }

    private void launchGallery() {
        Intent intent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
        galleryLauncher.launch(intent);
    }

    private Uri createImageFileUri() throws IOException {
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.US).format(new Date());
        String imageFileName = "JPEG_" + timeStamp + "_";
        File storageDir = activity.getExternalFilesDir(Environment.DIRECTORY_PICTURES);
        File image = File.createTempFile(imageFileName, ".jpg", storageDir);
        return FileProvider.getUriForFile(activity, activity.getApplicationContext().getPackageName() + ".provider", image);
    }
    
    private Uri copyToInternalStorage(Uri uri) {
        if (uri == null) return null;

        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            inputStream = activity.getContentResolver().openInputStream(uri);
            if (inputStream == null) return null;

            File outputFile = new File(activity.getFilesDir(), new File(uri.getPath()).getName());
            outputStream = new FileOutputStream(outputFile);
            
            byte[] buf = new byte[1024];
            int len;
            while ((len = inputStream.read(buf)) > 0) {
                outputStream.write(buf, 0, len);
            }
            return Uri.fromFile(outputFile);
        } catch (Exception e) {
            ErrorUtils.logError("Failed to copy image to internal storage", e);
            return uri; // fallback to original uri
        } finally {
            try {
                if (inputStream != null) inputStream.close();
                if (outputStream != null) outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    // 兼容旧调用
    public void showPhotoDialog() {
        showPhotoSelectionDialog();
    }

    // 静态方法：将图片复制到App存储
    public static String copyImageToAppStorage(Activity activity, Uri uri) {
        if (uri == null) return null;
        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            inputStream = activity.getContentResolver().openInputStream(uri);
            if (inputStream == null) return null;
            File outputFile = new File(activity.getFilesDir(), new File(uri.getPath()).getName());
            outputStream = new FileOutputStream(outputFile);
            byte[] buf = new byte[1024];
            int len;
            while ((len = inputStream.read(buf)) > 0) {
                outputStream.write(buf, 0, len);
            }
            return outputFile.getAbsolutePath();
        } catch (Exception e) {
            ErrorUtils.logError("Failed to copy image to app storage", e);
            return null;
        } finally {
            try {
                if (inputStream != null) inputStream.close();
                if (outputStream != null) outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}