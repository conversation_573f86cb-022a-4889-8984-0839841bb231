package com.wardrobe.app.utils;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Toast;

import androidx.annotation.ColorRes;
import androidx.annotation.DrawableRes;
import androidx.annotation.StringRes;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.snackbar.Snackbar;

/**
 * UI工具类
 * 提供统一的UI组件、动画效果和用户体验优化功能
 */
public class UIUtils {
    
    private static final String TAG = "UIUtils";
    
    // 动画持续时间
    private static final int ANIMATION_DURATION_SHORT = 200;
    private static final int ANIMATION_DURATION_MEDIUM = 300;
    private static final int ANIMATION_DURATION_LONG = 500;
    
    // Toast显示时间
    private static final int TOAST_DURATION_SHORT = Toast.LENGTH_SHORT;
    private static final int TOAST_DURATION_LONG = Toast.LENGTH_LONG;

    /**
     * 显示Toast消息
     * @param context 上下文
     * @param message 消息内容
     */
    public static void showToast(Context context, String message) {
        if (context == null || TextUtils.isEmpty(message)) {
            return;
        }
        Toast.makeText(context, message, TOAST_DURATION_SHORT).show();
    }

    /**
     * 显示Toast消息（长时间显示）
     * @param context 上下文
     * @param message 消息内容
     */
    public static void showLongToast(Context context, String message) {
        if (context == null || TextUtils.isEmpty(message)) {
            return;
        }
        Toast.makeText(context, message, TOAST_DURATION_LONG).show();
    }

    /**
     * 显示Toast消息（使用资源ID）
     * @param context 上下文
     * @param messageResId 消息资源ID
     */
    public static void showToast(Context context, @StringRes int messageResId) {
        if (context == null) {
            return;
        }
        Toast.makeText(context, messageResId, TOAST_DURATION_SHORT).show();
    }

    /**
     * 显示Snackbar消息
     * @param view 父视图
     * @param message 消息内容
     */
    public static void showSnackbar(View view, String message) {
        if (view == null || TextUtils.isEmpty(message)) {
            return;
        }
        Snackbar.make(view, message, Snackbar.LENGTH_SHORT).show();
    }

    /**
     * 显示Snackbar消息（带操作按钮）
     * @param view 父视图
     * @param message 消息内容
     * @param actionText 操作按钮文本
     * @param actionListener 操作按钮监听器
     */
    public static void showSnackbarWithAction(View view, String message, String actionText, View.OnClickListener actionListener) {
        if (view == null || TextUtils.isEmpty(message)) {
            return;
        }
        Snackbar.make(view, message, Snackbar.LENGTH_LONG)
                .setAction(actionText, actionListener)
                .show();
    }

    /**
     * 获取颜色资源
     * @param context 上下文
     * @param colorResId 颜色资源ID
     * @return 颜色值
     */
    public static int getColor(Context context, @ColorRes int colorResId) {
        if (context == null) {
            return Color.BLACK;
        }
        return ContextCompat.getColor(context, colorResId);
    }

    /**
     * 获取Drawable资源
     * @param context 上下文
     * @param drawableResId Drawable资源ID
     * @return Drawable对象
     */
    public static Drawable getDrawable(Context context, @DrawableRes int drawableResId) {
        if (context == null) {
            return null;
        }
        return ContextCompat.getDrawable(context, drawableResId);
    }

    /**
     * 获取字符串资源
     * @param context 上下文
     * @param stringResId 字符串资源ID
     * @return 字符串
     */
    public static String getString(Context context, @StringRes int stringResId) {
        if (context == null) {
            return "";
        }
        return context.getString(stringResId);
    }

    /**
     * 设置视图可见性
     * @param view 视图
     * @param visible 是否可见
     */
    public static void setViewVisibility(View view, boolean visible) {
        if (view != null) {
            view.setVisibility(visible ? View.VISIBLE : View.GONE);
        }
    }

    /**
     * 设置视图可见性（使用INVISIBLE）
     * @param view 视图
     * @param visible 是否可见
     */
    public static void setViewVisibilityInvisible(View view, boolean visible) {
        if (view != null) {
            view.setVisibility(visible ? View.VISIBLE : View.INVISIBLE);
        }
    }

    /**
     * 淡入动画
     * @param view 要动画的视图
     */
    public static void fadeIn(View view) {
        if (view == null) {
            return;
        }
        view.setAlpha(0f);
        view.setVisibility(View.VISIBLE);
        view.animate()
                .alpha(1f)
                .setDuration(ANIMATION_DURATION_MEDIUM)
                .start();
    }

    /**
     * 淡出动画
     * @param view 要动画的视图
     */
    public static void fadeOut(View view) {
        if (view == null) {
            return;
        }
        view.animate()
                .alpha(0f)
                .setDuration(ANIMATION_DURATION_MEDIUM)
                .withEndAction(() -> view.setVisibility(View.GONE))
                .start();
    }

    /**
     * 缩放动画
     * @param view 要动画的视图
     * @param scale 缩放比例
     */
    public static void scaleView(View view, float scale) {
        if (view == null) {
            return;
        }
        view.animate()
                .scaleX(scale)
                .scaleY(scale)
                .setDuration(ANIMATION_DURATION_SHORT)
                .start();
    }

    /**
     * 平移动画
     * @param view 要动画的视图
     * @param translationX X轴平移距离
     * @param translationY Y轴平移距离
     */
    public static void translateView(View view, float translationX, float translationY) {
        if (view == null) {
            return;
        }
        view.animate()
                .translationX(translationX)
                .translationY(translationY)
                .setDuration(ANIMATION_DURATION_MEDIUM)
                .start();
    }

    /**
     * 加载动画资源
     * @param context 上下文
     * @param animResId 动画资源ID
     * @return 动画对象
     */
    public static Animation loadAnimation(Context context, int animResId) {
        if (context == null) {
            return null;
        }
        return AnimationUtils.loadAnimation(context, animResId);
    }

    /**
     * 设置视图背景
     */
    public static void setViewBackground(View view, Drawable drawable) {
        if (view != null && drawable != null) {
            view.setBackground(drawable);
        }
    }

    /**
     * 设置视图背景颜色
     */
    public static void setViewBackgroundColor(View view, int color) {
        if (view != null) {
            view.setBackgroundColor(color);
        }
    }

    /**
     * 设置视图背景资源
     */
    public static void setViewBackgroundResource(View view, int resourceId) {
        if (view != null) {
            view.setBackgroundResource(resourceId);
        }
    }

    /**
     * 创建圆角背景
     */
    public static void setRoundedBackground(View view, int backgroundColor, int cornerRadius) {
        if (view == null) return;
        
        android.graphics.drawable.GradientDrawable shape = new android.graphics.drawable.GradientDrawable();
        shape.setShape(android.graphics.drawable.GradientDrawable.RECTANGLE);
        shape.setColor(backgroundColor);
        shape.setCornerRadius(cornerRadius);
        
        view.setBackground(shape);
    }

    /**
     * 创建带边框的圆角背景
     */
    public static void setRoundedBackgroundWithBorder(View view, int backgroundColor, int borderColor, int borderWidth, int cornerRadius) {
        if (view == null) return;
        
        android.graphics.drawable.GradientDrawable shape = new android.graphics.drawable.GradientDrawable();
        shape.setShape(android.graphics.drawable.GradientDrawable.RECTANGLE);
        shape.setColor(backgroundColor);
        shape.setStroke(borderWidth, borderColor);
        shape.setCornerRadius(cornerRadius);
        
        view.setBackground(shape);
    }

    /**
     * 设置RecyclerView的布局管理器
     * @param recyclerView RecyclerView
     * @param spanCount 列数
     */
    public static void setupStaggeredGridLayout(RecyclerView recyclerView, int spanCount) {
        if (recyclerView == null) {
            return;
        }
        androidx.recyclerview.widget.StaggeredGridLayoutManager layoutManager = 
                new androidx.recyclerview.widget.StaggeredGridLayoutManager(spanCount, androidx.recyclerview.widget.StaggeredGridLayoutManager.VERTICAL);
        recyclerView.setLayoutManager(layoutManager);
    }

    /**
     * 设置RecyclerView的线性布局管理器
     * @param recyclerView RecyclerView
     * @param orientation 方向（RecyclerView.VERTICAL 或 RecyclerView.HORIZONTAL）
     */
    public static void setupLinearLayout(RecyclerView recyclerView, int orientation) {
        if (recyclerView == null) {
            return;
        }
        androidx.recyclerview.widget.LinearLayoutManager layoutManager = 
                new androidx.recyclerview.widget.LinearLayoutManager(recyclerView.getContext(), orientation, false);
        recyclerView.setLayoutManager(layoutManager);
    }

    /**
     * 设置RecyclerView的网格布局管理器
     * @param recyclerView RecyclerView
     * @param spanCount 列数
     */
    public static void setupGridLayout(RecyclerView recyclerView, int spanCount) {
        if (recyclerView == null) {
            return;
        }
        androidx.recyclerview.widget.GridLayoutManager layoutManager = 
                new androidx.recyclerview.widget.GridLayoutManager(recyclerView.getContext(), spanCount);
        recyclerView.setLayoutManager(layoutManager);
    }

    /**
     * 添加RecyclerView的分割线
     * @param recyclerView RecyclerView
     * @param colorResId 分割线颜色资源ID
     * @param size 分割线大小
     */
    public static void addItemDecoration(RecyclerView recyclerView, @ColorRes int colorResId, int size) {
        if (recyclerView == null) {
            return;
        }
        int color = getColor(recyclerView.getContext(), colorResId);
        androidx.recyclerview.widget.DividerItemDecoration decoration = 
                new androidx.recyclerview.widget.DividerItemDecoration(recyclerView.getContext(), androidx.recyclerview.widget.DividerItemDecoration.VERTICAL);
        decoration.setDrawable(new android.graphics.drawable.ColorDrawable(color));
        recyclerView.addItemDecoration(decoration);
    }

    /**
     * 设置视图的点击效果
     * @param view 视图
     * @param rippleColorResId 涟漪效果颜色资源ID
     */
    public static void setRippleEffect(View view, @ColorRes int rippleColorResId) {
        if (view == null) {
            return;
        }
        int rippleColor = getColor(view.getContext(), rippleColorResId);
        android.graphics.drawable.RippleDrawable rippleDrawable = new android.graphics.drawable.RippleDrawable(
                android.content.res.ColorStateList.valueOf(rippleColor),
                view.getBackground(),
                null
        );
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            view.setBackground(rippleDrawable);
        } else {
            view.setBackgroundDrawable(rippleDrawable);
        }
    }

    /**
     * 设置视图的阴影效果
     * @param view 视图
     * @param elevation 阴影高度
     */
    public static void setElevation(View view, float elevation) {
        if (view == null || Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
            return;
        }
        view.setElevation(elevation);
    }

    /**
     * 设置视图的圆角
     * @param view 视图
     * @param radius 圆角半径
     */
    public static void setCornerRadius(View view, float radius) {
        if (view == null) {
            return;
        }
        android.graphics.drawable.GradientDrawable shape = new android.graphics.drawable.GradientDrawable();
        shape.setCornerRadius(radius);
        shape.setColor(getColor(view.getContext(), android.R.color.white));
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            view.setBackground(shape);
        } else {
            view.setBackgroundDrawable(shape);
        }
    }

    /**
     * 设置视图的边框
     * @param view 视图
     * @param strokeWidth 边框宽度
     * @param strokeColorResId 边框颜色资源ID
     */
    public static void setBorder(View view, int strokeWidth, @ColorRes int strokeColorResId) {
        if (view == null) {
            return;
        }
        android.graphics.drawable.GradientDrawable shape = new android.graphics.drawable.GradientDrawable();
        shape.setStroke(strokeWidth, getColor(view.getContext(), strokeColorResId));
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            view.setBackground(shape);
        } else {
            view.setBackgroundDrawable(shape);
        }
    }

    /**
     * 检查设备是否支持特定功能
     * @param feature 功能名称
     * @return 是否支持
     */
    public static boolean hasFeature(Context context, String feature) {
        if (context == null) {
            return false;
        }
        return context.getPackageManager().hasSystemFeature(feature);
    }

    /**
     * 获取屏幕密度
     * @param context 上下文
     * @return 屏幕密度
     */
    public static float getScreenDensity(Context context) {
        if (context == null) {
            return 1.0f;
        }
        return context.getResources().getDisplayMetrics().density;
    }

    /**
     * 将dp转换为px
     * @param context 上下文
     * @param dp dp值
     * @return px值
     */
    public static int dpToPx(Context context, float dp) {
        if (context == null) {
            return (int) dp;
        }
        return (int) (dp * getScreenDensity(context) + 0.5f);
    }

    /**
     * 将px转换为dp
     * @param context 上下文
     * @param px px值
     * @return dp值
     */
    public static float pxToDp(Context context, int px) {
        if (context == null) {
            return px;
        }
        return px / getScreenDensity(context);
    }
} 