<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:paddingHorizontal="16dp">

    <ImageView
        android:id="@+id/item_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="12dp"
        android:contentDescription="图标"
        android:visibility="gone" />

    <TextView
        android:id="@+id/item_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toEndOf="@id/item_icon"
        android:layout_centerVertical="true"
        android:text="名称"
        android:textColor="@color/labelPrimary"
        android:textSize="17sp"
        android:visibility="gone" />

    <TextView
        android:id="@+id/label_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:text="Label"
        android:textColor="@color/labelPrimary"
        android:textSize="17sp" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/value_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:text="请选择"
            android:textColor="@color/labelSecondary"
            android:textSize="17sp" />

        <ImageView
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:src="@drawable/ic_chevron_right"
            android:tint="@color/separator" />
    </LinearLayout>

    <ImageView
        android:id="@+id/item_checkmark"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:src="@drawable/ic_check_circle"
        android:tint="@color/labelSecondary"
        android:visibility="gone" />

</RelativeLayout> 