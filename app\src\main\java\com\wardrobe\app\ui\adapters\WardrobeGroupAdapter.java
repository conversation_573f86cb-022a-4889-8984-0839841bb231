package com.wardrobe.app.ui.adapters;

import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.wardrobe.app.R;
import com.wardrobe.app.model.ClothingItem;
import com.wardrobe.app.ui.activities.ClothingDetailActivity;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 衣橱分组适配器
 * 实现iOS风格的分组展示，每组内为横向滑动的衣物卡片
 */
public class WardrobeGroupAdapter extends RecyclerView.Adapter<WardrobeGroupAdapter.GroupViewHolder> {
    
    private Map<String, List<ClothingItem>> groupedData = new java.util.HashMap<>();
    private List<String> categoryOrder = new ArrayList<>();

    public void updateData(Map<String, List<ClothingItem>> newData) {
        this.groupedData = newData;
        this.categoryOrder.clear();
        
        // 定义分类显示顺序
        String[] order = {"上衣", "下装", "外套", "鞋子", "配饰", "套装", "内衣", "运动", "其他"};
        
        for (String category : order) {
            if (newData.containsKey(category) && !newData.get(category).isEmpty()) {
                categoryOrder.add(category);
            }
        }
        
        // 添加其他未在预定义顺序中的分类
        for (String category : newData.keySet()) {
            if (!categoryOrder.contains(category) && !newData.get(category).isEmpty()) {
                categoryOrder.add(category);
            }
        }
        
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public GroupViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_wardrobe_group, parent, false);
        return new GroupViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull GroupViewHolder holder, int position) {
        String category = categoryOrder.get(position);
        List<ClothingItem> items = groupedData.get(category);
        
        holder.bind(category, items);
    }

    @Override
    public int getItemCount() {
        return categoryOrder.size();
    }

    static class GroupViewHolder extends RecyclerView.ViewHolder {
        private TextView tvCategoryTitle;
        private TextView tvItemCount;
        private RecyclerView rvClothingItems;
        private ClothingListAdapter clothingAdapter;

        public GroupViewHolder(@NonNull View itemView) {
            super(itemView);
            tvCategoryTitle = itemView.findViewById(R.id.tv_category_title);
            tvItemCount = itemView.findViewById(R.id.tv_item_count);
            rvClothingItems = itemView.findViewById(R.id.rv_clothing_items);
            
            // 设置横向滑动
            rvClothingItems.setLayoutManager(
                new LinearLayoutManager(itemView.getContext(), LinearLayoutManager.HORIZONTAL, false)
            );
            
            clothingAdapter = new ClothingListAdapter();
            rvClothingItems.setAdapter(clothingAdapter);
        }

        public void bind(String category, List<ClothingItem> items) {
            tvCategoryTitle.setText(category);
            tvItemCount.setText(items.size() + " 件");
            
            // 更新衣物列表
            clothingAdapter.updateData(items);
            
            // 设置点击事件
            clothingAdapter.setOnItemClickListener(item -> {
                Intent intent = new Intent(itemView.getContext(), ClothingDetailActivity.class);
                intent.putExtra("clothing_item", item);
                itemView.getContext().startActivity(intent);
            });
        }
    }
} 