<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_add_clothing" modulePackage="com.wardrobe.app" filePath="app\src\main\res\layout\activity_add_clothing.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_add_clothing_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="176" endOffset="14"/></Target><Target id="@+id/image_container" view="FrameLayout"><Expressions/><location startLine="48" startOffset="20" endLine="87" endOffset="33"/></Target><Target id="@+id/image_placeholder" view="FrameLayout"><Expressions/><location startLine="54" startOffset="24" endLine="67" endOffset="37"/></Target><Target id="@+id/clothing_image_view" view="ImageView"><Expressions/><location startLine="69" startOffset="24" endLine="74" endOffset="55"/></Target><Target id="@+id/button_replace_photo" view="ImageView"><Expressions/><location startLine="76" startOffset="24" endLine="86" endOffset="53"/></Target><Target id="@+id/edit_text_name" view="EditText"><Expressions/><location startLine="89" startOffset="20" endLine="96" endOffset="49"/></Target><Target id="@+id/layout_category" view="RelativeLayout"><Expressions/><location startLine="103" startOffset="16" endLine="114" endOffset="32"/></Target><Target id="@+id/text_view_category_value" view="TextView"><Expressions/><location startLine="109" startOffset="20" endLine="112" endOffset="69"/></Target><Target id="@+id/layout_color" view="RelativeLayout"><Expressions/><location startLine="134" startOffset="16" endLine="145" endOffset="32"/></Target><Target id="@+id/text_view_color_value" view="TextView"><Expressions/><location startLine="140" startOffset="20" endLine="143" endOffset="66"/></Target><Target id="@+id/layout_occasion" view="RelativeLayout"><Expressions/><location startLine="150" startOffset="16" endLine="161" endOffset="32"/></Target><Target id="@+id/text_view_occasion_value" view="TextView"><Expressions/><location startLine="156" startOffset="20" endLine="159" endOffset="69"/></Target><Target id="@+id/button_save" view="Button"><Expressions/><location startLine="168" startOffset="4" endLine="174" endOffset="44"/></Target></Targets></Layout>