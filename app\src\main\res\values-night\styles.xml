<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 深色模式主题 -->
    <style name="AppTheme" parent="Theme.Material3.DayNight">
        <!-- 主色调 -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryVariant">@color/colorPrimaryVariant</item>
        <item name="colorOnPrimary">@color/colorOnPrimary</item>
        
        <!-- 次要色调 -->
        <item name="colorSecondary">@color/colorSecondary</item>
        <item name="colorSecondaryVariant">@color/colorSecondaryVariant</item>
        <item name="colorOnSecondary">@color/colorOnSecondary</item>
        
        <!-- 背景色 -->
        <item name="android:colorBackground">@color/colorBackground</item>
        <item name="colorOnBackground">@color/colorOnBackground</item>
        
        <!-- 表面色 -->
        <item name="colorSurface">@color/colorSurface</item>
        <item name="colorOnSurface">@color/colorOnSurface</item>
        
        <!-- 错误色 -->
        <item name="colorError">@color/colorError</item>
        <item name="colorOnError">@color/colorOnError</item>
        
        <!-- 状态栏 -->
        <item name="android:statusBarColor">@color/statusBarColor</item>
        <item name="android:windowLightStatusBar">false</item>
        
        <!-- 导航栏 -->
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:windowLightNavigationBar">false</item>
        
        <!-- 文本颜色 -->
        <item name="android:textColorPrimary">@color/textColorPrimary</item>
        <item name="android:textColorSecondary">@color/textColorSecondary</item>
        <item name="android:textColorHint">@color/textColorHint</item>
        
        <!-- 强制深色模式 -->
        <item name="android:forceDarkAllowed">true</item>
    </style>
    
    <!-- 深色模式按钮样式 -->
    <style name="ButtonStyle" parent="Widget.Material3.Button">
        <item name="android:background">@color/buttonBackground</item>
        <item name="android:textColor">@color/buttonTextColor</item>
        <item name="backgroundTint">@color/buttonBackground</item>
    </style>
    
    <!-- 深色模式卡片样式 -->
    <style name="CardStyle" parent="Widget.Material3.CardView.Elevated">
        <item name="cardBackgroundColor">@color/cardBackground</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style>
</resources>
