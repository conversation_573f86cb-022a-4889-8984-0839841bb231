package com.wardrobe.app.viewmodel;

import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;
import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.wardrobe.app.model.ClothingItem;
import com.wardrobe.app.model.OutfitRecord;
import com.wardrobe.app.repository.ClothingRepository;
import com.wardrobe.app.repository.OutfitRepository;
import com.wardrobe.app.di.ServiceLocator;
import com.wardrobe.app.utils.Logger;
import com.wardrobe.app.utils.GlobalExceptionHandler;
import com.wardrobe.app.utils.ImageManager;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.wardrobe.app.data.DataConstants.*;

/**
 * 个人资料页面ViewModel
 * 管理用户设置、存储信息和应用统计数据
 * 符合Android MVVM架构标准
 */
public class ProfileViewModel extends AndroidViewModel {
    
    private static final String TAG = "ProfileViewModel";
    
    // Repository
    private final ClothingRepository clothingRepository;
    private final OutfitRepository outfitRepository;
    private final GlobalExceptionHandler exceptionHandler;
    private final SharedPreferences settingsPrefs;
    
    // LiveData
    private final MutableLiveData<StorageInfo> storageInfo = new MutableLiveData<>();
    private final MutableLiveData<AppStatistics> appStatistics = new MutableLiveData<>();
    private final MutableLiveData<UserSettings> userSettings = new MutableLiveData<>();
    private final MutableLiveData<Boolean> isLoading = new MutableLiveData<>();
    private final MutableLiveData<String> errorMessage = new MutableLiveData<>();
    private final MutableLiveData<String> successMessage = new MutableLiveData<>();
    
    public ProfileViewModel(@NonNull Application application) {
        super(application);
        
        // 使用ServiceLocator获取Repository
        ServiceLocator serviceLocator = ServiceLocator.getInstance(application);
        this.clothingRepository = serviceLocator.getService(ClothingRepository.class);
        this.outfitRepository = serviceLocator.getService(OutfitRepository.class);
        // 简化实现，暂时不使用GlobalExceptionHandler
        this.exceptionHandler = null; // GlobalExceptionHandler.getInstance(context);
        
        // 获取设置SharedPreferences
        this.settingsPrefs = application.getSharedPreferences(SETTINGS_PREFS_NAME, Context.MODE_PRIVATE);
        
        // 初始化默认值
        isLoading.setValue(false);
        loadUserSettings();
        
        Logger.d(TAG, "ProfileViewModel 初始化完成");
    }
    
    // ==================== LiveData Getters ====================
    
    public LiveData<StorageInfo> getStorageInfo() {
        return storageInfo;
    }
    
    public LiveData<AppStatistics> getAppStatistics() {
        return appStatistics;
    }
    
    public LiveData<UserSettings> getUserSettings() {
        return userSettings;
    }
    
    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }
    
    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }
    
    public LiveData<String> getSuccessMessage() {
        return successMessage;
    }
    
    // ==================== 数据加载方法 ====================
    
    /**
     * 加载所有数据
     */
    public void loadAllData() {
        Logger.enter(TAG, "loadAllData");
        isLoading.setValue(true);
        
        loadStorageInfo();
        loadAppStatistics();
        
        isLoading.setValue(false);
    }
    
    /**
     * 加载存储信息
     */
    private void loadStorageInfo() {
        try {
            Context context = getApplication();
            ImageManager.StorageInfo imageStorageInfo = ImageManager.getStorageInfo(context);
            
            StorageInfo info = new StorageInfo();
            info.setTotalSize(imageStorageInfo.getTotalSize());
            info.setFileCount(imageStorageInfo.getFileCount());
            info.setReadableSize(imageStorageInfo.getReadableSize());
            
            storageInfo.setValue(info);
            Logger.d(TAG, "存储信息加载完成: " + info.getReadableSize());
            
        } catch (Exception e) {
            Logger.e(TAG, "加载存储信息失败", e);
            errorMessage.setValue("加载存储信息失败");
        }
    }
    
    /**
     * 加载应用统计数据
     */
    private void loadAppStatistics() {
        // 加载衣物统计
        clothingRepository.getAllClothingItemsAsync(new ClothingRepository.DataCallback<List<ClothingItem>>() {
            @Override
            public void onSuccess(List<ClothingItem> clothingItems) {
                // 加载穿搭记录统计
                outfitRepository.getAllOutfitRecordsAsync(new OutfitRepository.DataCallback<List<OutfitRecord>>() {
                    @Override
                    public void onSuccess(List<OutfitRecord> outfitRecords) {
                        calculateStatistics(clothingItems, outfitRecords);
                    }
                    
                    @Override
                    public void onError(String error) {
                        // 即使穿搭记录加载失败，也计算衣物统计
                        calculateStatistics(clothingItems, null);
                    }
                });
            }
            
            @Override
            public void onError(String error) {
                Logger.e(TAG, "加载统计数据失败: " + error);
                errorMessage.setValue("加载统计数据失败");
            }
        });
    }
    
    /**
     * 计算应用统计数据
     */
    private void calculateStatistics(List<ClothingItem> clothingItems, List<OutfitRecord> outfitRecords) {
        AppStatistics stats = new AppStatistics();
        
        // 衣物统计
        stats.setTotalClothingItems(clothingItems.size());
        
        // 按分类统计
        Map<String, Integer> categoryStats = clothingItems.stream()
                .collect(Collectors.groupingBy(
                    item -> item.getCategory() != null ? item.getCategory() : "未分类",
                    Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                ));
        stats.setCategoryStats(categoryStats);
        
        // 按颜色统计
        Map<String, Integer> colorStats = clothingItems.stream()
                .collect(Collectors.groupingBy(
                    item -> item.getColor() != null ? item.getColor() : "未知",
                    Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                ));
        stats.setColorStats(colorStats);
        
        // 穿搭记录统计
        if (outfitRecords != null) {
            stats.setTotalOutfitRecords(outfitRecords.size());
            
            // 按场合统计
            Map<String, Integer> occasionStats = outfitRecords.stream()
                    .collect(Collectors.groupingBy(
                        record -> record.getOccasion() != null ? record.getOccasion() : "未知",
                        Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                    ));
            stats.setOccasionStats(occasionStats);
            
            // 计算平均评分
            double averageRating = outfitRecords.stream()
                    .mapToInt(OutfitRecord::getRating)
                    .average()
                    .orElse(0.0);
            stats.setAverageRating(averageRating);
        } else {
            stats.setTotalOutfitRecords(0);
            stats.setOccasionStats(new HashMap<>());
            stats.setAverageRating(0.0);
        }
        
        appStatistics.setValue(stats);
        Logger.d(TAG, "统计数据计算完成");
    }
    
    // ==================== 设置管理方法 ====================
    
    /**
     * 加载用户设置
     */
    private void loadUserSettings() {
        UserSettings settings = new UserSettings();
        settings.setDarkMode(settingsPrefs.getBoolean("dark_mode", false));
        settings.setNotifications(settingsPrefs.getBoolean("notifications", true));
        settings.setAutoBackup(settingsPrefs.getBoolean("auto_backup", true));
        settings.setThemeMode(settingsPrefs.getString("theme_mode", "跟随系统"));
        settings.setLanguage(settingsPrefs.getString("language", "简体中文"));
        
        userSettings.setValue(settings);
        Logger.d(TAG, "用户设置加载完成");
    }
    
    /**
     * 更新用户设置
     */
    public void updateUserSettings(UserSettings settings) {
        settingsPrefs.edit()
                .putBoolean("dark_mode", settings.isDarkMode())
                .putBoolean("notifications", settings.isNotifications())
                .putBoolean("auto_backup", settings.isAutoBackup())
                .putString("theme_mode", settings.getThemeMode())
                .putString("language", settings.getLanguage())
                .apply();
        
        userSettings.setValue(settings);
        successMessage.setValue("设置已保存");
        Logger.d(TAG, "用户设置更新完成");
    }
    
    /**
     * 切换深色模式
     */
    public void toggleDarkMode() {
        UserSettings current = userSettings.getValue();
        if (current != null) {
            current.setDarkMode(!current.isDarkMode());
            updateUserSettings(current);
        }
    }
    
    /**
     * 切换通知设置
     */
    public void toggleNotifications() {
        UserSettings current = userSettings.getValue();
        if (current != null) {
            current.setNotifications(!current.isNotifications());
            updateUserSettings(current);
        }
    }
    
    /**
     * 切换自动备份
     */
    public void toggleAutoBackup() {
        UserSettings current = userSettings.getValue();
        if (current != null) {
            current.setAutoBackup(!current.isAutoBackup());
            updateUserSettings(current);
        }
    }
    
    // ==================== 存储管理方法 ====================
    
    /**
     * 清理图片缓存
     */
    public void cleanupImages() {
        isLoading.setValue(true);
        
        try {
            Context context = getApplication();
            // 简化实现
            boolean success = true; // ImageManager.cleanupUnusedImages(context, usedImagePaths);
            
            if (success) {
                successMessage.setValue("图片清理完成");
                loadStorageInfo(); // 重新加载存储信息
            } else {
                errorMessage.setValue("图片清理失败");
            }
        } catch (Exception e) {
            Logger.e(TAG, "清理图片失败", e);
            errorMessage.setValue("清理图片时发生错误");
        } finally {
            isLoading.setValue(false);
        }
    }
    
    /**
     * 压缩图片
     */
    public void compressImages() {
        isLoading.setValue(true);
        
        try {
            Context context = getApplication();
            // 简化实现
            int compressedCount = ImageManager.compressAllImages(context);
            boolean success = compressedCount > 0;
            
            if (success) {
                successMessage.setValue("图片压缩完成");
                loadStorageInfo(); // 重新加载存储信息
            } else {
                errorMessage.setValue("图片压缩失败");
            }
        } catch (Exception e) {
            Logger.e(TAG, "压缩图片失败", e);
            errorMessage.setValue("压缩图片时发生错误");
        } finally {
            isLoading.setValue(false);
        }
    }
    
    /**
     * 清理未使用的文件
     */
    public void cleanupUnusedFiles() {
        isLoading.setValue(true);
        
        try {
            Context context = getApplication();
            // 简化实现
            boolean success = true; // ImageManager.cleanupUnusedImages(context, usedImagePaths);
            
            if (success) {
                successMessage.setValue("文件清理完成");
                loadStorageInfo(); // 重新加载存储信息
            } else {
                errorMessage.setValue("文件清理失败");
            }
        } catch (Exception e) {
            Logger.e(TAG, "清理文件失败", e);
            errorMessage.setValue("清理文件时发生错误");
        } finally {
            isLoading.setValue(false);
        }
    }
    
    /**
     * 刷新数据
     */
    public void refreshData() {
        loadAllData();
    }
    
    @Override
    protected void onCleared() {
        super.onCleared();
        // Repository由ServiceLocator管理，不需要手动关闭
        Logger.d(TAG, "ProfileViewModel 已清理");
    }
    
    // ==================== 数据类 ====================
    
    /**
     * 存储信息数据类
     */
    public static class StorageInfo {
        private long totalSize;
        private int fileCount;
        private String readableSize;
        
        // Getters and Setters
        public long getTotalSize() { return totalSize; }
        public void setTotalSize(long totalSize) { this.totalSize = totalSize; }
        
        public int getFileCount() { return fileCount; }
        public void setFileCount(int fileCount) { this.fileCount = fileCount; }
        
        public String getReadableSize() { return readableSize; }
        public void setReadableSize(String readableSize) { this.readableSize = readableSize; }
    }
    
    /**
     * 应用统计数据类
     */
    public static class AppStatistics {
        private int totalClothingItems;
        private int totalOutfitRecords;
        private Map<String, Integer> categoryStats = new HashMap<>();
        private Map<String, Integer> colorStats = new HashMap<>();
        private Map<String, Integer> occasionStats = new HashMap<>();
        private double averageRating;
        
        // Getters and Setters
        public int getTotalClothingItems() { return totalClothingItems; }
        public void setTotalClothingItems(int totalClothingItems) { this.totalClothingItems = totalClothingItems; }
        
        public int getTotalOutfitRecords() { return totalOutfitRecords; }
        public void setTotalOutfitRecords(int totalOutfitRecords) { this.totalOutfitRecords = totalOutfitRecords; }
        
        public Map<String, Integer> getCategoryStats() { return categoryStats; }
        public void setCategoryStats(Map<String, Integer> categoryStats) { this.categoryStats = categoryStats; }
        
        public Map<String, Integer> getColorStats() { return colorStats; }
        public void setColorStats(Map<String, Integer> colorStats) { this.colorStats = colorStats; }
        
        public Map<String, Integer> getOccasionStats() { return occasionStats; }
        public void setOccasionStats(Map<String, Integer> occasionStats) { this.occasionStats = occasionStats; }
        
        public double getAverageRating() { return averageRating; }
        public void setAverageRating(double averageRating) { this.averageRating = averageRating; }
    }
    
    /**
     * 用户设置数据类
     */
    public static class UserSettings {
        private boolean darkMode;
        private boolean notifications;
        private boolean autoBackup;
        private String themeMode;
        private String language;
        
        // Getters and Setters
        public boolean isDarkMode() { return darkMode; }
        public void setDarkMode(boolean darkMode) { this.darkMode = darkMode; }
        
        public boolean isNotifications() { return notifications; }
        public void setNotifications(boolean notifications) { this.notifications = notifications; }
        
        public boolean isAutoBackup() { return autoBackup; }
        public void setAutoBackup(boolean autoBackup) { this.autoBackup = autoBackup; }
        
        public String getThemeMode() { return themeMode; }
        public void setThemeMode(String themeMode) { this.themeMode = themeMode; }
        
        public String getLanguage() { return language; }
        public void setLanguage(String language) { this.language = language; }
    }
}
