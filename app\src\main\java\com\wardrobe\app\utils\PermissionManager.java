package com.wardrobe.app.utils;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 权限管理器
 * 提供统一的权限请求、检查和管理功能
 * 支持动态权限请求和权限状态跟踪
 */
public class PermissionManager {
    
    private static final String TAG = "PermissionManager";
    
    // 常用权限常量
    public static final String CAMERA = android.Manifest.permission.CAMERA;
    public static final String WRITE_EXTERNAL_STORAGE = android.Manifest.permission.WRITE_EXTERNAL_STORAGE;
    public static final String READ_EXTERNAL_STORAGE = android.Manifest.permission.READ_EXTERNAL_STORAGE;
    
    // 权限请求码
    public static final int REQUEST_CODE_CAMERA = 1001;
    public static final int REQUEST_CODE_STORAGE = 1002;
    public static final int REQUEST_CODE_MULTIPLE = 1003;
    
    private final Context context;
    private final Map<String, PermissionCallback> pendingCallbacks = new HashMap<>();
    
    /**
     * 权限回调接口
     */
    public interface PermissionCallback {
        void onPermissionGranted(String permission);
        void onPermissionDenied(String permission, boolean shouldShowRationale);
        void onPermissionPermanentlyDenied(String permission);
    }
    
    /**
     * 多权限回调接口
     */
    public interface MultiplePermissionCallback {
        void onPermissionsResult(Map<String, Boolean> permissions);
        void onAllPermissionsGranted();
        void onSomePermissionsDenied(List<String> deniedPermissions);
    }
    
    public PermissionManager(Context context) {
        this.context = context.getApplicationContext();
        Logger.d(TAG, "PermissionManager 初始化完成");
    }
    
    /**
     * 检查单个权限是否已授予
     * 
     * @param permission 权限名称
     * @return 是否已授予
     */
    public boolean isPermissionGranted(String permission) {
        boolean granted = ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED;
        Logger.d(TAG, "权限检查: " + permission + " = " + granted);
        return granted;
    }
    
    /**
     * 检查多个权限是否都已授予
     * 
     * @param permissions 权限数组
     * @return 是否都已授予
     */
    public boolean arePermissionsGranted(String... permissions) {
        for (String permission : permissions) {
            if (!isPermissionGranted(permission)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 获取未授予的权限列表
     * 
     * @param permissions 权限数组
     * @return 未授予的权限列表
     */
    public List<String> getDeniedPermissions(String... permissions) {
        List<String> deniedPermissions = new ArrayList<>();
        for (String permission : permissions) {
            if (!isPermissionGranted(permission)) {
                deniedPermissions.add(permission);
            }
        }
        return deniedPermissions;
    }
    
    /**
     * 请求单个权限
     * 
     * @param activity 活动
     * @param permission 权限名称
     * @param requestCode 请求码
     * @param callback 回调
     */
    public void requestPermission(Activity activity, String permission, int requestCode, PermissionCallback callback) {
        if (isPermissionGranted(permission)) {
            callback.onPermissionGranted(permission);
            return;
        }
        
        pendingCallbacks.put(permission, callback);
        
        if (shouldShowRequestPermissionRationale(activity, permission)) {
            Logger.d(TAG, "需要显示权限说明: " + permission);
            callback.onPermissionDenied(permission, true);
        } else {
            Logger.d(TAG, "请求权限: " + permission);
            ActivityCompat.requestPermissions(activity, new String[]{permission}, requestCode);
        }
    }
    
    /**
     * 请求多个权限
     * 
     * @param activity 活动
     * @param permissions 权限数组
     * @param requestCode 请求码
     * @param callback 回调
     */
    public void requestPermissions(Activity activity, String[] permissions, int requestCode, MultiplePermissionCallback callback) {
        List<String> deniedPermissions = getDeniedPermissions(permissions);
        
        if (deniedPermissions.isEmpty()) {
            callback.onAllPermissionsGranted();
            return;
        }
        
        Logger.d(TAG, "请求多个权限: " + deniedPermissions.size() + " 个");
        ActivityCompat.requestPermissions(activity, deniedPermissions.toArray(new String[0]), requestCode);
    }
    
    /**
     * 处理权限请求结果
     * 
     * @param activity 活动
     * @param requestCode 请求码
     * @param permissions 权限数组
     * @param grantResults 授权结果
     */
    public void onRequestPermissionsResult(Activity activity, int requestCode, String[] permissions, int[] grantResults) {
        Logger.d(TAG, "权限请求结果: requestCode=" + requestCode + ", permissions=" + permissions.length);
        
        for (int i = 0; i < permissions.length; i++) {
            String permission = permissions[i];
            boolean granted = grantResults[i] == PackageManager.PERMISSION_GRANTED;
            
            PermissionCallback callback = pendingCallbacks.remove(permission);
            if (callback != null) {
                if (granted) {
                    callback.onPermissionGranted(permission);
                } else {
                    boolean shouldShowRationale = shouldShowRequestPermissionRationale(activity, permission);
                    if (shouldShowRationale) {
                        callback.onPermissionDenied(permission, true);
                    } else {
                        callback.onPermissionPermanentlyDenied(permission);
                    }
                }
            }
        }
    }
    
    /**
     * 检查是否应该显示权限说明
     * 
     * @param activity 活动
     * @param permission 权限名称
     * @return 是否应该显示说明
     */
    public boolean shouldShowRequestPermissionRationale(Activity activity, String permission) {
        return ActivityCompat.shouldShowRequestPermissionRationale(activity, permission);
    }
    
    /**
     * 打开应用设置页面
     * 
     * @param activity 活动
     */
    public void openAppSettings(Activity activity) {
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", activity.getPackageName(), null);
        intent.setData(uri);
        activity.startActivity(intent);
        Logger.d(TAG, "打开应用设置页面");
    }
    
    /**
     * 检查相机权限
     * 
     * @return 是否有相机权限
     */
    public boolean hasCameraPermission() {
        return isPermissionGranted(CAMERA);
    }
    
    /**
     * 检查存储权限
     * 
     * @return 是否有存储权限
     */
    public boolean hasStoragePermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 不需要存储权限
            return true;
        } else {
            return isPermissionGranted(WRITE_EXTERNAL_STORAGE) && isPermissionGranted(READ_EXTERNAL_STORAGE);
        }
    }
    
    /**
     * 请求相机权限
     * 
     * @param activity 活动
     * @param callback 回调
     */
    public void requestCameraPermission(Activity activity, PermissionCallback callback) {
        requestPermission(activity, CAMERA, REQUEST_CODE_CAMERA, callback);
    }
    
    /**
     * 请求存储权限
     * 
     * @param activity 活动
     * @param callback 回调
     */
    public void requestStoragePermission(Activity activity, PermissionCallback callback) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 不需要存储权限
            callback.onPermissionGranted(WRITE_EXTERNAL_STORAGE);
            return;
        }
        
        String[] permissions = {WRITE_EXTERNAL_STORAGE, READ_EXTERNAL_STORAGE};
        requestPermissions(activity, permissions, REQUEST_CODE_STORAGE, new MultiplePermissionCallback() {
            @Override
            public void onPermissionsResult(Map<String, Boolean> permissions) {
                boolean allGranted = true;
                for (Boolean granted : permissions.values()) {
                    if (!granted) {
                        allGranted = false;
                        break;
                    }
                }
                
                if (allGranted) {
                    callback.onPermissionGranted(WRITE_EXTERNAL_STORAGE);
                } else {
                    callback.onPermissionDenied(WRITE_EXTERNAL_STORAGE, false);
                }
            }
            
            @Override
            public void onAllPermissionsGranted() {
                callback.onPermissionGranted(WRITE_EXTERNAL_STORAGE);
            }
            
            @Override
            public void onSomePermissionsDenied(List<String> deniedPermissions) {
                callback.onPermissionDenied(WRITE_EXTERNAL_STORAGE, false);
            }
        });
    }
    
    /**
     * 请求相机和存储权限
     * 
     * @param activity 活动
     * @param callback 回调
     */
    public void requestCameraAndStoragePermissions(Activity activity, MultiplePermissionCallback callback) {
        List<String> permissionsToRequest = new ArrayList<>();
        
        if (!hasCameraPermission()) {
            permissionsToRequest.add(CAMERA);
        }
        
        if (!hasStoragePermission() && Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
            permissionsToRequest.add(WRITE_EXTERNAL_STORAGE);
            permissionsToRequest.add(READ_EXTERNAL_STORAGE);
        }
        
        if (permissionsToRequest.isEmpty()) {
            callback.onAllPermissionsGranted();
        } else {
            requestPermissions(activity, permissionsToRequest.toArray(new String[0]), REQUEST_CODE_MULTIPLE, callback);
        }
    }
    
    /**
     * 获取权限状态描述
     * 
     * @param permission 权限名称
     * @return 状态描述
     */
    public String getPermissionStatusDescription(String permission) {
        if (isPermissionGranted(permission)) {
            return "已授予";
        } else {
            return "未授予";
        }
    }
    
    /**
     * 清理待处理的回调
     */
    public void clearPendingCallbacks() {
        pendingCallbacks.clear();
        Logger.d(TAG, "清理待处理的权限回调");
    }
    
    /**
     * 获取所有需要的权限列表
     * 
     * @return 权限列表
     */
    public String[] getAllRequiredPermissions() {
        List<String> permissions = new ArrayList<>();
        permissions.add(CAMERA);
        
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
            permissions.add(WRITE_EXTERNAL_STORAGE);
            permissions.add(READ_EXTERNAL_STORAGE);
        }
        
        return permissions.toArray(new String[0]);
    }
    
    /**
     * 检查所有必需权限是否都已授予
     * 
     * @return 是否都已授予
     */
    public boolean areAllRequiredPermissionsGranted() {
        return arePermissionsGranted(getAllRequiredPermissions());
    }
}
