package com.wardrobe.app.viewmodel;

import android.app.Application;
import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.wardrobe.app.model.ClothingItem;
import com.wardrobe.app.model.OutfitRecord;
import com.wardrobe.app.repository.ClothingRepository;
import com.wardrobe.app.repository.OutfitRepository;
import com.wardrobe.app.di.ServiceLocator;
import com.wardrobe.app.utils.Logger;
import com.wardrobe.app.utils.GlobalExceptionHandler;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * 搭配页面ViewModel
 * 管理搭配推荐和搭配记录的业务逻辑
 * 符合Android MVVM架构标准
 */
public class OutfitViewModel extends AndroidViewModel {
    
    private static final String TAG = "OutfitViewModel";
    
    // Repository
    private final ClothingRepository clothingRepository;
    private final OutfitRepository outfitRepository;
    private final GlobalExceptionHandler exceptionHandler;
    
    // LiveData
    private final MutableLiveData<List<OutfitRecommendation>> recommendations = new MutableLiveData<>();
    private final MutableLiveData<List<ClothingItem>> availableClothing = new MutableLiveData<>();
    private final MutableLiveData<OutfitRecommendation> selectedRecommendation = new MutableLiveData<>();
    private final MutableLiveData<Boolean> isLoading = new MutableLiveData<>();
    private final MutableLiveData<String> errorMessage = new MutableLiveData<>();
    private final MutableLiveData<String> successMessage = new MutableLiveData<>();
    private final MutableLiveData<Map<String, Integer>> outfitStats = new MutableLiveData<>();
    
    // 内部数据
    private List<ClothingItem> allClothingItems = new ArrayList<>();
    private List<OutfitRecord> recentOutfits = new ArrayList<>();
    private final Random random = new Random();
    
    public OutfitViewModel(@NonNull Application application) {
        super(application);
        
        // 使用ServiceLocator获取Repository
        ServiceLocator serviceLocator = ServiceLocator.getInstance(application);
        this.clothingRepository = serviceLocator.getService(ClothingRepository.class);
        this.outfitRepository = serviceLocator.getService(OutfitRepository.class);
        this.exceptionHandler = GlobalExceptionHandler.getInstance();
        
        // 初始化默认值
        isLoading.setValue(false);
        
        Logger.d(TAG, "OutfitViewModel 初始化完成");
    }
    
    // ==================== LiveData Getters ====================
    
    public LiveData<List<OutfitRecommendation>> getRecommendations() {
        return recommendations;
    }
    
    public LiveData<List<ClothingItem>> getAvailableClothing() {
        return availableClothing;
    }
    
    public LiveData<OutfitRecommendation> getSelectedRecommendation() {
        return selectedRecommendation;
    }
    
    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }
    
    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }
    
    public LiveData<String> getSuccessMessage() {
        return successMessage;
    }
    
    public LiveData<Map<String, Integer>> getOutfitStats() {
        return outfitStats;
    }
    
    // ==================== 数据加载方法 ====================
    
    /**
     * 加载所有数据
     */
    public void loadAllData() {
        Logger.enter(TAG, "loadAllData");
        isLoading.setValue(true);
        
        // 先加载衣物数据
        loadClothingItems();
        
        // 然后加载最近的穿搭记录
        loadRecentOutfits();
    }
    
    /**
     * 加载衣物数据
     */
    private void loadClothingItems() {
        clothingRepository.getAllClothingItemsAsync(new ClothingRepository.DataCallback<List<ClothingItem>>() {
            @Override
            public void onSuccess(List<ClothingItem> items) {
                allClothingItems = items;
                availableClothing.setValue(items);
                
                // 衣物数据加载完成后生成推荐
                generateRecommendations();
                
                Logger.d(TAG, "加载了 " + items.size() + " 件衣物");
            }
            
            @Override
            public void onError(String error) {
                exceptionHandler.handleException("加载衣物数据", new Exception(error), false);
                errorMessage.setValue("加载衣物数据失败: " + error);
                isLoading.setValue(false);
            }
        });
    }
    
    /**
     * 加载最近的穿搭记录
     */
    private void loadRecentOutfits() {
        outfitRepository.getAllOutfitRecordsAsync(new OutfitRepository.DataCallback<List<OutfitRecord>>() {
            @Override
            public void onSuccess(List<OutfitRecord> records) {
                recentOutfits = records;
                
                // 计算统计数据
                calculateOutfitStats();
                
                isLoading.setValue(false);
                Logger.d(TAG, "加载了 " + records.size() + " 条穿搭记录");
            }
            
            @Override
            public void onError(String error) {
                Logger.w(TAG, "加载穿搭记录失败: " + error);
                // 穿搭记录加载失败不影响推荐功能
                recentOutfits = new ArrayList<>();
                isLoading.setValue(false);
            }
        });
    }
    
    // ==================== 搭配推荐方法 ====================
    
    /**
     * 生成搭配推荐
     */
    public void generateRecommendations() {
        if (allClothingItems.isEmpty()) {
            recommendations.setValue(new ArrayList<>());
            return;
        }
        
        List<OutfitRecommendation> newRecommendations = new ArrayList<>();
        
        // 按类别分组衣物
        Map<String, List<ClothingItem>> categoryGroups = groupClothingByCategory();
        
        // 生成不同类型的推荐
        newRecommendations.addAll(generateBasicRecommendations(categoryGroups));
        newRecommendations.addAll(generateSeasonalRecommendations(categoryGroups));
        newRecommendations.addAll(generateOccasionRecommendations(categoryGroups));
        
        // 打乱顺序并限制数量
        Collections.shuffle(newRecommendations, random);
        if (newRecommendations.size() > 10) {
            newRecommendations = newRecommendations.subList(0, 10);
        }
        
        recommendations.setValue(newRecommendations);
        Logger.d(TAG, "生成了 " + newRecommendations.size() + " 个搭配推荐");
    }
    
    /**
     * 生成基础搭配推荐
     */
    private List<OutfitRecommendation> generateBasicRecommendations(Map<String, List<ClothingItem>> categoryGroups) {
        List<OutfitRecommendation> basicRecommendations = new ArrayList<>();
        
        List<ClothingItem> tops = categoryGroups.getOrDefault("上衣", new ArrayList<>());
        List<ClothingItem> bottoms = categoryGroups.getOrDefault("下装", new ArrayList<>());
        List<ClothingItem> shoes = categoryGroups.getOrDefault("鞋子", new ArrayList<>());
        
        // 生成基础搭配（上衣+下装+鞋子）
        int maxCombinations = Math.min(5, Math.min(tops.size(), Math.min(bottoms.size(), shoes.size())));
        
        for (int i = 0; i < maxCombinations; i++) {
            OutfitRecommendation recommendation = new OutfitRecommendation();
            recommendation.setName("日常搭配 " + (i + 1));
            recommendation.setType("基础搭配");
            recommendation.setDescription("简约舒适的日常穿搭");
            
            List<ClothingItem> items = new ArrayList<>();
            if (!tops.isEmpty()) items.add(tops.get(random.nextInt(tops.size())));
            if (!bottoms.isEmpty()) items.add(bottoms.get(random.nextInt(bottoms.size())));
            if (!shoes.isEmpty()) items.add(shoes.get(random.nextInt(shoes.size())));
            
            recommendation.setClothingItems(items);
            recommendation.setConfidenceScore(calculateConfidenceScore(items));
            
            basicRecommendations.add(recommendation);
        }
        
        return basicRecommendations;
    }
    
    /**
     * 生成季节性推荐
     */
    private List<OutfitRecommendation> generateSeasonalRecommendations(Map<String, List<ClothingItem>> categoryGroups) {
        List<OutfitRecommendation> seasonalRecommendations = new ArrayList<>();
        
        // 根据当前季节生成推荐
        String currentSeason = getCurrentSeason();
        List<ClothingItem> outerwear = categoryGroups.getOrDefault("外套", new ArrayList<>());
        
        if (!outerwear.isEmpty() && ("秋季".equals(currentSeason) || "冬季".equals(currentSeason))) {
            OutfitRecommendation recommendation = new OutfitRecommendation();
            recommendation.setName(currentSeason + "保暖搭配");
            recommendation.setType("季节搭配");
            recommendation.setDescription("适合" + currentSeason + "的保暖穿搭");
            
            List<ClothingItem> items = new ArrayList<>();
            items.add(outerwear.get(random.nextInt(outerwear.size())));
            
            // 添加其他类别的衣物
            addRandomItemFromCategory(items, categoryGroups, "上衣");
            addRandomItemFromCategory(items, categoryGroups, "下装");
            addRandomItemFromCategory(items, categoryGroups, "鞋子");
            
            recommendation.setClothingItems(items);
            recommendation.setConfidenceScore(calculateConfidenceScore(items));
            
            seasonalRecommendations.add(recommendation);
        }
        
        return seasonalRecommendations;
    }
    
    /**
     * 生成场合推荐
     */
    private List<OutfitRecommendation> generateOccasionRecommendations(Map<String, List<ClothingItem>> categoryGroups) {
        List<OutfitRecommendation> occasionRecommendations = new ArrayList<>();
        
        String[] occasions = {"工作", "约会", "运动", "聚会"};
        
        for (String occasion : occasions) {
            List<ClothingItem> suitableItems = findItemsForOccasion(occasion);
            if (suitableItems.size() >= 2) {
                OutfitRecommendation recommendation = new OutfitRecommendation();
                recommendation.setName(occasion + "搭配");
                recommendation.setType("场合搭配");
                recommendation.setDescription("适合" + occasion + "的专业穿搭");
                
                // 随机选择适合的衣物
                Collections.shuffle(suitableItems, random);
                List<ClothingItem> selectedItems = suitableItems.subList(0, Math.min(4, suitableItems.size()));
                
                recommendation.setClothingItems(selectedItems);
                recommendation.setConfidenceScore(calculateConfidenceScore(selectedItems));
                
                occasionRecommendations.add(recommendation);
            }
        }
        
        return occasionRecommendations;
    }
    
    // ==================== 搭配操作方法 ====================
    
    /**
     * 选择推荐搭配
     */
    public void selectRecommendation(OutfitRecommendation recommendation) {
        selectedRecommendation.setValue(recommendation);
        Logger.d(TAG, "选择了推荐搭配: " + recommendation.getName());
    }
    
    /**
     * 保存搭配为穿搭记录
     */
    public void saveOutfitRecord(OutfitRecommendation recommendation, String occasion, String weather) {
        if (recommendation == null || recommendation.getClothingItems().isEmpty()) {
            errorMessage.setValue("请先选择一个搭配");
            return;
        }
        
        OutfitRecord record = new OutfitRecord();
        record.setDate(new Date());
        record.setOccasion(occasion);
        record.setWeather(weather);
        
        List<String> clothingIds = recommendation.getClothingItems().stream()
                .map(ClothingItem::getId)
                .collect(Collectors.toList());
        record.setClothingItemIds(clothingIds);
        record.setRating(3); // 默认评分
        
        outfitRepository.addOutfitRecordAsync(record, new OutfitRepository.BooleanCallback() {
            @Override
            public void onSuccess(boolean result) {
                successMessage.setValue("搭配已保存到日历");
                recentOutfits.add(0, record);
                calculateOutfitStats();
                Logger.d(TAG, "搭配记录保存成功");
            }
            
            @Override
            public void onError(String error) {
                errorMessage.setValue("保存搭配失败: " + error);
                Logger.e(TAG, "保存搭配记录失败: " + error);
            }
        });
    }
    
    /**
     * 刷新推荐
     */
    public void refreshRecommendations() {
        Logger.d(TAG, "刷新搭配推荐");
        generateRecommendations();
    }
    
    // ==================== 辅助方法 ====================
    
    /**
     * 按类别分组衣物
     */
    private Map<String, List<ClothingItem>> groupClothingByCategory() {
        Map<String, List<ClothingItem>> groups = new HashMap<>();
        
        for (ClothingItem item : allClothingItems) {
            String category = item.getCategory();
            if (category != null) {
                groups.computeIfAbsent(category, k -> new ArrayList<>()).add(item);
            }
        }
        
        return groups;
    }
    
    /**
     * 计算搭配的置信度分数
     */
    private double calculateConfidenceScore(List<ClothingItem> items) {
        if (items.isEmpty()) return 0.0;
        
        double score = 0.5; // 基础分数
        
        // 根据衣物数量调整
        if (items.size() >= 3) score += 0.2;
        if (items.size() >= 4) score += 0.1;
        
        // 根据颜色搭配调整
        if (hasGoodColorCombination(items)) score += 0.2;
        
        // 根据场合适配调整
        if (hasConsistentOccasion(items)) score += 0.1;
        
        return Math.min(1.0, score);
    }
    
    /**
     * 检查颜色搭配是否协调
     */
    private boolean hasGoodColorCombination(List<ClothingItem> items) {
        // 简单的颜色搭配逻辑
        List<String> colors = items.stream()
                .map(ClothingItem::getColor)
                .filter(color -> color != null && !color.isEmpty())
                .collect(Collectors.toList());
        
        if (colors.size() <= 1) return true;
        
        // 检查是否有经典搭配
        return colors.contains("黑色") || colors.contains("白色") || colors.contains("灰色");
    }
    
    /**
     * 检查场合是否一致
     */
    private boolean hasConsistentOccasion(List<ClothingItem> items) {
        List<String> occasions = items.stream()
                .map(ClothingItem::getOccasion)
                .filter(occasion -> occasion != null && !occasion.isEmpty())
                .distinct()
                .collect(Collectors.toList());
        
        return occasions.size() <= 1;
    }
    
    /**
     * 获取当前季节
     */
    private String getCurrentSeason() {
        int month = java.util.Calendar.getInstance().get(java.util.Calendar.MONTH) + 1;
        if (month >= 3 && month <= 5) return "春季";
        if (month >= 6 && month <= 8) return "夏季";
        if (month >= 9 && month <= 11) return "秋季";
        return "冬季";
    }
    
    /**
     * 从指定类别随机添加衣物
     */
    private void addRandomItemFromCategory(List<ClothingItem> items, Map<String, List<ClothingItem>> categoryGroups, String category) {
        List<ClothingItem> categoryItems = categoryGroups.get(category);
        if (categoryItems != null && !categoryItems.isEmpty()) {
            items.add(categoryItems.get(random.nextInt(categoryItems.size())));
        }
    }
    
    /**
     * 查找适合指定场合的衣物
     */
    private List<ClothingItem> findItemsForOccasion(String occasion) {
        return allClothingItems.stream()
                .filter(item -> occasion.equals(item.getOccasion()))
                .collect(Collectors.toList());
    }
    
    /**
     * 计算搭配统计数据
     */
    private void calculateOutfitStats() {
        Map<String, Integer> stats = new HashMap<>();
        
        stats.put("total_recommendations", recommendations.getValue() != null ? recommendations.getValue().size() : 0);
        stats.put("total_clothing", allClothingItems.size());
        stats.put("recent_outfits", recentOutfits.size());
        
        // 计算最常用的场合
        Map<String, Long> occasionCounts = recentOutfits.stream()
                .collect(Collectors.groupingBy(
                    record -> record.getOccasion() != null ? record.getOccasion() : "未知",
                    Collectors.counting()
                ));
        
        String mostCommonOccasion = occasionCounts.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("日常");
        
        stats.put("most_common_occasion_count", occasionCounts.getOrDefault(mostCommonOccasion, 0L).intValue());
        
        outfitStats.setValue(stats);
    }
    
    @Override
    protected void onCleared() {
        super.onCleared();
        // Repository由ServiceLocator管理，不需要手动关闭
        Logger.d(TAG, "OutfitViewModel 已清理");
    }
    
    // ==================== 内部数据类 ====================
    
    /**
     * 搭配推荐数据类
     */
    public static class OutfitRecommendation {
        private String name;
        private String type;
        private String description;
        private List<ClothingItem> clothingItems = new ArrayList<>();
        private double confidenceScore;
        
        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public List<ClothingItem> getClothingItems() { return clothingItems; }
        public void setClothingItems(List<ClothingItem> clothingItems) { 
            this.clothingItems = clothingItems != null ? clothingItems : new ArrayList<>(); 
        }
        
        public double getConfidenceScore() { return confidenceScore; }
        public void setConfidenceScore(double confidenceScore) { this.confidenceScore = confidenceScore; }
        
        public String getDisplayDescription() {
            StringBuilder desc = new StringBuilder();
            if (clothingItems != null) {
                for (int i = 0; i < clothingItems.size(); i++) {
                    if (i > 0) desc.append(" + ");
                    desc.append(clothingItems.get(i).getName());
                }
            }
            return desc.toString();
        }
    }
}
