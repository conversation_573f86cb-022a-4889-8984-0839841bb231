// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemClothingHorizontalBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageView ivClothing;

  @NonNull
  public final ImageView ivSelectedIcon;

  @NonNull
  public final View selectionOverlay;

  @NonNull
  public final TextView tvName;

  private ItemClothingHorizontalBinding(@NonNull CardView rootView, @NonNull ImageView ivClothing,
      @NonNull ImageView ivSelectedIcon, @NonNull View selectionOverlay, @NonNull TextView tvName) {
    this.rootView = rootView;
    this.ivClothing = ivClothing;
    this.ivSelectedIcon = ivSelectedIcon;
    this.selectionOverlay = selectionOverlay;
    this.tvName = tvName;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemClothingHorizontalBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemClothingHorizontalBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_clothing_horizontal, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemClothingHorizontalBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.iv_clothing;
      ImageView ivClothing = ViewBindings.findChildViewById(rootView, id);
      if (ivClothing == null) {
        break missingId;
      }

      id = R.id.iv_selected_icon;
      ImageView ivSelectedIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivSelectedIcon == null) {
        break missingId;
      }

      id = R.id.selection_overlay;
      View selectionOverlay = ViewBindings.findChildViewById(rootView, id);
      if (selectionOverlay == null) {
        break missingId;
      }

      id = R.id.tv_name;
      TextView tvName = ViewBindings.findChildViewById(rootView, id);
      if (tvName == null) {
        break missingId;
      }

      return new ItemClothingHorizontalBinding((CardView) rootView, ivClothing, ivSelectedIcon,
          selectionOverlay, tvName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
