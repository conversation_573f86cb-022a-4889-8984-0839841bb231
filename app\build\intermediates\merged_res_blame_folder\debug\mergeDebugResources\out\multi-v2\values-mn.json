{"logs": [{"outputFile": "com.wardrobe.app-mergeDebugResources-41:/values-mn/values-mn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e7fea473efc4287bc0512d9da8cbf7e2\\transformed\\core-1.13.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3414,3512,3614,3715,3813,3918,4030,9855", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "3507,3609,3710,3808,3913,4025,4144,9951"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\86e6281540a3cdfac31ba0f2ba29551c\\transformed\\material-1.12.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,500,587,672,770,889,974,1035,1100,1198,1279,1338,1431,1493,1556,1614,1685,1747,1801,1922,1979,2040,2094,2165,2298,2382,2462,2558,2641,2724,2857,2939,3017,3149,3239,3319,3373,3424,3490,3561,3639,3710,3789,3864,3942,4022,4105,4210,4298,4377,4467,4560,4634,4704,4795,4849,4929,4996,5080,5165,5227,5291,5354,5425,5529,5644,5741,5855,5913,5968,6052,6139,6215", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,76,78,86,84,97,118,84,60,64,97,80,58,92,61,62,57,70,61,53,120,56,60,53,70,132,83,79,95,82,82,132,81,77,131,89,79,53,50,65,70,77,70,78,74,77,79,82,104,87,78,89,92,73,69,90,53,79,66,83,84,61,63,62,70,103,114,96,113,57,54,83,86,75,81", "endOffsets": "260,339,416,495,582,667,765,884,969,1030,1095,1193,1274,1333,1426,1488,1551,1609,1680,1742,1796,1917,1974,2035,2089,2160,2293,2377,2457,2553,2636,2719,2852,2934,3012,3144,3234,3314,3368,3419,3485,3556,3634,3705,3784,3859,3937,4017,4100,4205,4293,4372,4462,4555,4629,4699,4790,4844,4924,4991,5075,5160,5222,5286,5349,5420,5524,5639,5736,5850,5908,5963,6047,6134,6210,6292"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3086,3163,3242,3329,4149,4247,4366,4451,4512,4577,4675,4756,4815,4908,4970,5033,5091,5162,5224,5278,5399,5456,5517,5571,5642,5775,5859,5939,6035,6118,6201,6334,6416,6494,6626,6716,6796,6850,6901,6967,7038,7116,7187,7266,7341,7419,7499,7582,7687,7775,7854,7944,8037,8111,8181,8272,8326,8406,8473,8557,8642,8704,8768,8831,8902,9006,9121,9218,9332,9390,9445,9610,9697,9773", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,78,76,78,86,84,97,118,84,60,64,97,80,58,92,61,62,57,70,61,53,120,56,60,53,70,132,83,79,95,82,82,132,81,77,131,89,79,53,50,65,70,77,70,78,74,77,79,82,104,87,78,89,92,73,69,90,53,79,66,83,84,61,63,62,70,103,114,96,113,57,54,83,86,75,81", "endOffsets": "310,3081,3158,3237,3324,3409,4242,4361,4446,4507,4572,4670,4751,4810,4903,4965,5028,5086,5157,5219,5273,5394,5451,5512,5566,5637,5770,5854,5934,6030,6113,6196,6329,6411,6489,6621,6711,6791,6845,6896,6962,7033,7111,7182,7261,7336,7414,7494,7577,7682,7770,7849,7939,8032,8106,8176,8267,8321,8401,8468,8552,8637,8699,8763,8826,8897,9001,9116,9213,9327,9385,9440,9524,9692,9768,9850"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0a486d55bfd94b1f617db1e4b30fa627\\transformed\\appcompat-1.7.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,429,529,638,724,830,944,1027,1108,1199,1292,1387,1483,1580,1673,1767,1859,1950,2040,2120,2227,2330,2427,2534,2636,2749,2908,9529", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "424,524,633,719,825,939,1022,1103,1194,1287,1382,1478,1575,1668,1762,1854,1945,2035,2115,2222,2325,2422,2529,2631,2744,2903,3002,9605"}}]}]}