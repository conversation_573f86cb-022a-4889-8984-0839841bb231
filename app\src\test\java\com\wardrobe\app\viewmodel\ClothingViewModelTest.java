package com.wardrobe.app.viewmodel;

import android.app.Application;

import androidx.arch.core.executor.testing.InstantTaskExecutorRule;
import androidx.lifecycle.Observer;

import com.wardrobe.app.ClothingManager;
import com.wardrobe.app.model.ClothingItem;
import com.wardrobe.app.utils.DataException;

import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

public class ClothingViewModelTest {
    @Rule
    public InstantTaskExecutorRule instantTaskExecutorRule = new InstantTaskExecutorRule();

    @Mock
    private Application mockApp;
    @Mock
    private ClothingManager mockManager;

    private ClothingViewModel viewModel;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        // 用反射注入mockManager
        viewModel = new ClothingViewModel(mockApp) {
            { this.clothingManager = mockManager; }
        };
    }

    @Test
    public void testLoadClothingItems_success() throws Exception {
        List<ClothingItem> mockList = Arrays.asList(new ClothingItem(), new ClothingItem());
        when(mockManager.getAllClothingItems()).thenReturn(mockList);
        viewModel.loadClothingItems();
        assertEquals(mockList, viewModel.getClothingItems().getValue());
        assertNull(viewModel.getErrorMessage().getValue());
    }

    @Test
    public void testLoadClothingItems_dataException() throws Exception {
        when(mockManager.getAllClothingItems()).thenThrow(new DataException("数据异常"));
        viewModel.loadClothingItems();
        assertNotNull(viewModel.getErrorMessage().getValue());
    }

    @Test
    public void testAddClothingItem_success() throws Exception {
        ClothingItem item = new ClothingItem();
        when(mockManager.getAllClothingItems()).thenReturn(Collections.singletonList(item));
        viewModel.addClothingItem(item);
        assertNull(viewModel.getErrorMessage().getValue());
        assertEquals(1, viewModel.getClothingItems().getValue().size());
    }

    @Test
    public void testAddClothingItem_dataException() throws Exception {
        ClothingItem item = new ClothingItem();
        doThrow(new DataException("添加异常")).when(mockManager).addClothingItem(any());
        viewModel.addClothingItem(item);
        assertNotNull(viewModel.getErrorMessage().getValue());
    }

    // 可继续补充update、delete、search等方法的异常与正常分支测试
} 