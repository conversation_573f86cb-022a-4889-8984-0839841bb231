// Generated by view binder compiler. Do not edit!
package com.wardrobe.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.wardrobe.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutErrorBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnRetry;

  @NonNull
  public final ImageView ivError;

  @NonNull
  public final TextView tvErrorMessage;

  @NonNull
  public final TextView tvErrorTitle;

  private LayoutErrorBinding(@NonNull LinearLayout rootView, @NonNull Button btnRetry,
      @NonNull ImageView ivError, @NonNull TextView tvErrorMessage,
      @NonNull TextView tvErrorTitle) {
    this.rootView = rootView;
    this.btnRetry = btnRetry;
    this.ivError = ivError;
    this.tvErrorMessage = tvErrorMessage;
    this.tvErrorTitle = tvErrorTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutErrorBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutErrorBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_error, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutErrorBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_retry;
      Button btnRetry = ViewBindings.findChildViewById(rootView, id);
      if (btnRetry == null) {
        break missingId;
      }

      id = R.id.iv_error;
      ImageView ivError = ViewBindings.findChildViewById(rootView, id);
      if (ivError == null) {
        break missingId;
      }

      id = R.id.tv_error_message;
      TextView tvErrorMessage = ViewBindings.findChildViewById(rootView, id);
      if (tvErrorMessage == null) {
        break missingId;
      }

      id = R.id.tv_error_title;
      TextView tvErrorTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvErrorTitle == null) {
        break missingId;
      }

      return new LayoutErrorBinding((LinearLayout) rootView, btnRetry, ivError, tvErrorMessage,
          tvErrorTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
