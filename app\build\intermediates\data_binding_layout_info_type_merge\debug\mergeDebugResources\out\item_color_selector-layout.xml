<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_color_selector" modulePackage="com.wardrobe.app" filePath="app\src\main\res\layout\item_color_selector.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_color_selector_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="33" endOffset="14"/></Target><Target id="@+id/color_circle" view="ImageView"><Expressions/><location startLine="12" startOffset="4" endLine="16" endOffset="57"/></Target><Target id="@+id/color_preview_circle" view="View"><Expressions/><location startLine="18" startOffset="4" endLine="22" endOffset="64"/></Target><Target id="@+id/color_name" view="TextView"><Expressions/><location startLine="24" startOffset="4" endLine="31" endOffset="49"/></Target></Targets></Layout>