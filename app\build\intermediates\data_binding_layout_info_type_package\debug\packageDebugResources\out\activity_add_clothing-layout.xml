<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_add_clothing" modulePackage="com.wardrobe.app" filePath="app\src\main\res\layout\activity_add_clothing.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_add_clothing_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="206" endOffset="14"/></Target><Target id="@+id/image_container" view="FrameLayout"><Expressions/><location startLine="48" startOffset="20" endLine="90" endOffset="33"/></Target><Target id="@+id/image_placeholder" view="FrameLayout"><Expressions/><location startLine="54" startOffset="24" endLine="68" endOffset="37"/></Target><Target id="@+id/clothing_image_view" view="ImageView"><Expressions/><location startLine="70" startOffset="24" endLine="76" endOffset="93"/></Target><Target id="@+id/button_replace_photo" view="ImageView"><Expressions/><location startLine="78" startOffset="24" endLine="89" endOffset="79"/></Target><Target id="@+id/edit_text_name" view="EditText"><Expressions/><location startLine="92" startOffset="20" endLine="106" endOffset="54"/></Target><Target id="@+id/layout_category" view="RelativeLayout"><Expressions/><location startLine="113" startOffset="16" endLine="130" endOffset="32"/></Target><Target id="@+id/text_view_category_label" view="TextView"><Expressions/><location startLine="116" startOffset="20" endLine="119" endOffset="63"/></Target><Target id="@+id/text_view_category_value" view="TextView"><Expressions/><location startLine="120" startOffset="20" endLine="126" endOffset="69"/></Target><Target id="@+id/chevron_category" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="127" startOffset="20" endLine="129" endOffset="51"/></Target><Target id="@+id/layout_color" view="RelativeLayout"><Expressions/><location startLine="150" startOffset="16" endLine="167" endOffset="32"/></Target><Target id="@+id/text_view_color_label" view="TextView"><Expressions/><location startLine="153" startOffset="20" endLine="156" endOffset="60"/></Target><Target id="@+id/text_view_color_value" view="TextView"><Expressions/><location startLine="157" startOffset="20" endLine="163" endOffset="66"/></Target><Target id="@+id/chevron_color" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="164" startOffset="20" endLine="166" endOffset="51"/></Target><Target id="@+id/layout_occasion" view="RelativeLayout"><Expressions/><location startLine="172" startOffset="16" endLine="189" endOffset="32"/></Target><Target id="@+id/text_view_occasion_label" view="TextView"><Expressions/><location startLine="175" startOffset="20" endLine="178" endOffset="63"/></Target><Target id="@+id/text_view_occasion_value" view="TextView"><Expressions/><location startLine="179" startOffset="20" endLine="185" endOffset="69"/></Target><Target id="@+id/chevron_occasion" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="186" startOffset="20" endLine="188" endOffset="51"/></Target><Target id="@+id/button_save" view="Button"><Expressions/><location startLine="196" startOffset="4" endLine="204" endOffset="44"/></Target></Targets></Layout>