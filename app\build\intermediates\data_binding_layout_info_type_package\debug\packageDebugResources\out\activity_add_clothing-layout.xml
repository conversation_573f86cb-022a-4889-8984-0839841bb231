<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_add_clothing" modulePackage="com.wardrobe.app" filePath="app\src\main\res\layout\activity_add_clothing.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_add_clothing_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="202" endOffset="14"/></Target><Target id="@+id/image_container" view="FrameLayout"><Expressions/><location startLine="48" startOffset="20" endLine="90" endOffset="33"/></Target><Target id="@+id/image_placeholder" view="FrameLayout"><Expressions/><location startLine="54" startOffset="24" endLine="68" endOffset="37"/></Target><Target id="@+id/clothing_image_view" view="ImageView"><Expressions/><location startLine="70" startOffset="24" endLine="76" endOffset="93"/></Target><Target id="@+id/button_replace_photo" view="ImageView"><Expressions/><location startLine="78" startOffset="24" endLine="89" endOffset="79"/></Target><Target id="@+id/edit_text_name" view="EditText"><Expressions/><location startLine="92" startOffset="20" endLine="102" endOffset="60"/></Target><Target id="@+id/layout_category" view="RelativeLayout"><Expressions/><location startLine="109" startOffset="16" endLine="126" endOffset="32"/></Target><Target id="@+id/text_view_category_label" view="TextView"><Expressions/><location startLine="112" startOffset="20" endLine="115" endOffset="63"/></Target><Target id="@+id/text_view_category_value" view="TextView"><Expressions/><location startLine="116" startOffset="20" endLine="122" endOffset="69"/></Target><Target id="@+id/chevron_category" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="123" startOffset="20" endLine="125" endOffset="51"/></Target><Target id="@+id/layout_color" view="RelativeLayout"><Expressions/><location startLine="146" startOffset="16" endLine="163" endOffset="32"/></Target><Target id="@+id/text_view_color_label" view="TextView"><Expressions/><location startLine="149" startOffset="20" endLine="152" endOffset="60"/></Target><Target id="@+id/text_view_color_value" view="TextView"><Expressions/><location startLine="153" startOffset="20" endLine="159" endOffset="66"/></Target><Target id="@+id/chevron_color" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="160" startOffset="20" endLine="162" endOffset="51"/></Target><Target id="@+id/layout_occasion" view="RelativeLayout"><Expressions/><location startLine="168" startOffset="16" endLine="185" endOffset="32"/></Target><Target id="@+id/text_view_occasion_label" view="TextView"><Expressions/><location startLine="171" startOffset="20" endLine="174" endOffset="63"/></Target><Target id="@+id/text_view_occasion_value" view="TextView"><Expressions/><location startLine="175" startOffset="20" endLine="181" endOffset="69"/></Target><Target id="@+id/chevron_occasion" view="androidx.appcompat.widget.AppCompatImageView"><Expressions/><location startLine="182" startOffset="20" endLine="184" endOffset="51"/></Target><Target id="@+id/button_save" view="Button"><Expressions/><location startLine="192" startOffset="4" endLine="200" endOffset="44"/></Target></Targets></Layout>