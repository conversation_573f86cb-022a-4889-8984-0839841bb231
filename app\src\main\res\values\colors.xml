<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Material Design 3 浅色主题颜色 -->
    <color name="md_theme_light_primary">#6750A4</color>
    <color name="md_theme_light_onPrimary">#FFFFFF</color>
    <color name="md_theme_light_primaryContainer">#EADDFF</color>
    <color name="md_theme_light_onPrimaryContainer">#21005D</color>

    <color name="md_theme_light_secondary">#625B71</color>
    <color name="md_theme_light_onSecondary">#FFFFFF</color>
    <color name="md_theme_light_secondaryContainer">#E8DEF8</color>
    <color name="md_theme_light_onSecondaryContainer">#1D192B</color>

    <color name="md_theme_light_tertiary">#7D5260</color>
    <color name="md_theme_light_onTertiary">#FFFFFF</color>
    <color name="md_theme_light_tertiaryContainer">#FFD8E4</color>
    <color name="md_theme_light_onTertiaryContainer">#31111D</color>

    <color name="md_theme_light_error">#BA1A1A</color>
    <color name="md_theme_light_onError">#FFFFFF</color>
    <color name="md_theme_light_errorContainer">#FFDAD6</color>
    <color name="md_theme_light_onErrorContainer">#410002</color>

    <color name="md_theme_light_background">#FFFBFE</color>
    <color name="md_theme_light_onBackground">#1C1B1F</color>
    <color name="md_theme_light_surface">#FFFBFE</color>
    <color name="md_theme_light_onSurface">#1C1B1F</color>
    <color name="md_theme_light_surfaceVariant">#E7E0EC</color>
    <color name="md_theme_light_onSurfaceVariant">#49454F</color>

    <color name="md_theme_light_outline">#79747E</color>
    <color name="md_theme_light_outlineVariant">#CAC4D0</color>

    <!-- Material Design 3 深色主题颜色 -->
    <color name="md_theme_dark_primary">#D0BCFF</color>
    <color name="md_theme_dark_onPrimary">#381E72</color>
    <color name="md_theme_dark_primaryContainer">#4F378B</color>
    <color name="md_theme_dark_onPrimaryContainer">#EADDFF</color>

    <color name="md_theme_dark_secondary">#CCC2DC</color>
    <color name="md_theme_dark_onSecondary">#332D41</color>
    <color name="md_theme_dark_secondaryContainer">#4A4458</color>
    <color name="md_theme_dark_onSecondaryContainer">#E8DEF8</color>

    <color name="md_theme_dark_tertiary">#EFB8C8</color>
    <color name="md_theme_dark_onTertiary">#492532</color>
    <color name="md_theme_dark_tertiaryContainer">#633B48</color>
    <color name="md_theme_dark_onTertiaryContainer">#FFD8E4</color>

    <color name="md_theme_dark_error">#FFB4AB</color>
    <color name="md_theme_dark_onError">#690005</color>
    <color name="md_theme_dark_errorContainer">#93000A</color>
    <color name="md_theme_dark_onErrorContainer">#FFDAD6</color>

    <color name="md_theme_dark_background">#1C1B1F</color>
    <color name="md_theme_dark_onBackground">#E6E1E5</color>
    <color name="md_theme_dark_surface">#1C1B1F</color>
    <color name="md_theme_dark_onSurface">#E6E1E5</color>
    <color name="md_theme_dark_surfaceVariant">#49454F</color>
    <color name="md_theme_dark_onSurfaceVariant">#CAC4D0</color>

    <color name="md_theme_dark_outline">#938F99</color>
    <color name="md_theme_dark_outlineVariant">#49454F</color>

    <!-- 原有颜色（保持兼容性） -->
    <color name="primary">#6750A4</color>
    <color name="primary_dark">#4F378B</color>
    <color name="accent">#D0BCFF</color>

    <!-- 语义化颜色 -->
    <color name="success">#4CAF50</color>
    <color name="warning">#FF9800</color>
    <color name="info">#2196F3</color>

    <!-- 背景颜色 -->
    <color name="background_primary">#FFFBFE</color>
    <color name="background_secondary">#F7F2FA</color>
    <color name="background_tertiary">#E7E0EC</color>

    <!-- 文本颜色 -->
    <color name="text_primary">#1C1B1F</color>
    <color name="text_secondary">#49454F</color>
    <color name="text_tertiary">#79747E</color>
    <color name="text_inverse">#FFFFFF</color>

    <!-- 分割线和边框 -->
    <color name="divider">#CAC4D0</color>
    <color name="border">#79747E</color>

    <!-- 状态颜色 -->
    <color name="selected">#EADDFF</color>
    <color name="pressed">#E8DEF8</color>
    <color name="disabled">#F7F2FA</color>

    <!-- 透明度变体 -->
    <color name="black_alpha_12">#1F000000</color>
    <color name="black_alpha_24">#3D000000</color>
    <color name="black_alpha_54">#8A000000</color>
    <color name="white_alpha_12">#1FFFFFFF</color>
    <color name="white_alpha_24">#3DFFFFFF</color>
    <color name="white_alpha_54">#8AFFFFFF</color>

    <!-- 特殊用途颜色 -->
    <color name="shimmer_base">#F0F0F0</color>
    <color name="shimmer_highlight">#FFFFFF</color>
    <color name="overlay">#80000000</color>

    <!-- 衣物分类颜色 -->
    <color name="category_tops">#E3F2FD</color>
    <color name="category_bottoms">#F3E5F5</color>
    <color name="category_outerwear">#E8F5E8</color>
    <color name="category_shoes">#FFF3E0</color>
    <color name="category_accessories">#FCE4EC</color>
    <color name="category_other">#F5F5F5</color>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    <color name="systemBackground">#FFFFFF</color>
    <color name="systemGroupedBackground">#F2F2F7</color>
    <color name="text_secondary_ios">#8E8E93</color>
    <color name="text_tertiary_ios">#C7C7CC</color>
    <color name="surface_primary">#FFFFFF</color>
    <!-- System Colors -->
    <color name="systemBlue">#007AFF</color>
    <color name="systemGray">#8E8E93</color>
    <color name="systemGray2">#AEAEB2</color>
    <color name="systemGray3">#C7C7CC</color>
    <color name="systemGray4">#D1D1D6</color>
    <color name="systemGray5">#E5E5EA</color>
    <color name="systemGray6">#F2F2F7</color>
    <color name="separator">#3C3C43</color>
    <color name="opaqueSeparator">#C6C6C8</color>
    <color name="secondarySystemGroupedBackground">#F2F2F7</color>
    <color name="placeholderText">#8E8E93</color>
    <color name="destructive">#FF3B30</color>
    <!-- Label Colors -->
    <color name="labelPrimary">#000000</color>
    <color name="labelSecondary">#3C3C43</color>
    <color name="labelTertiary">#3C3C43</color>
    <color name="labelQuaternary">#3C3C43</color>
<!-- 新增缺失的颜色资源 -->
<color name="fillTertiary">#D1D1D6</color>
<color name="systemRed">#FF3B30</color>
<color name="quaternarySystemGroupedBackground">#FFFFFF</color>
<color name="background_secondary_ios">#F2F2F7</color>
<color name="border_primary">#D1D1D6</color>
<color name="accent_primary">#007AFF</color>
<color name="background_primary_ios">#FFFFFF</color>
<color name="text_primary_ios">#000000</color>
    <!-- Destructive Color -->
    <color name="destructiveRed">#FF3B30</color>

    <!-- 添加缺失的基础颜色定义 -->
    <color name="colorPrimary">#6750A4</color>
    <color name="colorPrimaryVariant">#4F378B</color>
    <color name="colorOnPrimary">#FFFFFF</color>

    <color name="colorSecondary">#625B71</color>
    <color name="colorSecondaryVariant">#4A4458</color>
    <color name="colorOnSecondary">#FFFFFF</color>

    <color name="colorBackground">#FFFBFE</color>
    <color name="colorOnBackground">#1C1B1F</color>

    <color name="colorSurface">#FFFBFE</color>
    <color name="colorOnSurface">#1C1B1F</color>

    <color name="colorError">#BA1A1A</color>
    <color name="colorOnError">#FFFFFF</color>

    <!-- 文本颜色基础定义 -->
    <color name="textColorPrimary">#1C1B1F</color>
    <color name="textColorSecondary">#49454F</color>
    <color name="textColorHint">#79747E</color>

    <!-- 分割线和边框基础定义 -->
    <color name="dividerColor">#CAC4D0</color>
    <color name="borderColor">#79747E</color>

    <!-- 卡片和容器背景基础定义 -->
    <color name="cardBackground">#FFFFFF</color>
    <color name="containerBackground">#F7F2FA</color>

    <!-- 按钮颜色基础定义 -->
    <color name="buttonBackground">#6750A4</color>
    <color name="buttonTextColor">#FFFFFF</color>

    <!-- 状态栏和导航栏基础定义 -->
    <color name="statusBarColor">#6750A4</color>
    <color name="navigationBarColor">#FFFBFE</color>

    <!-- 选中状态基础定义 -->
    <color name="selectedItemBackground">#EADDFF</color>
    <color name="rippleColor">#1F6750A4</color>
</resources>