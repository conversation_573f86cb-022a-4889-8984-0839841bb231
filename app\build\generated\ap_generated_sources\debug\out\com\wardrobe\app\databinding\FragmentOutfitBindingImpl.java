package com.wardrobe.app.databinding;
import com.wardrobe.app.R;
import com.wardrobe.app.BR;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.view.View;
@SuppressWarnings("unchecked")
public class FragmentOutfitBindingImpl extends FragmentOutfitBinding  {

    @Nullable
    private static final androidx.databinding.ViewDataBinding.IncludedLayouts sIncludes;
    @Nullable
    private static final android.util.SparseIntArray sViewsWithIds;
    static {
        sIncludes = null;
        sViewsWithIds = new android.util.SparseIntArray();
        sViewsWithIds.put(R.id.tv_title, 5);
    }
    // views
    @NonNull
    private final androidx.constraintlayout.widget.ConstraintLayout mboundView0;
    // variables
    // values
    // listeners
    // Inverse Binding Event Handlers

    public FragmentOutfitBindingImpl(@Nullable androidx.databinding.DataBindingComponent bindingComponent, @NonNull View root) {
        this(bindingComponent, root, mapBindings(bindingComponent, root, 6, sIncludes, sViewsWithIds));
    }
    private FragmentOutfitBindingImpl(androidx.databinding.DataBindingComponent bindingComponent, View root, Object[] bindings) {
        super(bindingComponent, root, 0
            , (android.widget.ProgressBar) bindings[4]
            , (androidx.recyclerview.widget.RecyclerView) bindings[2]
            , (android.widget.LinearLayout) bindings[3]
            , (android.widget.TextView) bindings[1]
            , (android.widget.TextView) bindings[5]
            );
        this.mboundView0 = (androidx.constraintlayout.widget.ConstraintLayout) bindings[0];
        this.mboundView0.setTag(null);
        this.progressLoading.setTag(null);
        this.recyclerView.setTag(null);
        this.tvEmptyState.setTag(null);
        this.tvSuggestion.setTag(null);
        setRootTag(root);
        // listeners
        invalidateAll();
    }

    @Override
    public void invalidateAll() {
        synchronized(this) {
                mDirtyFlags = 0x8L;
        }
        requestRebind();
    }

    @Override
    public boolean hasPendingBindings() {
        synchronized(this) {
            if (mDirtyFlags != 0) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean setVariable(int variableId, @Nullable Object variable)  {
        boolean variableSet = true;
        if (BR.isEmpty == variableId) {
            setIsEmpty((java.lang.Boolean) variable);
        }
        else if (BR.viewModel == variableId) {
            setViewModel((com.wardrobe.app.viewmodel.OutfitViewModel) variable);
        }
        else if (BR.isLoading == variableId) {
            setIsLoading((java.lang.Boolean) variable);
        }
        else {
            variableSet = false;
        }
            return variableSet;
    }

    public void setIsEmpty(@Nullable java.lang.Boolean IsEmpty) {
        this.mIsEmpty = IsEmpty;
        synchronized(this) {
            mDirtyFlags |= 0x1L;
        }
        notifyPropertyChanged(BR.isEmpty);
        super.requestRebind();
    }
    public void setViewModel(@Nullable com.wardrobe.app.viewmodel.OutfitViewModel ViewModel) {
        this.mViewModel = ViewModel;
    }
    public void setIsLoading(@Nullable java.lang.Boolean IsLoading) {
        this.mIsLoading = IsLoading;
        synchronized(this) {
            mDirtyFlags |= 0x4L;
        }
        notifyPropertyChanged(BR.isLoading);
        super.requestRebind();
    }

    @Override
    protected boolean onFieldChange(int localFieldId, Object object, int fieldId) {
        switch (localFieldId) {
        }
        return false;
    }

    @Override
    protected void executeBindings() {
        long dirtyFlags = 0;
        synchronized(this) {
            dirtyFlags = mDirtyFlags;
            mDirtyFlags = 0;
        }
        boolean androidxDatabindingViewDataBindingSafeUnboxIsLoading = false;
        boolean isEmptyIsLoadingBooleanFalse = false;
        boolean isEmpty = false;
        java.lang.String isLoadingJavaLangStringJavaLangString = null;
        boolean isLoading = false;
        boolean IsEmptyIsLoadingBooleanFalse1 = false;
        java.lang.Boolean IsEmpty1 = mIsEmpty;
        java.lang.Boolean IsLoading1 = mIsLoading;
        boolean androidxDatabindingViewDataBindingSafeUnboxIsEmpty = false;
        boolean AndroidxDatabindingViewDataBindingSafeUnboxIsEmpty1 = false;

        if ((dirtyFlags & 0xdL) != 0) {



                // read androidx.databinding.ViewDataBinding.safeUnbox(isEmpty)
                androidxDatabindingViewDataBindingSafeUnboxIsEmpty = androidx.databinding.ViewDataBinding.safeUnbox(IsEmpty1);
            if((dirtyFlags & 0xdL) != 0) {
                if(androidxDatabindingViewDataBindingSafeUnboxIsEmpty) {
                        dirtyFlags |= 0x200L;
                }
                else {
                        dirtyFlags |= 0x100L;
                }
            }


                // read !androidx.databinding.ViewDataBinding.safeUnbox(isEmpty)
                isEmpty = !androidxDatabindingViewDataBindingSafeUnboxIsEmpty;
            if((dirtyFlags & 0xdL) != 0) {
                if(isEmpty) {
                        dirtyFlags |= 0x20L;
                }
                else {
                        dirtyFlags |= 0x10L;
                }
            }

            if ((dirtyFlags & 0x9L) != 0) {

                    // read androidx.databinding.ViewDataBinding.safeUnbox(!androidx.databinding.ViewDataBinding.safeUnbox(isEmpty))
                    AndroidxDatabindingViewDataBindingSafeUnboxIsEmpty1 = androidx.databinding.ViewDataBinding.safeUnbox(isEmpty);
            }
        }
        if ((dirtyFlags & 0xcL) != 0) {



                // read androidx.databinding.ViewDataBinding.safeUnbox(isLoading)
                androidxDatabindingViewDataBindingSafeUnboxIsLoading = androidx.databinding.ViewDataBinding.safeUnbox(IsLoading1);
            if((dirtyFlags & 0xcL) != 0) {
                if(androidxDatabindingViewDataBindingSafeUnboxIsLoading) {
                        dirtyFlags |= 0x80L;
                }
                else {
                        dirtyFlags |= 0x40L;
                }
            }


                // read androidx.databinding.ViewDataBinding.safeUnbox(isLoading) ? "正在生成搭配建议..." : "基于您的衣橱，为您推荐以下搭配组合"
                isLoadingJavaLangStringJavaLangString = ((androidxDatabindingViewDataBindingSafeUnboxIsLoading) ? ("正在生成搭配建议...") : ("基于您的衣橱，为您推荐以下搭配组合"));
        }
        // batch finished

        if ((dirtyFlags & 0x220L) != 0) {



                // read androidx.databinding.ViewDataBinding.safeUnbox(isLoading)
                androidxDatabindingViewDataBindingSafeUnboxIsLoading = androidx.databinding.ViewDataBinding.safeUnbox(IsLoading1);
            if((dirtyFlags & 0xcL) != 0) {
                if(androidxDatabindingViewDataBindingSafeUnboxIsLoading) {
                        dirtyFlags |= 0x80L;
                }
                else {
                        dirtyFlags |= 0x40L;
                }
            }


                // read !androidx.databinding.ViewDataBinding.safeUnbox(isLoading)
                isLoading = !androidxDatabindingViewDataBindingSafeUnboxIsLoading;
        }

        if ((dirtyFlags & 0xdL) != 0) {

                // read !androidx.databinding.ViewDataBinding.safeUnbox(isEmpty) ? !androidx.databinding.ViewDataBinding.safeUnbox(isLoading) : false
                isEmptyIsLoadingBooleanFalse = ((isEmpty) ? (isLoading) : (false));
                // read androidx.databinding.ViewDataBinding.safeUnbox(isEmpty) ? !androidx.databinding.ViewDataBinding.safeUnbox(isLoading) : false
                IsEmptyIsLoadingBooleanFalse1 = ((androidxDatabindingViewDataBindingSafeUnboxIsEmpty) ? (isLoading) : (false));
        }
        // batch finished
        if ((dirtyFlags & 0xcL) != 0) {
            // api target 1

            com.wardrobe.app.ui.binding.BindingAdapters.setVisibleIf(this.progressLoading, androidxDatabindingViewDataBindingSafeUnboxIsLoading);
            androidx.databinding.adapters.TextViewBindingAdapter.setText(this.tvSuggestion, isLoadingJavaLangStringJavaLangString);
        }
        if ((dirtyFlags & 0xdL) != 0) {
            // api target 1

            com.wardrobe.app.ui.binding.BindingAdapters.setVisibleIf(this.recyclerView, isEmptyIsLoadingBooleanFalse);
            com.wardrobe.app.ui.binding.BindingAdapters.setFadeIn(this.recyclerView, isEmptyIsLoadingBooleanFalse);
            com.wardrobe.app.ui.binding.BindingAdapters.setVisibleIf(this.tvEmptyState, IsEmptyIsLoadingBooleanFalse1);
            com.wardrobe.app.ui.binding.BindingAdapters.setFadeIn(this.tvEmptyState, IsEmptyIsLoadingBooleanFalse1);
        }
        if ((dirtyFlags & 0x9L) != 0) {
            // api target 1

            com.wardrobe.app.ui.binding.BindingAdapters.setVisibleIf(this.tvSuggestion, AndroidxDatabindingViewDataBindingSafeUnboxIsEmpty1);
        }
    }
    // Listener Stub Implementations
    // callback impls
    // dirty flag
    private  long mDirtyFlags = 0xffffffffffffffffL;
    /* flag mapping
        flag 0 (0x1L): isEmpty
        flag 1 (0x2L): viewModel
        flag 2 (0x3L): isLoading
        flag 3 (0x4L): null
        flag 4 (0x5L): !androidx.databinding.ViewDataBinding.safeUnbox(isEmpty) ? !androidx.databinding.ViewDataBinding.safeUnbox(isLoading) : false
        flag 5 (0x6L): !androidx.databinding.ViewDataBinding.safeUnbox(isEmpty) ? !androidx.databinding.ViewDataBinding.safeUnbox(isLoading) : false
        flag 6 (0x7L): androidx.databinding.ViewDataBinding.safeUnbox(isLoading) ? "正在生成搭配建议..." : "基于您的衣橱，为您推荐以下搭配组合"
        flag 7 (0x8L): androidx.databinding.ViewDataBinding.safeUnbox(isLoading) ? "正在生成搭配建议..." : "基于您的衣橱，为您推荐以下搭配组合"
        flag 8 (0x9L): androidx.databinding.ViewDataBinding.safeUnbox(isEmpty) ? !androidx.databinding.ViewDataBinding.safeUnbox(isLoading) : false
        flag 9 (0xaL): androidx.databinding.ViewDataBinding.safeUnbox(isEmpty) ? !androidx.databinding.ViewDataBinding.safeUnbox(isLoading) : false
    flag mapping end*/
    //end
}