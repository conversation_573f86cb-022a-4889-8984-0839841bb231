<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_profile" modulePackage="com.wardrobe.app" filePath="app\src\main\res\layout\fragment_profile.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_profile_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="429" endOffset="12"/></Target><Target id="@+id/switch_dark_mode" view="Switch"><Expressions/><location startLine="52" startOffset="12" endLine="55" endOffset="54"/></Target><Target id="@+id/switch_notifications" view="Switch"><Expressions/><location startLine="77" startOffset="12" endLine="80" endOffset="54"/></Target><Target id="@+id/switch_auto_backup" view="Switch"><Expressions/><location startLine="102" startOffset="12" endLine="105" endOffset="54"/></Target><Target id="@+id/layout_theme" view="LinearLayout"><Expressions/><location startLine="110" startOffset="8" endLine="145" endOffset="22"/></Target><Target id="@+id/tv_theme_mode" view="TextView"><Expressions/><location startLine="130" startOffset="12" endLine="137" endOffset="48"/></Target><Target id="@+id/layout_language" view="LinearLayout"><Expressions/><location startLine="148" startOffset="8" endLine="183" endOffset="22"/></Target><Target id="@+id/tv_language" view="TextView"><Expressions/><location startLine="168" startOffset="12" endLine="175" endOffset="48"/></Target><Target id="@+id/tv_storage_info" view="TextView"><Expressions/><location startLine="210" startOffset="16" endLine="218" endOffset="55"/></Target><Target id="@+id/tv_image_count" view="TextView"><Expressions/><location startLine="220" startOffset="16" endLine="228" endOffset="55"/></Target><Target id="@+id/tv_clothing_count" view="TextView"><Expressions/><location startLine="230" startOffset="16" endLine="237" endOffset="53"/></Target><Target id="@+id/btn_compress_images" view="Button"><Expressions/><location startLine="269" startOffset="16" endLine="276" endOffset="55"/></Target><Target id="@+id/btn_cleanup_unused" view="Button"><Expressions/><location startLine="279" startOffset="16" endLine="286" endOffset="56"/></Target><Target id="@+id/btn_cleanup_images" view="Button"><Expressions/><location startLine="289" startOffset="16" endLine="298" endOffset="45"/></Target><Target id="@+id/layout_about" view="LinearLayout"><Expressions/><location startLine="370" startOffset="8" endLine="396" endOffset="22"/></Target><Target id="@+id/layout_feedback" view="LinearLayout"><Expressions/><location startLine="399" startOffset="8" endLine="425" endOffset="22"/></Target></Targets></Layout>