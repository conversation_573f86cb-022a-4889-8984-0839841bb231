<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_profile" modulePackage="com.wardrobe.app" filePath="app\src\main\res\layout\fragment_profile.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_profile_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="253" endOffset="12"/></Target><Target id="@+id/tv_storage_info" view="TextView"><Expressions/><location startLine="44" startOffset="16" endLine="51" endOffset="55"/></Target><Target id="@+id/tv_image_count" view="TextView"><Expressions/><location startLine="53" startOffset="16" endLine="60" endOffset="55"/></Target><Target id="@+id/tv_clothing_count" view="TextView"><Expressions/><location startLine="62" startOffset="16" endLine="68" endOffset="63"/></Target><Target id="@+id/btn_cleanup_images" view="Button"><Expressions/><location startLine="96" startOffset="16" endLine="102" endOffset="55"/></Target><Target id="@+id/btn_compress_images" view="Button"><Expressions/><location startLine="104" startOffset="16" endLine="110" endOffset="55"/></Target><Target id="@+id/btn_cleanup_unused" view="Button"><Expressions/><location startLine="112" startOffset="16" endLine="117" endOffset="44"/></Target><Target id="@+id/layout_language_setting" view="LinearLayout"><Expressions/><location startLine="145" startOffset="16" endLine="180" endOffset="30"/></Target><Target id="@+id/tv_current_language" view="TextView"><Expressions/><location startLine="165" startOffset="20" endLine="172" endOffset="56"/></Target><Target id="@+id/switch_dark_mode" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="197" startOffset="20" endLine="200" endOffset="62"/></Target><Target id="@+id/switch_notifications" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="219" startOffset="20" endLine="222" endOffset="62"/></Target><Target id="@+id/switch_auto_backup" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="240" startOffset="20" endLine="243" endOffset="62"/></Target></Targets></Layout>