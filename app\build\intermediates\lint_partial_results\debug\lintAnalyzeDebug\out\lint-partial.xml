<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="partial_results">
    <map id="AppBundleLocaleChanges">
        <location id="localeChangeLocation"
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/LocalizationManager.java"
            line="201"
            column="13"
            startOffset="6364"
            endLine="201"
            endColumn="44"
            endOffset="6395"/>
    </map>
    <map id="NotificationPermission">
        <location id="class"
            file="$GRADLE_USER_HOME/caches/8.11.1/transforms/cda4372faace0d2ab027ac1660d7fb39/transformed/jetified-leakcanary-android-core-2.12/jars/classes.jar"/>
        <entry
            name="className"
            string="leakcanary/NotificationEventListener"/>
    </map>
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsSent">
                    <map id="android.settings.APPLICATION_DETAILS_SETTINGS (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/wardrobe/app/utils/PermissionManager.java"
                            line="200"
                            column="25"
                            startOffset="6366"
                            endLine="200"
                            endColumn="81"
                            endOffset="6422"/>
                    </map>
            </map>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.wardrobe.app.ui.activities.MainActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.anim.scale_in"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/scale_in.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="15"
            endColumn="7"
            endOffset="462"/>
        <location id="R.anim.slide_in_left"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/slide_in_left.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="14"
            endColumn="7"
            endOffset="386"/>
        <location id="R.anim.slide_in_right"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/slide_in_right.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="7"
            endOffset="340"/>
        <location id="R.anim.slide_out_left"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/slide_out_left.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="7"
            endOffset="341"/>
        <location id="R.anim.slide_out_right"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/slide_out_right.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="14"
            endColumn="7"
            endOffset="385"/>
        <location id="R.anim.slide_up"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/slide_up.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="14"
            endColumn="7"
            endOffset="385"/>
        <location id="R.color.accent"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="68"
            column="12"
            startOffset="3226"
            endLine="68"
            endColumn="25"
            endOffset="3239"/>
        <location id="R.color.background_primary_ios"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="147"
            column="8"
            startOffset="6347"
            endLine="147"
            endColumn="37"
            endOffset="6376"/>
        <location id="R.color.background_secondary_ios"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="144"
            column="8"
            startOffset="6202"
            endLine="144"
            endColumn="39"
            endOffset="6233"/>
        <location id="R.color.background_tertiary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="78"
            column="12"
            startOffset="3537"
            endLine="78"
            endColumn="38"
            endOffset="3563"/>
        <location id="R.color.black"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="115"
            column="12"
            startOffset="4868"
            endLine="115"
            endColumn="24"
            endOffset="4880"/>
        <location id="R.color.black_alpha_12"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="96"
            column="12"
            startOffset="4072"
            endLine="96"
            endColumn="33"
            endOffset="4093"/>
        <location id="R.color.black_alpha_24"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="97"
            column="12"
            startOffset="4123"
            endLine="97"
            endColumn="33"
            endOffset="4144"/>
        <location id="R.color.black_alpha_54"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="98"
            column="12"
            startOffset="4174"
            endLine="98"
            endColumn="33"
            endOffset="4195"/>
        <location id="R.color.border"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="88"
            column="12"
            startOffset="3864"
            endLine="88"
            endColumn="25"
            endOffset="3877"/>
        <location id="R.color.borderColor"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="34"
            column="12"
            startOffset="1054"
            endLine="34"
            endColumn="30"
            endOffset="1072"/>
        <location id="R.color.buttonBackground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="41"
            column="12"
            startOffset="1254"
            endLine="41"
            endColumn="35"
            endOffset="1277"/>
        <location id="R.color.buttonTextColor"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="42"
            column="12"
            startOffset="1305"
            endLine="42"
            endColumn="34"
            endOffset="1327"/>
        <location id="R.color.cardBackground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="37"
            column="12"
            startOffset="1128"
            endLine="37"
            endColumn="33"
            endOffset="1149"/>
        <location id="R.color.category_accessories"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="113"
            column="12"
            startOffset="4764"
            endLine="113"
            endColumn="39"
            endOffset="4791"/>
        <location id="R.color.category_bottoms"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="110"
            column="12"
            startOffset="4611"
            endLine="110"
            endColumn="35"
            endOffset="4634"/>
        <location id="R.color.category_other"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="114"
            column="12"
            startOffset="4819"
            endLine="114"
            endColumn="33"
            endOffset="4840"/>
        <location id="R.color.category_outerwear"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="111"
            column="12"
            startOffset="4662"
            endLine="111"
            endColumn="37"
            endOffset="4687"/>
        <location id="R.color.category_shoes"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="112"
            column="12"
            startOffset="4715"
            endLine="112"
            endColumn="33"
            endOffset="4736"/>
        <location id="R.color.category_tops"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="109"
            column="12"
            startOffset="4563"
            endLine="109"
            endColumn="32"
            endOffset="4583"/>
        <location id="R.color.colorBackground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="16"
            column="12"
            startOffset="457"
            endLine="16"
            endColumn="34"
            endOffset="479"/>
        <location id="R.color.colorError"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="24"
            column="12"
            startOffset="699"
            endLine="24"
            endColumn="29"
            endOffset="716"/>
        <location id="R.color.colorOnBackground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="17"
            column="12"
            startOffset="507"
            endLine="17"
            endColumn="36"
            endOffset="531"/>
        <location id="R.color.colorOnError"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="25"
            column="12"
            startOffset="744"
            endLine="25"
            endColumn="31"
            endOffset="763"/>
        <location id="R.color.colorOnPrimary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="8"
            column="12"
            startOffset="207"
            endLine="8"
            endColumn="33"
            endOffset="228"/>
        <location id="R.color.colorOnSecondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="13"
            column="12"
            startOffset="384"
            endLine="13"
            endColumn="35"
            endOffset="407"/>
        <location id="R.color.colorOnSurface"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="21"
            column="12"
            startOffset="628"
            endLine="21"
            endColumn="33"
            endOffset="649"/>
        <location id="R.color.colorPrimary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="6"
            column="12"
            startOffset="106"
            endLine="6"
            endColumn="31"
            endOffset="125"/>
        <location id="R.color.colorPrimaryVariant"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="7"
            column="12"
            startOffset="153"
            endLine="7"
            endColumn="38"
            endOffset="179"/>
        <location id="R.color.colorSecondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="11"
            column="12"
            startOffset="279"
            endLine="11"
            endColumn="33"
            endOffset="300"/>
        <location id="R.color.colorSecondaryVariant"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="12"
            column="12"
            startOffset="328"
            endLine="12"
            endColumn="40"
            endOffset="356"/>
        <location id="R.color.colorSurface"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="20"
            column="12"
            startOffset="581"
            endLine="20"
            endColumn="31"
            endOffset="600"/>
        <location id="R.color.containerBackground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="38"
            column="12"
            startOffset="1177"
            endLine="38"
            endColumn="38"
            endOffset="1203"/>
        <location id="R.color.destructiveRed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="150"
            column="12"
            startOffset="6482"
            endLine="150"
            endColumn="33"
            endOffset="6503"/>
        <location id="R.color.disabled"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="93"
            column="12"
            startOffset="4009"
            endLine="93"
            endColumn="27"
            endOffset="4024"/>
        <location id="R.color.divider"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="87"
            column="12"
            startOffset="3822"
            endLine="87"
            endColumn="26"
            endOffset="3836"/>
        <location id="R.color.dividerColor"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="33"
            column="12"
            startOffset="1005"
            endLine="33"
            endColumn="31"
            endOffset="1024"/>
        <location id="R.color.info"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="73"
            column="12"
            startOffset="3371"
            endLine="73"
            endColumn="23"
            endOffset="3382"/>
        <location id="R.color.labelQuaternary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="139"
            column="12"
            startOffset="5990"
            endLine="139"
            endColumn="34"
            endOffset="6012"/>
        <location id="R.color.md_theme_dark_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="55"
            column="12"
            startOffset="2629"
            endLine="55"
            endColumn="43"
            endOffset="2660"/>
        <location id="R.color.md_theme_dark_error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="50"
            column="12"
            startOffset="2390"
            endLine="50"
            endColumn="38"
            endOffset="2416"/>
        <location id="R.color.md_theme_dark_errorContainer"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="52"
            column="12"
            startOffset="2500"
            endLine="52"
            endColumn="47"
            endOffset="2535"/>
        <location id="R.color.md_theme_dark_onBackground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="56"
            column="12"
            startOffset="2688"
            endLine="56"
            endColumn="45"
            endOffset="2721"/>
        <location id="R.color.md_theme_dark_onError"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="51"
            column="12"
            startOffset="2444"
            endLine="51"
            endColumn="40"
            endOffset="2472"/>
        <location id="R.color.md_theme_dark_onErrorContainer"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="53"
            column="12"
            startOffset="2563"
            endLine="53"
            endColumn="49"
            endOffset="2600"/>
        <location id="R.color.md_theme_dark_onPrimary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="36"
            column="12"
            startOffset="1693"
            endLine="36"
            endColumn="42"
            endOffset="1723"/>
        <location id="R.color.md_theme_dark_onPrimaryContainer"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="38"
            column="12"
            startOffset="1816"
            endLine="38"
            endColumn="51"
            endOffset="1855"/>
        <location id="R.color.md_theme_dark_onSecondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="41"
            column="12"
            startOffset="1942"
            endLine="41"
            endColumn="44"
            endOffset="1974"/>
        <location id="R.color.md_theme_dark_onSecondaryContainer"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="43"
            column="12"
            startOffset="2069"
            endLine="43"
            endColumn="53"
            endOffset="2110"/>
        <location id="R.color.md_theme_dark_onSurface"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="58"
            column="12"
            startOffset="2805"
            endLine="58"
            endColumn="42"
            endOffset="2835"/>
        <location id="R.color.md_theme_dark_onSurfaceVariant"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="60"
            column="12"
            startOffset="2926"
            endLine="60"
            endColumn="49"
            endOffset="2963"/>
        <location id="R.color.md_theme_dark_onTertiary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="46"
            column="12"
            startOffset="2196"
            endLine="46"
            endColumn="43"
            endOffset="2227"/>
        <location id="R.color.md_theme_dark_onTertiaryContainer"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="48"
            column="12"
            startOffset="2321"
            endLine="48"
            endColumn="52"
            endOffset="2361"/>
        <location id="R.color.md_theme_dark_outline"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="62"
            column="12"
            startOffset="2992"
            endLine="62"
            endColumn="40"
            endOffset="3020"/>
        <location id="R.color.md_theme_dark_outlineVariant"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="63"
            column="12"
            startOffset="3048"
            endLine="63"
            endColumn="47"
            endOffset="3083"/>
        <location id="R.color.md_theme_dark_primary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="35"
            column="12"
            startOffset="1637"
            endLine="35"
            endColumn="40"
            endOffset="1665"/>
        <location id="R.color.md_theme_dark_primaryContainer"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="37"
            column="12"
            startOffset="1751"
            endLine="37"
            endColumn="49"
            endOffset="1788"/>
        <location id="R.color.md_theme_dark_secondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="40"
            column="12"
            startOffset="1884"
            endLine="40"
            endColumn="42"
            endOffset="1914"/>
        <location id="R.color.md_theme_dark_secondaryContainer"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="42"
            column="12"
            startOffset="2002"
            endLine="42"
            endColumn="51"
            endOffset="2041"/>
        <location id="R.color.md_theme_dark_surface"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="57"
            column="12"
            startOffset="2749"
            endLine="57"
            endColumn="40"
            endOffset="2777"/>
        <location id="R.color.md_theme_dark_surfaceVariant"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="59"
            column="12"
            startOffset="2863"
            endLine="59"
            endColumn="47"
            endOffset="2898"/>
        <location id="R.color.md_theme_dark_tertiary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="45"
            column="12"
            startOffset="2139"
            endLine="45"
            endColumn="41"
            endOffset="2168"/>
        <location id="R.color.md_theme_dark_tertiaryContainer"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="47"
            column="12"
            startOffset="2255"
            endLine="47"
            endColumn="50"
            endOffset="2293"/>
        <location id="R.color.md_theme_light_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="24"
            column="12"
            startOffset="1108"
            endLine="24"
            endColumn="44"
            endOffset="1140"/>
        <location id="R.color.md_theme_light_errorContainer"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="21"
            column="12"
            startOffset="977"
            endLine="21"
            endColumn="48"
            endOffset="1013"/>
        <location id="R.color.md_theme_light_onBackground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="25"
            column="12"
            startOffset="1168"
            endLine="25"
            endColumn="46"
            endOffset="1202"/>
        <location id="R.color.md_theme_light_onError"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="20"
            column="12"
            startOffset="920"
            endLine="20"
            endColumn="41"
            endOffset="949"/>
        <location id="R.color.md_theme_light_onErrorContainer"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="22"
            column="12"
            startOffset="1041"
            endLine="22"
            endColumn="50"
            endOffset="1079"/>
        <location id="R.color.md_theme_light_onPrimary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="5"
            column="12"
            startOffset="157"
            endLine="5"
            endColumn="43"
            endOffset="188"/>
        <location id="R.color.md_theme_light_onPrimaryContainer"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="7"
            column="12"
            startOffset="282"
            endLine="7"
            endColumn="52"
            endOffset="322"/>
        <location id="R.color.md_theme_light_onSecondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="10"
            column="12"
            startOffset="410"
            endLine="10"
            endColumn="45"
            endOffset="443"/>
        <location id="R.color.md_theme_light_onSecondaryContainer"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="12"
            column="12"
            startOffset="539"
            endLine="12"
            endColumn="54"
            endOffset="581"/>
        <location id="R.color.md_theme_light_onSurface"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="27"
            column="12"
            startOffset="1287"
            endLine="27"
            endColumn="43"
            endOffset="1318"/>
        <location id="R.color.md_theme_light_onSurfaceVariant"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="29"
            column="12"
            startOffset="1410"
            endLine="29"
            endColumn="50"
            endOffset="1448"/>
        <location id="R.color.md_theme_light_onTertiary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="15"
            column="12"
            startOffset="668"
            endLine="15"
            endColumn="44"
            endOffset="700"/>
        <location id="R.color.md_theme_light_onTertiaryContainer"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="17"
            column="12"
            startOffset="795"
            endLine="17"
            endColumn="53"
            endOffset="836"/>
        <location id="R.color.md_theme_light_outline"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="31"
            column="12"
            startOffset="1477"
            endLine="31"
            endColumn="41"
            endOffset="1506"/>
        <location id="R.color.md_theme_light_outlineVariant"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="32"
            column="12"
            startOffset="1534"
            endLine="32"
            endColumn="48"
            endOffset="1570"/>
        <location id="R.color.md_theme_light_primary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="4"
            column="12"
            startOffset="100"
            endLine="4"
            endColumn="41"
            endOffset="129"/>
        <location id="R.color.md_theme_light_primaryContainer"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="6"
            column="12"
            startOffset="216"
            endLine="6"
            endColumn="50"
            endOffset="254"/>
        <location id="R.color.md_theme_light_secondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="9"
            column="12"
            startOffset="351"
            endLine="9"
            endColumn="43"
            endOffset="382"/>
        <location id="R.color.md_theme_light_secondaryContainer"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="11"
            column="12"
            startOffset="471"
            endLine="11"
            endColumn="52"
            endOffset="511"/>
        <location id="R.color.md_theme_light_surface"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="26"
            column="12"
            startOffset="1230"
            endLine="26"
            endColumn="41"
            endOffset="1259"/>
        <location id="R.color.md_theme_light_surfaceVariant"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="28"
            column="12"
            startOffset="1346"
            endLine="28"
            endColumn="48"
            endOffset="1382"/>
        <location id="R.color.md_theme_light_tertiary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="14"
            column="12"
            startOffset="610"
            endLine="14"
            endColumn="42"
            endOffset="640"/>
        <location id="R.color.md_theme_light_tertiaryContainer"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="16"
            column="12"
            startOffset="728"
            endLine="16"
            endColumn="51"
            endOffset="767"/>
        <location id="R.color.navigationBarColor"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="46"
            column="12"
            startOffset="1430"
            endLine="46"
            endColumn="37"
            endOffset="1455"/>
        <location id="R.color.opaqueSeparator"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="131"
            column="12"
            startOffset="5607"
            endLine="131"
            endColumn="34"
            endOffset="5629"/>
        <location id="R.color.overlay"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="106"
            column="12"
            startOffset="4498"
            endLine="106"
            endColumn="26"
            endOffset="4512"/>
        <location id="R.color.placeholderText"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="133"
            column="12"
            startOffset="5724"
            endLine="133"
            endColumn="34"
            endOffset="5746"/>
        <location id="R.color.pressed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="92"
            column="12"
            startOffset="3967"
            endLine="92"
            endColumn="26"
            endOffset="3981"/>
        <location id="R.color.primary_dark"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="67"
            column="12"
            startOffset="3179"
            endLine="67"
            endColumn="31"
            endOffset="3198"/>
        <location id="R.color.rippleColor"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="50"
            column="12"
            startOffset="1565"
            endLine="50"
            endColumn="30"
            endOffset="1583"/>
        <location id="R.color.selected"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="91"
            column="12"
            startOffset="3924"
            endLine="91"
            endColumn="27"
            endOffset="3939"/>
        <location id="R.color.selectedItemBackground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="49"
            column="12"
            startOffset="1506"
            endLine="49"
            endColumn="41"
            endOffset="1535"/>
        <location id="R.color.shimmer_base"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="104"
            column="12"
            startOffset="4399"
            endLine="104"
            endColumn="31"
            endOffset="4418"/>
        <location id="R.color.shimmer_highlight"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="105"
            column="12"
            startOffset="4446"
            endLine="105"
            endColumn="36"
            endOffset="4470"/>
        <location id="R.color.statusBarColor"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="45"
            column="12"
            startOffset="1381"
            endLine="45"
            endColumn="33"
            endOffset="1402"/>
        <location id="R.color.success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="71"
            column="12"
            startOffset="3287"
            endLine="71"
            endColumn="26"
            endOffset="3301"/>
        <location id="R.color.systemGray2"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="125"
            column="12"
            startOffset="5333"
            endLine="125"
            endColumn="30"
            endOffset="5351"/>
        <location id="R.color.systemGray3"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="126"
            column="12"
            startOffset="5379"
            endLine="126"
            endColumn="30"
            endOffset="5397"/>
        <location id="R.color.systemGray4"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="127"
            column="12"
            startOffset="5425"
            endLine="127"
            endColumn="30"
            endOffset="5443"/>
        <location id="R.color.systemGray5"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="128"
            column="12"
            startOffset="5471"
            endLine="128"
            endColumn="30"
            endOffset="5489"/>
        <location id="R.color.systemGroupedBackground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="118"
            column="12"
            startOffset="5003"
            endLine="118"
            endColumn="42"
            endOffset="5033"/>
        <location id="R.color.systemRed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="142"
            column="8"
            startOffset="6098"
            endLine="142"
            endColumn="24"
            endOffset="6114"/>
        <location id="R.color.textColorHint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="30"
            column="12"
            startOffset="930"
            endLine="30"
            endColumn="32"
            endOffset="950"/>
        <location id="R.color.textColorPrimary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="28"
            column="12"
            startOffset="824"
            endLine="28"
            endColumn="35"
            endOffset="847"/>
        <location id="R.color.textColorSecondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/colors.xml"
            line="29"
            column="12"
            startOffset="875"
            endLine="29"
            endColumn="37"
            endOffset="900"/>
        <location id="R.color.text_inverse"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="84"
            column="12"
            startOffset="3754"
            endLine="84"
            endColumn="31"
            endOffset="3773"/>
        <location id="R.color.text_primary_ios"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="148"
            column="8"
            startOffset="6400"
            endLine="148"
            endColumn="31"
            endOffset="6423"/>
        <location id="R.color.text_secondary_ios"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="119"
            column="12"
            startOffset="5061"
            endLine="119"
            endColumn="37"
            endOffset="5086"/>
        <location id="R.color.text_tertiary_ios"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="120"
            column="12"
            startOffset="5114"
            endLine="120"
            endColumn="36"
            endOffset="5138"/>
        <location id="R.color.warning"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="72"
            column="12"
            startOffset="3329"
            endLine="72"
            endColumn="26"
            endOffset="3343"/>
        <location id="R.color.white_alpha_12"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="99"
            column="12"
            startOffset="4225"
            endLine="99"
            endColumn="33"
            endOffset="4246"/>
        <location id="R.color.white_alpha_24"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="100"
            column="12"
            startOffset="4276"
            endLine="100"
            endColumn="33"
            endOffset="4297"/>
        <location id="R.color.white_alpha_54"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="101"
            column="12"
            startOffset="4327"
            endLine="101"
            endColumn="33"
            endOffset="4348"/>
        <location id="R.drawable.button_background_destructive"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/button_background_destructive.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="24"
            endColumn="12"
            endOffset="859"/>
        <location id="R.drawable.edit_text_background_ios"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/edit_text_background_ios.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="9"
            endColumn="9"
            endOffset="345"/>
        <location id="R.drawable.ic_category_accessories"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_accessories.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="55631"/>
        <location id="R.drawable.ic_category_bottoms"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_bottoms.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="9464"/>
        <location id="R.drawable.ic_category_dresses"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_dresses.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="42"
            endColumn="10"
            endOffset="38025"/>
        <location id="R.drawable.ic_category_outerwear"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_outerwear.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="30331"/>
        <location id="R.drawable.ic_category_sets"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_sets.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="69"
            endColumn="10"
            endOffset="36276"/>
        <location id="R.drawable.ic_category_shoes"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_shoes.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="42"
            endColumn="10"
            endOffset="19617"/>
        <location id="R.drawable.ic_category_suits"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_suits.png"/>
        <location id="R.drawable.ic_category_tuxedo"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_tuxedo.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="27"
            endColumn="10"
            endOffset="1034"/>
        <location id="R.drawable.ic_category_underwear"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_underwear.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="24"
            endColumn="10"
            endOffset="6651"/>
        <location id="R.drawable.ic_category_workwear"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_category_workwear.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="21"
            endColumn="10"
            endOffset="28222"/>
        <location id="R.drawable.ic_clothing_placeholder"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_clothing_placeholder.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="12"
            endColumn="10"
            endOffset="529"/>
        <location id="R.drawable.ic_occasion_casual"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_occasion_casual.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="16"
            endColumn="10"
            endOffset="662"/>
        <location id="R.drawable.ic_occasion_daily"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_occasion_daily.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="28"
            endColumn="10"
            endOffset="1024"/>
        <location id="R.drawable.ic_occasion_date"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_occasion_date.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="16"
            endColumn="10"
            endOffset="606"/>
        <location id="R.drawable.ic_occasion_formal"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_occasion_formal.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="16"
            endColumn="10"
            endOffset="561"/>
        <location id="R.drawable.ic_occasion_home"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_occasion_home.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="16"
            endColumn="10"
            endOffset="503"/>
        <location id="R.drawable.ic_occasion_outdoor"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_occasion_outdoor.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="16"
            endColumn="10"
            endOffset="519"/>
        <location id="R.drawable.ic_occasion_party"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_occasion_party.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="16"
            endColumn="10"
            endOffset="549"/>
        <location id="R.drawable.ic_occasion_sports"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_occasion_sports.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="21"
            endColumn="10"
            endOffset="643"/>
        <location id="R.drawable.ic_occasion_travel"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_occasion_travel.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="16"
            endColumn="10"
            endOffset="607"/>
        <location id="R.drawable.ic_occasion_work"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_occasion_work.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="16"
            endColumn="10"
            endOffset="589"/>
        <location id="R.drawable.ic_subcategory_bag"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_bag.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="18702"/>
        <location id="R.drawable.ic_subcategory_belt"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_belt.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="14164"/>
        <location id="R.drawable.ic_subcategory_boots"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_boots.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="33"
            endColumn="10"
            endOffset="24111"/>
        <location id="R.drawable.ic_subcategory_bra"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_bra.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="20710"/>
        <location id="R.drawable.ic_subcategory_briefs"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_briefs.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="14878"/>
        <location id="R.drawable.ic_subcategory_camisole"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_camisole.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="15"
            endColumn="10"
            endOffset="3518"/>
        <location id="R.drawable.ic_subcategory_camisole_dress"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_camisole_dress.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="27"
            endColumn="10"
            endOffset="8464"/>
        <location id="R.drawable.ic_subcategory_canvas_shoes"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_canvas_shoes.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="27"
            endColumn="10"
            endOffset="80987"/>
        <location id="R.drawable.ic_subcategory_coat"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_coat.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="28405"/>
        <location id="R.drawable.ic_subcategory_dress"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_dress.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="24"
            endColumn="10"
            endOffset="28396"/>
        <location id="R.drawable.ic_subcategory_evening_dress"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_evening_dress.png"/>
        <location id="R.drawable.ic_subcategory_flared_skirt"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_flared_skirt.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="27"
            endColumn="10"
            endOffset="13762"/>
        <location id="R.drawable.ic_subcategory_formal_dress"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_formal_dress.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="9"
            endColumn="10"
            endOffset="630"/>
        <location id="R.drawable.ic_subcategory_formal_pants"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_formal_pants.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="12"
            endColumn="10"
            endOffset="3445"/>
        <location id="R.drawable.ic_subcategory_formal_shoes"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_formal_shoes.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="24"
            endColumn="10"
            endOffset="41199"/>
        <location id="R.drawable.ic_subcategory_functional"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_functional.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="43779"/>
        <location id="R.drawable.ic_subcategory_hat"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_hat.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="13121"/>
        <location id="R.drawable.ic_subcategory_heels"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_heels.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="27"
            endColumn="10"
            endOffset="18718"/>
        <location id="R.drawable.ic_subcategory_hoodie"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_hoodie.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="25818"/>
        <location id="R.drawable.ic_subcategory_jacket"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jacket.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="25225"/>
        <location id="R.drawable.ic_subcategory_jeans"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jeans.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="24033"/>
        <location id="R.drawable.ic_subcategory_jewelry"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jewelry.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="33"
            endColumn="10"
            endOffset="21410"/>
        <location id="R.drawable.ic_subcategory_jumpsuit"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_jumpsuit.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="24928"/>
        <location id="R.drawable.ic_subcategory_mermaid_skirt"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_mermaid_skirt.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="27"
            endColumn="10"
            endOffset="60259"/>
        <location id="R.drawable.ic_subcategory_other_acc"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_other_acc.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="36505"/>
        <location id="R.drawable.ic_subcategory_outer_vest"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_outer_vest.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="16710"/>
        <location id="R.drawable.ic_subcategory_pajamas"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pajamas.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="39129"/>
        <location id="R.drawable.ic_subcategory_pants"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pants.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="21163"/>
        <location id="R.drawable.ic_subcategory_pencil_skirt"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pencil_skirt.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="27"
            endColumn="10"
            endOffset="8922"/>
        <location id="R.drawable.ic_subcategory_pleated_skirt"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_pleated_skirt.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="45657"/>
        <location id="R.drawable.ic_subcategory_polo"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_polo.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="23821"/>
        <location id="R.drawable.ic_subcategory_rain_boots"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_rain_boots.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="27"
            endColumn="10"
            endOffset="48211"/>
        <location id="R.drawable.ic_subcategory_sandals"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sandals.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="24"
            endColumn="10"
            endOffset="13101"/>
        <location id="R.drawable.ic_subcategory_scarf"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_scarf.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="24"
            endColumn="10"
            endOffset="17166"/>
        <location id="R.drawable.ic_subcategory_shirt"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_shirt.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="22798"/>
        <location id="R.drawable.ic_subcategory_shorts"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_shorts.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="24"
            endColumn="10"
            endOffset="14715"/>
        <location id="R.drawable.ic_subcategory_skirt"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_skirt.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="24474"/>
        <location id="R.drawable.ic_subcategory_skirt_bottom"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_skirt_bottom.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="27"
            endColumn="10"
            endOffset="24226"/>
        <location id="R.drawable.ic_subcategory_slit_skirt"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_slit_skirt.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="27"
            endColumn="10"
            endOffset="17837"/>
        <location id="R.drawable.ic_subcategory_sneakers"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sneakers.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="24"
            endColumn="10"
            endOffset="8651"/>
        <location id="R.drawable.ic_subcategory_socks"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_socks.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="24"
            endColumn="10"
            endOffset="22324"/>
        <location id="R.drawable.ic_subcategory_spaghetti_dress"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_spaghetti_dress.png"/>
        <location id="R.drawable.ic_subcategory_sport_pants"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sport_pants.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="22731"/>
        <location id="R.drawable.ic_subcategory_sport_shoes"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sport_shoes.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="24"
            endColumn="10"
            endOffset="33618"/>
        <location id="R.drawable.ic_subcategory_straight_skirt"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_straight_skirt.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="27"
            endColumn="10"
            endOffset="14348"/>
        <location id="R.drawable.ic_subcategory_suit"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_suit.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="42275"/>
        <location id="R.drawable.ic_subcategory_sweater"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_sweater.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="24"
            endColumn="10"
            endOffset="60675"/>
        <location id="R.drawable.ic_subcategory_thermal"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_thermal.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="24"
            endColumn="10"
            endOffset="10485"/>
        <location id="R.drawable.ic_subcategory_tracksuit"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_tracksuit.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="24"
            endColumn="10"
            endOffset="26906"/>
        <location id="R.drawable.ic_subcategory_traditional"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_traditional.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="27"
            endColumn="10"
            endOffset="25209"/>
        <location id="R.drawable.ic_subcategory_tshirt"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_tshirt.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="24"
            endColumn="10"
            endOffset="11061"/>
        <location id="R.drawable.ic_subcategory_vest"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_vest.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="24"
            endColumn="10"
            endOffset="7765"/>
        <location id="R.drawable.ic_subcategory_wedding"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_subcategory_wedding.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="27"
            endColumn="10"
            endOffset="22022"/>
        <location id="R.drawable.option_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/option_background.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="15"
            endColumn="12"
            endOffset="457"/>
        <location id="R.drawable.spinner_background"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/spinner_background.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="7"
            endColumn="9"
            endOffset="281"/>
        <location id="R.layout.dialog_category_selection"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_category_selection.xml"
            line="3"
            column="1"
            startOffset="124"
            endLine="13"
            endColumn="15"
            endOffset="548"/>
        <location id="R.layout.dialog_color_selection"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_color_selection.xml"
            line="3"
            column="1"
            startOffset="124"
            endLine="13"
            endColumn="15"
            endOffset="545"/>
        <location id="R.layout.dialog_occasion_selection"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_occasion_selection.xml"
            line="3"
            column="1"
            startOffset="124"
            endLine="13"
            endColumn="15"
            endOffset="548"/>
        <location id="R.layout.dialog_photo_selection"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_photo_selection.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="127"
            endColumn="16"
            endOffset="4176"/>
        <location id="R.layout.dialog_photo_selection_ios"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_photo_selection_ios.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="67"
            endColumn="16"
            endOffset="2406"/>
        <location id="R.layout.item_color_spinner"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_color_spinner.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="26"
            endColumn="16"
            endOffset="941"/>
        <location id="R.menu.action_mode_menu"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/action_mode_menu.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="11"
            endColumn="8"
            endOffset="368"/>
        <location id="R.menu.menu"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/menu.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="4"
            endColumn="8"
            endOffset="116"/>
        <location id="R.string.accessibility_add_button"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="178"
            column="13"
            startOffset="7531"
            endLine="178"
            endColumn="44"
            endOffset="7562"/>
        <location id="R.string.accessibility_clothing_image"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="181"
            column="13"
            startOffset="7712"
            endLine="181"
            endColumn="48"
            endOffset="7747"/>
        <location id="R.string.accessibility_filter_button"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="180"
            column="13"
            startOffset="7651"
            endLine="180"
            endColumn="47"
            endOffset="7685"/>
        <location id="R.string.accessibility_search_button"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="179"
            column="13"
            startOffset="7590"
            endLine="179"
            endColumn="47"
            endOffset="7624"/>
        <location id="R.string.button_add"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="116"
            column="13"
            startOffset="5037"
            endLine="116"
            endColumn="30"
            endOffset="5054"/>
        <location id="R.string.button_cancel"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="117"
            column="13"
            startOffset="5079"
            endLine="117"
            endColumn="33"
            endOffset="5099"/>
        <location id="R.string.button_clear"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="123"
            column="13"
            startOffset="5348"
            endLine="123"
            endColumn="32"
            endOffset="5367"/>
        <location id="R.string.button_confirm"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="118"
            column="13"
            startOffset="5124"
            endLine="118"
            endColumn="34"
            endOffset="5145"/>
        <location id="R.string.button_delete"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="119"
            column="13"
            startOffset="5170"
            endLine="119"
            endColumn="33"
            endOffset="5190"/>
        <location id="R.string.button_deselect_all"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="125"
            column="13"
            startOffset="5441"
            endLine="125"
            endColumn="39"
            endOffset="5467"/>
        <location id="R.string.button_edit"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="120"
            column="13"
            startOffset="5215"
            endLine="120"
            endColumn="31"
            endOffset="5233"/>
        <location id="R.string.button_filter"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="122"
            column="13"
            startOffset="5303"
            endLine="122"
            endColumn="33"
            endOffset="5323"/>
        <location id="R.string.button_search"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="121"
            column="13"
            startOffset="5258"
            endLine="121"
            endColumn="33"
            endOffset="5278"/>
        <location id="R.string.button_select_all"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="124"
            column="13"
            startOffset="5392"
            endLine="124"
            endColumn="37"
            endOffset="5416"/>
        <location id="R.string.category_accessories"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="72"
            column="13"
            startOffset="3325"
            endLine="72"
            endColumn="40"
            endOffset="3352"/>
        <location id="R.string.category_bottoms"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="68"
            column="13"
            startOffset="3132"
            endLine="68"
            endColumn="36"
            endOffset="3155"/>
        <location id="R.string.category_dresses"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="69"
            column="13"
            startOffset="3180"
            endLine="69"
            endColumn="36"
            endOffset="3203"/>
        <location id="R.string.category_outerwear"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="70"
            column="13"
            startOffset="3229"
            endLine="70"
            endColumn="38"
            endOffset="3254"/>
        <location id="R.string.category_primary_placeholder"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="23"
            column="13"
            startOffset="787"
            endLine="23"
            endColumn="48"
            endOffset="822"/>
        <location id="R.string.category_sets"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="128"
            column="13"
            startOffset="5511"
            endLine="128"
            endColumn="33"
            endOffset="5531"/>
        <location id="R.string.category_shoes"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="71"
            column="13"
            startOffset="3279"
            endLine="71"
            endColumn="34"
            endOffset="3300"/>
        <location id="R.string.category_suits"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="129"
            column="13"
            startOffset="5556"
            endLine="129"
            endColumn="34"
            endOffset="5577"/>
        <location id="R.string.category_tops"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="67"
            column="13"
            startOffset="3087"
            endLine="67"
            endColumn="33"
            endOffset="3107"/>
        <location id="R.string.category_underwear"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="130"
            column="13"
            startOffset="5602"
            endLine="130"
            endColumn="38"
            endOffset="5627"/>
        <location id="R.string.category_workwear"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="131"
            column="13"
            startOffset="5652"
            endLine="131"
            endColumn="37"
            endOffset="5676"/>
        <location id="R.string.color_beige"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="100"
            column="13"
            startOffset="4474"
            endLine="100"
            endColumn="31"
            endOffset="4492"/>
        <location id="R.string.color_custom"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="88"
            column="13"
            startOffset="3958"
            endLine="88"
            endColumn="32"
            endOffset="3977"/>
        <location id="R.string.color_khaki"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="101"
            column="13"
            startOffset="4517"
            endLine="101"
            endColumn="31"
            endOffset="4535"/>
        <location id="R.string.dialog_option_camera"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="31"
            column="13"
            startOffset="1176"
            endLine="31"
            endColumn="40"
            endOffset="1203"/>
        <location id="R.string.dialog_option_gallery"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="32"
            column="13"
            startOffset="1228"
            endLine="32"
            endColumn="41"
            endOffset="1256"/>
        <location id="R.string.dialog_title_select_photo"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="30"
            column="13"
            startOffset="1117"
            endLine="30"
            endColumn="45"
            endOffset="1149"/>
        <location id="R.string.error_file_not_found"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="173"
            column="13"
            startOffset="7359"
            endLine="173"
            endColumn="40"
            endOffset="7386"/>
        <location id="R.string.error_generic"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="175"
            column="13"
            startOffset="7466"
            endLine="175"
            endColumn="33"
            endOffset="7486"/>
        <location id="R.string.error_invalid_data"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="174"
            column="13"
            startOffset="7414"
            endLine="174"
            endColumn="38"
            endOffset="7439"/>
        <location id="R.string.error_load_data"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="44"
            column="13"
            startOffset="1747"
            endLine="44"
            endColumn="35"
            endOffset="1769"/>
        <location id="R.string.error_network"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="171"
            column="13"
            startOffset="7259"
            endLine="171"
            endColumn="33"
            endOffset="7279"/>
        <location id="R.string.error_permission"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="172"
            column="13"
            startOffset="7308"
            endLine="172"
            endColumn="36"
            endOffset="7331"/>
        <location id="R.string.filter_by_category"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="158"
            column="13"
            startOffset="6772"
            endLine="158"
            endColumn="38"
            endOffset="6797"/>
        <location id="R.string.filter_by_color"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="159"
            column="13"
            startOffset="6825"
            endLine="159"
            endColumn="35"
            endOffset="6847"/>
        <location id="R.string.filter_by_occasion"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="160"
            column="13"
            startOffset="6875"
            endLine="160"
            endColumn="38"
            endOffset="6900"/>
        <location id="R.string.filter_clear_all"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="161"
            column="13"
            startOffset="6928"
            endLine="161"
            endColumn="36"
            endOffset="6951"/>
        <location id="R.string.message_add_success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="134"
            column="13"
            startOffset="5719"
            endLine="134"
            endColumn="39"
            endOffset="5745"/>
        <location id="R.string.message_delete_success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="135"
            column="13"
            startOffset="5772"
            endLine="135"
            endColumn="42"
            endOffset="5801"/>
        <location id="R.string.message_loading"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="142"
            column="13"
            startOffset="6162"
            endLine="142"
            endColumn="35"
            endOffset="6184"/>
        <location id="R.string.message_network_error"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="139"
            column="13"
            startOffset="5996"
            endLine="139"
            endColumn="41"
            endOffset="6024"/>
        <location id="R.string.message_no_data"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="141"
            column="13"
            startOffset="6113"
            endLine="141"
            endColumn="35"
            endOffset="6135"/>
        <location id="R.string.message_operation_failed"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="138"
            column="13"
            startOffset="5938"
            endLine="138"
            endColumn="44"
            endOffset="5969"/>
        <location id="R.string.message_permission_denied"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="140"
            column="13"
            startOffset="6053"
            endLine="140"
            endColumn="45"
            endOffset="6085"/>
        <location id="R.string.message_save_success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="136"
            column="13"
            startOffset="5828"
            endLine="136"
            endColumn="40"
            endOffset="5855"/>
        <location id="R.string.message_search_no_results"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="143"
            column="13"
            startOffset="6213"
            endLine="143"
            endColumn="45"
            endOffset="6245"/>
        <location id="R.string.message_update_success"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="137"
            column="13"
            startOffset="5882"
            endLine="137"
            endColumn="42"
            endOffset="5911"/>
        <location id="R.string.nav_calendar"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="112"
            column="13"
            startOffset="4933"
            endLine="112"
            endColumn="32"
            endOffset="4952"/>
        <location id="R.string.nav_outfit"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="111"
            column="13"
            startOffset="4891"
            endLine="111"
            endColumn="30"
            endOffset="4908"/>
        <location id="R.string.nav_profile"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="113"
            column="13"
            startOffset="4977"
            endLine="113"
            endColumn="31"
            endOffset="4995"/>
        <location id="R.string.nav_wardrobe"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="110"
            column="13"
            startOffset="4847"
            endLine="110"
            endColumn="32"
            endOffset="4866"/>
        <location id="R.string.occasion_daily"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="84"
            column="13"
            startOffset="3821"
            endLine="84"
            endColumn="34"
            endOffset="3842"/>
        <location id="R.string.occasion_outdoor"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="85"
            column="13"
            startOffset="3867"
            endLine="85"
            endColumn="36"
            endOffset="3890"/>
        <location id="R.string.occasion_sports"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="78"
            column="13"
            startOffset="3545"
            endLine="78"
            endColumn="35"
            endOffset="3567"/>
        <location id="R.string.photo_choose_gallery"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="153"
            column="13"
            startOffset="6603"
            endLine="153"
            endColumn="40"
            endOffset="6630"/>
        <location id="R.string.photo_remove"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="154"
            column="13"
            startOffset="6658"
            endLine="154"
            endColumn="32"
            endOffset="6677"/>
        <location id="R.string.photo_take_photo"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="152"
            column="13"
            startOffset="6555"
            endLine="152"
            endColumn="36"
            endOffset="6578"/>
        <location id="R.string.placeholder_select"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="25"
            column="13"
            startOffset="917"
            endLine="25"
            endColumn="38"
            endOffset="942"/>
        <location id="R.string.search_hint"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="157"
            column="13"
            startOffset="6724"
            endLine="157"
            endColumn="31"
            endOffset="6742"/>
        <location id="R.string.settings_about"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="168"
            column="13"
            startOffset="7194"
            endLine="168"
            endColumn="34"
            endOffset="7215"/>
        <location id="R.string.settings_language"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="164"
            column="13"
            startOffset="6997"
            endLine="164"
            endColumn="37"
            endOffset="7021"/>
        <location id="R.string.settings_notifications"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="166"
            column="13"
            startOffset="7092"
            endLine="166"
            endColumn="42"
            endOffset="7121"/>
        <location id="R.string.settings_privacy"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="167"
            column="13"
            startOffset="7146"
            endLine="167"
            endColumn="36"
            endOffset="7169"/>
        <location id="R.string.settings_theme"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="165"
            column="13"
            startOffset="7046"
            endLine="165"
            endColumn="34"
            endOffset="7067"/>
        <location id="R.string.toast_permissions_denied"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="41"
            column="13"
            startOffset="1655"
            endLine="41"
            endColumn="44"
            endOffset="1686"/>
        <location id="R.string.validation_category_required"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="147"
            column="13"
            startOffset="6356"
            endLine="147"
            endColumn="48"
            endOffset="6391"/>
        <location id="R.string.validation_invalid_input"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="149"
            column="13"
            startOffset="6478"
            endLine="149"
            endColumn="44"
            endOffset="6509"/>
        <location id="R.string.validation_name_required"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="146"
            column="13"
            startOffset="6294"
            endLine="146"
            endColumn="44"
            endOffset="6325"/>
        <location id="R.string.validation_name_too_long"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="148"
            column="13"
            startOffset="6420"
            endLine="148"
            endColumn="44"
            endOffset="6451"/>
        <location id="R.style.Animation_WardrobeApp_Activity"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="224"
            column="12"
            startOffset="11952"
            endLine="224"
            endColumn="49"
            endOffset="11989"/>
        <location id="R.style.AppModalStyle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="208"
            column="12"
            startOffset="10506"
            endLine="208"
            endColumn="32"
            endOffset="10526"/>
        <location id="R.style.AppTheme"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/styles.xml"
            line="4"
            column="12"
            startOffset="82"
            endLine="4"
            endColumn="27"
            endOffset="97"/>
        <location id="R.style.Base_Theme_WardrobeApp"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="90"
            column="12"
            startOffset="4296"
            endLine="90"
            endColumn="41"
            endOffset="4325"/>
        <location id="R.style.BottomSheetDialogTheme"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="190"
            column="12"
            startOffset="9476"
            endLine="190"
            endColumn="41"
            endOffset="9505"/>
        <location id="R.style.BottomSheetStyle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="193"
            column="12"
            startOffset="9649"
            endLine="193"
            endColumn="35"
            endOffset="9672"/>
        <location id="R.style.ButtonDestructive"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="177"
            column="12"
            startOffset="8800"
            endLine="177"
            endColumn="36"
            endOffset="8824"/>
        <location id="R.style.ButtonPrimary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="141"
            column="12"
            startOffset="6808"
            endLine="141"
            endColumn="32"
            endOffset="6828"/>
        <location id="R.style.ButtonStyle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/styles.xml"
            line="45"
            column="12"
            startOffset="1825"
            endLine="45"
            endColumn="30"
            endOffset="1843"/>
        <location id="R.style.CardStyle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/styles.xml"
            line="52"
            column="12"
            startOffset="2136"
            endLine="52"
            endColumn="28"
            endOffset="2152"/>
        <location id="R.style.DestructiveButton"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="83"
            column="12"
            startOffset="3947"
            endLine="83"
            endColumn="36"
            endOffset="3971"/>
        <location id="R.style.TextAppearance_App_Body"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="118"
            column="12"
            startOffset="5657"
            endLine="118"
            endColumn="42"
            endOffset="5687"/>
        <location id="R.style.TextAppearance_App_Callout"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="121"
            column="12"
            startOffset="5799"
            endLine="121"
            endColumn="45"
            endOffset="5832"/>
        <location id="R.style.TextAppearance_App_Headline"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="114"
            column="12"
            startOffset="5445"
            endLine="114"
            endColumn="46"
            endOffset="5479"/>
        <location id="R.style.TextAppearance_App_LargeTitle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="101"
            column="12"
            startOffset="4800"
            endLine="101"
            endColumn="48"
            endOffset="4836"/>
        <location id="R.style.TextAppearance_App_Subheadline"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="124"
            column="12"
            startOffset="5944"
            endLine="124"
            endColumn="49"
            endOffset="5981"/>
        <location id="R.style.TextAppearance_App_Title1"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="105"
            column="12"
            startOffset="5013"
            endLine="105"
            endColumn="44"
            endOffset="5045"/>
        <location id="R.style.TextAppearance_App_Title2"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="108"
            column="12"
            startOffset="5157"
            endLine="108"
            endColumn="44"
            endOffset="5189"/>
        <location id="R.style.TextAppearance_App_Title3"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="111"
            column="12"
            startOffset="5301"
            endLine="111"
            endColumn="44"
            endOffset="5333"/>
        <location id="R.style.TextAppearance_WardrobeApp_BodyLarge"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="155"
            column="12"
            startOffset="8701"
            endLine="155"
            endColumn="55"
            endOffset="8744"/>
        <location id="R.style.TextAppearance_WardrobeApp_BodyMedium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="159"
            column="12"
            startOffset="8873"
            endLine="159"
            endColumn="56"
            endOffset="8917"/>
        <location id="R.style.TextAppearance_WardrobeApp_BodySmall"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="163"
            column="12"
            startOffset="9047"
            endLine="163"
            endColumn="55"
            endOffset="9090"/>
        <location id="R.style.TextAppearance_WardrobeApp_HeadlineLarge"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="131"
            column="12"
            startOffset="7593"
            endLine="131"
            endColumn="59"
            endOffset="7640"/>
        <location id="R.style.TextAppearance_WardrobeApp_HeadlineMedium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="135"
            column="12"
            startOffset="7780"
            endLine="135"
            endColumn="60"
            endOffset="7828"/>
        <location id="R.style.TextAppearance_WardrobeApp_HeadlineSmall"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="139"
            column="12"
            startOffset="7969"
            endLine="139"
            endColumn="59"
            endOffset="8016"/>
        <location id="R.style.TextAppearance_WardrobeApp_LabelMedium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="171"
            column="12"
            startOffset="9400"
            endLine="171"
            endColumn="57"
            endOffset="9445"/>
        <location id="R.style.TextAppearance_WardrobeApp_LabelSmall"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="175"
            column="12"
            startOffset="9583"
            endLine="175"
            endColumn="56"
            endOffset="9627"/>
        <location id="R.style.TextAppearance_WardrobeApp_TitleLarge"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="143"
            column="12"
            startOffset="8156"
            endLine="143"
            endColumn="56"
            endOffset="8200"/>
        <location id="R.style.TextAppearance_WardrobeApp_TitleMedium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="147"
            column="12"
            startOffset="8337"
            endLine="147"
            endColumn="57"
            endOffset="8382"/>
        <location id="R.style.TextAppearance_WardrobeApp_TitleSmall"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="151"
            column="12"
            startOffset="8520"
            endLine="151"
            endColumn="56"
            endOffset="8564"/>
        <location id="R.style.Theme_WardrobeApp"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="95"
            column="12"
            startOffset="4532"
            endLine="95"
            endColumn="36"
            endOffset="4556"/>
        <location id="R.style.Theme_WardrobeApp_AppBarOverlay"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="211"
            column="12"
            startOffset="10679"
            endLine="211"
            endColumn="50"
            endOffset="10717"/>
        <location id="R.style.Theme_WardrobeApp_BottomSheetDialog"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="205"
            column="12"
            startOffset="10323"
            endLine="205"
            endColumn="54"
            endOffset="10365"/>
        <location id="R.style.Theme_WardrobeApp_Button_Destructive"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="223"
            column="12"
            startOffset="11461"
            endLine="223"
            endColumn="55"
            endOffset="11504"/>
        <location id="R.style.Theme_WardrobeApp_Button_Prominent"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="222"
            column="12"
            startOffset="11381"
            endLine="222"
            endColumn="53"
            endOffset="11422"/>
        <location id="R.style.Theme_WardrobeApp_EditTextStyle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="213"
            column="12"
            startOffset="10871"
            endLine="213"
            endColumn="50"
            endOffset="10909"/>
        <location id="R.style.Theme_WardrobeApp_Material3"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="6"
            column="12"
            startOffset="119"
            endLine="6"
            endColumn="46"
            endOffset="153"/>
        <location id="R.style.Theme_WardrobeApp_Material3_Dark"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="81"
            column="12"
            startOffset="4792"
            endLine="81"
            endColumn="51"
            endOffset="4831"/>
        <location id="R.style.Theme_WardrobeApp_NoActionBar"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="201"
            column="12"
            startOffset="10160"
            endLine="201"
            endColumn="48"
            endOffset="10196"/>
        <location id="R.style.Theme_WardrobeApp_PopupOverlay"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="212"
            column="12"
            startOffset="10780"
            endLine="212"
            endColumn="49"
            endOffset="10817"/>
        <location id="R.style.Widget_WardrobeApp_Button"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="180"
            column="12"
            startOffset="9786"
            endLine="180"
            endColumn="44"
            endOffset="9818"/>
        <location id="R.style.Widget_WardrobeApp_Button_Text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="196"
            column="12"
            startOffset="10600"
            endLine="196"
            endColumn="49"
            endOffset="10637"/>
        <location id="R.style.Widget_WardrobeApp_CardView"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="203"
            column="12"
            startOffset="10965"
            endLine="203"
            endColumn="46"
            endOffset="10999"/>
        <location id="R.style.Widget_WardrobeApp_Chip"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="209"
            column="12"
            startOffset="11224"
            endLine="209"
            endColumn="42"
            endOffset="11254"/>
        <location id="R.style.Widget_WardrobeApp_TextInputLayout"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes_material3.xml"
            line="215"
            column="12"
            startOffset="11517"
            endLine="215"
            endColumn="53"
            endOffset="11558"/>
        <location id="R.style.iOSGroupedBackground_Bottom"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="14"
            column="12"
            startOffset="675"
            endLine="14"
            endColumn="46"
            endOffset="709"/>
        <location id="R.style.iOSGroupedBackground_Middle"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="11"
            column="12"
            startOffset="491"
            endLine="11"
            endColumn="46"
            endOffset="525"/>
        <location id="R.style.iOSGroupedBackground_Top"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml"
            line="8"
            column="12"
            startOffset="313"
            endLine="8"
            endColumn="43"
            endOffset="344"/>
        <entry
            name="model"
            string="anim[fade_in(U),fade_out(U),scale_in(D),slide_in_left(D),slide_in_right(D),slide_out_left(D),slide_out_right(D),slide_up(D)],attr[colorOnPrimary(R),colorControlNormal(R),selectableItemBackground(E),textAppearanceBody1(R)],color[bottom_nav_color_selector(U),systemBlue(U),labelSecondary(U),fillTertiary(U),systemRed(D),secondarySystemGroupedBackground(U),separator(U),quaternarySystemGroupedBackground(U),background_secondary(U),border_primary(U),accent_primary(U),systemGray6(U),systemGray(U),material_dynamic_primary0(R),background_primary(U),text_primary(U),systemBackground(U),systemGroupedBackground(D),text_secondary(U),text_tertiary(U),surface_primary(U),white(U),labelPrimary(U),md_theme_light_error(U),primary(U),destructive(U),md_theme_light_primary(D),md_theme_light_onPrimary(D),md_theme_light_primaryContainer(D),md_theme_light_onPrimaryContainer(D),md_theme_light_secondary(D),md_theme_light_onSecondary(D),md_theme_light_secondaryContainer(D),md_theme_light_onSecondaryContainer(D),md_theme_light_tertiary(D),md_theme_light_onTertiary(D),md_theme_light_tertiaryContainer(D),md_theme_light_onTertiaryContainer(D),md_theme_light_onError(D),md_theme_light_errorContainer(D),md_theme_light_onErrorContainer(D),md_theme_light_background(D),md_theme_light_onBackground(D),md_theme_light_surface(D),md_theme_light_onSurface(D),md_theme_light_surfaceVariant(D),md_theme_light_onSurfaceVariant(D),md_theme_light_outline(D),md_theme_light_outlineVariant(D),md_theme_dark_primary(D),md_theme_dark_onPrimary(D),md_theme_dark_primaryContainer(D),md_theme_dark_onPrimaryContainer(D),md_theme_dark_secondary(D),md_theme_dark_onSecondary(D),md_theme_dark_secondaryContainer(D),md_theme_dark_onSecondaryContainer(D),md_theme_dark_tertiary(D),md_theme_dark_onTertiary(D),md_theme_dark_tertiaryContainer(D),md_theme_dark_onTertiaryContainer(D),md_theme_dark_error(D),md_theme_dark_onError(D),md_theme_dark_errorContainer(D),md_theme_dark_onErrorContainer(D),md_theme_dark_background(D),md_theme_dark_onBackground(D),md_theme_dark_surface(D),md_theme_dark_onSurface(D),md_theme_dark_surfaceVariant(D),md_theme_dark_onSurfaceVariant(D),md_theme_dark_outline(D),md_theme_dark_outlineVariant(D),primary_dark(D),accent(D),success(D),warning(D),info(D),background_tertiary(D),text_inverse(D),divider(D),border(D),selected(D),pressed(D),disabled(D),black_alpha_12(D),black_alpha_24(D),black_alpha_54(D),white_alpha_12(D),white_alpha_24(D),white_alpha_54(D),shimmer_base(D),shimmer_highlight(D),overlay(D),category_tops(D),category_bottoms(D),category_outerwear(D),category_shoes(D),category_accessories(D),category_other(D),black(D),text_secondary_ios(D),text_tertiary_ios(D),systemGray2(D),systemGray3(D),systemGray4(D),systemGray5(D),opaqueSeparator(D),placeholderText(D),labelTertiary(U),labelQuaternary(D),background_secondary_ios(D),background_primary_ios(D),text_primary_ios(D),destructiveRed(D),colorPrimary(D),colorPrimaryVariant(D),colorOnPrimary(D),colorSecondary(D),colorSecondaryVariant(D),colorOnSecondary(D),colorBackground(D),colorOnBackground(D),colorSurface(D),colorOnSurface(D),colorError(D),colorOnError(D),textColorPrimary(D),textColorSecondary(D),textColorHint(D),dividerColor(D),borderColor(D),cardBackground(D),containerBackground(D),buttonBackground(D),buttonTextColor(D),statusBarColor(D),navigationBarColor(D),selectedItemBackground(D),rippleColor(D)],drawable[button_background(U),button_background_destructive(D),button_background_prominent(U),color_circle_background(U),dialog_background(U),dialog_rounded_background(U),dotted_border_background(U),edit_text_background_ios(D),ic_add(U),ic_calendar(U),ic_category_accessories(D),ic_category_bottoms(D),ic_category_dresses(D),ic_category_outerwear(D),ic_category_sets(D),ic_category_shoes(D),ic_category_suits(D),ic_category_tops(U),ic_category_tuxedo(D),ic_category_underwear(D),ic_category_workwear(D),ic_check_circle(U),ic_chevron_right(U),ic_clear(U),ic_clothing_placeholder(D),ic_empty_box(U),ic_error(U),ic_launcher_background(U),ic_launcher_foreground(U),ic_launcher_foreground_1(R),ic_occasion_casual(D),ic_occasion_daily(D),ic_occasion_date(D),ic_occasion_formal(D),ic_occasion_home(D),ic_occasion_outdoor(D),ic_occasion_party(D),ic_occasion_sports(D),ic_occasion_travel(D),ic_occasion_work(D),ic_outfit(U),ic_profile(U),ic_replace_photo(U),ic_search(U),ic_subcategory_bag(D),ic_subcategory_belt(D),ic_subcategory_boots(D),ic_subcategory_bra(D),ic_subcategory_briefs(D),ic_subcategory_camisole(D),ic_subcategory_camisole_dress(D),ic_subcategory_canvas_shoes(D),ic_subcategory_coat(D),ic_subcategory_dress(D),ic_subcategory_evening_dress(D),ic_subcategory_flared_skirt(D),ic_subcategory_formal_dress(D),ic_subcategory_formal_pants(D),ic_subcategory_formal_shoes(D),ic_subcategory_functional(D),ic_subcategory_hat(D),ic_subcategory_heels(D),ic_subcategory_hoodie(D),ic_subcategory_jacket(D),ic_subcategory_jeans(D),ic_subcategory_jewelry(D),ic_subcategory_jumpsuit(D),ic_subcategory_mermaid_skirt(D),ic_subcategory_other_acc(D),ic_subcategory_outer_vest(D),ic_subcategory_pajamas(D),ic_subcategory_pants(D),ic_subcategory_pencil_skirt(D),ic_subcategory_pleated_skirt(D),ic_subcategory_polo(D),ic_subcategory_rain_boots(D),ic_subcategory_sandals(D),ic_subcategory_scarf(D),ic_subcategory_shirt(D),ic_subcategory_shorts(D),ic_subcategory_skirt(D),ic_subcategory_skirt_bottom(D),ic_subcategory_slit_skirt(D),ic_subcategory_sneakers(D),ic_subcategory_socks(D),ic_subcategory_spaghetti_dress(D),ic_subcategory_sport_pants(D),ic_subcategory_sport_shoes(D),ic_subcategory_straight_skirt(D),ic_subcategory_suit(D),ic_subcategory_sweater(D),ic_subcategory_thermal(D),ic_subcategory_tracksuit(D),ic_subcategory_traditional(D),ic_subcategory_tshirt(D),ic_subcategory_vest(D),ic_subcategory_wedding(D),ic_wardrobe(U),ios_style_group_background_bottom(U),ios_style_group_background_middle(U),ios_style_group_background_single(U),ios_style_group_background_top(U),option_background(D),replace_icon_background(U),search_background(U),secondary_system_grouped_background_ripple(U),selected_item_background(U),spinner_background(D)],id[image_container(D),image_placeholder(U),clothing_image_view(U),button_replace_photo(U),edit_text_name(U),layout_category(U),text_view_category_value(U),layout_color(U),text_view_color_value(U),layout_occasion(U),text_view_occasion_value(U),button_save(U),batch_category_selector_view(D),edit_notes_batch(D),edit_story_batch(D),edit_tags_batch(D),btn_select_images_batch(D),grid_view_selected_images(D),btn_save_batch(D),nav_bar(U),btn_back(U),tv_title(U),recycler_view(U),category_title(D),count_view(D),btn_batch_add(D),empty_view(D),btn_add_color(U),fragment_container(U),bottom_nav_container(U),bottom_navigation(U),category_recycler_view(D),color_recycler_view(D),fullscreen_image_view(U),close_button(U),occasion_recycler_view(D),option_camera(D),option_gallery(D),option_cancel(D),text_title(D),button_take_photo(D),button_choose_gallery(D),button_cancel(D),tv_current_date(U),tv_empty_state(U),tv_suggestion(U),recycler_view_outfits(U),layout_empty_state(D),tv_storage_info(U),tv_image_count(U),tv_clothing_count(U),btn_cleanup_images(U),btn_compress_images(U),btn_cleanup_unused(U),switch_dark_mode(U),switch_notifications(U),switch_auto_backup(U),multi_select_toolbar(D),et_search(U),iv_clear_search(U),chip_group_filter(U),group_recycler_view(U),tv_loading_state(U),fab_add_clothing(U),tv_date(U),tv_day_of_week(U),tv_outfit_description(U),btn_view_detail(D),category_icon(D),tv_category(D),iv_clothing(U),tv_name(U),selection_overlay(U),iv_selected_icon(D),clothing_image(U),clothing_name(D),clothing_category(D),color_circle(D),color_preview_circle(U),color_name(D),spinner_text(U),occasion_icon(U),occasion_name(U),tv_outfit_name(U),btn_save_outfit(D),btn_share_outfit(D),tv_category_title(U),tv_item_count(U),rv_clothing_items(U),category_selector(U),subcategory_selector(U),color_selector(U),occasion_selector(U),iv_empty(U),tv_empty_title(U),tv_empty_message(U),iv_error(U),tv_error_title(U),tv_error_message(U),btn_retry(U),progress_bar(U),item_icon(U),item_name(U),label_text(U),value_text(U),item_checkmark(U),btn_cancel(D),tv_selected_count(D),btn_select_all(D),btn_delete(D),spinner_icon(U),action_delete_multiple(D),nav_wardrobe(U),nav_outfit(U),nav_calendar(U),nav_profile(U)],layout[activity_add_clothing(U),activity_batch_add_clothing(U),layout_category_selector(U),activity_category_select(U),item_category_selector(U),activity_clothing_detail(U),activity_color_select(U),item_color_selector(U),activity_main(U),activity_occasion_select(U),item_occasion_selector(U),dialog_category_selection(D),dialog_color_selection(D),dialog_image_fullscreen(U),dialog_occasion_selection(D),dialog_photo_selection(D),dialog_photo_selection_ios(D),dialog_recyclerview(U),fragment_calendar(U),item_calendar(U),fragment_outfit(U),item_outfit(U),fragment_profile(U),fragment_wardrobe(U),multi_select_toolbar(U),item_wardrobe_group(U),item_clothing_horizontal(U),item_clothing_pro(U),item_color_spinner(D),list_item_selector_row(U),layout_empty(U),layout_error(U),layout_loading(U),spinner_item_with_icon(U)],menu[bottom_navigation_menu(U),action_mode_menu(D),menu(D)],mipmap[ic_launcher(U),ic_launcher_round(U)],string[app_name(U),section_required(U),hint_clothing_name(U),label_category(U),category_placeholder(U),section_optional(U),label_color(U),color_placeholder(U),label_occasion(U),occasion_placeholder(U),button_save(U),title_add_new_clothing(U),title_edit_clothing(U),category_primary_placeholder(D),category_secondary_placeholder(U),placeholder_select(D),dialog_title_select_color(U),dialog_title_select_occasion(U),dialog_title_select_photo(D),dialog_option_camera(D),dialog_option_gallery(D),dialog_button_cancel(U),toast_save_successful(U),toast_save_failed(U),toast_load_failed(U),toast_select_image_first(U),toast_image_selection_cancelled(U),toast_permissions_denied(D),error_load_data(D),error_get_storage_info(U),success_cleanup_all_images(U),error_cleanup_images(U),compressing(U),compress_all_images(U),success_compress_images(U),error_compress_images(U),error_start_compress(U),success_cleanup_unused_images(U),error_main_category_missing_batch(U),title_batch_add_prefix(U),images_selected_count_batch(U),error_no_images_selected_batch(U),error_incomplete_selection_batch(U),success_batch_add_clothing_summary(U),success_batch_add_clothing_summary_with_failures(U),error_batch_add_all_failed(U),info_no_items_to_add_batch(U),category_tops(D),category_bottoms(D),category_dresses(D),category_outerwear(D),category_shoes(D),category_accessories(D),occasion_work(U),occasion_casual(U),occasion_formal(U),occasion_sports(D),occasion_sport(U),occasion_party(U),occasion_travel(U),occasion_home(U),occasion_date(U),occasion_daily(D),occasion_outdoor(D),color_custom(D),color_black(U),color_white(U),color_gray(U),color_red(U),color_green(U),color_blue(U),color_yellow(U),color_pink(U),color_purple(U),color_orange(U),color_brown(U),color_beige(D),color_khaki(D),color_navy(U),toast_permission_granted_retry(U),toast_permission_denied(U),toast_create_image_file_failed(U),nav_wardrobe(D),nav_outfit(D),nav_calendar(D),nav_profile(D),button_add(D),button_cancel(D),button_confirm(D),button_delete(D),button_edit(D),button_search(D),button_filter(D),button_clear(D),button_select_all(D),button_deselect_all(D),category_sets(D),category_suits(D),category_underwear(D),category_workwear(D),message_add_success(D),message_delete_success(D),message_save_success(D),message_update_success(D),message_operation_failed(D),message_network_error(D),message_permission_denied(D),message_no_data(D),message_loading(D),message_search_no_results(D),validation_name_required(D),validation_category_required(D),validation_name_too_long(D),validation_invalid_input(D),photo_take_photo(D),photo_choose_gallery(D),photo_remove(D),search_hint(D),filter_by_category(D),filter_by_color(D),filter_by_occasion(D),filter_clear_all(D),settings_language(D),settings_theme(D),settings_notifications(D),settings_privacy(D),settings_about(D),error_network(D),error_permission(D),error_file_not_found(D),error_invalid_data(D),error_generic(D),accessibility_add_button(D),accessibility_search_button(D),accessibility_filter_button(D),accessibility_clothing_image(D)],style[Theme_MaterialComponents_DayNight_NoActionBar(R),SectionHeader(U),iOSGroupedBackground_Single(U),Separator(U),iOSListItem(U),iOSLabel(U),iOSValue(U),iOSChevron(U),ProminentButton(U),Widget_MaterialComponents_TextInputLayout_OutlinedBox(R),TextAppearance_App_Caption1(U),Widget_Material3_Button_OutlinedButton(R),ButtonTertiary(U),TextAppearance_App_Footnote(U),TextAppearance_App_Caption2(U),ButtonSecondary(U),Widget_WardrobeApp_Button_Outlined(U),iOSGroupedBackground(U),iOSGroupedBackground_Top(D),iOSGroupedBackground_Middle(D),iOSGroupedBackground_Bottom(D),Widget_AppCompat_Button(R),DestructiveButton(D),Base_Theme_WardrobeApp(D),Theme_WardrobeApp_Material3(D),Theme_WardrobeApp(D),TextAppearance_App_Base(U),TextAppearance_App_LargeTitle(D),TextAppearance_App_Title1(D),TextAppearance_App_Title2(D),TextAppearance_App_Title3(D),TextAppearance_App_Headline(D),TextAppearance_App_Body(D),TextAppearance_App_Callout(D),TextAppearance_App_Subheadline(D),ButtonPrimary(D),ButtonDestructive(D),BottomSheetDialogTheme(D),Theme_Design_Light_BottomSheetDialog(E),BottomSheetStyle(D),Widget_Design_BottomSheet_Modal(E),Theme_WardrobeApp_Dialog(U),Theme_Material3_DayNight_Dialog_Alert(R),Theme_WardrobeApp_NoActionBar(D),Theme(E),Theme_WardrobeApp_BottomSheetDialog(D),AppModalStyle(D),Theme_WardrobeApp_AppBarOverlay(D),ThemeOverlay_AppCompat_Dark_ActionBar(E),Theme_WardrobeApp_PopupOverlay(D),ThemeOverlay_AppCompat_Light(E),Theme_WardrobeApp_EditTextStyle(D),Widget_AppCompat_EditText(E),Theme_WardrobeApp_Button_Prominent(D),Theme_WardrobeApp_Button_Destructive(D),Theme_Material3_DayNight(E),TextAppearance_WardrobeApp_HeadlineLarge(D),TextAppearance_WardrobeApp_HeadlineMedium(D),TextAppearance_WardrobeApp_HeadlineSmall(D),TextAppearance_WardrobeApp_TitleLarge(D),TextAppearance_WardrobeApp_TitleMedium(D),TextAppearance_WardrobeApp_TitleSmall(D),TextAppearance_WardrobeApp_BodyLarge(D),TextAppearance_WardrobeApp_BodyMedium(D),TextAppearance_WardrobeApp_BodySmall(D),TextAppearance_WardrobeApp_LabelLarge(U),TextAppearance_WardrobeApp_LabelMedium(D),TextAppearance_WardrobeApp_LabelSmall(D),Widget_WardrobeApp_Button(D),Widget_WardrobeApp_CardView(D),Widget_WardrobeApp_Chip(D),Widget_WardrobeApp_TextInputLayout(D),Animation_WardrobeApp_Activity(D),Theme_WardrobeApp_Material3_Dark(D),TextAppearance_Material3_HeadlineLarge(E),TextAppearance_Material3_HeadlineMedium(E),TextAppearance_Material3_HeadlineSmall(E),TextAppearance_Material3_TitleLarge(E),TextAppearance_Material3_TitleMedium(E),TextAppearance_Material3_TitleSmall(E),TextAppearance_Material3_BodyLarge(E),TextAppearance_Material3_BodyMedium(E),TextAppearance_Material3_BodySmall(E),TextAppearance_Material3_LabelLarge(R),TextAppearance_Material3_LabelMedium(E),TextAppearance_Material3_LabelSmall(E),Widget_Material3_Button(E),Widget_WardrobeApp_Button_Text(D),Widget_Material3_Button_TextButton(E),Widget_Material3_CardView_Elevated(E),Widget_Material3_Chip_Filter(E),Widget_Material3_TextInputLayout_OutlinedBox(E),AppTheme(D),ButtonStyle(D),CardStyle(D)],xml[data_extraction_rules(U),backup_rules(U),file_paths(U)];c^d^e,99^f^10,9a^f^d,9f^11^12,a0^8,b4^b5,c2^9,fa^13,fb^13,fc^13,fd^13,100^14^15,101^f^11,102^16,178^17^1a0^228^229^9e^a0^18^ff^c2^1a1^22a^22b^1a2^22c^1a3^22d^22e^1a4^1a5^1a6^1a7^1a8^1a9^22f,179^17a^230^19,17a^fd^195^12^fb^fa,17b^1a^ae^1b^117^17c,17c^1a^1b^ae^1f,17e^1a^ae^1b^a0^d^117^17f,17f^101^9b^b^22,180^1c^121^12^c^231^19a,181^1a^ae^1b^117^182,182^1a^1b^ae^1f,183^1d,184^1d,185^9c^af,186^1d,187^9c,188^9d^fc^a^d,18a^1a^1b^1e^119^12f^18b^a1^1f,18b^20^1b^1e^233,18c^1a^1b^1e^119^131^18d^c0^1f,18d^20^1b^1e^236^233,18e^1a^1b^1e^232,18f^1a^190^20^100^c3^1f^1b^af^191^1e^f9^a0^16^21,190^20^1e^1b^16^25,191^1b^1e,192^20^f9^1b^16^ad^21,193^12^234^22^235^e,194^11^9b^b^22,195^169^22^e^ae^12^ad,196^b1^1f^1b^1e,197^b2^23^1b^1e^237,198^24^1e,199^a9,19a^f9^c0^a1^c1,19d^b3^b4,19e^b3^b4,228^18,229^238^fc,22a^12,22b^101,22c^22,22d^18,22e^ae,22f^23c^9a^21,231^241^e,233^23c^98^e,234^241^e,235^241^79,236^23c^98^d,237^232^268,238^21,239^238^fd,23a^238^fb,23b^238^fa,23d^23c^99^7e,23e^23f,23f^25e^26^27^28^29^2a^2b^2c^2d^2e^2f^30^31^23^32^33^34^35^36^37^38^39^3a^3b^3c^25f^260^261^262^263^264^265^266^267^268^269^26a^26b^26c^26d^26e^26f^4^5,240^23e,241^22,242^241,243^241,244^241,245^241,246^241,247^241,248^241,249^241^e,24a^23c^9a^21,24b^23c^99^21,24c^24d^24e,24e^24f,250^251^d^11^9d,252^240^253,254^24d^255,255^24f^9d,256^257,258^259,25a^25b^22^78,25c^24a,25d^24b,25f^271,260^272,261^273,262^274,263^275,264^276,265^277,266^278,267^279,268^27a,269^27b,26a^27c,26b^27d^268,26c^280,26d^281^269,26e^282,26f^4^5^3^6,270^25e^3d^3e^3f^40^41^42^43^44^45^46^47^48^49^4a^4b^4c^4d^4e^4f^50^51^52^53^54,27e^27f^268,283^25e^7f^80^81^82^83^84^85^86^87^88^89^8a^94^95^8b^8c^8d,284^27d^92^93,285^280^90;;;"/>
    </map>

</incidents>
